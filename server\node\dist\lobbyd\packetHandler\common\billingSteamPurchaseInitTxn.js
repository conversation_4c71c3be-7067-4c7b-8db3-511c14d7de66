"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingSteamPurchaseInitTxn = void 0;
const merror_1 = require("../../../motiflib/merror");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const userConnection_1 = require("../../userConnection");
const iPlatformBillingApiClient_1 = require("../../../motiflib/mhttp/iPlatformBillingApiClient");
// ----------------------------------------------------------------------------
class Cph_Common_BillingSteamPurchaseInitTxn {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        if (user.storeCode !== iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.STEAM) {
            // user.storeCode 는 엄밀히 LGBillingCode 의 대역대(?)는 아니지만..
            throw new merror_1.MError('not-in-steam', merror_1.MErrorCode.INVALID_REQUEST, { storeCode: user.storeCode });
        }
        const reqBody = packet.bodyObj;
        const { orderId, steamLangauge, steamCurrency, steamItemInfos } = reqBody;
        if (orderId === undefined) {
            throw new merror_1.MError('orderId-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
        }
        if (steamLangauge === undefined) {
            throw new merror_1.MError('steamLangauge-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
        }
        if (steamCurrency === undefined) {
            throw new merror_1.MError('steamCurrency-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
        }
        if (!Array.isArray(steamItemInfos)) {
            throw new merror_1.MError('steamItemInfos-array-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
        }
        for (const elem of steamItemInfos) {
            if (!Number.isInteger(elem.steamItemId) ||
                !Number.isInteger(elem.steamQty) ||
                !Number.isInteger(elem.steamAmount)) {
                throw new merror_1.MError('invalid-steamItemInfo', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
            }
            if (typeof elem.steamDescription !== 'string') {
                throw new merror_1.MError('steamItemInfos-steamDescription-string-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN, { reqBody });
            }
        }
        return Promise.resolve()
            .then(() => {
            return mhttp_1.default.platformBillingApi.steamPurchaseInitTxn(orderId, user.steamUserId, user.steamAppId, steamLangauge, steamCurrency, steamItemInfos);
        })
            .then((billingApiResp) => {
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                billingApiResp,
            });
        });
    }
}
exports.Cph_Common_BillingSteamPurchaseInitTxn = Cph_Common_BillingSteamPurchaseInitTxn;
//# sourceMappingURL=billingSteamPurchaseInitTxn.js.map