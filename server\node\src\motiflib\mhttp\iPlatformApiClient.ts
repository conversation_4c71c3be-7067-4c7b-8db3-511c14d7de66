// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { LGServerListResponse } from './linegamesApiClient';

/**
 * 플랫폼 API 공통 인터페이스
 */
export interface IPlatformApiClient {
  /**
   * 로그인
   */
  login(sessionToken: string): Promise<unknown>;

  /**
   * 월드 점검 여부 확인
   */
  isWorldInMaintenance(worldId: string, force?: boolean): Promise<boolean>;

  /**
   * 대량 메일 결과 보고
   */
  reportBulkMailResult(
    successUserList: string[],
    failUserList: { userTarget: string; reasonCd: string }[],
    configId: number
  ): Promise<boolean>;

  /**
   * 계정 삭제 결과 보고
   */
  reportAccountDeletionResult(
    successNidList: string[],
    failNidList: { nid: string; reasonCd: string }[]
  ): Promise<boolean>;

  /**
   * 메일 조회
   */
  getMails(userId: number, langCulture: string, level: number): Promise<any>;

  /**
   * 금칙어 포함 여부 확인
   */
  hasBadWord(text: string): Promise<boolean>;

  /**
   * 금칙어 텍스트 치환
   */
  replaceBadWordText?(text: string): Promise<string>;

  /**
   * 불건전 채팅 신고
   */
  reportBadChat(
    reportUserId: number,
    targetUserId: number,
    reasonCd: string,
    chatMsg: string,
    addInfo: string
  ): Promise<void>;

  /**
   * 서버 마이그레이션을 위한 NID 변경
   */
  changeNidForServerMigration(nid: string, toGameServerId: string): Promise<void>;

  /**
   * 회원 탈퇴
   */
  revoke(nid: string): Promise<boolean>;

  /**
   * 회원 탈퇴 취소
   */
  cancelRevoke(gnidSessionToken: string): Promise<boolean>;

  /**
   * 화이트 서버 IP 목록 요청
   */
  reqWhiteServerIpList(): Promise<string[]>;

  /**
   * GNID로 NID 및 서버 정보 가져오기
   */
  getNidAndServerInfoByGnid(gnidSessionToken: string): Promise<LGServerListResponse>;

  /**
   * 서버 목록 가져오기
   */
  getServerList(): Promise<any[]>;

  /**
   * 푸시 알림 전송
   */
  sendPushNotification(message: string, nid: string): Promise<void>;
  sendPushNotification(message: string, nids: string[]): Promise<void>;
  sendPushNotification(message: string, arg: string | string[]): Promise<void>;

  /**
   * 불건전 채팅 신고 사유 목록 조회
   */
  reportBadChatReasonList(langCulture: string): Promise<any>;
}
