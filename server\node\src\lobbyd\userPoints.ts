// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { update } from 'lodash/fp';
import assert from 'assert';
import * as crypto from 'crypto';
import { Promise as promise } from 'bluebird';

import { LoginInfo, GLogParam, User } from './user';
import { All } from './type/sync';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import mlog from '../motiflib/mlog';
import { CalcDucatExchangeRatio } from '../formula';
import * as sync from './type/sync';
import { MError, MErrorCode } from '../motiflib/merror';
import * as mutil from '../motiflib/mutil';
import mhttp from '../motiflib/mhttp';
import { isCash } from '../cms/pointDesc';
import { PrData } from '../motiflib/gameLog';
import { GuildLogUtil } from './guildUtil';
import mconf from '../motiflib/mconf';
import { PLATFORM } from '../motiflib/model/auth/enum';

interface GlogPointExchangeData {
  /**
   * CMS.Point.id(두캇|블루젬)
   */
  target_type: number;
  /**
   * 지불해야하는 "환전하지 않았을 때 원래 금액"
   */
  target_price: number;
  /**
   * 환전 전 재화 보유량으로, "환전하지 않고 지불된 재화"
   * ( 지불해야하는 금액보다 현재 보유량이 적을 때 환전이 발생하기 때문에 )
   */
  cur_value: number;
  /**
   * 대체 지불된 재화 (두캇|블루젬)
   */
  exchange_value: number;
  /**
   * 대체 지불한 레드젬
   */
  exchange_gem: number;
}

export interface GLogCashParam extends GLogParam {
  pointExchangeData?: GlogPointExchangeData;
  exchangeHash?: string;
}

export interface PointChange {
  cmsId: number;
  value: number;
}

export interface LGCashParam {
  /**
   * cash를 획득하는 경우
     https://developer.line.games/pages/viewpage.action?pageId=7276168 에 사용되는 reason
   */
  gainReason?: string;
  /**
   * cash를 사용하는 경우 값이 존재하면 https://developer.line.games/pages/viewpage.action?pageId=7276291
   * api를 사용한다.
   */
  itemId?: string;
  /**
   * cash를 사용하는 경우 값이 존재하면 https://developer.line.games/pages/viewpage.action?pageId=19598027
   * api를 사용한다. (uwo_${cashShop cms id or admiral cms id)})
   */
  productId?: string;

  /**
   * 동시에 여러번 구매하는 경우 https://developer.line.games/pages/viewpage.action?pageId=43422731
   * api를 사용한다.
   */
  buyCount?: number;
}

// 레드젬, 마일리지 소모한 경우.
export interface CashPayment {
  cmsId: number;
  amount: number;
  lgCashParam: LGCashParam;

  /**
   * 재화 환전을 한 경우, 환전에 대한 데이터. glog 를 위함.
   */
  glogPointExchangeData?: GlogPointExchangeData;
}

// 레드젬, 마일리지 얻은 경우.
export interface CashGain {
  cmsId: number;
  amount: number;
  reason: string; // 라인게임즈 api 에 사용됨.
}

export interface PointAndCashChanges {
  pointChanges: PointChange[];
  cashPayments: CashPayment[];
}

export interface InstallmentSavings {
  accumPoint: number;
  accumRate: number;
  accumCount: number;
  lastCmsId: number;
  lastDepositTimeUtc: number;
}

export interface Insurance {
  insuranceCmsId: number;
  unpaidTradeGoods: number;
  unpaidShip: number;
  unpaidSailor: number;
  unpaidDucat: number;
}

export interface InsuranceChange {
  insuranceCmsId?: number;
  unpaidTradeGoods?: number;
  unpaidShip?: number;
  unpaidSailor?: number;
  unpaidDucat?: number;
}

export interface InsuranceUnpaidChange {
  unpaidTradeGoods: number;
  unpaidShip: number;
  unpaidSailor: number;
  unpaidDucat: number;
}

export interface PointConsumptionCostParam {
  cmsId: number;
  cost: number;
}

export interface Mileage {
  month: number;
  value: number;
  bIsExpirationNotified: boolean;
}

// ----------------------------------------------------------------------------
// UserPoints object.
// ----------------------------------------------------------------------------
class UserPoints {
  private _points: { [cmsId: number]: number };
  private _installmentSavings: InstallmentSavings;
  private _insurance: Insurance;
  private _userId: number;
  private _mileages: Mileage[];

  // glog 를 위한 변수.
  private _freeRedGem: number;
  private _paidRedGem: number;

  constructor() {
    this._points = {};

    this._installmentSavings = {
      accumPoint: 0,
      accumRate: 0,
      accumCount: 0,
      lastCmsId: 0,
      lastDepositTimeUtc: 0,
    };

    this._insurance = {
      insuranceCmsId: 0,
      unpaidTradeGoods: 0,
      unpaidShip: 0,
      unpaidSailor: 0,
      unpaidDucat: 0,
    };

    this._userId = 0;

    this._freeRedGem = 0;
    this._paidRedGem = 0;

    this._mileages = [];
  }

  clone(): UserPoints {
    const c = new UserPoints();
    c.cloneSet(
      _.cloneDeep(this._points),
      _.cloneDeep(this._installmentSavings),
      _.cloneDeep(this._insurance),
      this._userId,
      this._freeRedGem,
      this._paidRedGem,
      _.cloneDeep(this._mileages)
    );
    return c;
  }

  cloneSet(
    points: { [cmsId: number]: number },
    installmentSavings: InstallmentSavings,
    insurance: Insurance,
    userId: number,
    freeRedGem: number,
    paidRedGem: number,
    mileages: Mileage[]
  ): void {
    this._points = points;
    this._installmentSavings = installmentSavings;
    this._insurance = insurance;
    this._userId = userId;
    this._freeRedGem = freeRedGem;
    this._paidRedGem = paidRedGem;
    this._mileages = mileages;
  }

  initWithLoginInfo(loginInfo: LoginInfo): void {
    for (const point of loginInfo.points) {
      const value = point.value;
      if (mutil.isNotANumber(value)) {
        mlog.error('points point.cmsId is NaN', { userId: loginInfo.userId, cmsId: point.cmsId });
        continue;
        // TODO: login should be failed.
      }

      this._points[point.cmsId] = point.value;
    }

    this._installmentSavings = update('lastDepositTimeUtc', parseInt, loginInfo.installmentSavings);

    this._insurance = {
      insuranceCmsId: loginInfo.insurance.insuranceCmsId,
      unpaidTradeGoods: parseInt(loginInfo.insurance.unpaidTradeGoods, 10),
      unpaidShip: parseInt(loginInfo.insurance.unpaidShip, 10),
      unpaidSailor: parseInt(loginInfo.insurance.unpaidSailor, 10),
      unpaidDucat: parseInt(loginInfo.insurance.unpaidDucat, 10),
    };

    this._userId = loginInfo.userId;

    if (loginInfo.mileages) {
      for (const elem of loginInfo.mileages) {
        this._mileages.push({
          month: elem.month,
          value: elem.value,
          bIsExpirationNotified: elem.isExpirationNotified === 1,
        });
      }
      this._mileages.sort((a, b) => {
        return a.month - b.month;
      });
    }
  }

  getPoint(cmsId: number): number {
    assert(cmsId !== cmsEx.EnergyPointCmsId);
    assert(cmsId !== cmsEx.CashShopMileage);

    if (this._points[cmsId]) {
      return this._points[cmsId];
    }
    return 0;
  }

  getMileage(curTimeUtc: number): number {
    let v = 0;
    const thisMonth = UserPoints.calcMileageMonth(curTimeUtc);
    for (const elem of this._mileages) {
      if (elem.month <= thisMonth - cms.Define.MileageExpirationMonth) {
        continue;
      }
      v += elem.value;
    }
    return v;
  }

  getExpireMileageInThisMonth(curTimeUtc: number): Mileage {
    const month = UserPoints.calcMileageMonth(curTimeUtc) - cms.Define.MileageExpirationMonth + 1;
    const mileage = this._mileages.find((elem) => elem.month === month);
    return mileage;
  }

  // battleParam 을 만들 때 사용함. 다른 경우에는 사용하지 않는걸 권장함 (write 하면 절대 안됨)
  getPointTable(): { [cmsId: number]: number } {
    return this._points;
  }

  // ----------------------------------------------------------------------------
  // 추가로 소지 가능한 금액을 리턴
  // ----------------------------------------------------------------------------
  calcAddable(cmsId: number, value: number): number {
    assert(cmsId !== cmsEx.EnergyPointCmsId);
    assert(cmsId !== cmsEx.CashShopMileage);

    const hardCap = cms.Point[cmsId].hardCap;
    if (!hardCap) {
      mlog.error('undefined hardCap.', { userId: this._userId, cmsId, value });
      return 0;
    }

    const myCurrentPoint = this.getPoint(cmsId);
    if (myCurrentPoint === undefined) {
      mlog.error('undefined _point.', { userId: this._userId, cmsId, value });
      return 0;
    }

    if (value < 0) {
      if (myCurrentPoint <= 0) {
        return 0;
      } else if (myCurrentPoint + value < 0) {
        return -myCurrentPoint;
      }
    }

    if (myCurrentPoint + value > hardCap) {
      return hardCap - myCurrentPoint;
    }

    return value;
  }

  // ----------------------------------------------------------------------------
  // 포인트의 값이 hardcap보다 많은 지 체크
  // ----------------------------------------------------------------------------
  isGreaterThanHardCap(cmsId: number, value: number): boolean {
    assert(cmsId !== cmsEx.EnergyPointCmsId);

    const hardCap = cms.Point[cmsId].hardCap;
    if (!hardCap) {
      mlog.error('undefined hard cap.', { userId: this._userId, cmsId, value });
      return false;
    }

    return value > hardCap ? true : false;
  }

  // ----------------------------------------------------------------------------
  // 포인트가 마이너스 값인지 체크
  // ----------------------------------------------------------------------------
  isMinusPoint(point: number): boolean {
    return point < 0 ? true : false;
  }

  // glog 과정이 없기에 userChangeOperator 와 같이 실 데이터 반영하지 않는 곳에서만 사용해야 됨.
  // applyPointChanges 사용 바람.
  setPointForChangeTask(cmsId: number, value: number): void {
    assert(cmsId !== cmsEx.EnergyPointCmsId);
    assert(cmsId !== cmsEx.CashShopMileage);

    const hardCap = cms.Point[cmsId].hardCap;
    if (value > hardCap) {
      mlog.error('Point value is greater than hard cap.', { userId: this._userId, cmsId, value });
      value = hardCap;
    }

    this._points[cmsId] = value;
  }

  getInstallmentSavings(): InstallmentSavings {
    return this._installmentSavings;
  }

  setInstallmentSavings(installmentSavings: InstallmentSavings): void {
    this._installmentSavings = installmentSavings;
  }

  setInstallmentSavingsLastDepositTimeUtc(timeUtc: number) {
    this._installmentSavings.lastDepositTimeUtc = timeUtc;
  }

  getInsuranceCmsId(): number {
    return this._insurance.insuranceCmsId;
  }

  setInsuranceCmsId(cmsId: number): void {
    this._insurance.insuranceCmsId = cmsId;
  }

  getInsurance(): Insurance {
    return this._insurance;
  }

  applyInsuranceChange(x: InsuranceChange): void {
    if (x.unpaidTradeGoods !== undefined) {
      this._insurance.unpaidTradeGoods = x.unpaidTradeGoods;
    }
    if (x.unpaidShip !== undefined) {
      this._insurance.unpaidShip = x.unpaidShip;
    }
    if (x.unpaidSailor !== undefined) {
      this._insurance.unpaidSailor = x.unpaidSailor;
    }
    if (x.unpaidDucat !== undefined) {
      this._insurance.unpaidDucat = x.unpaidDucat;
    }
  }

  applyInsuranceUnpaidChange(x: InsuranceUnpaidChange): void {
    _.merge<Insurance, InsuranceUnpaidChange>(this._insurance, x);
  }

  /**
   *
   * @param pointCosts
   * @param bPermitExchange 포인트가 부족할 경우 환전을 허용할 것인지.
   * @param lgCashParam lg cash 사용에 필요한 인자
   * @param ensurePointIsEnough
   * @returns null 인 경우 포인트 부족
   */
  buildPointAndCashChangesByPayment(
    pointCosts: PointConsumptionCostParam[],
    bPermitExchange: boolean,
    lgCashParam: LGCashParam,
    ensurePointIsEnough: boolean,
    pr_data?: PrData[]
  ): PointAndCashChanges {
    if (!pointCosts || pointCosts.length === 0) {
      return undefined;
    }

    const pointChanges: PointChange[] = [];
    const cashPayments: CashPayment[] = [];

    const prDataObj: { [pointCmsId: number]: PrData } = {};

    for (const pointCost of pointCosts) {
      assert(pointCost.cmsId !== cmsEx.EnergyPointCmsId);
      assert(pointCost.cmsId !== cmsEx.CashShopMileage);
      if (pointCost.cost === 0) {
        continue;
      }

      assert(pointCost.cost > 0);

      if (isCash(pointCost.cmsId)) {
        assert(lgCashParam && (lgCashParam.itemId || lgCashParam.productId));
        cashPayments.push({
          cmsId: pointCost.cmsId,
          amount: pointCost.cost,
          lgCashParam,
        });

        if (!prDataObj[pointCost.cmsId]) {
          prDataObj[pointCost.cmsId] = {
            type: pointCost.cmsId,
            amt: 0,
          };
        }
        prDataObj[pointCost.cmsId].amt += pointCost.cost;
      } else {
        const change = pointChanges.find((elem) => elem.cmsId === pointCost.cmsId);
        let oldPoint;
        if (change) {
          oldPoint = change.value;
        } else {
          oldPoint = this.getPoint(pointCost.cmsId);
        }
        let newPoint = oldPoint - pointCost.cost;
        if (
          newPoint < 0 &&
          bPermitExchange &&
          (pointCost.cmsId === cmsEx.DucatPointCmsId || pointCost.cmsId === cmsEx.BlueGemPointCmsId)
        ) {
          // 환전 처리
          let ratio;
          if (pointCost.cmsId === cmsEx.DucatPointCmsId) {
            ratio = CalcDucatExchangeRatio(-newPoint);
          } else if (pointCost.cmsId === cmsEx.BlueGemPointCmsId) {
            ratio = cms.Const.BluegemExchangePer.value;
          }

          // 필요 레드젬
          const exchangingCost = Math.ceil(-newPoint / ratio);
          assert(lgCashParam && lgCashParam.itemId);
          cashPayments.push({
            cmsId: cmsEx.RedGemPointCmsId,
            amount: exchangingCost,
            lgCashParam,
            glogPointExchangeData: {
              target_type: pointCost.cmsId,
              target_price: pointCost.cost,
              cur_value: oldPoint,
              exchange_value: -newPoint,
              exchange_gem: exchangingCost,
            },
          });
          if (!prDataObj[cmsEx.RedGemPointCmsId]) {
            prDataObj[cmsEx.RedGemPointCmsId] = {
              type: cmsEx.RedGemPointCmsId,
              amt: 0,
            };
          }
          prDataObj[cmsEx.RedGemPointCmsId].amt += exchangingCost;

          newPoint = 0;
        } else if (newPoint < 0) {
          if (ensurePointIsEnough) {
            throw new MError('not-enough-point', MErrorCode.NOT_ENOUGH_POINT, {
              pointCost,
              oldPoint,
              newPoint,
              bPermitExchange,
            });
          }
          return undefined;
        }

        if (oldPoint !== newPoint) {
          if (change) {
            change.value = newPoint;
          } else {
            pointChanges.push({
              cmsId: pointCost.cmsId,
              value: newPoint,
            });
          }

          if (!prDataObj[pointCost.cmsId]) {
            prDataObj[pointCost.cmsId] = {
              type: pointCost.cmsId,
              amt: 0,
            };
          }
          prDataObj[pointCost.cmsId].amt += oldPoint - newPoint;
        }
      }
    }

    if (pr_data) {
      _.forOwn(prDataObj, (elem) => {
        pr_data.push(elem);
      });
    }

    return { pointChanges, cashPayments };
  }

  applyPointChanges(pointChanges: PointChange[], glogParam: GLogParam): sync.Sync {
    if (!pointChanges || pointChanges.length === 0) {
      return {};
    }

    if (glogParam) {
      for (const change of pointChanges) {
        assert(change.cmsId !== cmsEx.EnergyPointCmsId);
        assert(change.cmsId !== cmsEx.CashShopMileage);
        assert(!isCash(change.cmsId));

        if (change.cmsId === cmsEx.DucatPointCmsId || change.cmsId === cmsEx.BlueGemPointCmsId) {
          glogParam.user.glog(
            change.cmsId === cmsEx.DucatPointCmsId ? 'common_gamemoney' : 'common_bluegem',
            {
              rsn: glogParam.rsn,
              add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
              fcv: change.value - this.getPoint(change.cmsId),
              frv: change.value,
              pcv: 0,
              prv: 0,
              sk: glogParam.user.storeCode,
            }
          );
        } else if (
          change.cmsId === cmsEx.ContributionPointCmsId ||
          change.cmsId === cmsEx.ResearchPointCmsId
        ) {
          glogParam.user.glog(
            change.cmsId === cmsEx.ContributionPointCmsId ? 'contribution_point' : 'research_point',
            {
              rsn: glogParam.rsn,
              add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
              cv: change.value - this.getPoint(change.cmsId),
              rv: change.value,
            }
          );
        } else if (change.cmsId === cmsEx.GuildCoinCmsId) {
          GuildLogUtil.gLog_GuildPoint(
            glogParam.user,
            glogParam.rsn,
            glogParam.add_rsn ? glogParam.add_rsn : null,
            change.value - this.getPoint(change.cmsId), // cv
            change.value // rv
          );
        }
      }
    }

    const sync: sync.Sync = {};
    for (const elem of pointChanges) {
      this._points[elem.cmsId] = elem.value;
      _.merge<sync.Sync, sync.Sync>(sync, {
        add: {
          points: {
            [elem.cmsId]: elem,
          },
        },
      });
    }

    return sync;
  }

  queryCash(user: User): Promise<void> {
    return Promise.resolve().then(() => {
      if (user.isTestBot()) {
        return Promise.resolve();
      } else {
        return mhttp.lgbillingd
          .queryCash(user.userId, user.storeCode, user.countryCreated)
          .then((ret) => {
            this._handleCashResp(ret, undefined);
          });
      }
    });
  }

  static generateExchangeHash(userId: number) {
    return crypto
      .createHash('sha1')
      .update(`exchange_${userId}_${new Date().getTime()}_${Math.random()}`, 'utf8')
      .digest('hex');
  }

  tryConsumeCashs(
    cashPayments: CashPayment[],
    sync: sync.Sync,
    user: User,
    glogParam: GLogCashParam
  ): Promise<any> {
    if (!cashPayments || cashPayments.findIndex((elem) => isCash(elem.cmsId)) === -1) {
      return Promise.resolve();
    }

    return promise
      .each(cashPayments, (change) => {
        assert(change.lgCashParam && (change.lgCashParam.itemId || change.lgCashParam.productId));
        return this.consumeCash(change.cmsId, change.amount, change.lgCashParam, user, {
          user: glogParam.user,
          rsn: glogParam.rsn,
          add_rsn: glogParam.add_rsn,
          exchangeHash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
          pointExchangeData: change.glogPointExchangeData,
        });
      })
      .then(() => {
        for (const change of cashPayments) {
          _.merge<sync.Sync, sync.Sync>(sync, {
            add: {
              points: {
                [change.cmsId]: {
                  cmsId: change.cmsId,
                  value: this.getPoint(change.cmsId),
                },
              },
            },
          });
        }
      });
  }

  tryAddCashs(
    cashGains: CashGain[],
    sync: sync.Sync,
    user: User,
    glogParam: GLogParam
  ): Promise<any> {
    if (!cashGains || cashGains.findIndex((elem) => isCash(elem.cmsId)) === -1) {
      return Promise.resolve();
    }

    const promises = [];
    for (const change of cashGains) {
      promises.push(this.addCash(change.cmsId, change.amount, change.reason, user, glogParam));
    }
    return Promise.all(promises).then(() => {
      for (const change of cashGains) {
        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            points: {
              [change.cmsId]: {
                cmsId: change.cmsId,
                value: this.getPoint(change.cmsId),
              },
            },
          },
        });
      }
    });
  }

  consumeCash(
    pointCmsId: number,
    amount: number,
    lgCashParam: LGCashParam,
    user: User,
    glogParam: GLogCashParam
  ): Promise<any> {
    assert(isCash(pointCmsId));
    assert(amount > 0);

    return mhttp.lgbillingd
      .consumeCash(
        user.userId,
        user.accountId,
        user.storeCode,
        user.countryCreated,
        pointCmsId,
        amount,
        lgCashParam
      )
      .then((ret) => {
        if (lgCashParam && lgCashParam.productId) {
          this._handleCashResp(ret ? ret.balanceList : undefined, glogParam);
        } else {
          this._handleCashResp(ret, glogParam);
        }
      });
  }

  addCash(
    pointCmsId: number,
    amount: number,
    reason: string,
    user: User,
    glogParam: GLogParam
  ): Promise<void> {
    assert(isCash(pointCmsId));
    assert(amount > 0);

    return mhttp.lgbillingd
      .addCash(
        user.userId,
        user.accountId,
        user.storeCode,
        user.countryCreated,
        pointCmsId,
        amount,
        reason
      )
      .then((ret) => {
        this._handleCashResp(ret, glogParam);
      });
  }

  tryBidding(
    blindCmsId: number,
    user: User,
    amount: number,
    reason: string,
    sync: sync.Sync,
    glogParam: GLogParam
  ) {
    return mhttp.lgbillingd
      .tryBidding(
        blindCmsId,
        user.userId,
        user.storeCode,
        user.accountId,
        user.countryCreated,
        amount,
        reason
      )
      .then((ret) => {
        _.merge<sync.Sync, sync.Sync>(sync, this._handleCashResp(ret, glogParam));
      });
  }

  bidResult(
    blindCmsId: number,
    user: User,
    bWinner: boolean,
    sync: sync.Sync,
    rsn: string,
    glogParam: GLogParam
  ) {
    return mhttp.lgbillingd
      .bidResult(
        blindCmsId,
        user.userId,
        user.storeCode,
        user.accountId,
        user.countryCreated,
        bWinner,
        rsn
      )
      .then((ret) => {
        _.merge<sync.Sync, sync.Sync>(sync, this._handleCashResp(ret, glogParam));
      });
  }

  /**
   * 빌링으로 구입한 재화 충전 요청이 완료된 뒤에 호출.
   * 유저 데이터 적용 및 glog
   */
  onChargeByPurchaseProduct(
    data: Parameters<UserPoints['_handleCashResp']>[0],
    glogParam: GLogParam
  ): sync.Sync {
    // [빌링] 중국의 경우에는 처리 로직이 다름. (유료/무료 재화를 플랫폼에 의존하지 않고 서버에서 직접 처리)
    if (mconf.isSDO) {
      return this._handleCashResp_SDO(data, glogParam);
    } else {
      return this._handleCashResp(data, glogParam);
    }
  }

  private _handleCashResp(
    data: { coinCd: string; balance: number; paymentType: string }[],
    glogParam: GLogCashParam
  ): sync.Sync {
    if (!data) {
      return {};
    }
    const oldRedGem = this.getPoint(cmsEx.RedGemPointCmsId);
    const oldFreeRedGem = this._freeRedGem;
    const oldPaidRedGem = this._paidRedGem;

    this._points[cmsEx.RedGemPointCmsId] = 0;
    for (const elem of data) {
      if (elem.coinCd === 'red_gem') {
        const v = elem.balance;
        this._points[cmsEx.RedGemPointCmsId] += v;

        if (elem.paymentType === 'PAID') {
          this._paidRedGem = v;
        } else {
          this._freeRedGem = v;
        }
      }
    }

    const newRedGem = this.getPoint(cmsEx.RedGemPointCmsId);

    if (glogParam && oldRedGem !== newRedGem) {
      glogParam.user.glog('common_gem', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        fcv: this._freeRedGem - oldFreeRedGem,
        frv: this._freeRedGem,
        pcv: this._paidRedGem - oldPaidRedGem,
        prv: this._paidRedGem,
        sk: glogParam.user.storeCode,
        exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
      });
    }
    if (glogParam && glogParam.pointExchangeData) {
      // TODO 확인 필요
      // '만들어진 환전 정보(빌링 서버에 요청한 amount)'와
      // '빌링 서버에서 받은 수치(balance)'가 다를 경우가 있는 지
      // 레드젬 10을 소모하는 걸로 요청했지만 마일리지가 대신 깎여서 처리 된다는 등..
      const glogExchangeData = glogParam.pointExchangeData;
      glogParam.user.glog('common_gem_exchange', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        target_type: glogExchangeData.target_type,
        target_price: glogExchangeData.target_price,
        cur_value: glogExchangeData.cur_value,
        exchange_value: glogExchangeData.exchange_value,
        exchange_gem: glogExchangeData.exchange_gem,
        exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
      });
    }

    return {
      add: {
        points: {
          [cmsEx.RedGemPointCmsId]: {
            cmsId: cmsEx.RedGemPointCmsId,
            value: newRedGem,
          },
        },
      },
    };
  }

  // [빌링] 중국 전용 (재화를 직접 처리하기 때문에 로직 차이가 있음. 차후 리팩토링은 필요함.)
  private _handleCashResp_SDO(
    data: { coinCd: string; balance: number; paymentType: string }[],
    glogParam: GLogCashParam
  ): sync.Sync {
    if (!data) {
      return {};
    }

    const oldFreeRedGemPoint = this.getPoint(cmsEx.RedGemPointCmsId);
    const oldPaidRedGemPoint = this.getPoint(cmsEx.PaidRedGemPointCmsId);

    const oldFreeRedGem = this._freeRedGem;
    const oldPaidRedGem = this._paidRedGem;

    this._points[cmsEx.RedGemPointCmsId] = 0;
    this._points[cmsEx.PaidRedGemPointCmsId] = 0;

    for (const elem of data) {
      if (elem.coinCd === 'red_gem') {
        const v = elem.balance;
        if (elem.paymentType === 'PAID') {
          this._points[cmsEx.PaidRedGemPointCmsId] += v;
        } else {
          this._points[cmsEx.RedGemPointCmsId] += v;
        }
      }
    }

    this._paidRedGem = this._points[cmsEx.PaidRedGemPointCmsId];
    this._freeRedGem = this._points[cmsEx.RedGemPointCmsId];

    const newFreeRedGemPoint = this.getPoint(cmsEx.RedGemPointCmsId);
    const newPaidRedGemPoint = this.getPoint(cmsEx.PaidRedGemPointCmsId);

    if (glogParam && (oldFreeRedGemPoint !== newFreeRedGemPoint || oldPaidRedGemPoint !== newPaidRedGemPoint)) {
      glogParam.user.glog('common_gem', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        fcv: this._freeRedGem - oldFreeRedGem,
        frv: this._freeRedGem,
        pcv: this._paidRedGem - oldPaidRedGem,
        prv: this._paidRedGem,
        sk: glogParam.user.storeCode,
        exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
      });
    }
    if (glogParam?.pointExchangeData) {
      // TODO 확인 필요
      // '만들어진 환전 정보(빌링 서버에 요청한 amount)'와
      // '빌링 서버에서 받은 수치(balance)'가 다를 경우가 있는 지
      // 레드젬 10을 소모하는 걸로 요청했지만 마일리지가 대신 깎여서 처리 된다는 등..
      const glogExchangeData = glogParam.pointExchangeData;
      glogParam.user.glog('common_gem_exchange', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        target_type: glogExchangeData.target_type,
        target_price: glogExchangeData.target_price,
        cur_value: glogExchangeData.cur_value,
        exchange_value: glogExchangeData.exchange_value,
        exchange_gem: glogExchangeData.exchange_gem,
        exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
      });
    }

    return {
      add: {
        points: {
          // 무료 레드젬
          [cmsEx.RedGemPointCmsId]: {
            cmsId: cmsEx.RedGemPointCmsId,
            value: newFreeRedGemPoint,
          },
          // 유료 레드젬
          [cmsEx.PaidRedGemPointCmsId]: {
            cmsId: cmsEx.PaidRedGemPointCmsId,
            value: newPaidRedGemPoint,
          },
        },
      },
    };
  }

  static calcMileageMonth(curTimeUtc: number): number {
    const curDate = new Date(curTimeUtc * 1000);
    return mutil.getLocalFullYear(curDate) * 12 + mutil.getLocalMonth(curDate);
  }

  /**
   * 마일리지 획득, 소모에 따른 변경 사항 반환
   * @param added 양수인 경우 획득, 음수인 경우 소모
   * @param curTimeUtc
   */
  buildMileageChanges(added: number, curTimeUtc: number): Mileage[] {
    const changes = [];
    const curMileageMonth = UserPoints.calcMileageMonth(curTimeUtc);
    for (const elem of this._mileages) {
      if (elem.month <= curMileageMonth - cms.Define.MileageExpirationMonth) {
        changes.push({
          month: elem.month,
          value: 0,
        });
      }
    }
    if (added < 0) {
      let remaining = -added;
      for (let i = 0; i < this._mileages.length; i++) {
        const mileage = this._mileages[i];
        if (mileage.month <= curMileageMonth - cms.Define.MileageExpirationMonth) {
          continue;
        }

        const use = Math.min(remaining, mileage.value);
        changes.push({
          month: mileage.month,
          value: mileage.value - use,
        });

        remaining -= use;

        if (remaining === 0) {
          break;
        }
      }
    } else if (added > 0) {
      const idx = this._mileages.findIndex((elem) => {
        return elem.month === curMileageMonth;
      });
      if (idx === -1) {
        changes.push({
          month: curMileageMonth,
          value: added,
        });
      } else {
        changes.push({
          month: curMileageMonth,
          value: this._mileages[idx].value + added,
        });
      }
    }

    return changes;
  }

  applyMileageChanges(changes: Mileage[], glogParam: GLogParam): sync.Sync {
    if (!changes) {
      return {};
    }

    // 시간 변화에 따라 기대 결과랑 다르게 기록될 수 있음.
    // TODO 정확한 기록을 위한 처리 필요( 변화량을 인자로 넣어주는 등 )
    const oldMileageForGlog = this.getMileage(mutil.curTimeUtc());

    for (const change of changes) {
      const idx = this._mileages.findIndex((elem) => {
        return elem.month === change.month;
      });
      if (change.value === 0) {
        if (idx !== -1) {
          this._mileages.splice(idx, 1);
        }
      } else {
        if (idx !== -1) {
          this._mileages[idx] = change;
        } else {
          this._mileages.push(change);
        }
      }
    }

    let mileage = 0;
    for (const elem of this._mileages) {
      mileage += elem.value;
    }

    if (glogParam && oldMileageForGlog !== mileage) {
      glogParam.user.glog('mileage', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        cv: mileage - oldMileageForGlog,
        rv: mileage,
        flag: mileage - oldMileageForGlog > 0 ? 0 : 1,
      });
    }

    const sync: sync.Sync = {
      add: {
        mileages: this._mileages,
        // [TEMP] 클라 마일리지 구현 후 제거한다.
        // https://jira.line.games/browse/UWO-16497
        points: {
          [cmsEx.CashShopMileage]: {
            cmsId: cmsEx.CashShopMileage,
            value: mileage,
          },
        },
      },
      remove: {
        mileages: true,
      },
    };

    return sync;
  }

  get paidRedGem(): number {
    return this._paidRedGem;
  }
  get freeRedGem(): number {
    return this._freeRedGem;
  }

  getSyncData(): All {
    const ret: All = {
      points: {},
    };
    _.forOwn(this._points, (value, cmsIdStr) => {
      const cmsId = parseInt(cmsIdStr, 10);
      ret.points[cmsIdStr] = {
        cmsId,
        value,
      };
    });

    ret.installmentSavings = this._installmentSavings;
    ret.insurance = this._insurance;

    ret.mileages = this._mileages;
    let mileage = 0;
    const curTimeUtc = mutil.curTimeUtc();
    const curMileageMonth = UserPoints.calcMileageMonth(curTimeUtc);
    for (const elem of this._mileages) {
      if (elem.month <= curMileageMonth - cms.Define.MileageExpirationMonth) {
        continue;
      }
      mileage += elem.value;
    }
    // [TEMP] 클라 마일리지 구현 후 제거한다.
    // https://jira.line.games/browse/UWO-16497
    if (mileage > 0) {
      ret.points[cmsEx.CashShopMileage] = {
        cmsId: cmsEx.CashShopMileage,
        value: mileage,
      };
    }

    return ret;
  }

  // ----------------------------------------------------------------------------
  // 이벤트 처리 결과에 따른 함수 호출
  // ----------------------------------------------------------------------------

  // point가 변경 되었을 경우 추가 처리 로직
  // cmsId:Point CmsId, prevPoint: 이전 금액 curPoint: 변경 된 현재 금액
  onChangedMoney(cmsId: number, prevPoint: number, curPoint: number): void {}
}

// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------

export default UserPoints;
