"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingQueryPurchaseDetail = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const userConnection_1 = require("../../userConnection");
// ----------------------------------------------------------------------------
class Cph_Common_BillingQueryPurchaseDetail {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const reqBody = packet.bodyObj;
        const { orderId } = reqBody;
        return Promise.resolve()
            .then(() => {
            return mhttp_1.default.platformBillingApi.queryPurchaseDetail(orderId);
        })
            .then((billingApiResp) => {
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                billingApiResp,
            });
        });
    }
}
exports.Cph_Common_BillingQueryPurchaseDetail = Cph_Common_BillingQueryPurchaseDetail;
//# sourceMappingURL=billingQueryPurchaseDetail.js.map