libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -fstrict-aliasing -ffast-math -Wall -fexceptions -MT src/prep_cif.lo -MD -MP -MF src/.deps/prep_cif.Tpo -c src/prep_cif.c  -fPIC -DPIC -o src/prep_cif.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -fstrict-aliasing -ffast-math -Wall -fexceptions -MT src/types.lo -MD -MP -MF src/.deps/types.Tpo -c src/types.c  -fPIC -DPIC -o src/types.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -fstrict-aliasing -ffast-math -Wall -fexceptions -MT src/raw_api.lo -MD -MP -MF src/.deps/raw_api.Tpo -c src/raw_api.c  -fPIC -DPIC -o src/raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -fstrict-aliasing -ffast-math -Wall -fexceptions -MT src/java_raw_api.lo -MD -MP -MF src/.deps/java_raw_api.Tpo -c src/java_raw_api.c  -fPIC -DPIC -o src/java_raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUIL