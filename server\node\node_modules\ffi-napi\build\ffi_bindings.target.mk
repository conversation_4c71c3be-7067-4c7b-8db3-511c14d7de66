# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := ffi_bindings
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=ffi_bindings' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG' \
	'-DV8_ENABLE_CHECKS'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-std=gnu++14

INCS_Debug := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api \
	-I/mnt/c/work/uwo/game/server/node/node_modules/get-uv-event-loop-napi-h/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/get-symbol-from-current-process-h/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/ref-napi/include \
	-I$(srcdir)/deps/libffi/include \
	-I$(srcdir)/deps/libffi/config/linux/x64

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=ffi_bindings' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-std=gnu++14

INCS_Release := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api \
	-I/mnt/c/work/uwo/game/server/node/node_modules/get-uv-event-loop-napi-h/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/get-symbol-from-current-process-h/include \
	-I/mnt/c/work/uwo/game/server/node/node_modules/ref-napi/include \
	-I$(srcdir)/deps/libffi/include \
	-I$(srcdir)/deps/libffi/config/linux/x64

OBJS := \
	$(obj).target/$(TARGET)/src/ffi.o \
	$(obj).target/$(TARGET)/src/callback_info.o \
	$(obj).target/$(TARGET)/src/threaded_callback_invokation.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# Make sure our dependencies are built before any of us.
$(OBJS): | $(builddir)/nothing.a $(builddir)/libffi.a $(obj).target/../node-addon-api/nothing.a $(obj).target/deps/libffi/libffi.a

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(obj).target/ffi_bindings.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/ffi_bindings.node: LIBS := $(LIBS)
$(obj).target/ffi_bindings.node: TOOLSET := $(TOOLSET)
$(obj).target/ffi_bindings.node: $(OBJS) $(obj).target/../node-addon-api/nothing.a $(obj).target/deps/libffi/libffi.a FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/ffi_bindings.node
# Add target alias
.PHONY: ffi_bindings
ffi_bindings: $(builddir)/ffi_bindings.node

# Copy this to the executable output path.
$(builddir)/ffi_bindings.node: TOOLSET := $(TOOLSET)
$(builddir)/ffi_bindings.node: $(obj).target/ffi_bindings.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/ffi_bindings.node
# Short alias for building this executable.
.PHONY: ffi_bindings.node
ffi_bindings.node: $(obj).target/ffi_bindings.node $(builddir)/ffi_bindings.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/ffi_bindings.node

