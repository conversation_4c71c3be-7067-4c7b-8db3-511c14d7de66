// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import Container from 'typedi';

import mhttp from '../../../motiflib/mhttp';
import mconf from '../../../motiflib/mconf';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { getUserLightInfos, UserLightInfo } from '../../../motiflib/userCacheRedisHelper';
import cms from '../../../cms';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { LobbyService } from '../../server';
import { ClientPacketHandler } from '../index';
import { FRIEND_STATE } from '../../userFriends';

const rsn = 'friend_enemy';
const add_rsn = null;
// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

interface RequestBody {
  targetUserId: number;
}

interface ResponseBody {
  //
}

// ----------------------------------------------------------------------------
export class Cph_Common_ChatMuteUser implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;
    const { targetUserId } = reqBody;

    if (targetUserId === undefined || !Number.isInteger(targetUserId)) {
      throw new MError(
        'targetUserId-integer-expected',
        MErrorCode.INVALID_REQ_BODY_CHAT_MUTE_USER,
        { reqBody }
      );
    }
    if (targetUserId == user.userId) {
      throw new MError('can-not-mute-self', MErrorCode.INVALID_REQ_BODY_CHAT_MUTE_USER, {
        reqBody,
      });
    }

    if (user.userFriends.getFriend(targetUserId)) {
      throw new MError('target-is-friend', MErrorCode.CANNOT_BE_MUTE_FRIEND, {
        reqBody,
      });
    }

    const volanteUserId: string = user.userId.toString();
    const volanteTargetUserId: string = targetUserId.toString();
    let targetUser: UserLightInfo;
    return Promise.resolve()
      .then(() => {
        // 에러 절차를 명확하게 하기 위해.
        // chatInit 이 된 상태인지와 볼란테에 접속 중인지를 알기 위함.
        // 무거운 조회는 아닐 거라 보고..
        return mhttp.platformChatApi.getSessions(volanteUserId);
      })
      .then(() => {
        return mhttp.platformChatApi.getUserMuteUserCount(volanteUserId).then((muteUserCount) => {
          const bEnough = muteUserCount < cms.Define.ChatMaxMuteUserCount;
          if (!bEnough) {
            throw new MError('exceed-max-mute-count', MErrorCode.CHAT_EXCEED_MAX_MUTE_USER_COUNT, {
              muteUserCount,
              max: cms.Define.ChatMaxMuteUserCount,
            });
          }
          return null;
        });
      })
      .then(() => {
        // 대상 유저가 이 게임 서버에 존재하는 유저인지 확인용.
        // - 현재 시스템상 다른 서버 userId 도 볼란테에 등록될 가능성이 있을 듯 함. 예방 차원
        // getUserLightInfos 가 단순 조회치고는 무거울 수 있을 듯 함..
        const { userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr } =
          Container.get(LobbyService);
        const worldConfg = mconf.getWorldConfig();
        return getUserLightInfos(
          [targetUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((results) => {
          targetUser = results?.[targetUserId];
          if (!targetUser) {
            throw new MError(
              'target-user-not-exist',
              MErrorCode.CHAT_MUTE_USER_NOT_EXIST_TARGET_USER,
              { reqBody }
            );
          }
          return null;
        });
      })

      .then(() => {
        return mhttp.platformChatApi.muteUser(volanteUserId, volanteTargetUserId);
      })
      .then(() => {
        user.glog('friend_enemy', {
          rsn,
          add_rsn,
          friend_nid: targetUser.pubId,
          friend_gameUserId: targetUserId,
          type: 1, // 차단:1 해제:0
        });
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {});
      });
  }
}
