// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';
import moment from 'moment';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { UNBUYABLE_REASON, RestrictedProduct, FixedTermProduct } from '../../userCashShop';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import {
  CASH_SHOP_SALE_TYPE,
  CASH_SHOP_SALE_POINT_TYPE,
  CashShopDesc,
  CASH_SHOP_PRODUCT_TYPE,
  getCashShopMileageBonus,
  isExistWorldBuffTimeField,
  getWorldBuffAddTime,
} from '../../../cms/cashShopDesc';
import { UserChangeSpec } from '../../UserChangeTask/commonChangeSpec';
import {
  RewardAndPaymentElem,
  RewardAndPaymentSpec,
  RNP_TYPE,
} from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  TryData,
  Changes,
  CHANGE_TASK_RESULT,
  UserChangeTask,
  CHANGE_TASK_REASON,
  ActualGain,
} from '../../UserChangeTask/userChangeTask';
import { BuffSync, WorldBuffUtil } from '../../userBuffs';
import {
  opAddPoint,
  opDeleteCashShopRestrictedProduct,
  opSetCashShopRestrictedProduct,
  opDeleteCashShopFixedTermProduct,
  opSetCashShopFixedTermProduct,
  opAddItem,
  opAddMateEquip,
  opAddShip,
  opAddMate,
  opUpgradeShipBlueprint,
  opSetCashShopGachaBoxGuaranteeAccum,
  opAddSoundPack,
  opAddDirectmail,
  opAddMileage,
  opAddShipSlotItem,
  opBuyDailySubscription,
  opSetHotSpotCoolTimeUtc,
} from '../../UserChangeTask/userChangeOperator';
import {
  SECONDS_PER_DAY,
  CalcCashShopQuestPassWithMateCost,
  SECONDS_PER_MINUTE,
  CalcCashShopBuffUpgradeCostPerMinute,
  SECONDS_PER_HOUR,
} from '../../../formula';
import {
  CashShopBoxRatioDesc,
  ContentsDesc,
  pickGachaBox,
} from '../../../cms/cashShopBoxRatioDesc';
import mlog from '../../../motiflib/mlog';
import { REWARD_TYPE, getRewardNameCmsTableByRewardType } from '../../../cms/rewardDesc';
import { CostData, PrData, RewardData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { AccumulateParam } from '../../userAchievement';
import { RewardFixedElemDesc } from '../../../cms/rewardFixedDesc';
import { ClientPacketHandler } from '../index';
import mconf from '../../../motiflib/mconf';
import { ITEM_TYPE } from '../../../cms/itemDesc';
import { DailySubscriptionDesc } from '../../../cms/dailySubscription';
import { QuestPassType } from '../../../cms/questPassDesc';
import mhttp from '../../../motiflib/mhttp';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'cash_shop_buy_without_purchase';
const add_rsn = null;

interface RequestBody {
  cmsId: number;
  amount?: number; // CASH_SHOP_PRODUCT_TYPE.REWARD_FIXED 인 경우 사용, 없으면 1로 간주
  bUseGachaTicket?: boolean;
  bPermitExchange?: boolean;
}

export interface GLogBoxData {
  id: number;
  uid: number;
  name: string;
  amt: number;
  type: string;
  is_duplicated: number;
  duplicated_id: number;
  duplicated_name: string;
  duplicated_type: string;
  duplicated_amt: number;
  eleven_bonus: number;
}

enum GLOG_BUFF_BUY_TYPE {
  /** 일반 구매(기존에 관련 버프가 없던 상태) */
  ADD = 1,
  /** 동일 등급 기간 연장 */
  EXTEND_TERM = 2,
  /** 상위 등급 구매 */
  UPGRADE = 3,
}

interface Response extends BuffSync {
  gains: ActualGain[];
  pickedCashShopBoxRatioCmsIds: number[];
  mailIdsForOverflow: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Common_CashShopBuyWithoutPurchase implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { cmsId, bPermitExchange, bUseGachaTicket } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const cashShopCms = cms.CashShop[cmsId];
    if (!cashShopCms || cashShopCms.salePointType !== CASH_SHOP_SALE_POINT_TYPE.POINT) {
      throw new MError(
        'invalid-cms-id',
        MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
        {
          cmsId,
        }
      );
    }

    user.userContentsTerms.ensureContentsTerms(cashShopCms.contentsTerms, user);

    const buyingAmount: number = (() => {
      switch (cashShopCms.productType) {
        case CASH_SHOP_PRODUCT_TYPE.REWARD_FIXED:
          const amount = body.amount ?? 1;
          if (!Number.isInteger(amount)) {
            throw new MError(
              'invalid-amount',
              MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
              {
                amount,
              }
            );
          }
          if (amount < 1 || amount > cms.Const.ShopPerPurchaseMaxVal.value) {
            //* 최대치 Const값이 단순히 UI 프로그래스바에서 사용할 목적이라 999 로 되어 있는 데,
            // 추후 성능 문제 확인 필요
            throw new MError(
              'invalid-amount-range',
              MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
              {
                amount,
                max: cms.Const.ProductVolumeMaxVal.value,
              }
            );
          }
          return amount;
        case CASH_SHOP_PRODUCT_TYPE.GACHA_BOX:
          //* 가챠 박스가 기획에서 제거된 뒤에 glog 에 amt 키가 추가되었는데,
          // 다시 사용하게 된다면 bMultiple 인 경우 어떻게 기록되어야 하는지 확인 필요.
          return cashShopCms.singleBoxCashShopId ? cms.Const.CashShopMultipleAmount.value : 1;
        case CASH_SHOP_PRODUCT_TYPE.DAILY_SUBSCRIPTION:
          // 2개 이상 구매 기능 추가 시 구매 요청 api 기능 추가 필요.
          // 현재 구매 요청 api는 1개만 처리되는 api로 파악.
          return 1;
        default:
          return 1;
      }
    })();

    const curTimeUtc = mutil.curTimeUtc();
    const expiredRestrictedProducts = user.userCashShop.getExpiredRestrictedProducts(curTimeUtc);

    // 구매할 수 있는 상품인지 검사
    const curDate = new Date(curTimeUtc * 1000);
    const restrictedProducts = user.userCashShop.getRestrictedProducts();
    const unbuyableReason = user.userCashShop.isBuyableProduct(
      user,
      cmsId,
      curDate,
      expiredRestrictedProducts,
      buyingAmount
    );
    if (unbuyableReason !== UNBUYABLE_REASON.BUYABLE) {
      throw new MError('unbuyable-product', MErrorCode.UNBUYABLE_CASH_SHOP_PRODUCT, {
        unbuyableReason,
        curDate,
        restrictedProducts,
      });
    }

    // build restrictedProductChange
    let restrictedProductChange: RestrictedProduct;
    if (cashShopCms.saleType !== CASH_SHOP_SALE_TYPE.UNLIMITED) {
      if (expiredRestrictedProducts.has(cmsId) || !restrictedProducts[cmsId]) {
        restrictedProductChange = {
          cmsId,
          amount: buyingAmount,
          lastBuyingTimeUtc: curTimeUtc,
        };
      } else {
        restrictedProductChange = {
          cmsId,
          amount: restrictedProducts[cmsId].amount + buyingAmount,
          lastBuyingTimeUtc: curTimeUtc,
        };
      }
    } else if (cmsEx.isCashShopPreviousId(cmsId) && !restrictedProducts[cmsId]) {
      restrictedProductChange = {
        cmsId,
        amount: buyingAmount,
        lastBuyingTimeUtc: curTimeUtc,
      };
    } else if (
      cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.GACHA_BOX &&
      cashShopCms.singleBoxCashShopId
    ) {
      if (mconf.binaryCode !== 'GL') {
        throw new MError(
          'cannot-gacha',
          MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
          {
            binaryCode: mconf.binaryCode,
            body,
          }
        );
      }

      const singleBoxCashShopCms = cms.CashShop[cashShopCms.singleBoxCashShopId];
      if (singleBoxCashShopCms && singleBoxCashShopCms.saleType !== CASH_SHOP_SALE_TYPE.UNLIMITED) {
        const boxAmount = cms.Const.CashShopMultipleAmount.value - 1;

        // 10연차는 1연차의 saleType을 기준으로 해서 1연차 상품으로 다시 체크
        if (
          user.userCashShop.isSoldOut(singleBoxCashShopCms, expiredRestrictedProducts, boxAmount)
        ) {
          throw new MError('unbuyable-product', MErrorCode.UNBUYABLE_CASH_SHOP_PRODUCT, {
            unbuyableReason: UNBUYABLE_REASON.SOLD_OUT,
            curDate,
            restrictedProducts,
          });
        }

        const restrictedProductAmount = expiredRestrictedProducts.has(singleBoxCashShopCms.id)
          ? 0
          : restrictedProducts[singleBoxCashShopCms.id]?.amount ?? 0;

        restrictedProductChange = {
          cmsId: singleBoxCashShopCms.id,
          amount: restrictedProductAmount + boxAmount,
          lastBuyingTimeUtc: curTimeUtc,
        };
      }
    }

    let fixedTermProductCmsIdToDelete: number;
    let newFixedTermProduct: FixedTermProduct;

    // for cacha box
    const pickedCashShopBoxRatioCmsIds: number[] = [];
    let newGuaranteeAccum;
    let oldGuaranteeAccum;

    // 공간이 꽉차 메일로 간 것들
    const mailIdsForOverflow: number[] = [];

    // for glog
    const pr_data: PrData[] = [];
    const cost_data: CostData[] = [];
    const box_data: GLogBoxData[] = [];
    let buff_buy_type: GLOG_BUFF_BUY_TYPE | null = undefined;

    let changeTask: UserChangeTask;
    const gains: ActualGain[] = [];
    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.REWARD_FIXED) {
      const cost = cashShopCms.salePointVal * buyingAmount;

      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_REWARD_FIXED,
        new CashShopBuyWithoutPurchaseRewardFixedSpec(
          cashShopCms.salePointId,
          cost,
          bPermitExchange,
          getCashShopMileageBonus(cashShopCms, cost),
          expiredRestrictedProducts,
          restrictedProductChange,
          cashShopCms.productRewardFixedId,
          buyingAmount,
          curTimeUtc,
          mailIdsForOverflow,
          cashShopCms.id
        )
      );
      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cost,
      });
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
      let cost: number = cashShopCms.salePointVal;

      const oldBuffProduct = user.userCashShop.getFixedTermProductByGroup(
        cashShopCms.buffGroup,
        curTimeUtc
      );
      if (oldBuffProduct) {
        const oldBuffProductCms = cms.CashShop[oldBuffProduct.cmsId];
        if (oldBuffProductCms.buffGroupLevel > cashShopCms.buffGroupLevel) {
          // 같은 그룹일 경우 이전 레벨은 구매 불가
          throw new MError(
            'can-not-buy-lower-level-buff-product',
            MErrorCode.CANT_BUY_LOWER_LEVEL_BUFF_PRODUCT,
            {
              oldBuffProduct,
              cmsId,
            }
          );
        } else if (oldBuffProductCms.buffGroupLevel === cashShopCms.buffGroupLevel) {
          // 같은 그룹에 같은 레벨 구매 시 버프 시간을 더해 줌
          newFixedTermProduct = {
            cmsId,
            startTimeUtc: oldBuffProduct.startTimeUtc,
            endTimeUtc: oldBuffProduct.endTimeUtc,
          };

          if (isExistWorldBuffTimeField(cashShopCms) == false) {
            throw new MError(
              'invalid-duration-days-and-duration-hours',
              MErrorCode.INVALID_DURATION_DAYS_AND_DURATION_HOURS,
              {
                cmsId,
              }
            );
          }
          newFixedTermProduct.endTimeUtc += getWorldBuffAddTime(cashShopCms);

          buff_buy_type = GLOG_BUFF_BUY_TYPE.EXTEND_TERM;
        } else {
          // 같은 그룹에 더 높은 레벨 구매 시 이전에 남아있던 버프는 지워주고 새로운 버프 추가
          // 단 시작, 종료 시간은 기존 버프의 값을 그대로 이용한다.
          fixedTermProductCmsIdToDelete = oldBuffProduct.cmsId;
          newFixedTermProduct = {
            cmsId,
            startTimeUtc: oldBuffProduct.startTimeUtc,
            endTimeUtc: oldBuffProduct.endTimeUtc,
          };
          const remainingMin = Math.floor(
            (oldBuffProduct.endTimeUtc - curTimeUtc) / SECONDS_PER_MINUTE
          );
          cost = CalcCashShopBuffUpgradeCostPerMinute(cashShopCms, oldBuffProductCms, remainingMin);
          buff_buy_type = GLOG_BUFF_BUY_TYPE.UPGRADE;
        }
      } else {
        // 기존에 같은 그룹의 버프 상품이 없을 경우
        newFixedTermProduct = {
          cmsId,
          startTimeUtc: curTimeUtc,
          endTimeUtc: curTimeUtc,
        };

        if (isExistWorldBuffTimeField(cashShopCms) == false) {
          throw new MError(
            'invalid-duration-or-expireAt-days-and-duration-hours',
            MErrorCode.INVALID_DURATION_DAYS_AND_DURATION_HOURS,
            {
              cmsId,
            }
          );
        }
        newFixedTermProduct.endTimeUtc += getWorldBuffAddTime(cashShopCms)
        
        buff_buy_type = GLOG_BUFF_BUY_TYPE.ADD;
      }
      const expiredFixedTermProducts = user.userCashShop.getExpiredFixedTermProducts(curTimeUtc);

      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF,
        new CashShopBuyWithoutPurchaseBuffSpec(
          cashShopCms.salePointId,
          cost,
          bPermitExchange,
          getCashShopMileageBonus(cashShopCms, cost),
          cashShopCms.id,
          expiredRestrictedProducts,
          restrictedProductChange,
          curTimeUtc,
          expiredFixedTermProducts,
          fixedTermProductCmsIdToDelete,
          newFixedTermProduct
        )
      );

      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cost,
      });
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.TAX_FREE_PERMIT) {
      throw new MError(
        'invalid-product-type',
        MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
        {
          cmsId,
        }
      );
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
      if (mconf.binaryCode !== 'GL') {
        throw new MError(
          'cannot-gacha',
          MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
          {
            binaryCode: mconf.binaryCode,
            body,
          }
        );
      }

      // 가챠는 더이상 레드젬으로 구매 불가능
      if (!bUseGachaTicket) {
        throw new MError(
          'gacha-only-use-ticket',
          MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
          {
            body,
          }
        );
      }

      const gachaTicketToUses: { [ticketId: number]: number } = {};
      if (bUseGachaTicket) {
        let remainCount = cashShopCms.cashShopBoxTicketVal;
        for (const itemId of cashShopCms.cashShopBoxTicketId.reverse()) {
          const itemCount = user.userInven.itemInven.getCount(itemId);
          const useCount = itemCount >= remainCount ? remainCount : itemCount;

          gachaTicketToUses[itemId] = useCount;
          remainCount -= useCount;
        }

        if (remainCount > 0) {
          throw new MError('not-enough-gacha-ticket', MErrorCode.NOT_ENOUGH_GACHA_TICKET, {
            gachaTicketToUses,
            remainCount: remainCount,
          });
        }
      }
      const singleBoxCashShopId = cms.CashShop[cmsId].singleBoxCashShopId;
      oldGuaranteeAccum = user.userCashShop.getGachaBoxGuaranteeAccum(
        singleBoxCashShopId ? singleBoxCashShopId : cmsId
      );
      if (oldGuaranteeAccum === undefined) {
        oldGuaranteeAccum = 0;
      }
      newGuaranteeAccum = oldGuaranteeAccum;

      let boxAmount = 1;
      const cashShopBoxRatioCmses: {
        cashShopBoxRatioCms: CashShopBoxRatioDesc;
        isBonusGachaRewardForGlog: boolean;
      }[] = [];
      if (singleBoxCashShopId) {
        boxAmount = cms.Const.CashShopMultipleAmount.value - 1;

        const picked = pickGachaBox(cashShopCms.bonusboxGroup);
        const cashShopBoxRatioCms = cms.CashShopBoxRatio[picked];
        pickedCashShopBoxRatioCmsIds.push(picked);

        cashShopBoxRatioCmses.push({
          cashShopBoxRatioCms: cashShopBoxRatioCms,
          isBonusGachaRewardForGlog: true,
        });
      }

      for (let i = 0; i < boxAmount; i++) {
        const picked = pickGachaBox(cashShopCms.boxGroup);
        const cashShopBoxRatioCms = cms.CashShopBoxRatio[picked];
        pickedCashShopBoxRatioCmsIds.push(picked);

        if (cashShopCms.ceilingCashShopBoxRatioGroup) {
          newGuaranteeAccum++;
        }

        cashShopBoxRatioCmses.push({
          cashShopBoxRatioCms: cashShopBoxRatioCms,
          isBonusGachaRewardForGlog: false,
        });
      }

      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_GACHA_BOX,
        new CashShopBuyWithoutPurchaseGachaBoxSpec(
          cashShopCms.salePointId,
          cashShopCms.salePointVal,
          false, // 상자(가챠)는 환전 불가능
          getCashShopMileageBonus(cashShopCms, cashShopCms.salePointVal),
          expiredRestrictedProducts,
          restrictedProductChange,
          cashShopCms.id,
          curTimeUtc,
          cashShopBoxRatioCmses,
          oldGuaranteeAccum === newGuaranteeAccum ? undefined : newGuaranteeAccum,
          gains,
          box_data,
          mailIdsForOverflow,
          bUseGachaTicket,
          gachaTicketToUses
        )
      );

      if (!bUseGachaTicket) {
        pr_data.push({
          type: cashShopCms.salePointId,
          amt: cashShopCms.salePointVal,
        });
      } else {
        _.forOwn(gachaTicketToUses, (count, itemId) => {
          const itemCms = cms.Item[itemId];
          cost_data.push({
            type: ITEM_TYPE[itemCms.type],
            id: itemCms.id,
            amt: count,
          });
        });
      }
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.QUEST_PASS) {
      const oldProduct = user.userCashShop.getFixedTermProduct(cmsId, curTimeUtc);
      if (oldProduct) {
        throw new MError('already-bought', MErrorCode.ALREADY_BOUGHT_QUEST_PASS, {
          oldProduct,
          cmsId,
        });
      }

      // questPassCms.achievementId 업적들 모두 보상을 받았을 경우 구매 불가.
      const questPassCms = cms.QuestPass[cashShopCms.questPassId];
      if (questPassCms.achievementId && questPassCms.achievementId.length > 0) {
        const notRewarded = questPassCms.achievementId.findIndex((elem) => {
          return !user.userAchievement.isRewarded(elem);
        });

        if (notRewarded === -1) {
          throw new MError(
            'quest-pass-achievements-is-rewarded',
            MErrorCode.QUEST_PASS_ACHIEVEMENTS_IS_REWARDED,
            {
              cmsId,
              achievements: user.userAchievement.getAchievements(),
            }
          );
        }
      }

      let cost = cashShopCms.salePointVal;
      // '제독+회고록' 같은 경우 회고록 비용만큼만 마일리지 적립이 되어야한다고함.
      const mileageBonus = getCashShopMileageBonus(cashShopCms, cost);
      let mateCmsId;
      // 인연 연대기는 항해사 지급에서 제외.
      if (!user.userMates.getMate(questPassCms.mateId)) {
        if (questPassCms.type === QuestPassType.RelationShip) {
          throw new MError('invalid-quest-pass-mate', MErrorCode.INVALID_QUEST_PASS_MATE, {
            cmsId,
            mateId: questPassCms.mateId,
          });
        } else if (questPassCms.type === QuestPassType.Admiral) {
          mateCmsId = questPassCms.mateId;
          // 항해사도 같이 구매
          const admiralCms = cmsEx.getAdmiralByMateCmsId(mateCmsId);
          // 제독 구매 재화가 레드젬이 아닌 경우 제독+회고록 구매 불가능
          if (admiralCms.recruitingPoint === cmsEx.RedGemPointCmsId) {
            user.userContentsTerms.ensureContentsTerms(admiralCms.contentsTerms, user);

            // CashShop.productType 이 5인 경우 해당되는 제독의 Admiral.recruitingPoint 이 레드젬일 경우 회고록을 구매하면서
            // 제독도 같이 구매되고 제독 구매로 인해 추가되는 비용은 Admiral.recruitingPointValue 을 사용한다.
            cost = CalcCashShopQuestPassWithMateCost(cost, admiralCms.recruitingPointValue);
          }
        }
      }

      const expiredFixedTermProducts = user.userCashShop.getExpiredFixedTermProducts(curTimeUtc);

      newFixedTermProduct = {
        cmsId,
        startTimeUtc: curTimeUtc,
        endTimeUtc: null, // 무기한
      };

      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF,
        new CashShopBuyWithoutPurchaseQuestPassSpec(
          cashShopCms.salePointId,
          cost,
          bPermitExchange,
          mileageBonus,
          expiredRestrictedProducts,
          restrictedProductChange,
          cashShopCms.id,
          curTimeUtc,
          questPassCms.researchPoint,
          expiredFixedTermProducts,
          newFixedTermProduct,
          mateCmsId
        )
      );

      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cost,
      });
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.SOUND) {
      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF,
        new CashShopBuyWithoutPurchaseSoundPackSpec(
          cashShopCms.salePointId,
          cashShopCms.salePointVal,
          bPermitExchange,
          getCashShopMileageBonus(cashShopCms, cashShopCms.salePointVal),
          expiredRestrictedProducts,
          restrictedProductChange,
          cashShopCms.id,
          curTimeUtc,
          cashShopCms.soundPackId
        )
      );

      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cashShopCms.salePointVal,
      });
      // } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.ENCOUNT_SHIELD) {
      //   changeTask = new UserChangeTask(
      //     user,
      //     CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_ENCOUNT_SHIELD,
      //     new CashShopBuyWithoutPurchaseEncountShieldSpec(
      //       cashShopCms.salePointId,
      //       cashShopCms.salePointVal,
      //       bPermitExchange,
      //       getCashShopMileageBonus(cashShopCms, cashShopCms.salePointVal),
      //       expiredRestrictedProducts,
      //       restrictedProductChange,
      //       cashShopCms.id,
      //       cashShopCms.countProductVal
      //     )
      //   );

      //   pr_data.push({
      //     type: cashShopCms.salePointId,
      //     amt: cashShopCms.salePointVal,
      //   });
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.DAILY_SUBSCRIPTION) {
      const dsCms: DailySubscriptionDesc = cms.DailySubscription[cashShopCms.dailySubscriptionId];
      if (!dsCms) {
        throw new MError(
          'invalid-daily-subscription-cms',
          MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
          {
            cashShopCmsId: cashShopCms.id,
            dsCmsId: cashShopCms.dailySubscriptionId,
          }
        );
      }

      //
      const cost = cashShopCms.salePointVal * buyingAmount;
      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_DAILY_SUBSCRIPTION,
        new CashShopBuyWithoutPurchaseDailySubscriptionSpec(
          cashShopCms.salePointId,
          cost,
          bPermitExchange,
          getCashShopMileageBonus(cashShopCms, cost),
          expiredRestrictedProducts,
          restrictedProductChange,
          dsCms,
          buyingAmount,
          curTimeUtc,
          mailIdsForOverflow,
          cashShopCms.id
        )
      );

      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cost,
      });
    } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
      const cost = cashShopCms.salePointVal * buyingAmount;

      changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_HOT_SPOT,
        new CashShopBuyWithoutPurchaseHotSpotSpec(
          cashShopCms.salePointId,
          cost,
          bPermitExchange,
          getCashShopMileageBonus(cashShopCms, cost),
          expiredRestrictedProducts,
          restrictedProductChange,
          cashShopCms.productRewardFixedId,
          buyingAmount,
          curTimeUtc,
          mailIdsForOverflow,
          cashShopCms.id
        )
      );

      pr_data.push({
        type: cashShopCms.salePointId,
        amt: cost,
      });
    } else {
      throw new MError(
        'invalid-product-type',
        MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
        {
          cmsId,
        }
      );
    }

    if (newFixedTermProduct) {
      if (
        newFixedTermProduct.endTimeUtc &&
        newFixedTermProduct.endTimeUtc - curTimeUtc >=
          cms.Const.CashShopDurationDaysLimit.value * SECONDS_PER_DAY
      ) {
        throw new MError(
          'exceeds-expiration-time',
          MErrorCode.EXCEEDS_EXPIRATION_TIME_OF_CASH_SHOP_PRODUCT,
          {
            cmsId,
            newExpirationTimeUtc: newFixedTermProduct.endTimeUtc,
            curTimeUtc,
          }
        );
      }
    }

    const res = changeTask.trySpec();
    // 캐시샵에서는 엄격하게 OK 아닌 경우 에러
    if (res !== CHANGE_TASK_RESULT.OK) {
      throw new MError('failed-to-receive', MErrorCode.FAILED_TO_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
        res,
        cmsId,
      });
    }

    const oldPaidRedGemForGlog = user.userPoints.paidRedGem;
    const oldFreeRedGemForGlog = user.userPoints.freeRedGem;

    return changeTask
      .apply()
      .then((sync) => {
        if (cashShopCms.productType !== CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
          gains.push(changeTask.getActualGain());
        }

        // accumulate achievement
        const accums: AccumulateParam[] = [];
        let accumCashShopBuyCountTargetId = cashShopCms.id;
        let accumBuyingAmount = buyingAmount;

        if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.OPEN_CASH_SHOP_GACHA_BOX,
            addedValue: cashShopCms.singleBoxCashShopId
              ? cms.Const.CashShopMultipleAmount.value
              : 1, // (10+1)회 가챠는 11회로 적용
          });

          accumCashShopBuyCountTargetId = cashShopCms.singleBoxCashShopId ?? cashShopCms.id;
          if (cashShopCms.singleBoxCashShopId) {
            const singleBoxCashShopCms = cms.CashShop[cashShopCms.singleBoxCashShopId];
            accumBuyingAmount =
              cashShopCms.cashShopBoxTicketVal / singleBoxCashShopCms.cashShopBoxTicketVal;
          }
        }

        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.CASH_SHOP_BUY_COUNT,
          addedValue: accumBuyingAmount,
          targets: [accumCashShopBuyCountTargetId],
        });

        return user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
      })
      .then((sync) => {
        // glog
        let remain_cnt = null;
        if (restrictedProductChange) {
          remain_cnt = cashShopCms.saleTypeVal - restrictedProductChange.amount;
        }
        const reward_data: RewardData[] = [];
        for (const gain of gains) {
          reward_data.push(...UserChangeTask.covertActualGainToGLogRewardData(gain));
        }

        // 재화 사용은 제거
        for (const pr of pr_data) {
          let remainingMinus = pr.amt;
          for (const reward of reward_data) {
            if (remainingMinus === 0) {
              break;
            }
            if (pr.type !== reward.id) {
              continue;
            }
            if (reward.amt >= 0) {
              continue;
            }

            const t = Math.min(remainingMinus, -reward.amt);
            reward.amt += t;
            remainingMinus -= t;
          }
        }
        for (let i = reward_data.length - 1; i >= 0; i--) {
          if (reward_data[i].amt === 0) {
            reward_data.splice(i, 1);
          }
        }

        user.glog('cash_shop_buy', {
          rsn,
          add_rsn,
          category: cashShopCms.productCategory,
          id: cmsId,
          name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
          type: cashShopCms.productType,
          limit_type: cashShopCms.saleType,
          limit_period: cashShopCms.saleTypeVal ? cashShopCms.saleTypeVal : null,
          remain_cnt,
          amt: buyingAmount,
          expiredate:
            newFixedTermProduct && newFixedTermProduct.endTimeUtc
              ? moment(new Date(newFixedTermProduct.endTimeUtc * 1000)).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              : null,
          buff_buy_type: buff_buy_type !== undefined ? GLOG_BUFF_BUY_TYPE[buff_buy_type] : null,
          pr_data,
          reward_data: reward_data.length > 0 ? reward_data : null,
          exchange_hash: changeTask.getExchangeHash(),
        });
        if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
          user.glog('summon_data', {
            rsn,
            add_rsn,
            exchange_hash: changeTask.getExchangeHash(),
            summon_id: cashShopCms.boxGroup,
            box_data,
            is_sequence: cashShopCms.singleBoxCashShopId ? 2 : 1,
            cv: cashShopCms.ceilingCashShopBoxRatioGroup
              ? newGuaranteeAccum - oldGuaranteeAccum
              : null,
            rv: cashShopCms.ceilingCashShopBoxRatioGroup ? newGuaranteeAccum : null,
            pr_data,
            cost_data,
          });
        } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
          user.glog('hot_spot_product_start', {
            rsn,
            add_rsn,
            exchange_hash: changeTask.getExchangeHash(),
            id: cmsId,
            name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
            limit_type: cashShopCms.saleType,
            remain_cnt:
              cashShopCms.saleType === CASH_SHOP_SALE_TYPE.UNLIMITED
                ? 999
                : remain_cnt ?? cashShopCms.saleTypeVal,
            amt: buyingAmount,
            pr_data,
            reward_data,
            is_exposure: false,
          });
        }
        // buff 처리
        const resp: Response = {
          sync,
          gains,
          pickedCashShopBoxRatioCmsIds,
          mailIdsForOverflow: mailIdsForOverflow.length > 0 ? mailIdsForOverflow : undefined,
        };
        if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
          handleBuff(
            user,
            fixedTermProductCmsIdToDelete,
            cashShopCms,
            cmsId,
            newFixedTermProduct,
            resp
          );
        }

        // [SDO]
        if (mconf.isSDO) {
          return mhttp.platformBillingApi.queryCashPair(user.userId, user.storeCode, user.countryCreated)
            .then((ret) => {
              user.userPoints.onChargeByPurchaseProduct(
                [
                  {
                    coinCd: 'red_gem',
                    paymentType: 'PAID',
                    balance: ret.paidRedGemBalance,
                  },
                  {
                    coinCd: 'red_gem',
                    paymentType: 'FREE',
                    balance: ret.freeRedGemBalance,
                  },
                ],
                null
              );
              return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
            })
            .catch((err) => {
              mlog.error(err);
              return resp;
            });
        } else {
          return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
        }
      });
  }
}

function handleBuff(
  user: User,
  fixedTermProductCmsIdToDelete: number,
  cashShopCms: CashShopDesc,
  cmsId: number,
  newFixedTermProduct: FixedTermProduct,
  buffSync: BuffSync
) {
  const userBuffs = user.userBuffs;

  // 캐쉬샵에 버프는 무조건 1번 함대에 적용한다고 함.
  const targetId = cmsEx.FirstFleetIndex;

  if (fixedTermProductCmsIdToDelete) {
    // 기존 버프 지워주어야 됨.
    const deleteBuffCms = cms.CashShop[fixedTermProductCmsIdToDelete];
    for (const buffId of deleteBuffCms.productWorldBuffId) {
      const newBuffIdx = cashShopCms.productWorldBuffId.findIndex((newBuffCmsId) => {
        const newBuffCms = cms.WorldBuff[newBuffCmsId];
        const buffCmsToDelete = cms.WorldBuff[buffId];
        return newBuffCms.groupNo === buffCmsToDelete.groupNo;
      });
      if (newBuffIdx !== -1) {
        continue;
      }

      userBuffs.removeBuff(buffId, targetId, user, rsn, add_rsn, buffSync);
    }
  }

  for (const buffId of cashShopCms.productWorldBuffId) {
    const worldBuffCms = cms.WorldBuff[buffId];
    const wbNub = WorldBuffUtil.makeNubWithCustomEndTime(
      worldBuffCms,
      targetId,
      cmsEx.WorldBuffSourceType.CASH_SHOP_BUY_WITHOUT_PURCHASE,
      cmsId,
      newFixedTermProduct.startTimeUtc,
      newFixedTermProduct.endTimeUtc
    );

    user.userBuffs.addSingleBuffByWBNub(wbNub, user, buffSync);
  }
}

// 해당 클래스에는 캐시샵에서 필수적으로 사용해야하는 로직이 있다.
// 하지만 CashShopBuyWithoutPurchaseRewardFixedSpec에서 RewardAndPaymentSpec의 일부 맴버함수 사용이 필요해서
// 이 클래스를 상속받지 않고 RewardAndPaymentSpec를 상속받기 때문에
// 각 클래스에서 캐시샵의 필요한 로직을 함수를 이용하여 사용하는 것 참고.
class CashShopBuyWithoutPurchaseSpec implements UserChangeSpec {
  constructor(
    private costPointCmsId: number,
    private costPointValue: number,
    private bPermitExchange: boolean,
    private mileageBonus: number | undefined,
    protected cashShopCmsId: number,
    private expiredRestrictedProducts: Set<number>,
    private restrictedProductChange: RestrictedProduct,
    protected curTimeUtc: number
  ) {
    //
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    return CashShopOperatorUtil.executeCashShopBuyWithoutPurchaseOps(
      user,
      tryData,
      changes,
      this.costPointCmsId,
      this.costPointValue,
      this.bPermitExchange,
      this.mileageBonus,
      this.expiredRestrictedProducts,
      this.restrictedProductChange,
      this.cashShopCmsId,
      undefined,
      this.curTimeUtc
    );
  }
}

class CashShopBuyWithoutPurchaseRewardFixedSpec extends RewardAndPaymentSpec {
  private readonly _costPointCmsId: number;
  private readonly _costPointValue: number;
  private readonly _bPermitExchange: boolean;
  private readonly _mileageBonus: number | undefined;
  private readonly _expiredRestrictedProducts: Set<number>;
  private readonly _restrictedProductChange: RestrictedProduct;

  private readonly _rewardFixedCmsId: number;
  private readonly _rewardAmount: number; // rewardFixedCmsId가 적용될 횟수
  private readonly _curTimeUtc: number;
  private readonly _mailIdsForOverflow: number[];
  private readonly _cashShopCmsId: number;

  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    rewardFixedCmsId: number,
    rewardAmount: number,
    curTimeUtc: number, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow: number[],
    cashShopCmsId: number
  ) {
    super();

    this._costPointCmsId = costPointCmsId;
    this._costPointValue = costPointValue;
    this._bPermitExchange = bPermitExchange;
    this._mileageBonus = mileageBonus;
    this._expiredRestrictedProducts = expiredRestrictedProducts;
    this._restrictedProductChange = restrictedProductChange;

    this._rewardFixedCmsId = rewardFixedCmsId;
    this._rewardAmount = rewardAmount;
    this._curTimeUtc = curTimeUtc;
    this._mailIdsForOverflow = mailIdsForOverflow;
    this._cashShopCmsId = cashShopCmsId;
  }

  //* BattleRewardSpec 처럼 super.accumulate 는 사용 않고 _tryElem(맴버 함수)만 사용
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    //* super.accumulate 를 사용하지 않기 때문에
    //* 맴버 변수로 user, tryData, changes 설정해줘야 하는 것 주의(_tryElem에서 사용됨)
    this._user = user;
    this._tryData = tryData;
    this._changes = changes;

    let res = CashShopOperatorUtil.executeCashShopBuyWithoutPurchaseOps(
      user,
      tryData,
      changes,
      this._costPointCmsId,
      this._costPointValue,
      this._bPermitExchange,
      this._mileageBonus,
      this._expiredRestrictedProducts,
      this._restrictedProductChange,
      this._cashShopCmsId,
      this._rewardAmount,
      this._curTimeUtc
    );
    if (res !== CHANGE_TASK_RESULT.OK) {
      return res;
    }

    const disallowExceedRnpElem = {
      type: RNP_TYPE.REWARD_FIXED,
      bAllowExceed: false,
      bIsBound: true,
      bIsAccum: true,
      shipFixedRandomStat: cms.Const.CashShipBlueprintRatio.value,
    };
    const defaultRnpElem = {
      type: RNP_TYPE.REWARD_FIXED,
      bAllowExceed: true,
      bIsBound: true,
      bIsAccum: true,
      shipFixedRandomStat: cms.Const.CashShipBlueprintRatio.value,
    };

    const getRnpElem = (rewardType: REWARD_TYPE): RewardAndPaymentElem => {
      switch (rewardType) {
        case REWARD_TYPE.MATE_EQUIP:
        case REWARD_TYPE.ITEM:
        case REWARD_TYPE.SHIP:
        case REWARD_TYPE.SHIP_SLOT_ITEM:
          return disallowExceedRnpElem;
        default:
          return defaultRnpElem;
      }
    };

    const rewardFixedCms = cms.RewardFixed[this._rewardFixedCmsId];
    const receivedMailIds: number[] = [];

    const curTimeUtc = mutil.curTimeUtc();
    for (let i = 0; i < this._rewardAmount; i += 1) {
      const equipmentsForMail: RewardFixedElemDesc[] = [];
      const itemsForMail: RewardFixedElemDesc[] = [];
      const shipsForMail: RewardFixedElemDesc[] = [];

      for (const elem of rewardFixedCms.rewardFixed) {
        res = this._tryElem(elem, getRnpElem(elem.Type), curTimeUtc);
        if (res === CHANGE_TASK_RESULT.MATE_EQUIP_FULL) {
          equipmentsForMail.push(elem);
        } else if (res === CHANGE_TASK_RESULT.INVEN_FULL) {
          itemsForMail.push(elem);
        } else if (res === CHANGE_TASK_RESULT.DOCK_FULL) {
          shipsForMail.push(elem);
        } else if (res <= CHANGE_TASK_RESULT.OK_MAX) {
          // do nothing
        } else {
          return res;
        }
      }

      if (equipmentsForMail.length > 0 || itemsForMail.length > 0 || shipsForMail.length > 0) {
        if (!tryData.mails) {
          tryData.mails = user.userMails.clone();
        }
        const oldLastMailId = tryData.mails.getLastDirectMailId();

        if (equipmentsForMail.length > 0) {
          res = CashShopOperatorUtil.executeAddDirectMailOp(
            user,
            tryData,
            changes,
            cms.Const.CashShopCEquipMailId.value,
            this._curTimeUtc,
            equipmentsForMail,
            true
          );
          if (res !== CHANGE_TASK_RESULT.OK) {
            return res;
          }
        }
        if (itemsForMail.length > 0) {
          res = CashShopOperatorUtil.executeAddDirectMailOp(
            user,
            tryData,
            changes,
            cms.Const.CashShopCEquipMailId.value,
            this._curTimeUtc,
            itemsForMail,
            true
          );
          if (res !== CHANGE_TASK_RESULT.OK) {
            return res;
          }
        }
        if (shipsForMail.length > 0) {
          res = CashShopOperatorUtil.executeAddDirectMailOp(
            user,
            tryData,
            changes,
            cms.Const.CashShopShipMailId.value,
            this._curTimeUtc,
            shipsForMail,
            true
          );
          if (res !== CHANGE_TASK_RESULT.OK) {
            return res;
          }
        }

        const curLastMailId = tryData.mails.getLastDirectMailId();
        for (let mailId = oldLastMailId + 1; mailId <= curLastMailId; mailId += 1) {
          receivedMailIds.push(mailId);
        }
      }
    }

    // 외부 변수 적용
    this._mailIdsForOverflow.push(...receivedMailIds);

    return CHANGE_TASK_RESULT.OK;
  }
}

class CashShopBuyWithoutPurchaseBuffSpec extends CashShopBuyWithoutPurchaseSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    cashShopCmsId: number,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    curTimeUtc: number,
    private expiredFixedTermProducts: Set<number>,
    private fixedTermProductCmsIdToDelete: number,
    private newFixedTermProduct: FixedTermProduct
  ) {
    super(
      costPointCmsId,
      costPointValue,
      bPermitExchange,
      mileageBonus,
      cashShopCmsId,
      expiredRestrictedProducts,
      restrictedProductChange,
      curTimeUtc
    );
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const ops = [super.accumulate(user, tryData, changes)];

    if (this.expiredFixedTermProducts) {
      for (const cmsId of this.expiredFixedTermProducts) {
        ops.push(opDeleteCashShopFixedTermProduct(user, tryData, changes, cmsId));
      }
    }

    if (this.fixedTermProductCmsIdToDelete) {
      ops.push(
        opDeleteCashShopFixedTermProduct(user, tryData, changes, this.fixedTermProductCmsIdToDelete)
      );
    }

    if (this.newFixedTermProduct) {
      ops.push(opSetCashShopFixedTermProduct(user, tryData, changes, this.newFixedTermProduct));
    }

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
}

class CashShopBuyWithoutPurchaseGachaBoxSpec extends CashShopBuyWithoutPurchaseSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    cashShopCmsId: number,
    curTimeUtc: number,
    private cashShopBoxRatioCmses: {
      cashShopBoxRatioCms: CashShopBoxRatioDesc;
      isBonusGachaRewardForGlog: boolean;
    }[],
    private guaranteeAccum: number,
    private gains: ActualGain[],
    private glogBoxData: GLogBoxData[],
    // private curTimeUtc: number,
    private mailIdsForOverflow: number[],
    private bUseGachaTicket: boolean,
    private gachaTicketToUses: { [ticketId: number]: number }
  ) {
    super(
      bUseGachaTicket ? undefined : costPointCmsId,
      bUseGachaTicket ? undefined : costPointValue,
      bPermitExchange,
      bUseGachaTicket ? 0 : mileageBonus,
      cashShopCmsId,
      expiredRestrictedProducts,
      restrictedProductChange,
      curTimeUtc
    );
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    let res = super.accumulate(user, tryData, changes);
    if (res !== CHANGE_TASK_RESULT.OK) {
      return res;
    }

    if (this.bUseGachaTicket) {
      _.forOwn(this.gachaTicketToUses, (count, itemId) => {
        res = opAddItem(
          user,
          tryData,
          changes,
          parseInt(itemId, 10),
          -count,
          false,
          false,
          undefined,
          true
        );
        if (res !== CHANGE_TASK_RESULT.OK) {
          return res;
        }
      });
    }

    if (this.guaranteeAccum !== undefined) {
      const singleBoxCashShopId = cms.CashShop[this.cashShopCmsId].singleBoxCashShopId;
      res = opSetCashShopGachaBoxGuaranteeAccum(
        user,
        tryData,
        changes,
        singleBoxCashShopId ? singleBoxCashShopId : this.cashShopCmsId,
        this.guaranteeAccum
      );
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    const getLastIdsForActualGain = () => {
      if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
      }
      if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
      }
      if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
      }
      return {
        lastShipId: tryData.fleets.getLastShipId(),
        lastMateEquipmentId: tryData.mates.getLastMateEquipmentId(),
        lastShipSlotItemId: tryData.inven.getLastShipSlotItemId(),
        lastQuestItemId: tryData.inven.itemInven.getLastQuestItemId(),
      };
    };

    type ContentToReceive = {
      contentElem: ContentsDesc;
      bDuplicated?: boolean;
      gain: ActualGain;
      isBonusGachaRewardForGlog: boolean;
    };
    type ContentToMail = {
      contentElem: ContentsDesc;
      bDuplicated?: boolean;
      isBonusGachaRewardForGlog: boolean;
    };

    // 받을 것들
    const totalContentsToReceive: ContentToReceive[][] = [];

    // 공간이 꽉차서 메일로 갈 것들
    const totalContentsToMailForEquipment: ContentToMail[][] = [];
    const totalContentsToMailForItem: ContentToMail[][] = [];
    const totalContentsToMailForShip: ContentToMail[][] = [];

    for (const cashShopBoxRatioCms of this.cashShopBoxRatioCmses) {
      const isBonusGachaRewardForGlog = cashShopBoxRatioCms.isBonusGachaRewardForGlog;

      const contentsToReceive: ContentToReceive[] = [];
      const contentsToMailForEquipment: ContentToMail[] = [];
      const contentsToMailForItem: ContentToMail[] = [];
      const contentsToMailForShip: ContentToMail[] = [];
      totalContentsToReceive.push(contentsToReceive);
      totalContentsToMailForEquipment.push(contentsToMailForEquipment);
      totalContentsToMailForItem.push(contentsToMailForItem);
      totalContentsToMailForShip.push(contentsToMailForShip);

      for (const contentElem of cashShopBoxRatioCms.cashShopBoxRatioCms.contents) {
        const oldLastIds = getLastIdsForActualGain();
        let bDuplicated = false;

        res = this.executeOp(
          user,
          tryData,
          changes,
          contentElem.RewardType,
          contentElem.Id,
          contentElem.Amount
        );
        if (res === CHANGE_TASK_RESULT.OK) {
          const gain = this.getActualGain(
            contentElem.RewardType,
            contentElem.Id,
            contentElem.Amount,
            oldLastIds,
            tryData
          );
          contentsToReceive.push({
            contentElem,
            bDuplicated,
            gain,
            isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
          });
        } else if (
          res === CHANGE_TASK_RESULT.NOTHING &&
          contentElem.DuplicateRewardType !== undefined
        ) {
          bDuplicated = true;

          res = this.executeOp(
            user,
            tryData,
            changes,
            contentElem.DuplicateRewardType,
            contentElem.DuplicateId,
            contentElem.DuplicateAmount
          );
          if (res === CHANGE_TASK_RESULT.OK) {
            const gain = this.getActualGain(
              contentElem.DuplicateRewardType,
              contentElem.DuplicateId,
              contentElem.DuplicateAmount,
              oldLastIds,
              tryData
            );
            contentsToReceive.push({
              contentElem,
              bDuplicated,
              gain,
              isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
            });
          }
        }

        if (res === CHANGE_TASK_RESULT.MATE_EQUIP_FULL) {
          contentsToMailForEquipment.push({
            contentElem,
            bDuplicated,
            isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
          });
        } else if (res === CHANGE_TASK_RESULT.INVEN_FULL) {
          contentsToMailForItem.push({
            contentElem,
            bDuplicated,
            isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
          });
        } else if (res === CHANGE_TASK_RESULT.DOCK_FULL) {
          contentsToMailForShip.push({
            contentElem,
            bDuplicated,
            isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
          });
        } else if (res <= CHANGE_TASK_RESULT.OK_MAX) {
          // do nothing
        } else {
          return res;
        }
      }
    }

    // 메일로 갈 것들 있으면 처리
    const spreadAndConvertToRewardFixedElem = (
      totalContents: ContentToMail[][]
    ): RewardFixedElemDesc[] => {
      const ret: RewardFixedElemDesc[] = [];
      for (const contents of totalContents) {
        for (const { contentElem, bDuplicated } of contents) {
          ret.push(
            CashShopBuyWithoutPurchaseGachaBoxSpec.convertContentElemToRewardFixedElem(
              contentElem,
              bDuplicated
            )
          );
        }
      }
      return ret;
    };
    const receivedMailIds: number[] = [];
    const equipmentsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForEquipment);
    const itemsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForItem);
    const shipsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForShip);
    if (equipmentsForMail.length > 0 || itemsForMail.length > 0 || shipsForMail.length > 0) {
      if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
      }
      const oldLastMailId = tryData.mails.getLastDirectMailId();
      if (equipmentsForMail.length > 0) {
        res = CashShopOperatorUtil.executeAddDirectMailOp(
          user,
          tryData,
          changes,
          cms.Const.CashShopCEquipMailId.value,
          this.curTimeUtc,
          equipmentsForMail,
          true
        );
        if (res !== CHANGE_TASK_RESULT.OK) {
          return res;
        }
      }
      if (itemsForMail.length > 0) {
        res = CashShopOperatorUtil.executeAddDirectMailOp(
          user,
          tryData,
          changes,
          cms.Const.CashShopCEquipMailId.value,
          this.curTimeUtc,
          itemsForMail,
          true
        );
        if (res !== CHANGE_TASK_RESULT.OK) {
          return res;
        }
      }
      if (shipsForMail.length > 0) {
        res = CashShopOperatorUtil.executeAddDirectMailOp(
          user,
          tryData,
          changes,
          cms.Const.CashShopShipMailId.value,
          this.curTimeUtc,
          shipsForMail,
          true
        );
        if (res !== CHANGE_TASK_RESULT.OK) {
          return res;
        }
      }
      const curLastMailId = tryData.mails.getLastDirectMailId();

      for (let i = oldLastMailId + 1; i <= curLastMailId; i += 1) {
        receivedMailIds.push(i);
      }
    }

    // 외부 변수에 적용된 것들을 넣어준다.
    for (const contents of totalContentsToReceive) {
      const mergedGain: ActualGain = {};
      for (const content of contents) {
        _.merge<ActualGain, ActualGain>(mergedGain, content.gain);
      }
      this.gains.push(mergedGain);
    }
    this.mailIdsForOverflow.push(...receivedMailIds);

    //* glogBoxData
    // CMS.CashShopBoxRatioDesc.contents의 RewardType/DuplicateRewardType은
    // Id/DuplicateId가 있는 상품(명성/경험치류X)들만 지급되는 듯함.this.executeOp 참고
    const addGlogBoxDataReceived = (contents: ContentToReceive[]) => {
      for (const content of contents) {
        if (content.bDuplicated) {
          const contentElem = content.contentElem;
          this.glogBoxData.push({
            id: contentElem.Id,
            uid: null,
            name: getRewardNameCmsTableByRewardType(contentElem.RewardType, contentElem.Id),
            amt: contentElem.Amount,
            type: REWARD_TYPE[contentElem.RewardType],
            is_duplicated: 1,
            duplicated_id: contentElem.DuplicateId,
            duplicated_name: getRewardNameCmsTableByRewardType(
              contentElem.DuplicateRewardType,
              contentElem.DuplicateId
            ),
            duplicated_type: REWARD_TYPE[contentElem.DuplicateRewardType],
            duplicated_amt: contentElem.DuplicateAmount,
            eleven_bonus: content.isBonusGachaRewardForGlog ? 1 : 0,
          });
        } else {
          // uid가 있는 것들을 위함인듯?
          const arrRewawrdData = UserChangeTask.covertActualGainToGLogRewardData(content.gain);
          for (const rewardData of arrRewawrdData) {
            this.glogBoxData.push({
              id: rewardData.id,
              uid: rewardData.uid,
              name: getRewardNameCmsTableByRewardType(REWARD_TYPE[rewardData.type], rewardData.id),
              amt: rewardData.amt,
              type: rewardData.type,
              is_duplicated: 0,
              duplicated_id: null,
              duplicated_name: null,
              duplicated_type: null,
              duplicated_amt: null,
              eleven_bonus: content.isBonusGachaRewardForGlog ? 1 : 0,
            });
          }
        }
      }
    };
    //TODO 메일로 받은 것들
    const addGLogBoxDataMailed = (contents: ContentToMail[]) => {
      for (const {
        contentElem,
        bDuplicated,
        isBonusGachaRewardForGlog: isBonusGachaReward,
      } of contents) {
        const glogBoxData: GLogBoxData = {
          id: contentElem.Id,
          uid: null,
          name: getRewardNameCmsTableByRewardType(contentElem.RewardType, contentElem.Id),
          amt: contentElem.Amount,
          type: REWARD_TYPE[contentElem.RewardType],
          is_duplicated: 0,
          duplicated_id: null,
          duplicated_name: null,
          duplicated_type: null,
          duplicated_amt: null,
          eleven_bonus: isBonusGachaReward ? 1 : 0,
        };
        if (bDuplicated) {
          glogBoxData.is_duplicated = 1;
          glogBoxData.duplicated_id = contentElem.DuplicateId;
          glogBoxData.duplicated_name = getRewardNameCmsTableByRewardType(
            contentElem.DuplicateRewardType,
            contentElem.DuplicateId
          );
          glogBoxData.duplicated_type = REWARD_TYPE[contentElem.DuplicateRewardType];
          glogBoxData.duplicated_amt = contentElem.DuplicateAmount;
        }
        this.glogBoxData.push(glogBoxData);
      }
    };

    for (let i = 0; i < this.cashShopBoxRatioCmses.length; i += 1) {
      addGlogBoxDataReceived(totalContentsToReceive[i]);

      addGLogBoxDataMailed(totalContentsToMailForEquipment[i]);
      addGLogBoxDataMailed(totalContentsToMailForItem[i]);
      addGLogBoxDataMailed(totalContentsToMailForShip[i]);
    }
    //

    return CHANGE_TASK_RESULT.OK;
  }

  private executeOp(
    user: User,
    tryData: TryData,
    changes: Changes,
    rewardType: REWARD_TYPE,
    cmsId: number,
    amount: number
  ): CHANGE_TASK_RESULT {
    switch (rewardType) {
      case REWARD_TYPE.POINT:
        return opAddPoint(user, tryData, changes, cmsId, amount, false, false, undefined, false);

      case REWARD_TYPE.ITEM:
        return opAddItem(user, tryData, changes, cmsId, amount, false, false, undefined, true);

      case REWARD_TYPE.MATE_EQUIP:
        return opAddMateEquip(user, tryData, changes, cmsId, amount, false, false, undefined, true);

      case REWARD_TYPE.SHIP:
        return opAddShip(
          user,
          tryData,
          changes,
          cmsId,
          false,
          null,
          false /* bAllowExceedDock */,
          undefined,
          null,
          true
        );

      case REWARD_TYPE.MATE:
        return opAddMate(user, tryData, changes, cmsId, false);

      case REWARD_TYPE.SHIP_BLUEPRINT:
        return opUpgradeShipBlueprint(user, tryData, changes, cmsId, true);

      case REWARD_TYPE.SHIP_SLOT_ITEM:
        return opAddShipSlotItem(
          user,
          tryData,
          changes,
          cmsId,
          amount,
          false,
          false,
          undefined,
          true
        );

      // 타입이 추가될 때 this.getActualGain 도 신경써줘야 될듯 함.
      default:
        mlog.error('not implemented-cash-shop-gacha-box-reward-type', {
          userId: user.userId,
          rewardType,
          cmsId,
        });
        return CHANGE_TASK_RESULT.NOT_IMPLEMENTED;
    }
  }

  private getActualGain(
    rewardType: REWARD_TYPE,
    cmsId: number,
    amount: number,
    lastIds: {
      lastShipId: number;
      lastMateEquipmentId: number;
      lastShipSlotItemId: number;
      lastQuestItemId: number;
    },
    tryData: TryData
  ): ActualGain {
    const ids: number[] = [];
    if (rewardType === REWARD_TYPE.SHIP) {
      const oldLastId = lastIds.lastShipId;
      const tryDataLastId = tryData.fleets.getLastShipId();
      for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
        ids.push(i);
      }
    } else if (rewardType === REWARD_TYPE.MATE_EQUIP) {
      const oldLastId = lastIds.lastMateEquipmentId;
      const tryDataLastId = tryData.mates.getLastMateEquipmentId();
      for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
        ids.push(i);
      }
    } else if (rewardType === REWARD_TYPE.SHIP_SLOT_ITEM) {
      const oldLastId = lastIds.lastShipSlotItemId;
      const tryDataLastId = tryData.inven.getLastShipSlotItemId();
      for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
        ids.push(i);
      }
    } else if (rewardType === REWARD_TYPE.QUEST_ITEM) {
      const oldLastId = lastIds.lastQuestItemId;
      const tryDataLastId = tryData.inven.itemInven.getLastQuestItemId();
      for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
        ids.push(i);
      }
    }

    switch (rewardType) {
      case REWARD_TYPE.POINT:
        return {
          points: {
            [cmsId]: amount,
          },
        };
      case REWARD_TYPE.ITEM:
        return {
          items: {
            [cmsId]: amount,
          },
        };
      case REWARD_TYPE.MATE_EQUIP:
        return {
          mateEquips: {
            [cmsId]: amount,
          },
          mateEquipIds: {
            [cmsId]: ids,
          },
        };
      case REWARD_TYPE.SHIP:
        return {
          ships: {
            [cmsId]: amount,
          },
          shipIds: {
            [cmsId]: ids,
          },
        };
      case REWARD_TYPE.MATE:
        return {
          mates: [cmsId],
        };
      case REWARD_TYPE.SHIP_BLUEPRINT:
        return {
          shipBlueprints: {
            [cmsId]: {
              level: 1,
              exp: 0,
            },
          },
        };
      case REWARD_TYPE.SHIP_SLOT_ITEM:
        return {
          shipSlotItems: {
            [cmsId]: amount,
          },
          shipSlotItemIds: {
            [cmsId]: ids,
          },
        };
    }
  }

  private static convertContentElemToRewardFixedElem(
    elem: ContentsDesc,
    bDuplicated: boolean
  ): RewardFixedElemDesc {
    return bDuplicated
      ? {
          Type: elem.DuplicateRewardType,
          Id: elem.DuplicateId,
          Quantity: elem.DuplicateAmount,
        }
      : {
          Type: elem.RewardType,
          Id: elem.Id,
          Quantity: elem.Amount,
        };
  }
}

class CashShopBuyWithoutPurchaseQuestPassSpec extends CashShopBuyWithoutPurchaseSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    cashShopCmsId: number,
    curTimeUtc: number,
    private researchPoint: number,
    private expiredFixedTermProducts: Set<number>,
    private newFixedTermProduct: FixedTermProduct,
    private mateCmsId: number
  ) {
    super(
      costPointCmsId,
      costPointValue,
      bPermitExchange,
      mileageBonus,
      cashShopCmsId,
      expiredRestrictedProducts,
      restrictedProductChange,
      curTimeUtc
    );
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const ops = [super.accumulate(user, tryData, changes)];

    if (this.expiredFixedTermProducts) {
      for (const cmsId of this.expiredFixedTermProducts) {
        ops.push(opDeleteCashShopFixedTermProduct(user, tryData, changes, cmsId));
      }
    }

    if (this.newFixedTermProduct) {
      ops.push(opSetCashShopFixedTermProduct(user, tryData, changes, this.newFixedTermProduct));
    }

    if (this.mateCmsId) {
      ops.push(opAddMate(user, tryData, changes, this.mateCmsId));
    }
    if (this.researchPoint) {
      ops.push(
        opAddPoint(
          user,
          tryData,
          changes,
          cmsEx.ResearchPointCmsId,
          this.researchPoint,
          false,
          false,
          { itemId: rsn },
          false,
          true
        )
      );
    }

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
}

class CashShopBuyWithoutPurchaseSoundPackSpec extends CashShopBuyWithoutPurchaseSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    cashShopCmsId: number,
    curTimeUtc: number,
    private soundPackCmsId: number
  ) {
    super(
      costPointCmsId,
      costPointValue,
      bPermitExchange,
      mileageBonus,
      cashShopCmsId,
      expiredRestrictedProducts,
      restrictedProductChange,
      curTimeUtc
    );
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const ops = [
      super.accumulate(user, tryData, changes),
      opAddSoundPack(user, tryData, changes, this.soundPackCmsId),
    ];

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
}

class CashShopBuyWithoutPurchaseDailySubscriptionSpec extends CashShopBuyWithoutPurchaseRewardFixedSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    private dsCms: DailySubscriptionDesc,
    private rewardAmount: number,
    private curTimeUtc: number, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow: number[],
    cashShopCmsId: number
  ) {
    super(
      costPointCmsId,
      costPointValue,
      bPermitExchange,
      mileageBonus,
      expiredRestrictedProducts,
      restrictedProductChange,
      dsCms.purchaseRewardId, // rewardFixedCmsId
      rewardAmount, // rewardAmount
      curTimeUtc,
      mailIdsForOverflow,
      cashShopCmsId
    );
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    //
    let res: CHANGE_TASK_RESULT = super.accumulate(user, tryData, changes);
    if (res !== CHANGE_TASK_RESULT.OK) {
      return res;
    }

    for (let i = 0; i < this.rewardAmount; i++) {
      res = opBuyDailySubscription(user, tryData, changes, this.dsCms.id, this.curTimeUtc);
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
}

class CashShopBuyWithoutPurchaseHotSpotSpec extends CashShopBuyWithoutPurchaseRewardFixedSpec {
  constructor(
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    rewardFixedCmsId: number,
    rewardAmount: number,
    private curTimeUtc: number, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow: number[],
    private cashShopCmsId: number
  ) {
    super(
      costPointCmsId,
      costPointValue,
      bPermitExchange,
      mileageBonus,
      expiredRestrictedProducts,
      restrictedProductChange,
      rewardFixedCmsId,
      rewardAmount,
      curTimeUtc,
      mailIdsForOverflow,
      cashShopCmsId
    );
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const ops = [
      super.accumulate(user, tryData, changes),
      opSetHotSpotCoolTimeUtc(user, tryData, changes, this.cashShopCmsId, this.curTimeUtc),
    ];

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
}

// class CashShopBuyWithoutPurchaseEncountShieldSpec extends CashShopBuyWithoutPurchaseSpec {
//   constructor(
//     costPointCmsId: number,
//     costPointValue: number,
//     bPermitExchange: boolean,
//     mileageBonus: number | undefined,
//     expiredRestrictedProducts: Set<number>,
//     restrictedProductChange: RestrictedProduct,
//     cashShopCmsId: number,
//     private amount: number
//   ) {
//     super(
//       costPointCmsId,
//       costPointValue,
//       bPermitExchange,
//       mileageBonus,
//       cashShopCmsId,
//       expiredRestrictedProducts,
//       restrictedProductChange
//     );
//   }
//   accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
//     const ops = [super.accumulate(user, tryData, changes)];

//     if (!tryData.encount) {
//       tryData.encount = user.userEncount.clone();
//     }

//     if (!tryData.shield) {
//       tryData.shield = user.userShield.clone();
//     }

//     // 보호막 구매 시 보호막을 켜준다.
//     if (!tryData.shield.isActivated(SHIELD_CMS_ID.ENCOUNT)) {
//       ops.push(opSetIsEncountShieldCountUsed(user, tryData, changes, 1));
//     }

//     ops.push(opAddEncountShieldCount(user, tryData, changes, this.amount));

//     for (const res of ops) {
//       if (res !== CHANGE_TASK_RESULT.OK) {
//         return res;
//       }
//     }

//     return CHANGE_TASK_RESULT.OK;
//   }
// }

namespace CashShopOperatorUtil {
  /**
   *! 캐시샵에서 구매할 때 필수적으로 사용해야하는 오퍼레이터들을 실행
   */
  export function executeCashShopBuyWithoutPurchaseOps(
    user: User,
    tryData: TryData,
    changes: Changes,
    costPointCmsId: number,
    costPointValue: number,
    bPermitExchange: boolean,
    mileageBonus: number | undefined,
    expiredRestrictedProducts: Set<number>,
    restrictedProductChange: RestrictedProduct,
    cashShopCmsId: number,
    buyCount: number | undefined,
    curTimeUtc: number
  ): CHANGE_TASK_RESULT {
    const ops = [];

    if (costPointValue && costPointValue > 0) {
      const cashShopCms = cms.CashShop[cashShopCmsId];
      const lgCashParam: any = {};
      if (costPointCmsId === cmsEx.RedGemPointCmsId && cashShopCms.isToBuy) {
        lgCashParam.productId =
          mconf.binaryCode === 'GL' ? `uwogl_${cashShopCmsId}` : `uwo_${cashShopCmsId}`;
      } else if (costPointCmsId === cmsEx.RedGemPointCmsId) {
        lgCashParam.itemId =
          mconf.binaryCode === 'GL' ? `uwogl_${cashShopCmsId}` : `uwo_${cashShopCmsId}`;
      } else {
        lgCashParam.itemId = rsn;
      }

      if (cashShopCms.isToBuy && buyCount > 1) {
        lgCashParam.buyCount = buyCount;
      }

      if (costPointCmsId === cmsEx.CashShopMileage) {
        ops.push(opAddMileage(user, tryData, changes, -costPointValue, curTimeUtc));
      } else {
        ops.push(
          opAddPoint(
            user,
            tryData,
            changes,
            costPointCmsId,
            -costPointValue,
            false,
            bPermitExchange,
            lgCashParam,
            false
          )
        );
      }
    }

    if (mileageBonus !== undefined && mileageBonus !== 0) {
      assert(mileageBonus > 0);
      ops.push(opAddMileage(user, tryData, changes, mileageBonus, curTimeUtc));
    }

    if (expiredRestrictedProducts) {
      for (const cmsId of expiredRestrictedProducts) {
        ops.push(opDeleteCashShopRestrictedProduct(user, tryData, changes, cmsId));
      }
    }

    if (restrictedProductChange) {
      ops.push(opSetCashShopRestrictedProduct(user, tryData, changes, restrictedProductChange));
    }

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }

  export function executeAddDirectMailOp(
    user: User,
    tryData: TryData,
    changes: Changes,
    mailCmsId: number,
    curTimeUtc: number,
    elems: RewardFixedElemDesc[],
    bIsAccum: boolean
  ) {
    const { expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment } =
      getMailExpirationParamForOperator(mailCmsId, curTimeUtc);
    return opAddAttachmentDirectMail(
      user,
      tryData,
      changes,
      mailCmsId,
      curTimeUtc,
      expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment,
      elems,
      bIsAccum
    );
  }

  function getMailExpirationParamForOperator(mailCmsId: number, curTimeUtc: number) {
    const mailCms = cms.Mail[mailCmsId];
    let expireTimeUtc = null;
    let bShouldSetExpirationWhenReceiveAttachment = 0;
    if (mailCms.mailKeepTime > 0) {
      expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
    } else if (mailCms.mailKeepTime === -1) {
      bShouldSetExpirationWhenReceiveAttachment = 1;
    }
    return {
      expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment,
    };
  }

  /**
   * opAddDirectmail Wrapper
   */
  function opAddAttachmentDirectMail(
    user: User,
    tryData: TryData,
    changes: Changes,
    mailCmsId: number,
    curTimeUtc: number,
    expireTimeUtc: number,
    bShouldSetExpirationWhenReceiveAttachment: number,
    elem: RewardFixedElemDesc[],
    bIsAccum: boolean
  ) {
    return opAddDirectmail(
      user,
      tryData,
      changes,
      mailCmsId,
      curTimeUtc,
      expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment,
      null,
      null,
      null,
      null,
      cmsEx.convertRewardFixedElemsToCustomAttachmentStr(elem, bIsAccum, curTimeUtc)
    );
  }
}
