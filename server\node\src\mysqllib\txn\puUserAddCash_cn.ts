// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_add_cash_cn';

// TODO 증가된 cash를 반환해야하지 않을까?
export interface Result {
  userId: number;
}

const spFunction = query.generateSPFunction(spName);

export default async function (
  connection: query.Connection,
  userId: number,
  cmsId: number,
  amount: number,
): Promise<Result> {
  const qr = await spFunction(connection, userId, cmsId, amount);
  const rows = qr.rows;
  return {
    userId: userId,
  };
}
