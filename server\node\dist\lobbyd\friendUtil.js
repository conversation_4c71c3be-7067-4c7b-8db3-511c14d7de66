"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FriendUtil = void 0;
const lodash_1 = __importDefault(require("lodash"));
const cms_1 = __importDefault(require("../cms"));
const merror_1 = require("../motiflib/merror");
const lobby_1 = require("../motiflib/model/lobby");
const mutil_1 = require("../motiflib/mutil");
const Container_1 = require("typedi/Container");
const server_1 = require("./server");
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mysqlUtil_1 = require("../mysqllib/mysqlUtil");
const formula_1 = require("../formula");
const userFriends_1 = require("./userFriends");
const guildUtil_1 = require("./guildUtil");
const mutil = __importStar(require("../motiflib/mutil"));
const puFriendLoad_1 = __importDefault(require("../mysqllib/sp/puFriendLoad"));
const puFriendAdd_1 = __importDefault(require("../mysqllib/sp/puFriendAdd"));
const puFriendDelete_1 = __importDefault(require("../mysqllib/sp/puFriendDelete"));
const puFriendUpdate_1 = __importDefault(require("../mysqllib/sp/puFriendUpdate"));
const puFriendPointUpdate_1 = __importDefault(require("../mysqllib/sp/puFriendPointUpdate"));
const puFriendPointLoad_1 = __importDefault(require("../mysqllib/sp/puFriendPointLoad"));
const tuPickupFriendPoint_1 = __importDefault(require("../mysqllib/txn/tuPickupFriendPoint"));
const userCacheRedisHelper_1 = require("../motiflib/userCacheRedisHelper");
const tuDeleteFriend_1 = __importDefault(require("../mysqllib/txn/tuDeleteFriend"));
const bluebird_1 = require("bluebird");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const rsn = 'friend_point';
const add_rsn = null;
var FriendUtil;
(function (FriendUtil) {
    //=========================================================================================================
    // 친구요청 신청하기.
    // 최대 인원 및 DB 에러등으로 친구요청이 불가능하거나, 상대방의 친구요청받기가 불가능할 경우 친구요청은 실패.
    //=========================================================================================================
    function requestFriend(user, friendUserId) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const userFriends = user.userFriends;
        const now = mutil.curTimeUtc();
        //------------------------------------------------------------------------------------------------
        // number타입 체크를 안할경우, 바로 아래에 있는 본인여부 조건에서 무조건 통과가 되기때문에 무결성 체크필요.
        //------------------------------------------------------------------------------------------------
        if (typeof friendUserId !== 'number') {
            throw new merror_1.MError('invalid-other-userid', merror_1.MErrorCode.INVALID_OTHER_USER_ID, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 본인에게 친구 요청 불가능.
        //------------------------------------------------------------------------------------------------
        if (user.userId === friendUserId) {
            throw new merror_1.MError('cannot-request-yourself', merror_1.MErrorCode.CANNOT_REQUEST_YOURSELF, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 최대 친구 수 검사.
        //------------------------------------------------------------------------------------------------
        if (userFriends.getCountByState(userFriends_1.FRIEND_STATE.ESTABLISHED) >= cms_1.default.Const.FriendLimit.value) {
            throw new merror_1.MError('the-number-of-friends-is-full', merror_1.MErrorCode.NUMBER_OF_FRIENDS_IS_FULL, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 최대 친구요청 수 검사.
        //------------------------------------------------------------------------------------------------
        if (userFriends.getCountByState(userFriends_1.FRIEND_STATE.REQUEST) >= cms_1.default.Const.FriendRequestLimit.value) {
            throw new merror_1.MError('the-number-of-requests-is-full', merror_1.MErrorCode.NUMBER_OF_REQUESTS_IS_FULL, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 대상이 친구상태 or 요청상태 or 요청받은상태에서는 실패.
        //------------------------------------------------------------------------------------------------
        if (userFriends.friends[friendUserId]) {
            const state = userFriends.friends[friendUserId].state;
            // 이미 친구상태?
            if (state === userFriends_1.FRIEND_STATE.ESTABLISHED) {
                throw new merror_1.MError('already-friend', merror_1.MErrorCode.ALREADY_FRIEND, {
                    friendUserId,
                });
            }
            // 요청을 보냈거나, 받은상태?
            if (state === userFriends_1.FRIEND_STATE.REQUEST || state === userFriends_1.FRIEND_STATE.RECEIVED) {
                throw new merror_1.MError('friend-request-is-already-registered', merror_1.MErrorCode.ALREADY_REGISTERED, {
                    friendUserId,
                    state,
                });
            }
        }
        // 차단유저 확인.
        const volanteUserId = user.userId.toString();
        return mhttp_1.default.platformChatApi
            .getMuteUserIds(volanteUserId)
            .then((result) => {
            const idx = result.findIndex((blockedUser) => friendUserId === blockedUser);
            if (idx !== -1) {
                throw new merror_1.MError('you-have-blocked-the-user', merror_1.MErrorCode.BLOCKED_USER, {
                    friendUserId,
                });
            }
            return mhttp_1.default.platformChatApi.getMuteUserIds(friendUserId.toString()).then((result) => {
                const idx = result.findIndex((blockedUser) => user.userId === blockedUser);
                if (idx !== -1) {
                    throw new merror_1.MError('you-have-been-blocked-by-the-user', merror_1.MErrorCode.BLOCKED_BY_TARGET, {
                        friendUserId,
                    });
                }
            });
        })
            .then(() => {
            return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                //------------------------------------------------------------------------------------------------
                // 존재하지 않는 유저는 실패.
                //------------------------------------------------------------------------------------------------
                if (!userLightInfos[friendUserId]) {
                    throw new merror_1.MError('cannot-find-the-user', merror_1.MErrorCode.CANNOT_FIND_USER, {
                        friendUserId,
                    });
                }
                //------------------------------------------------------------------------------------------------
                // 상대방이친구요청을 받을 수 있는지 확인.
                //------------------------------------------------------------------------------------------------
                return (0, puFriendLoad_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId)
                    .then((results) => {
                    //------------------------------------------------------------------------------------------------
                    // 상대방이 친구 수가 꽉찬경우.
                    //------------------------------------------------------------------------------------------------
                    const numFriends = results.filter((elem) => elem.state === userFriends_1.FRIEND_STATE.ESTABLISHED).length;
                    if (numFriends >= cms_1.default.Const.FriendLimit.value) {
                        throw new merror_1.MError('recipient-friends-are-full', merror_1.MErrorCode.OTHER_USER_FRIENDS_ARE_FULL, {
                            friendUserId,
                        });
                    }
                    //------------------------------------------------------------------------------------------------
                    // 상대방이 친구요청을 더이상 받을 수 없는 경우
                    //------------------------------------------------------------------------------------------------
                    const numReceiveds = results.filter((elem) => elem.state === userFriends_1.FRIEND_STATE.RECEIVED).length;
                    if (numReceiveds >= cms_1.default.Const.FriendAcceptLimit.value) {
                        throw new merror_1.MError('recipient-Accepts-are-full', merror_1.MErrorCode.OTHER_USER_ACCEPTS_ARE_FULL, {
                            friendUserId,
                        });
                    }
                    //------------------------------------------------------------------------------------------------
                    // 이미 등록되어있는지 확인.
                    //------------------------------------------------------------------------------------------------
                    const friend = results.find((elem) => elem.friendUserId === user.userId);
                    if (friend) {
                        throw new merror_1.MError('already-registered', merror_1.MErrorCode.ALREADY_REGISTERED, {
                            friendUserId,
                            state: friend.state,
                        });
                    }
                })
                    .then(() => {
                    return (0, puFriendAdd_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId, userFriends_1.FRIEND_STATE.REQUEST, now)
                        .then(() => {
                        return (0, puFriendAdd_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId, user.userId, userFriends_1.FRIEND_STATE.RECEIVED, now).catch((e1) => {
                            // 롤백....
                            return (0, puFriendDelete_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId)
                                .catch((e2) => {
                                // 롤백에 실패할 경우 로그를 남겨주도록 하자.
                                mlog_1.default.error('[friend-request] rollback-query-error', {
                                    userId: user.userId,
                                    friendUserId,
                                    error: e2.message,
                                    stack: e2.stack,
                                });
                            })
                                .finally(() => {
                                throw e1;
                            });
                        });
                    })
                        .catch((e1) => {
                        throw new merror_1.MError('[friend-request] faild-query', merror_1.MErrorCode.FRIEND_REQUEST_QUERY_ERROR, {
                            userId: user.userId,
                            friendUserId,
                            error: e1.message,
                            stack: e1.stack,
                        });
                    });
                })
                    .then(() => {
                    //------------------------------------------------------------------------------------------------
                    // 상대방에게 친구요청 알림 전달(상대방이 온라인일 경우)
                    //------------------------------------------------------------------------------------------------
                    return notifyOnlineFriends(user.userId, user.userName, [friendUserId], lobby_1.FRIEND_NOTIFICATION_TYPE.REQUESTED, now);
                })
                    .then(() => {
                    return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                        const userLightInfo = userLightInfos[friendUserId];
                        if (userLightInfo) {
                            user.glog('friend_request', {
                                rsn: 'friend_request',
                                add_rsn: null,
                                friend_nid: userLightInfo.pubId,
                                friend_gameUserId: friendUserId,
                                type: 1, // 신청 1: 취소 : 0
                            });
                        }
                        return;
                    });
                })
                    .then(() => {
                    const nub = {
                        friendUserId: friendUserId,
                        state: userFriends_1.FRIEND_STATE.REQUEST,
                        regTimeUtc: now,
                    };
                    return userFriends.updateFriend(nub);
                });
            });
        });
    }
    FriendUtil.requestFriend = requestFriend;
    //=========================================================================================================
    // 친구요청 취소,거절 및  친구제거시 양쪽 유저의 정보를 삭제합니다.
    //=========================================================================================================
    function cancelFriend(user, friendUserId, notiType) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const userFriends = user.userFriends;
        //------------------------------------------------------------------------------------------------
        // 등록된 정보에 없을 경우 실패.
        //------------------------------------------------------------------------------------------------
        if (!userFriends.friends[friendUserId]) {
            throw new merror_1.MError('cannot-find-the-user', merror_1.MErrorCode.CANNOT_FIND_USER, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 취소타입에 따라, 상태가 일치한지 확인.
        // 즉, 요청취소는 요청상태이어야하며, 요청거절은 요청받은 상태이어야한다.
        //------------------------------------------------------------------------------------------------
        if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.CANCELED) {
            if (userFriends.friends[friendUserId].state !== userFriends_1.FRIEND_STATE.REQUEST) {
                throw new merror_1.MError('invalid-friend-state', merror_1.MErrorCode.INVALID_FRIEND_STATE, {
                    friendUserId,
                    state: userFriends.friends[friendUserId].state,
                    notiType,
                });
            }
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.DENIED) {
            if (userFriends.friends[friendUserId].state !== userFriends_1.FRIEND_STATE.RECEIVED) {
                throw new merror_1.MError('invalid-friend-state', merror_1.MErrorCode.INVALID_FRIEND_STATE, {
                    friendUserId,
                    state: userFriends.friends[friendUserId].state,
                    notiType,
                });
            }
        }
        //------------------------------------------------------------------------------------------------
        // 양쪽모두 친구삭제한다.일단 요청자 부터..
        //------------------------------------------------------------------------------------------------
        return (0, puFriendDelete_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId)
            .then(() => {
            // 상대방도 삭제.
            return (0, puFriendDelete_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId, user.userId).catch((e) => {
                // 삭제는 양쪽 모두 완전한 동기화 대신 실패하더라도, 요청자는 그대로 삭제해주고 로그를 남기는 방식으로 처리.
                // 양쪽 모두 완벽히 삭제가 안되더라도, 한쪽유저만 삭제할 수 있으면 그대로 처리해 주도록 한다.
                // 시스템 에러등으로 삭제가 안될 경우 유저입장에서는 답답할 수 있기 때문이다.
                mlog_1.default.error('[friend] failed-to-cancel-both-of-friends', {
                    userId: user.userId,
                    friendUserId,
                    notiType: lobby_1.FRIEND_NOTIFICATION_TYPE[notiType],
                    nub: userFriends.friends[friendUserId],
                    error: e.message,
                    stack: mconf_1.default.isDev ? undefined : e.stack,
                });
            });
        })
            .then(() => {
            return notifyOnlineFriends(user.userId, user.userName, [friendUserId], notiType, (0, mutil_1.curTimeUtc)());
        })
            .then(() => {
            return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                const userLightInfo = userLightInfos[friendUserId];
                if (userLightInfo) {
                    if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.DENIED) {
                        user.glog('friend_accept', {
                            rsn: 'friend_accept',
                            add_rsn: null,
                            friend_nid: userLightInfo.pubId,
                            friend_gameUserId: friendUserId,
                            type: 0, // 수락 :1 거절: 0
                        });
                    }
                    else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.CANCELED) {
                        user.glog('friend_request', {
                            rsn: 'friend_request',
                            add_rsn: null,
                            friend_nid: userLightInfo.pubId,
                            friend_gameUserId: friendUserId,
                            type: 0, // 신청 1: 취소 : 0
                        });
                    }
                }
                return;
            });
        })
            .then(() => {
            return userFriends.cancelFriend(friendUserId);
        });
    }
    FriendUtil.cancelFriend = cancelFriend;
    //=========================================================================================================
    // 친구요청 취소,거절 및  친구제거시 양쪽 유저의 정보를 삭제합니다.
    //=========================================================================================================
    function deleteFriend(user, friendUserId) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const userFriends = user.userFriends;
        //------------------------------------------------------------------------------------------------
        // 등록된 정보에 없을 경우 실패.
        //------------------------------------------------------------------------------------------------
        if (!userFriends.friends[friendUserId]) {
            throw new merror_1.MError('cannot-find-the-user', merror_1.MErrorCode.CANNOT_FIND_USER, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 실제 친구상태인지 확인.
        //------------------------------------------------------------------------------------------------
        if (userFriends.friends[friendUserId].state !== userFriends_1.FRIEND_STATE.ESTABLISHED) {
            throw new merror_1.MError('invalid-friend-state', merror_1.MErrorCode.INVALID_FRIEND_STATE, {
                friendUserId,
                state: userFriends.friends[friendUserId].state,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 양쪽모두 친구삭제한다.일단 요청자 부터..
        //------------------------------------------------------------------------------------------------
        return (0, tuDeleteFriend_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId, 0)
            .then(() => {
            // 상대방도 삭제.
            return (0, tuDeleteFriend_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId, user.userId, 0).catch((e) => {
                // 삭제는 양쪽 모두 완전한 동기화 대신 실패하더라도, 요청자는 그대로 삭제해주고 로그를 남기는 방식으로 처리.
                // 양쪽 모두 완벽히 삭제가 안되더라도, 한쪽유저만 삭제할 수 있으면 그대로 처리해 주도록 한다.
                // 시스템 에러등으로 삭제가 안될 경우 유저입장에서는 답답할 수 있기 때문.
                mlog_1.default.error('[friend] failed-to-delete-both-of-friends', {
                    userId: user.userId,
                    friendUserId,
                    error: e.message,
                    stack: mconf_1.default.isDev ? undefined : e.stack,
                });
            });
        })
            .then(() => {
            return notifyOnlineFriends(user.userId, user.userName, [friendUserId], lobby_1.FRIEND_NOTIFICATION_TYPE.DELETED, (0, mutil_1.curTimeUtc)());
        })
            .then(() => {
            return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                const userLightInfo = userLightInfos[friendUserId];
                if (userLightInfo) {
                    user.glog('friend_delete', {
                        rsn: 'friend_delete',
                        add_rsn: null,
                        friend_nid: userLightInfo.pubId,
                        friend_gameUserId: friendUserId,
                    });
                }
                return;
            });
        })
            .then(() => {
            return userFriends.deleteFriend(friendUserId);
        });
    }
    FriendUtil.deleteFriend = deleteFriend;
    //=========================================================================================================
    // 친구요청을  수락하여 서로 친구관계를 등록합니다.
    //=========================================================================================================
    function acceptFriend(user, friendUserId) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const now = (0, mutil_1.curTimeUtc)();
        const userFriends = user.userFriends;
        const friendNub = userFriends.friends[friendUserId];
        //------------------------------------------------------------------------------------------------
        // 등록된 정보에 없을 경우 실패.
        //------------------------------------------------------------------------------------------------
        if (!userFriends.friends[friendUserId]) {
            throw new merror_1.MError('cannot-find-the-user', merror_1.MErrorCode.CANNOT_FIND_USER, {
                friendUserId,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 요청받은 상태인지 확인.
        //------------------------------------------------------------------------------------------------
        if (userFriends.friends[friendUserId].state !== userFriends_1.FRIEND_STATE.RECEIVED) {
            throw new merror_1.MError('invalid-friend-state', merror_1.MErrorCode.INVALID_FRIEND_STATE, {
                friendUserId,
                state: userFriends.friends[friendUserId].state,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 최대 친구 수 검사.
        //------------------------------------------------------------------------------------------------
        if (userFriends.getCountByState(userFriends_1.FRIEND_STATE.ESTABLISHED) >= cms_1.default.Const.FriendLimit.value) {
            throw new merror_1.MError('friends-are-full', merror_1.MErrorCode.NUMBER_OF_FRIENDS_IS_FULL, {
                friendUserId,
            });
        }
        return mhttp_1.default.platformChatApi
            .getMuteUserIds(user.userId.toString())
            .then((result) => {
            const idx = result.findIndex((blockedUser) => friendUserId === blockedUser);
            if (idx !== -1) {
                throw new merror_1.MError('you-have-blocked-the-user', merror_1.MErrorCode.BLOCKED_USER, {
                    friendUserId,
                });
            }
            return mhttp_1.default.platformChatApi.getMuteUserIds(friendUserId.toString()).then((result) => {
                const idx = result.findIndex((blockedUser) => user.userId === blockedUser);
                if (idx !== -1) {
                    throw new merror_1.MError('you-have-been-blocked-by-the-user', merror_1.MErrorCode.BLOCKED_BY_TARGET, {
                        friendUserId,
                    });
                }
            });
        })
            .then(() => {
            return (0, puFriendLoad_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId)
                .then((results) => {
                //------------------------------------------------------------------------------------------------
                //상대방의 친구 수가 꽉찬경우.
                //------------------------------------------------------------------------------------------------
                const numFriends = results.filter((elem) => elem.state === userFriends_1.FRIEND_STATE.ESTABLISHED).length;
                if (numFriends >= cms_1.default.Const.FriendLimit.value) {
                    throw new merror_1.MError('other-user-are-full', merror_1.MErrorCode.OTHER_USER_FRIENDS_ARE_FULL, {
                        friendUserId,
                    });
                }
                //------------------------------------------------------------------------------------------------
                // 상대방이 나를 요청했는지 확인.
                //------------------------------------------------------------------------------------------------
                const friend = results.find((elem) => elem.friendUserId == user.userId);
                if (!friend) {
                    throw new merror_1.MError('cannot-find-the-user', merror_1.MErrorCode.CANNOT_FIND_USER, {
                        friendUserId,
                    });
                }
                //------------------------------------------------------------------------------------------------
                // 상대방이 요청상태가 아닐 경우 실패.
                //------------------------------------------------------------------------------------------------
                if (friend.state !== userFriends_1.FRIEND_STATE.REQUEST) {
                    throw new merror_1.MError('invalid-friend-state', merror_1.MErrorCode.INVALID_FRIEND_STATE, {
                        friendUserId,
                        state: friend.state,
                    });
                }
            })
                .then(() => {
                // 요청자의 친구요청 추가
                return (0, puFriendUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId, userFriends_1.FRIEND_STATE.ESTABLISHED, now)
                    .then(() => {
                    // 수신자의 친구수신 추기.
                    return (0, puFriendUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(friendUserId)), friendUserId, user.userId, userFriends_1.FRIEND_STATE.ESTABLISHED, now).catch((e1) => {
                        // 롤백....
                        return (0, puFriendDelete_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, friendUserId).catch((e2) => {
                            mlog_1.default.error('[friend-accept] friend rollback query error', {
                                userId: user.userId,
                                recipientUserId: friendUserId,
                                error: e2.message,
                                stack: e2.stack,
                            });
                            throw e1;
                        });
                    });
                })
                    .catch((e1) => {
                    throw new merror_1.MError('[friend-accept] faild-query', merror_1.MErrorCode.FRIEND_ACCEPT_QUERY_ERROR, {
                        userId: user.userId,
                        recipientUserId: friendUserId,
                        error: e1.message,
                        stack: e1.stack,
                    });
                });
            })
                .then(() => {
                return notifyOnlineFriends(user.userId, user.userName, [friendUserId], lobby_1.FRIEND_NOTIFICATION_TYPE.ACCEPTED, now);
            })
                .then(() => {
                return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                    const userLightInfo = userLightInfos[friendUserId];
                    if (userLightInfo) {
                        user.glog('friend_accept', {
                            rsn: 'friend_accept',
                            add_rsn: null,
                            friend_nid: userLightInfo.pubId,
                            friend_gameUserId: friendUserId,
                            type: 1, // 수락:1 거절:2
                        });
                    }
                    return;
                });
            })
                .then(() => {
                friendNub.state = userFriends_1.FRIEND_STATE.ESTABLISHED;
                friendNub.regTimeUtc = now;
                return userFriends.updateFriend(friendNub);
            });
        });
    }
    FriendUtil.acceptFriend = acceptFriend;
    function notifyOnlineFriends(userId, userName, friendUserIds, notiType, timeUtc) {
        guildUtil_1.GuildUtil.getLobbyUrlsByUserIds(friendUserIds)
            .then((rets) => {
            rets.forEach((elem) => {
                const packet = {
                    userIds: elem.userIds,
                    notiType,
                    friendUserId: userId,
                    friendUserName: userName,
                    timeUtc,
                };
                // 친구알림에 실패나도 게임지장엔 영향이 없으므로 로그만  남겨주자.
                elem.lobbyApi.sendFriendNotification(packet).catch((e) => {
                    mlog_1.default.error('failed-to-send-friendNotification', {
                        packet,
                        error: e.message,
                        stack: e.stack,
                    });
                });
            });
        })
            .catch((e) => {
            mlog_1.default.error('an-error-occured', {
                error: e.message,
                stack: e.stack,
            });
        });
    }
    FriendUtil.notifyOnlineFriends = notifyOnlineFriends;
    //=========================================================================================================
    // 친구가 보낸 포인트를 수령.
    //=========================================================================================================
    function pickupPoint(user, friendUserId) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const now = (0, mutil_1.curTimeUtc)();
        const userFriends = user.userFriends;
        const friendNub = userFriends.friends[friendUserId];
        //------------------------------------------------------------------------------------------------
        // 친구상태에서만 전달 가능하다.
        //------------------------------------------------------------------------------------------------
        if (!friendNub || friendNub.state !== userFriends_1.FRIEND_STATE.ESTABLISHED) {
            throw new merror_1.MError('no-friend', merror_1.MErrorCode.NO_FRIEND, {
                friendUserId,
                state: friendNub ? friendNub.state : null,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 하루동안 받을 수 있는 횟수 체크.
        //------------------------------------------------------------------------------------------------
        const pickupCount = userFriends.getPickupCountToday(now);
        if (pickupCount >= cms_1.default.Const.FriendAP.value) {
            throw new merror_1.MError('exceeding-daily-pickup-limit', merror_1.MErrorCode.EXCEEDING_DAILY_PICKUP_LIMIT, {
                friendUserId,
                pickupCount,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 당일 보낸 포인트만 받을 수있다.
        //------------------------------------------------------------------------------------------------
        const friendPointNub = lodash_1.default.cloneDeep(userFriends.getFriendPoint(friendUserId));
        const passingCount = (0, formula_1.CalcContentsResetTimePassingCount)(now, friendPointNub.lastRecvDate, cms_1.default.ContentsResetHour.FriendAPReset.hour);
        if (passingCount > 0) {
            throw new merror_1.MError('not-received-point-today', merror_1.MErrorCode.NOT_RECEIVED_POINT_TODAY, {
                friendUserId,
                lastRecvDate: friendPointNub.lastRecvDate,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 오늘날짜로 이미 픽업?
        //------------------------------------------------------------------------------------------------
        if (friendPointNub.pickup) {
            throw new merror_1.MError('already-pick-up', merror_1.MErrorCode.ALREADY_PICK_UP, {
                friendUserId,
                friendPointNub,
            });
        }
        friendPointNub.pickup = 1;
        friendPointNub.totalReceivedPts++;
        const energyChange = user.userEnergy.buildEnergyChangeWithConsume(now, user.level, user.level, -cms_1.default.Const.FriendAPSendCount.value, false);
        return (0, tuPickupFriendPoint_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, energyChange, [friendPointNub])
            .then(() => {
            return (0, userCacheRedisHelper_1.getUserLightInfos)([friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                // 순수 친구포인트로 획득한 수치만 로그에 쌓는다.
                let prevEnergy = user.userEnergy.rawEnergy; // 업데이트 전 현재 포인트.
                user.glog('friend_point', {
                    flag: 2,
                    target_nid: userLightInfos[friendUserId] ? userLightInfos[friendUserId].pubId : null,
                    target_gameUserId: friendUserId,
                    cv: cms_1.default.Const.FriendAPSendCount.value,
                    rv: prevEnergy + cms_1.default.Const.FriendAPSendCount.value,
                });
            });
        })
            .then(() => {
            const sync = {};
            lodash_1.default.merge(sync, user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn }));
            lodash_1.default.merge(sync, userFriends.updateFriendPoint(friendPointNub));
            mlog_1.default.verbose('pickup-friend-point', {
                userId: user.userId,
                friendPointNub,
                energyPoint: energyChange.energy,
            });
            return sync;
        });
    }
    FriendUtil.pickupPoint = pickupPoint;
    //=========================================================================================================
    // 친구가 보낸 포인트를 수령.
    //=========================================================================================================
    function pickupPointAll(user) {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const worldConfg = mconf_1.default.getWorldConfig();
        const userFriends = user.userFriends;
        const now = (0, mutil_1.curTimeUtc)();
        const pickupCount = userFriends.getPickupCountToday(now);
        const ableToPickupNum = cms_1.default.Const.FriendAP.value - pickupCount; // 실제 수령받을 수 있는 갯수.
        // 업데이트할 객체 목록.
        const changePointNubs = [];
        lodash_1.default.forOwn(userFriends.friends, (elem) => {
            if (changePointNubs.length >= ableToPickupNum) {
                return;
            }
            if (elem.state !== userFriends_1.FRIEND_STATE.ESTABLISHED) {
                return;
            }
            const friendPointNub = lodash_1.default.cloneDeep(userFriends.getFriendPoint(elem.friendUserId));
            const passingCount = (0, formula_1.CalcContentsResetTimePassingCount)(now, friendPointNub.lastRecvDate, cms_1.default.ContentsResetHour.FriendAPReset.hour);
            if (passingCount > 0) {
                return;
            }
            if (friendPointNub.pickup) {
                return;
            }
            friendPointNub.pickup = 1;
            friendPointNub.totalReceivedPts++;
            changePointNubs.push(friendPointNub);
        });
        // no get points.
        if (changePointNubs.length === 0) {
            return bluebird_1.Promise.resolve({});
        }
        const addedPoint = -cms_1.default.Const.FriendAPSendCount.value * changePointNubs.length;
        const energyChange = user.userEnergy.buildEnergyChangeWithConsume(now, user.level, user.level, addedPoint, false);
        return (0, tuPickupFriendPoint_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, energyChange, changePointNubs)
            .then(() => {
            const friendUserIds = changePointNubs.map((elem) => elem.friendUserId);
            return (0, userCacheRedisHelper_1.getUserLightInfos)(friendUserIds, userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                /*
                   전체 수령시 array형식으로 한번에 보낼경우 라인게임즈측에서 추적이 어렵다는
                   의견이 있어서 어쩔 수없이 개별적으로 각각 획득한 양을 로그로 남기도록 함.
                */
                // 순수 친구포인트로 획득한 수치만 로그에 쌓는다.
                let curEnergy = user.userEnergy.rawEnergy; // 업데이트 전 현재 포인트.
                lodash_1.default.forOwn(userLightInfos, (elem) => {
                    curEnergy += cms_1.default.Const.FriendAPSendCount.value;
                    user.glog('friend_point', {
                        flag: 2,
                        target_nid: elem.pubId,
                        target_gameUserId: elem.userId,
                        cv: cms_1.default.Const.FriendAPSendCount.value,
                        rv: curEnergy,
                    });
                });
            });
        })
            .then(() => {
            const sync = {};
            lodash_1.default.merge(sync, user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn }));
            changePointNubs.forEach((elem) => {
                lodash_1.default.merge(sync, userFriends.updateFriendPoint(elem));
            });
            mlog_1.default.verbose('pickup-friend-point-all', {
                userId: user.userId,
                pickNum: changePointNubs.length,
                energyPoint: energyChange.energy,
            });
            return sync;
        });
    }
    FriendUtil.pickupPointAll = pickupPointAll;
    //=========================================================================================================
    // 친구에게 포인트 주기.
    //=========================================================================================================
    function sendPoint(user, friendUserId) {
        const { userDbConnPoolMgr } = Container_1.Container.get(server_1.LobbyService);
        const userFriends = user.userFriends;
        const now = (0, mutil_1.curTimeUtc)();
        //------------------------------------------------------------------------------------------------
        // 친구상태에서만 전달 가능하다.
        //------------------------------------------------------------------------------------------------
        const friendNub = userFriends.friends[friendUserId];
        if (!friendNub || friendNub.state !== userFriends_1.FRIEND_STATE.ESTABLISHED) {
            throw new merror_1.MError('no-friend', merror_1.MErrorCode.NO_FRIEND, {
                friendUserId,
                state: friendNub ? friendNub.state : null,
            });
        }
        //------------------------------------------------------------------------------------------------
        // 하루에 한번만 가능.
        //------------------------------------------------------------------------------------------------
        let friendPointNub = userFriends.getFriendPoint(friendUserId);
        const passingCount = (0, formula_1.CalcContentsResetTimePassingCount)(now, friendPointNub.lastSendDate, 0);
        if (passingCount === 0) {
            throw new merror_1.MError('already-sent-to-friend', merror_1.MErrorCode.ALREADY_SENT_TO_FRIEND, {
                friendUserId,
                lastSendDate: friendPointNub.lastSendDate,
            });
        }
        friendPointNub = lodash_1.default.cloneDeep(friendPointNub);
        return _sendPointToFriend(user, userDbConnPoolMgr, friendPointNub, now);
    }
    FriendUtil.sendPoint = sendPoint;
    //=========================================================================================================
    // 모든 친구에게 포인트 주기.
    //=========================================================================================================
    function sendPointAll(user) {
        const { userDbConnPoolMgr } = Container_1.Container.get(server_1.LobbyService);
        const userFriends = user.userFriends;
        const now = (0, mutil_1.curTimeUtc)();
        //------------------------------------------------------------------------------------------------
        // 포인트 전송가능한 유저만 긁어 모은다.
        //------------------------------------------------------------------------------------------------
        const candidates = [];
        lodash_1.default.forOwn(userFriends.friends, (elem) => {
            // 친구관계이며, 오늘 전달하지 않는 유저만 모은다.
            if (elem.state === userFriends_1.FRIEND_STATE.ESTABLISHED) {
                const friendPointNub = userFriends.getFriendPoint(elem.friendUserId);
                if ((0, formula_1.CalcContentsResetTimePassingCount)(now, friendPointNub.lastSendDate, cms_1.default.ContentsResetHour.FriendAPReset.hour) > 0) {
                    candidates.push(friendPointNub);
                }
            }
        });
        return bluebird_1.Promise.reduce(candidates, (sync, friendPointNub) => {
            return _sendPointToFriend(user, userDbConnPoolMgr, friendPointNub, now)
                .then((_sync) => {
                lodash_1.default.merge(sync, _sync);
                return sync;
            })
                .catch((e) => {
                mlog_1.default.error('[friend-send-point-all] an-error-occured-while-sending-point', {
                    userId: user.userId,
                    error: e.message,
                    stack: e.stack,
                });
                return sync;
            });
        }, {}).then((sync) => {
            return sync;
        });
    }
    FriendUtil.sendPointAll = sendPointAll;
    //=========================================================================================================
    // 친구에게 포인트 주기.
    //=========================================================================================================
    function _sendPointToFriend(user, userDbConnPoolMgr, friendPointNub, curTimeUtc) {
        const friendUserId = friendPointNub.friendUserId;
        const friendUserShardId = (0, mysqlUtil_1.getUserDbShardId)(friendUserId);
        return (0, puFriendPointLoad_1.default)(userDbConnPoolMgr.getPoolByShardId(friendUserShardId), friendUserId, user.userId)
            .then((result) => {
            let friendPointNubForReceiver;
            if (!result) {
                friendPointNubForReceiver = {
                    friendUserId: user.userId,
                    lastSendDate: 1,
                    lastRecvDate: 1,
                    pickup: 0,
                    totalReceivedPts: 0,
                };
            }
            else {
                friendPointNubForReceiver = {
                    friendUserId: user.userId,
                    lastSendDate: parseInt(result.lastSendDate, 10),
                    lastRecvDate: parseInt(result.lastRecvDate, 10),
                    pickup: result.pickup,
                    totalReceivedPts: result.totalReceivedPts,
                };
            }
            //------------------------------------------------------------------------------------------------
            // 하루에 한번만 줄 수 있다.
            //------------------------------------------------------------------------------------------------
            const passingCount = (0, formula_1.CalcContentsResetTimePassingCount)(curTimeUtc, friendPointNubForReceiver.lastRecvDate, cms_1.default.ContentsResetHour.FriendAPReset.hour);
            if (passingCount === 0) {
                throw new merror_1.MError('already-received-point', merror_1.MErrorCode.ALREADY_RECEIVED_POINT, {
                    friendUserId,
                    sendDate: friendPointNub.lastSendDate,
                    recvDate: friendPointNubForReceiver.lastRecvDate,
                });
            }
            return friendPointNubForReceiver;
        })
            .then((friendPointNubForReceiver) => {
            const sendTimeForRollback = friendPointNub.lastSendDate;
            friendPointNub.lastSendDate = curTimeUtc;
            friendPointNubForReceiver.pickup = 0;
            friendPointNubForReceiver.lastRecvDate = curTimeUtc;
            return _sendPointIndividually(user, userDbConnPoolMgr, friendPointNub, friendPointNubForReceiver, sendTimeForRollback);
        });
    }
    //=========================================================================================================
    // 친구에게 포인트 주기.
    //=========================================================================================================
    function _sendPointIndividually(user, userDbConnPoolMgr, pointNubForSender, pointNubForReciever, rollbackDate) {
        // 모든 검사가 끝났으면,  전달유저 먼저 업데이트.
        return (0, puFriendPointUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, pointNubForSender.friendUserId, pointNubForSender.lastSendDate, pointNubForSender.lastRecvDate, pointNubForSender.pickup, pointNubForSender.totalReceivedPts)
            .then(() => {
            // 받는유저 업데이트.
            const otherUserShardId = (0, mysqlUtil_1.getUserDbShardId)(pointNubForSender.friendUserId);
            return (0, puFriendPointUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId(otherUserShardId), pointNubForSender.friendUserId, pointNubForReciever.friendUserId, pointNubForReciever.lastSendDate, pointNubForReciever.lastRecvDate, pointNubForReciever.pickup, pointNubForReciever.totalReceivedPts).catch((e1) => {
                // 롤백 처리...
                return (0, puFriendPointUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, pointNubForSender.friendUserId, rollbackDate, pointNubForSender.lastRecvDate, pointNubForSender.pickup, pointNubForSender.totalReceivedPts)
                    .catch((e2) => {
                    mlog_1.default.error('[friend-send-point] rollback-query-error', {
                        userId: user.userId,
                        friendUserId: pointNubForSender.friendUserId,
                        error: e2.message,
                        stack: e2.stack,
                    });
                })
                    .finally(() => {
                    throw e1;
                });
            });
        })
            .then(() => {
            notifyOnlineFriends(user.userId, user.userName, [pointNubForSender.friendUserId], lobby_1.FRIEND_NOTIFICATION_TYPE.RECV_POINT, (0, mutil_1.curTimeUtc)());
        })
            .then(() => {
            const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } = Container_1.Container.get(server_1.LobbyService);
            const worldConfg = mconf_1.default.getWorldConfig();
            return (0, userCacheRedisHelper_1.getUserLightInfos)([pointNubForSender.friendUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                const userLightInfo = userLightInfos[pointNubForSender.friendUserId];
                user.glog('friend_point', {
                    flag: 1,
                    target_nid: userLightInfo ? userLightInfo.pubId : null,
                    target_gameUserId: userLightInfo ? userLightInfo.userId : null,
                    cv: 0,
                    rv: user.userEnergy.rawEnergy,
                });
            });
        })
            .then(() => {
            return user.userFriends.updateFriendPoint(pointNubForSender);
        })
            .catch((e1) => {
            throw new merror_1.MError('[friend-send-point] failed-to-query-puFriendPointUpdate', merror_1.MErrorCode.FRIEND_REQUEST_QUERY_ERROR, {
                userId: user.userId,
                SenderNub: pointNubForSender,
                ReceiverNub: pointNubForReciever,
                error: e1.message,
                stack: e1.stack,
            });
        });
    }
})(FriendUtil = exports.FriendUtil || (exports.FriendUtil = {}));
//# sourceMappingURL=friendUtil.js.map