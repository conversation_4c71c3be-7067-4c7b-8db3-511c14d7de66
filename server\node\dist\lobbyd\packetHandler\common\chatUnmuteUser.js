"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_ChatUnmuteUser = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const merror_1 = require("../../../motiflib/merror");
const userConnection_1 = require("../../userConnection");
const userCacheRedisHelper_1 = require("../../../motiflib/userCacheRedisHelper");
const server_1 = require("../../server");
const Container_1 = require("typedi/Container");
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const rsn = 'friend_enemy';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_ChatUnmuteUser {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    async exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const reqBody = packet.bodyObj;
        const { targetUserId } = reqBody;
        if (targetUserId === undefined || !Number.isInteger(targetUserId)) {
            throw new merror_1.MError('targetUserId-integer-expected', merror_1.MErrorCode.INVALID_REQ_BODY_CHAT_UNMUTE_USER, { reqBody });
        }
        const volanteUserId = user.userId.toString();
        const volanteTargetUserId = targetUserId.toString();
        return Promise.resolve()
            .then(() => {
            // 에러 절차를 명확하게 하기 위해.
            // chatInit 이 된 상태인지와 볼란테에 접속 중인지를 알기 위함.
            // 무거운 조회는 아닐 거라 보고..
            return mhttp_1.default.platformChatApi.getSessions(volanteUserId);
        })
            .then(() => {
            return mhttp_1.default.platformChatApi.unmuteUser(volanteUserId, volanteTargetUserId);
        })
            .then(() => {
            const { userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr } = Container_1.Container.get(server_1.LobbyService);
            const worldConfg = mconf_1.default.getWorldConfig();
            return (0, userCacheRedisHelper_1.getUserLightInfos)([targetUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((userLightInfos) => {
                const userLightInfo = userLightInfos[targetUserId];
                if (userLightInfo) {
                    user.glog('friend_enemy', {
                        rsn,
                        add_rsn,
                        friend_nid: userLightInfo.pubId,
                        friend_gameUserId: targetUserId,
                        type: 0, // 차단:1 해제:0
                    });
                }
                return;
            });
        })
            .then(() => {
            return user.sendJsonPacket(packet.seqNum, packet.type, {});
        });
    }
}
exports.Cph_Common_ChatUnmuteUser = Cph_Common_ChatUnmuteUser;
//# sourceMappingURL=chatUnmuteUser.js.map