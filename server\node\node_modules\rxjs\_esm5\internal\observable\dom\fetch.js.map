{"version": 3, "file": "fetch.js", "sources": ["../../../../src/internal/observable/dom/fetch.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAoD9C,MAAM,UAAU,SAAS,CAAC,KAAuB,EAAE,IAAkB;IACnE,OAAO,IAAI,UAAU,CAAW,UAAA,UAAU;QACxC,IAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACjC,IAAI,kBAA8B,CAAC;QACnC,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,IAAI,IAAI,EAAE;YAER,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,kBAAkB,GAAG;oBACnB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;wBACnB,UAAU,CAAC,KAAK,EAAE,CAAC;qBACpB;gBACH,CAAC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;aAAM;YACL,IAAI,GAAG,EAAE,MAAM,QAAA,EAAE,CAAC;SACnB;QAED,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ;YAC9B,SAAS,GAAG,KAAK,CAAC;YAClB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1B,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;YACV,SAAS,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,YAAY,EAAE;gBAEjB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY,GAAG,IAAI,CAAC;YACpB,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}