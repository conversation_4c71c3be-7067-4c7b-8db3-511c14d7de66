cmd_Release/obj.target/ffi/deps/libffi/src/x86/unix64.o := cc -o Release/obj.target/ffi/deps/libffi/src/x86/unix64.o ../deps/libffi/src/x86/unix64.S '-DNODE_GYP_MODULE_NAME=ffi' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DPIC' '-DFFI_BUILDING' '-DHAVE_CONFIG_H' '-DNDEBUG' -I/root/.cache/node-gyp/16.20.2/include/node -I/root/.cache/node-gyp/16.20.2/src -I/root/.cache/node-gyp/16.20.2/deps/openssl/config -I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include -I/root/.cache/node-gyp/16.20.2/deps/uv/include -I/root/.cache/node-gyp/16.20.2/deps/zlib -I/root/.cache/node-gyp/16.20.2/deps/v8/include -I../deps/libffi/include -I../deps/libffi/config/linux/x64  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -m64 -O3 -fno-omit-frame-pointer  -MMD -MF ./Release/.deps/Release/obj.target/ffi/deps/libffi/src/x86/unix64.o.d.raw   -c
Release/obj.target/ffi/deps/libffi/src/x86/unix64.o: \
 ../deps/libffi/src/x86/unix64.S \
 ../deps/libffi/config/linux/x64/fficonfig.h \
 ../deps/libffi/config/linux/x64/ffi.h \
 ../deps/libffi/config/linux/x64/ffitarget.h \
 ../deps/libffi/src/x86/internal64.h ../deps/libffi/src/x86/asmnames.h
../deps/libffi/src/x86/unix64.S:
../deps/libffi/config/linux/x64/fficonfig.h:
../deps/libffi/config/linux/x64/ffi.h:
../deps/libffi/config/linux/x64/ffitarget.h:
../deps/libffi/src/x86/internal64.h:
../deps/libffi/src/x86/asmnames.h:
