{"version": 3, "file": "login.js", "sourceRoot": "", "sources": ["../../../../src/authd/api/public/login.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;;;;;;;;;;;;;;;;;;AAE/E,oDAA+B;AAC/B,oDAA4B;AAE5B,mDAAsD;AAEtD,oEAA4C;AAE5C,oEAAuD;AACvD,qDAA8D;AAC9D,kEAA0C;AAC1C,4DAAmF;AACnF,+DAAiD;AACjD,yDAA4D;AAC5D,qDAAuC;AACvC,4EAAmF;AACnF,iDAAoD;AACpD,wEAA6C;AAC7C,6EAA0E;AAC1E,oDAAuB;AACvB,uEAAoF;AA6CpF,SAAS,SAAS,CAAC,OAAe;IAChC,KAAK,MAAM,KAAK,IAAI,eAAK,CAAC,MAAM,EAAE;QAChC,IAAI,KAAK,CAAC,EAAE,IAAI,OAAO,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;KACF;AACH,CAAC;AAiJD,KAAK,UAAU,MAAM,CACnB,GAA0B,EAC1B,OAAoB,EACpB,SAAiB,EACjB,UAAkB,EAClB,IAAc,EACd,aAAqB,EACrB,aAAqB,EACrB,OAAe,EACf,YAAqB,EACrB,SAAkB;IAElB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAChE,MAAM,UAAU,GAAG,gBAAS,CAAC,GAAG,CAAC,iBAAU,CAAC,CAAC;IAE7C,MAAM,aAAa,GAAG,MAAM,IAAA,iBAAO,EACjC,UAAU,CAAC,OAAO,EAAE,EACpB,SAAS,EACT,UAAU,EACV,eAAK,CAAC,kBAAkB,EACxB,aAAa,EACb,aAAa,CACd,CAAC;IAEF,IAAI,aAAa,CAAC,eAAe,EAAE;QACjC,OAAO,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;KACpD;IAED,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,UAAU,CAAC;IAC1C,IAAI,gBAAqB,EAAE,YAAiB,CAAC;IAC7C,IAAI,aAAa,CAAC,QAAQ,KAAK,CAAC,EAAE;QAChC,gBAAgB,GAAG,aAAa,CAAC,SAAS,CAAC;QAC3C,KAAK,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,EAAE;YACxC,IAAI,KAAK,CAAC,OAAO,KAAK,aAAa,CAAC,WAAW,EAAE;gBAC/C,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC5B,MAAM;aACP;SACF;KACF;IAED,cAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;IAEvD,sBAAsB;IACtB,IAAI,OAAe,CAAC;IACpB,IAAI,YAAY,EAAE;QAChB,8BAA8B;QAC9B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC,CAAC;KACb;SAAM,IAAI,SAAS,EAAE;QACpB,4BAA4B;QAC5B,8CAA8C;QAC9C,MAAM,KAAK,GAAG,UAAU,GAAG,eAAK,CAAC,mBAAmB,CAAC;QAErD,MAAM,UAAU,GAAG,gBAAS,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,yBAAc,CAAC,CAAC;QACnE,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,mBAAmB,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnF,IAAI,CAAC,SAAS,EAAE;YACd,cAAI,CAAC,IAAI,CAAC,iDAAiD,EAAE;gBAC3D,SAAS;aACV,CAAC,CAAC;YACH,4BAA4B;YAC5B,OAAO,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC9C;aAAM;YACL,gBAAgB;YAChB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACnB,OAAO,GAAG,CAAC,CAAC;SACb;KACF;SAAM;QACL,OAAO,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KAC9C;IAED,MAAM,cAAc,GAAG,gBAAS,CAAC,GAAG,CAAC,yBAAc,CAAC,CAAC;IACrD,MAAM,cAAc,CAAC,oBAAoB,CAAC,CACxC,SAAS,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,aAAa,EACrB,OAAO,EACP,OAAO,CACR,CAAC;IAEF,IAAI,YAAY,EAAE;QAChB,cAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACtC,SAAS;YACT,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,gBAAgB;SAC5B,CAAC,CAAC;QAEH,yBAAyB;QACzB,MAAM,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,EAAE,mBAAW,CAAC,eAAe,CAAC,CAAC;KAChF;IAED,oBAAoB;IACpB,MAAM,cAAc,CAAC,qBAAqB,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAEnE,MAAM,YAAY,GAAG,UAAU,GAAG,eAAK,CAAC,+BAA+B,CAAC;IACxE,MAAM,oBAAoB,GAAG,MAAM,cAAc,CAAC,uBAAuB,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACpG,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAE5D,8CAA8C;IAC9C,eAAe;IACf,uCAAuC;IACvC,qCAAqC;IACrC,MAAM;IAEN,8BAA8B;IAC9B,IAAI,kBAAkB,CAAC,MAAM,IAAI,kBAAkB,CAAC,KAAK,EAAE;QACzD,cAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,SAAS;YACT,MAAM,EAAE,kBAAkB,CAAC,MAAM;YACjC,KAAK,EAAE,kBAAkB,CAAC,KAAK;SAChC,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,IAAI,CACf,kBAAkB,CAAC,MAAM,EACzB,kBAAkB,CAAC,KAAK,EACxB,mBAAW,CAAC,mBAAmB,CAChC,CAAC;KACH;IAED,OAAO;IACP,MAAM,EACJ,EAAE,EACF,GAAG,EACH,EAAE,EACF,UAAU,EACV,IAAI,EACJ,CAAC,EACD,EAAE,EACF,aAAa,EAAE,gBAAgB,EAC/B,UAAU,EACV,SAAS,EACT,mBAAmB,GACpB,GAAgB,OAAO,CAAC;IAEzB,IAAI,mBAAmB,EAAE;QACvB,gEAAgE;QAChE,IAAA,iBAAI,EAAC,kBAAkB,EAAE;YACvB,KAAK,EAAE,IAAA,gBAAM,EAAC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACvD,EAAE;YACF,GAAG;YACH,EAAE;YACF,IAAI,EAAE,UAAU;YAChB,CAAC;YACD,EAAE;SACH,CAAC,CAAC;KACJ;IAED,IAAI,SAAS,EAAE;QACb,IAAA,iBAAI,EAAC,sBAAsB,EAAE;YAC3B,KAAK,EAAE,IAAA,gBAAM,EAAC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACvD,EAAE;YACF,GAAG;YACH,EAAE;YACF,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,IAAI;YACf,CAAC;YACD,EAAE;YACF,QAAQ,EAAE,gBAAgB;YAC1B,UAAU;YACV,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;KACJ;IAED,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACnB,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,OAAe,EAAE,IAAc;IAC1D,UAAU;IACV,MAAM,YAAY,GAAG,gBAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,yBAAc,CAAC,CAAC;IACvE,MAAM,YAAY,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;IAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAE3C,IAAI,aAAa,GAAW,CAAC,CAAC;IAC9B,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QACjC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC/C;IAED,MAAM,qBAAqB,GAAG,CAAC,eAAK,CAAC,gBAAgB,GAAG,eAAK,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC;IAC/F,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,eAAK,CAAC,mBAAmB,CAAC;IAEzE,MAAM,UAAU,GAAG,gBAAS,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,yBAAc,CAAC,CAAC;IACnE,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAEtF,2BAA2B;IAC3B,aAAa,GAAG,aAAa,GAAG,iBAAiB,CAAC;IAElD,IAAI,IAAY,CAAC;IACjB,IAAI,aAAa,IAAI,eAAK,CAAC,gBAAgB,EAAE;QAC3C,IAAI,GAAG,CAAC,CAAC,CAAC,uBAAuB;KAClC;SAAM,IAAI,aAAa,GAAG,qBAAqB,EAAE;QAChD,oDAAoD;QACpD,oBAAoB;QACpB,IAAI,GAAG,CAAC,CAAC;KACV;SAAM;QACL,IAAI,GAAG,CAAC,CAAC,CAAC,oBAAoB;KAC/B;IAED,6CAA6C;IAC7C,sBAAsB;IACtB,yCAAyC;IACzC,+CAA+C;IAC/C,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,sCAAsC,CAAC,CACxE,OAAO,EACP,IAAI,EACJ,WAAW,EACX,eAAK,CAAC,gBAAgB,EACtB,aAAa,EACb,eAAK,CAAC,qBAAqB,CAC5B,CAAC;IAEF,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAEzE,mBAAmB;QACnB,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;YAChB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,cAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,SAAS,EAAE,IAAI,CAAC,IAAI;gBACpB,gBAAgB,EAAE,eAAK,CAAC,gBAAgB;gBACxC,aAAa;gBACb,qBAAqB;gBACrB,KAAK;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;gBAC7C,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC,OAAO,CAAC;KACvB;AACH,CAAC;AAED,SAAS,YAAY,CACnB,GAA0B,EAC1B,IAAc,EACd,aAA8B,EAC9B,MAAc;IAEd,IAAI,aAAa,EAAE;QACjB,IAAI,CAAC,0BAA0B,GAAG,2BAAoB,CAAC,sBAAsB,CAAC;QAC9E,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;KAC9D;IACD,IAAI,IAAI,CAAC,eAAe,EAAE;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,KAAK,CAAC,GAA0B,EAAE,IAAc,EAAE,SAAiB,GAAG;IAC7E,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAEzC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAhZD,iBAAS,KAAK,EAAE,GAA2B,EAAE,GAA0B,EAAE,EAAE;;IACzE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,GAAgB,GAAG,CAAC,IAAI,CAAC;IAEjG,4CAA4C;IAC5C,yFAAyF;IAEzF,MAAM,QAAQ,GACZ,eAAK,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAK,CAAC,QAAQ,CAAC;IAEtF,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,eAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAE3E,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IAEjC,MAAM,IAAI,GAAa;QACrB,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,SAAS;QACd,eAAe,EAAE,SAAS;QAC1B,SAAS,EAAE,KAAK;QAChB,aAAa,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO;QAC7B,SAAS,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI;QACtB,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,CAAC;QACZ,mBAAmB,EAAE,SAAS;KAC/B,CAAC;IAEF,MAAM,aAAa,GAAG,gBAAS,CAAC,GAAG,CAAC,uCAAkB,CAAC,CAAC;IACxD,IAAI,aAAa,CAAC,WAAW,EAAE,EAAE;QAC/B,IAAI,CAAC,OAAO,GAAG,mBAAU,CAAC,kCAAkC,CAAC;QAE7D,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;QACrC,cAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QAExD,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;QACxC,OAAO;KACR;IAED,oEAAoE;IACpE,IAAI,SAAiB,CAAC,CAAC,qCAAqC;IAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,IAAI,YAAY,GAAG,KAAK,CAAC;IAEzB,oBAAoB;IAEpB,IAAI;QACF,mCAAmC;QACnC,IAAI,QAAQ,KAAK,eAAQ,CAAC,IAAI,EAAE;YAC9B,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAkB,CAAC;YAE9E,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE1B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;YAEtC,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE;gBAC/B,2DAA2D;gBAC3D,OAAO,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;aACzB;YAED,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;gBAChC,iCAAiC;gBACjC,OAAO,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;aACzB;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAClD,IAAI,OAAO,KAAK,GAAG,EAAE;gBACnB,YAAY,GAAG,IAAI,CAAC;aACrB;YAED,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;YACzC,IAAI,WAAW,IAAI,gBAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnE,+EAA+E;gBAC/E,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC;gBACzE,IAAI,YAAY,EAAE;oBAChB,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,cAAc,CAAC;iBACxD;aACF;SACF;aAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,GAAG,EAAE;YACpC,6DAA6D;YAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAErC,IAAI,GAAG,GAAG,SAAS,CAAC;YACpB,IAAI,eAAe,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,SAAS,GAAG,eAAe,CAAC;gBAC5B,GAAG,GAAG,IAAA,yBAAU,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aACtC;iBAAM;gBACL,MAAM,aAAa,GAAG,MAAM,eAAK,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAqB,CAAC;gBAEtF,SAAS,GAAG,MAAM,CAAC,MAAA,aAAa,CAAC,IAAI,0CAAE,MAAM,CAAC,CAAC;gBAC/C,GAAG,GAAG,IAAA,yBAAU,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAErC,MAAM,eAAK,CAAC,KAAK,CAAC,UAAU,CAAC;oBAC3B,MAAM,EAAE,SAAS;oBACjB,WAAW,EAAE,EAAE;oBACf,MAAM,EAAE,CAAA,MAAA,eAAK,CAAC,GAAG,0CAAE,MAAM,KAAI,CAAC,CAAC;oBAC/B,OAAO,EAAE,CAAA,MAAA,eAAK,CAAC,GAAG,0CAAE,OAAO,KAAI,CAAC,CAAC;oBACjC,UAAU,EAAE,GAAG,CAAC,EAAE;oBAClB,YAAY,EAAE,CAAC;oBACf,WAAW,EAAE,GAAG;oBAChB,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;YACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB;aAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,GAAG,EAAE;YACpC,SAAS,GAAG,YAAY,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;YAEtB,MAAM,SAAS,GAAG,eAAe,CAAC;YAClC,MAAM,YAAY,GAAG,OAAO,CAAC;YAC7B,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBAC7D,MAAM,IAAI,eAAM,CAAC,YAAY,EAAE,mBAAU,CAAC,mBAAmB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;aAC1E;SACF;aAAM;YACL,MAAM,IAAI,eAAM,CAAC,kBAAkB,EAAE,mBAAU,CAAC,qBAAqB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;SAClF;QAED,MAAM,MAAM,CACV,GAAG,EACH,GAAG,CAAC,IAAI,EACR,SAAS,EACT,UAAU,EACV,IAAI,EACJ,aAAa,EACb,aAAa,EACb,OAAO,EACP,YAAY,EACZ,SAAS,CACV,CAAC;KACH;IAAC,OAAO,KAAU,EAAE;QACnB,cAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC1B,GAAG,EAAE,KAAK,CAAC,OAAO;SACnB,CAAC,CAAC;QAEH,IAAI,KAAK,YAAY,eAAM,EAAE;YAC3B,MAAM,IAAI,eAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;SAClE;QACD,MAAM,IAAI,eAAM,CAAC,KAAK,CAAC,OAAO,EAAE,mBAAU,CAAC,gBAAgB,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;KACtF;AACH,CAAC,CAAC"}