CREATE PROCEDURE `mp_u_user_consume_cash_cn`(
  IN inUserId INT,
  IN inPointCmsId INT,
  IN inAmount INT
)
label_body:BEGIN
    SET @paid = (SELECT IFNULL(value, 0) FROM u_points WHERE userId = inUserId AND cmsId = 100009);
    SET @free = (SELECT IFNULL(value, 0) FROM u_points WHERE userId = inUserId AND cmsId = 100004);

    SET @amount = inAmount;

    IF @paid + @free < @amount THEN
        SELECT
            -1 as result,
            @paid as paidRedGemBalance,
            @free as freeRedGemBalance;
    ELSE
        IF @paid >= @amount THEN
            UPDATE u_points SET value = value - @amount WHERE userId = inUserId AND cmsId = 100009;
        ELSE
            IF @paid > 0 THEN
                SET @amount = @amount - @paid;
                UPDATE u_points SET value = 0 WHERE userId = inUserId AND cmsId = 100009;
            END IF;

            UPDATE u_points SET value = value - @amount WHERE userId = inUserId AND cmsId = 100004;
        END IF;

        SET @paid=(SELECT value FROM u_points WHERE userId = inUserId AND cmsId = 100009);
        SET @free=(SELECT value FROM u_points WHERE userId = inUserId AND cmsId = 100004);

        SELECT
            0 as result,
            @paid as paidRedGemBalance,
            @free as freeRedGemBalance;
    END IF;

END
