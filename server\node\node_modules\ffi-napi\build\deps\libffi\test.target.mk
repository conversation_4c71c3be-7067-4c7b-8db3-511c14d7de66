# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := test
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=test' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DDEBUG' \
	'-D_DEBUG' \
	'-DV8_ENABLE_CHECKS'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-exceptions \
	-std=gnu++14

INCS_Debug := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include \
	-I$(srcdir)/deps/libffi/include \
	-I$(srcdir)/deps/libffi/config/linux/x64

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=test' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DNDEBUG'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-exceptions \
	-std=gnu++14

INCS_Release := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include \
	-I$(srcdir)/deps/libffi/include \
	-I$(srcdir)/deps/libffi/config/linux/x64

OBJS := \
	$(obj).target/$(TARGET)/deps/libffi/test.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# Make sure our dependencies are built before any of us.
$(OBJS): | $(builddir)/libffi.a $(obj).target/deps/libffi/libffi.a

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(builddir)/test: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/test: LIBS := $(LIBS)
$(builddir)/test: LD_INPUTS := $(OBJS) $(obj).target/deps/libffi/libffi.a
$(builddir)/test: TOOLSET := $(TOOLSET)
$(builddir)/test: $(OBJS) $(obj).target/deps/libffi/libffi.a FORCE_DO_CMD
	$(call do_cmd,link)

all_deps += $(builddir)/test
# Add target alias
.PHONY: test
test: $(builddir)/test

