"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_CashShopBuyWithoutPurchase = void 0;
const lodash_1 = __importDefault(require("lodash"));
const assert_1 = __importDefault(require("assert"));
const moment_1 = __importDefault(require("moment"));
const cms_1 = __importDefault(require("../../../cms"));
const cmsEx = __importStar(require("../../../cms/ex"));
const userConnection_1 = require("../../userConnection");
const userCashShop_1 = require("../../userCashShop");
const merror_1 = require("../../../motiflib/merror");
const mutil = __importStar(require("../../../motiflib/mutil"));
const cashShopDesc_1 = require("../../../cms/cashShopDesc");
const rewardAndPaymentChangeSpec_1 = require("../../UserChangeTask/rewardAndPaymentChangeSpec");
const userChangeTask_1 = require("../../UserChangeTask/userChangeTask");
const userBuffs_1 = require("../../userBuffs");
const userChangeOperator_1 = require("../../UserChangeTask/userChangeOperator");
const formula_1 = require("../../../formula");
const cashShopBoxRatioDesc_1 = require("../../../cms/cashShopBoxRatioDesc");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const rewardDesc_1 = require("../../../cms/rewardDesc");
const displayNameUtil = __importStar(require("../../../motiflib/displayNameUtil"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const itemDesc_1 = require("../../../cms/itemDesc");
const questPassDesc_1 = require("../../../cms/questPassDesc");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------
const rsn = 'cash_shop_buy_without_purchase';
const add_rsn = null;
var GLOG_BUFF_BUY_TYPE;
(function (GLOG_BUFF_BUY_TYPE) {
    /** 일반 구매(기존에 관련 버프가 없던 상태) */
    GLOG_BUFF_BUY_TYPE[GLOG_BUFF_BUY_TYPE["ADD"] = 1] = "ADD";
    /** 동일 등급 기간 연장 */
    GLOG_BUFF_BUY_TYPE[GLOG_BUFF_BUY_TYPE["EXTEND_TERM"] = 2] = "EXTEND_TERM";
    /** 상위 등급 구매 */
    GLOG_BUFF_BUY_TYPE[GLOG_BUFF_BUY_TYPE["UPGRADE"] = 3] = "UPGRADE";
})(GLOG_BUFF_BUY_TYPE || (GLOG_BUFF_BUY_TYPE = {}));
// ----------------------------------------------------------------------------
class Cph_Common_CashShopBuyWithoutPurchase {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    async exec(user, packet) {
        const response = await Cph_Common_CashShopBuyWithoutPurchase.staticExec(user, packet.bodyObj);
        return user.sendJsonPacket(packet.seqNum, packet.type, response);
    }
    // --------------------------------------------------------------------------
    static staticExec(user, requestBody) {
        var _a, _b;
        const { cmsId, bPermitExchange, bUseGachaTicket } = requestBody;
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const cashShopCms = cms_1.default.CashShop[cmsId];
        if (!cashShopCms || cashShopCms.salePointType !== cashShopDesc_1.CASH_SHOP_SALE_POINT_TYPE.POINT) {
            throw new merror_1.MError('invalid-cms-id', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                cmsId,
            });
        }
        user.userContentsTerms.ensureContentsTerms(cashShopCms.contentsTerms, user);
        const buyingAmount = (() => {
            var _a;
            switch (cashShopCms.productType) {
                case cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.REWARD_FIXED:
                    const amount = (_a = requestBody.amount) !== null && _a !== void 0 ? _a : 1;
                    if (!Number.isInteger(amount)) {
                        throw new merror_1.MError('invalid-amount', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                            amount,
                        });
                    }
                    if (amount < 1 || amount > cms_1.default.Const.ShopPerPurchaseMaxVal.value) {
                        //* 최대치 Const값이 단순히 UI 프로그래스바에서 사용할 목적이라 999 로 되어 있는 데,
                        // 추후 성능 문제 확인 필요
                        throw new merror_1.MError('invalid-amount-range', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                            amount,
                            max: cms_1.default.Const.ProductVolumeMaxVal.value,
                        });
                    }
                    return amount;
                case cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX:
                    //* 가챠 박스가 기획에서 제거된 뒤에 glog 에 amt 키가 추가되었는데,
                    // 다시 사용하게 된다면 bMultiple 인 경우 어떻게 기록되어야 하는지 확인 필요.
                    return cashShopCms.singleBoxCashShopId ? cms_1.default.Const.CashShopMultipleAmount.value : 1;
                case cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.DAILY_SUBSCRIPTION:
                    // 2개 이상 구매 기능 추가 시 구매 요청 api 기능 추가 필요.
                    // 현재 구매 요청 api는 1개만 처리되는 api로 파악.
                    return 1;
                default:
                    return 1;
            }
        })();
        const curTimeUtc = mutil.curTimeUtc();
        const expiredRestrictedProducts = user.userCashShop.getExpiredRestrictedProducts(curTimeUtc);
        // 구매할 수 있는 상품인지 검사
        const curDate = new Date(curTimeUtc * 1000);
        const restrictedProducts = user.userCashShop.getRestrictedProducts();
        const unbuyableReason = user.userCashShop.isBuyableProduct(user, cmsId, curDate, expiredRestrictedProducts, buyingAmount);
        if (unbuyableReason !== userCashShop_1.UNBUYABLE_REASON.BUYABLE) {
            throw new merror_1.MError('unbuyable-product', merror_1.MErrorCode.UNBUYABLE_CASH_SHOP_PRODUCT, {
                unbuyableReason,
                curDate,
                restrictedProducts,
            });
        }
        // build restrictedProductChange
        let restrictedProductChange;
        if (cashShopCms.saleType !== cashShopDesc_1.CASH_SHOP_SALE_TYPE.UNLIMITED) {
            if (expiredRestrictedProducts.has(cmsId) || !restrictedProducts[cmsId]) {
                restrictedProductChange = {
                    cmsId,
                    amount: buyingAmount,
                    lastBuyingTimeUtc: curTimeUtc,
                };
            }
            else {
                restrictedProductChange = {
                    cmsId,
                    amount: restrictedProducts[cmsId].amount + buyingAmount,
                    lastBuyingTimeUtc: curTimeUtc,
                };
            }
        }
        else if (cmsEx.isCashShopPreviousId(cmsId) && !restrictedProducts[cmsId]) {
            restrictedProductChange = {
                cmsId,
                amount: buyingAmount,
                lastBuyingTimeUtc: curTimeUtc,
            };
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX &&
            cashShopCms.singleBoxCashShopId) {
            if (mconf_1.default.binaryCode !== 'GL') {
                throw new merror_1.MError('cannot-gacha', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                    binaryCode: mconf_1.default.binaryCode,
                    requestBody,
                });
            }
            const singleBoxCashShopCms = cms_1.default.CashShop[cashShopCms.singleBoxCashShopId];
            if (singleBoxCashShopCms && singleBoxCashShopCms.saleType !== cashShopDesc_1.CASH_SHOP_SALE_TYPE.UNLIMITED) {
                const boxAmount = cms_1.default.Const.CashShopMultipleAmount.value - 1;
                // 10연차는 1연차의 saleType을 기준으로 해서 1연차 상품으로 다시 체크
                if (user.userCashShop.isSoldOut(singleBoxCashShopCms, expiredRestrictedProducts, boxAmount)) {
                    throw new merror_1.MError('unbuyable-product', merror_1.MErrorCode.UNBUYABLE_CASH_SHOP_PRODUCT, {
                        unbuyableReason: userCashShop_1.UNBUYABLE_REASON.SOLD_OUT,
                        curDate,
                        restrictedProducts,
                    });
                }
                const restrictedProductAmount = expiredRestrictedProducts.has(singleBoxCashShopCms.id)
                    ? 0
                    : (_b = (_a = restrictedProducts[singleBoxCashShopCms.id]) === null || _a === void 0 ? void 0 : _a.amount) !== null && _b !== void 0 ? _b : 0;
                restrictedProductChange = {
                    cmsId: singleBoxCashShopCms.id,
                    amount: restrictedProductAmount + boxAmount,
                    lastBuyingTimeUtc: curTimeUtc,
                };
            }
        }
        let fixedTermProductCmsIdToDelete;
        let newFixedTermProduct;
        // for cacha box
        const pickedCashShopBoxRatioCmsIds = [];
        let newGuaranteeAccum;
        let oldGuaranteeAccum;
        // 공간이 꽉차 메일로 간 것들
        const mailIdsForOverflow = [];
        // for glog
        const pr_data = [];
        const cost_data = [];
        const box_data = [];
        let buff_buy_type = undefined;
        let changeTask;
        const gains = [];
        if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.REWARD_FIXED) {
            const cost = cashShopCms.salePointVal * buyingAmount;
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_REWARD_FIXED, new CashShopBuyWithoutPurchaseRewardFixedSpec(cashShopCms.salePointId, cost, bPermitExchange, (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cost), expiredRestrictedProducts, restrictedProductChange, cashShopCms.productRewardFixedId, buyingAmount, curTimeUtc, mailIdsForOverflow, cashShopCms.id));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cost,
            });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.BUFF) {
            let cost = cashShopCms.salePointVal;
            const oldBuffProduct = user.userCashShop.getFixedTermProductByGroup(cashShopCms.buffGroup, curTimeUtc);
            if (oldBuffProduct) {
                const oldBuffProductCms = cms_1.default.CashShop[oldBuffProduct.cmsId];
                if (oldBuffProductCms.buffGroupLevel > cashShopCms.buffGroupLevel) {
                    // 같은 그룹일 경우 이전 레벨은 구매 불가
                    throw new merror_1.MError('can-not-buy-lower-level-buff-product', merror_1.MErrorCode.CANT_BUY_LOWER_LEVEL_BUFF_PRODUCT, {
                        oldBuffProduct,
                        cmsId,
                    });
                }
                else if (oldBuffProductCms.buffGroupLevel === cashShopCms.buffGroupLevel) {
                    // 같은 그룹에 같은 레벨 구매 시 버프 시간을 더해 줌
                    newFixedTermProduct = {
                        cmsId,
                        startTimeUtc: oldBuffProduct.startTimeUtc,
                        endTimeUtc: oldBuffProduct.endTimeUtc,
                    };
                    if ((0, cashShopDesc_1.isExistWorldBuffTimeField)(cashShopCms) == false) {
                        throw new merror_1.MError('invalid-duration-days-and-duration-hours', merror_1.MErrorCode.INVALID_DURATION_DAYS_AND_DURATION_HOURS, {
                            cmsId,
                        });
                    }
                    newFixedTermProduct.endTimeUtc += (0, cashShopDesc_1.getWorldBuffAddTime)(cashShopCms);
                    buff_buy_type = GLOG_BUFF_BUY_TYPE.EXTEND_TERM;
                }
                else {
                    // 같은 그룹에 더 높은 레벨 구매 시 이전에 남아있던 버프는 지워주고 새로운 버프 추가
                    // 단 시작, 종료 시간은 기존 버프의 값을 그대로 이용한다.
                    fixedTermProductCmsIdToDelete = oldBuffProduct.cmsId;
                    newFixedTermProduct = {
                        cmsId,
                        startTimeUtc: oldBuffProduct.startTimeUtc,
                        endTimeUtc: oldBuffProduct.endTimeUtc,
                    };
                    const remainingMin = Math.floor((oldBuffProduct.endTimeUtc - curTimeUtc) / formula_1.SECONDS_PER_MINUTE);
                    cost = (0, formula_1.CalcCashShopBuffUpgradeCostPerMinute)(cashShopCms, oldBuffProductCms, remainingMin);
                    buff_buy_type = GLOG_BUFF_BUY_TYPE.UPGRADE;
                }
            }
            else {
                // 기존에 같은 그룹의 버프 상품이 없을 경우
                newFixedTermProduct = {
                    cmsId,
                    startTimeUtc: curTimeUtc,
                    endTimeUtc: curTimeUtc,
                };
                if ((0, cashShopDesc_1.isExistWorldBuffTimeField)(cashShopCms) == false) {
                    throw new merror_1.MError('invalid-duration-or-expireAt-days-and-duration-hours', merror_1.MErrorCode.INVALID_DURATION_DAYS_AND_DURATION_HOURS, {
                        cmsId,
                    });
                }
                newFixedTermProduct.endTimeUtc += (0, cashShopDesc_1.getWorldBuffAddTime)(cashShopCms);
                buff_buy_type = GLOG_BUFF_BUY_TYPE.ADD;
            }
            const expiredFixedTermProducts = user.userCashShop.getExpiredFixedTermProducts(curTimeUtc);
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF, new CashShopBuyWithoutPurchaseBuffSpec(cashShopCms.salePointId, cost, bPermitExchange, (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cost), cashShopCms.id, expiredRestrictedProducts, restrictedProductChange, curTimeUtc, expiredFixedTermProducts, fixedTermProductCmsIdToDelete, newFixedTermProduct));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cost,
            });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.TAX_FREE_PERMIT) {
            throw new merror_1.MError('invalid-product-type', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                cmsId,
            });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
            if (mconf_1.default.binaryCode !== 'GL') {
                throw new merror_1.MError('cannot-gacha', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                    binaryCode: mconf_1.default.binaryCode,
                    requestBody,
                });
            }
            // 가챠는 더이상 레드젬으로 구매 불가능
            if (!bUseGachaTicket) {
                throw new merror_1.MError('gacha-only-use-ticket', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                    requestBody,
                });
            }
            const gachaTicketToUses = {};
            if (bUseGachaTicket) {
                let remainCount = cashShopCms.cashShopBoxTicketVal;
                for (const itemId of cashShopCms.cashShopBoxTicketId.reverse()) {
                    const itemCount = user.userInven.itemInven.getCount(itemId);
                    const useCount = itemCount >= remainCount ? remainCount : itemCount;
                    gachaTicketToUses[itemId] = useCount;
                    remainCount -= useCount;
                }
                if (remainCount > 0) {
                    throw new merror_1.MError('not-enough-gacha-ticket', merror_1.MErrorCode.NOT_ENOUGH_GACHA_TICKET, {
                        gachaTicketToUses,
                        remainCount: remainCount,
                    });
                }
            }
            const singleBoxCashShopId = cms_1.default.CashShop[cmsId].singleBoxCashShopId;
            oldGuaranteeAccum = user.userCashShop.getGachaBoxGuaranteeAccum(singleBoxCashShopId ? singleBoxCashShopId : cmsId);
            if (oldGuaranteeAccum === undefined) {
                oldGuaranteeAccum = 0;
            }
            newGuaranteeAccum = oldGuaranteeAccum;
            let boxAmount = 1;
            const cashShopBoxRatioCmses = [];
            if (singleBoxCashShopId) {
                boxAmount = cms_1.default.Const.CashShopMultipleAmount.value - 1;
                const picked = (0, cashShopBoxRatioDesc_1.pickGachaBox)(cashShopCms.bonusboxGroup);
                const cashShopBoxRatioCms = cms_1.default.CashShopBoxRatio[picked];
                pickedCashShopBoxRatioCmsIds.push(picked);
                cashShopBoxRatioCmses.push({
                    cashShopBoxRatioCms: cashShopBoxRatioCms,
                    isBonusGachaRewardForGlog: true,
                });
            }
            for (let i = 0; i < boxAmount; i++) {
                const picked = (0, cashShopBoxRatioDesc_1.pickGachaBox)(cashShopCms.boxGroup);
                const cashShopBoxRatioCms = cms_1.default.CashShopBoxRatio[picked];
                pickedCashShopBoxRatioCmsIds.push(picked);
                if (cashShopCms.ceilingCashShopBoxRatioGroup) {
                    newGuaranteeAccum++;
                }
                cashShopBoxRatioCmses.push({
                    cashShopBoxRatioCms: cashShopBoxRatioCms,
                    isBonusGachaRewardForGlog: false,
                });
            }
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_GACHA_BOX, new CashShopBuyWithoutPurchaseGachaBoxSpec(cashShopCms.salePointId, cashShopCms.salePointVal, false, // 상자(가챠)는 환전 불가능
            (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cashShopCms.salePointVal), expiredRestrictedProducts, restrictedProductChange, cashShopCms.id, curTimeUtc, cashShopBoxRatioCmses, oldGuaranteeAccum === newGuaranteeAccum ? undefined : newGuaranteeAccum, gains, box_data, mailIdsForOverflow, bUseGachaTicket, gachaTicketToUses));
            if (!bUseGachaTicket) {
                pr_data.push({
                    type: cashShopCms.salePointId,
                    amt: cashShopCms.salePointVal,
                });
            }
            else {
                lodash_1.default.forOwn(gachaTicketToUses, (count, itemId) => {
                    const itemCms = cms_1.default.Item[itemId];
                    cost_data.push({
                        type: itemDesc_1.ITEM_TYPE[itemCms.type],
                        id: itemCms.id,
                        amt: count,
                    });
                });
            }
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.QUEST_PASS) {
            const oldProduct = user.userCashShop.getFixedTermProduct(cmsId, curTimeUtc);
            if (oldProduct) {
                throw new merror_1.MError('already-bought', merror_1.MErrorCode.ALREADY_BOUGHT_QUEST_PASS, {
                    oldProduct,
                    cmsId,
                });
            }
            // questPassCms.achievementId 업적들 모두 보상을 받았을 경우 구매 불가.
            const questPassCms = cms_1.default.QuestPass[cashShopCms.questPassId];
            if (questPassCms.achievementId && questPassCms.achievementId.length > 0) {
                const notRewarded = questPassCms.achievementId.findIndex((elem) => {
                    return !user.userAchievement.isRewarded(elem);
                });
                if (notRewarded === -1) {
                    throw new merror_1.MError('quest-pass-achievements-is-rewarded', merror_1.MErrorCode.QUEST_PASS_ACHIEVEMENTS_IS_REWARDED, {
                        cmsId,
                        achievements: user.userAchievement.getAchievements(),
                    });
                }
            }
            let cost = cashShopCms.salePointVal;
            // '제독+회고록' 같은 경우 회고록 비용만큼만 마일리지 적립이 되어야한다고함.
            const mileageBonus = (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cost);
            let mateCmsId;
            // 인연 연대기는 항해사 지급에서 제외.
            if (!user.userMates.getMate(questPassCms.mateId)) {
                if (questPassCms.type === questPassDesc_1.QuestPassType.RelationShip) {
                    throw new merror_1.MError('invalid-quest-pass-mate', merror_1.MErrorCode.INVALID_QUEST_PASS_MATE, {
                        cmsId,
                        mateId: questPassCms.mateId,
                    });
                }
                else if (questPassCms.type === questPassDesc_1.QuestPassType.Admiral) {
                    mateCmsId = questPassCms.mateId;
                    // 항해사도 같이 구매
                    const admiralCms = cmsEx.getAdmiralByMateCmsId(mateCmsId);
                    // 제독 구매 재화가 레드젬이 아닌 경우 제독+회고록 구매 불가능
                    if (admiralCms.recruitingPoint === cmsEx.RedGemPointCmsId) {
                        user.userContentsTerms.ensureContentsTerms(admiralCms.contentsTerms, user);
                        // CashShop.productType 이 5인 경우 해당되는 제독의 Admiral.recruitingPoint 이 레드젬일 경우 회고록을 구매하면서
                        // 제독도 같이 구매되고 제독 구매로 인해 추가되는 비용은 Admiral.recruitingPointValue 을 사용한다.
                        cost = (0, formula_1.CalcCashShopQuestPassWithMateCost)(cost, admiralCms.recruitingPointValue);
                    }
                }
            }
            const expiredFixedTermProducts = user.userCashShop.getExpiredFixedTermProducts(curTimeUtc);
            newFixedTermProduct = {
                cmsId,
                startTimeUtc: curTimeUtc,
                endTimeUtc: null, // 무기한
            };
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF, new CashShopBuyWithoutPurchaseQuestPassSpec(cashShopCms.salePointId, cost, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, cashShopCms.id, curTimeUtc, questPassCms.researchPoint, expiredFixedTermProducts, newFixedTermProduct, mateCmsId));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cost,
            });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.SOUND) {
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF, new CashShopBuyWithoutPurchaseSoundPackSpec(cashShopCms.salePointId, cashShopCms.salePointVal, bPermitExchange, (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cashShopCms.salePointVal), expiredRestrictedProducts, restrictedProductChange, cashShopCms.id, curTimeUtc, cashShopCms.soundPackId));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cashShopCms.salePointVal,
            });
            // } else if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.ENCOUNT_SHIELD) {
            //   changeTask = new UserChangeTask(
            //     user,
            //     CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_ENCOUNT_SHIELD,
            //     new CashShopBuyWithoutPurchaseEncountShieldSpec(
            //       cashShopCms.salePointId,
            //       cashShopCms.salePointVal,
            //       bPermitExchange,
            //       getCashShopMileageBonus(cashShopCms, cashShopCms.salePointVal),
            //       expiredRestrictedProducts,
            //       restrictedProductChange,
            //       cashShopCms.id,
            //       cashShopCms.countProductVal
            //     )
            //   );
            //   pr_data.push({
            //     type: cashShopCms.salePointId,
            //     amt: cashShopCms.salePointVal,
            //   });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.DAILY_SUBSCRIPTION) {
            const dsCms = cms_1.default.DailySubscription[cashShopCms.dailySubscriptionId];
            if (!dsCms) {
                throw new merror_1.MError('invalid-daily-subscription-cms', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                    cashShopCmsId: cashShopCms.id,
                    dsCmsId: cashShopCms.dailySubscriptionId,
                });
            }
            //
            const cost = cashShopCms.salePointVal * buyingAmount;
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_PURCHASE_DAILY_SUBSCRIPTION, new CashShopBuyWithoutPurchaseDailySubscriptionSpec(cashShopCms.salePointId, cost, bPermitExchange, (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cost), expiredRestrictedProducts, restrictedProductChange, dsCms, buyingAmount, curTimeUtc, mailIdsForOverflow, cashShopCms.id));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cost,
            });
        }
        else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
            const cost = cashShopCms.salePointVal * buyingAmount;
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CASH_SHOP_BUY_WITHOUT_HOT_SPOT, new CashShopBuyWithoutPurchaseHotSpotSpec(cashShopCms.salePointId, cost, bPermitExchange, (0, cashShopDesc_1.getCashShopMileageBonus)(cashShopCms, cost), expiredRestrictedProducts, restrictedProductChange, cashShopCms.productRewardFixedId, buyingAmount, curTimeUtc, mailIdsForOverflow, cashShopCms.id));
            pr_data.push({
                type: cashShopCms.salePointId,
                amt: cost,
            });
        }
        else {
            throw new merror_1.MError('invalid-product-type', merror_1.MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                cmsId,
            });
        }
        if (newFixedTermProduct) {
            if (newFixedTermProduct.endTimeUtc &&
                newFixedTermProduct.endTimeUtc - curTimeUtc >=
                    cms_1.default.Const.CashShopDurationDaysLimit.value * formula_1.SECONDS_PER_DAY) {
                throw new merror_1.MError('exceeds-expiration-time', merror_1.MErrorCode.EXCEEDS_EXPIRATION_TIME_OF_CASH_SHOP_PRODUCT, {
                    cmsId,
                    newExpirationTimeUtc: newFixedTermProduct.endTimeUtc,
                    curTimeUtc,
                });
            }
        }
        const res = changeTask.trySpec();
        // 캐시샵에서는 엄격하게 OK 아닌 경우 에러
        if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            throw new merror_1.MError('failed-to-receive', merror_1.MErrorCode.FAILED_TO_CASH_SHOP_BUY_WITHOUT_PURCHASE, {
                res,
                cmsId,
            });
        }
        const oldPaidRedGemForGlog = user.userPoints.paidRedGem;
        const oldFreeRedGemForGlog = user.userPoints.freeRedGem;
        return changeTask
            .apply()
            .then((sync) => {
            var _a;
            if (cashShopCms.productType !== cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
                gains.push(changeTask.getActualGain());
            }
            // accumulate achievement
            const accums = [];
            let accumCashShopBuyCountTargetId = cashShopCms.id;
            let accumBuyingAmount = buyingAmount;
            if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
                accums.push({
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.OPEN_CASH_SHOP_GACHA_BOX,
                    addedValue: cashShopCms.singleBoxCashShopId
                        ? cms_1.default.Const.CashShopMultipleAmount.value
                        : 1, // (10+1)회 가챠는 11회로 적용
                });
                accumCashShopBuyCountTargetId = (_a = cashShopCms.singleBoxCashShopId) !== null && _a !== void 0 ? _a : cashShopCms.id;
                if (cashShopCms.singleBoxCashShopId) {
                    const singleBoxCashShopCms = cms_1.default.CashShop[cashShopCms.singleBoxCashShopId];
                    accumBuyingAmount =
                        cashShopCms.cashShopBoxTicketVal / singleBoxCashShopCms.cashShopBoxTicketVal;
                }
            }
            accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.CASH_SHOP_BUY_COUNT,
                addedValue: accumBuyingAmount,
                targets: [accumCashShopBuyCountTargetId],
            });
            return user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
        })
            .then((sync) => {
            // glog
            let remain_cnt = null;
            if (restrictedProductChange) {
                remain_cnt = cashShopCms.saleTypeVal - restrictedProductChange.amount;
            }
            const reward_data = [];
            for (const gain of gains) {
                reward_data.push(...userChangeTask_1.UserChangeTask.covertActualGainToGLogRewardData(gain));
            }
            // 재화 사용은 제거
            for (const pr of pr_data) {
                let remainingMinus = pr.amt;
                for (const reward of reward_data) {
                    if (remainingMinus === 0) {
                        break;
                    }
                    if (pr.type !== reward.id) {
                        continue;
                    }
                    if (reward.amt >= 0) {
                        continue;
                    }
                    const t = Math.min(remainingMinus, -reward.amt);
                    reward.amt += t;
                    remainingMinus -= t;
                }
            }
            for (let i = reward_data.length - 1; i >= 0; i--) {
                if (reward_data[i].amt === 0) {
                    reward_data.splice(i, 1);
                }
            }
            user.glog('cash_shop_buy', {
                rsn,
                add_rsn,
                category: cashShopCms.productCategory,
                id: cmsId,
                name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
                type: cashShopCms.productType,
                limit_type: cashShopCms.saleType,
                limit_period: cashShopCms.saleTypeVal ? cashShopCms.saleTypeVal : null,
                remain_cnt,
                amt: buyingAmount,
                expiredate: newFixedTermProduct && newFixedTermProduct.endTimeUtc
                    ? (0, moment_1.default)(new Date(newFixedTermProduct.endTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss')
                    : null,
                buff_buy_type: buff_buy_type !== undefined ? GLOG_BUFF_BUY_TYPE[buff_buy_type] : null,
                pr_data,
                reward_data: reward_data.length > 0 ? reward_data : null,
                exchange_hash: changeTask.getExchangeHash(),
            });
            if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.GACHA_BOX) {
                user.glog('summon_data', {
                    rsn,
                    add_rsn,
                    exchange_hash: changeTask.getExchangeHash(),
                    summon_id: cashShopCms.boxGroup,
                    box_data,
                    is_sequence: cashShopCms.singleBoxCashShopId ? 2 : 1,
                    cv: cashShopCms.ceilingCashShopBoxRatioGroup
                        ? newGuaranteeAccum - oldGuaranteeAccum
                        : null,
                    rv: cashShopCms.ceilingCashShopBoxRatioGroup ? newGuaranteeAccum : null,
                    pr_data,
                    cost_data,
                });
            }
            else if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
                user.glog('hot_spot_product_start', {
                    rsn,
                    add_rsn,
                    exchange_hash: changeTask.getExchangeHash(),
                    id: cmsId,
                    name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
                    limit_type: cashShopCms.saleType,
                    remain_cnt: cashShopCms.saleType === cashShopDesc_1.CASH_SHOP_SALE_TYPE.UNLIMITED
                        ? 999
                        : remain_cnt !== null && remain_cnt !== void 0 ? remain_cnt : cashShopCms.saleTypeVal,
                    amt: buyingAmount,
                    pr_data,
                    reward_data,
                    is_exposure: false,
                });
            }
            // buff 처리
            const resp = {
                sync,
                gains,
                pickedCashShopBoxRatioCmsIds,
                mailIdsForOverflow: mailIdsForOverflow.length > 0 ? mailIdsForOverflow : undefined,
            };
            if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.BUFF) {
                handleBuff(user, fixedTermProductCmsIdToDelete, cashShopCms, cmsId, newFixedTermProduct, resp);
            }
            // [SDO]
            if (mconf_1.default.isSDO) {
                return mhttp_1.default.platformBillingApi.queryCashPair(user.userId, user.storeCode, user.countryCreated)
                    .then((ret) => {
                    user.userPoints.onChargeByPurchaseProduct([
                        {
                            coinCd: 'red_gem',
                            paymentType: 'PAID',
                            balance: ret.paidRedGemBalance,
                        },
                        {
                            coinCd: 'red_gem',
                            paymentType: 'FREE',
                            balance: ret.freeRedGemBalance,
                        },
                    ], null);
                    return resp;
                })
                    .catch((err) => {
                    mlog_1.default.error(err);
                    return resp;
                });
            }
        });
    }
}
exports.Cph_Common_CashShopBuyWithoutPurchase = Cph_Common_CashShopBuyWithoutPurchase;
function handleBuff(user, fixedTermProductCmsIdToDelete, cashShopCms, cmsId, newFixedTermProduct, buffSync) {
    const userBuffs = user.userBuffs;
    // 캐쉬샵에 버프는 무조건 1번 함대에 적용한다고 함.
    const targetId = cmsEx.FirstFleetIndex;
    if (fixedTermProductCmsIdToDelete) {
        // 기존 버프 지워주어야 됨.
        const deleteBuffCms = cms_1.default.CashShop[fixedTermProductCmsIdToDelete];
        for (const buffId of deleteBuffCms.productWorldBuffId) {
            const newBuffIdx = cashShopCms.productWorldBuffId.findIndex((newBuffCmsId) => {
                const newBuffCms = cms_1.default.WorldBuff[newBuffCmsId];
                const buffCmsToDelete = cms_1.default.WorldBuff[buffId];
                return newBuffCms.groupNo === buffCmsToDelete.groupNo;
            });
            if (newBuffIdx !== -1) {
                continue;
            }
            userBuffs.removeBuff(buffId, targetId, user, rsn, add_rsn, buffSync);
        }
    }
    for (const buffId of cashShopCms.productWorldBuffId) {
        const worldBuffCms = cms_1.default.WorldBuff[buffId];
        const wbNub = userBuffs_1.WorldBuffUtil.makeNubWithCustomEndTime(worldBuffCms, targetId, cmsEx.WorldBuffSourceType.CASH_SHOP_BUY_WITHOUT_PURCHASE, cmsId, newFixedTermProduct.startTimeUtc, newFixedTermProduct.endTimeUtc);
        user.userBuffs.addSingleBuffByWBNub(wbNub, user, buffSync);
    }
}
// 해당 클래스에는 캐시샵에서 필수적으로 사용해야하는 로직이 있다.
// 하지만 CashShopBuyWithoutPurchaseRewardFixedSpec에서 RewardAndPaymentSpec의 일부 맴버함수 사용이 필요해서
// 이 클래스를 상속받지 않고 RewardAndPaymentSpec를 상속받기 때문에
// 각 클래스에서 캐시샵의 필요한 로직을 함수를 이용하여 사용하는 것 참고.
class CashShopBuyWithoutPurchaseSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc) {
        this.costPointCmsId = costPointCmsId;
        this.costPointValue = costPointValue;
        this.bPermitExchange = bPermitExchange;
        this.mileageBonus = mileageBonus;
        this.cashShopCmsId = cashShopCmsId;
        this.expiredRestrictedProducts = expiredRestrictedProducts;
        this.restrictedProductChange = restrictedProductChange;
        this.curTimeUtc = curTimeUtc;
        //
    }
    accumulate(user, tryData, changes) {
        return CashShopOperatorUtil.executeCashShopBuyWithoutPurchaseOps(user, tryData, changes, this.costPointCmsId, this.costPointValue, this.bPermitExchange, this.mileageBonus, this.expiredRestrictedProducts, this.restrictedProductChange, this.cashShopCmsId, undefined, this.curTimeUtc);
    }
}
class CashShopBuyWithoutPurchaseRewardFixedSpec extends rewardAndPaymentChangeSpec_1.RewardAndPaymentSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, rewardFixedCmsId, rewardAmount, curTimeUtc, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow, cashShopCmsId) {
        super();
        this._costPointCmsId = costPointCmsId;
        this._costPointValue = costPointValue;
        this._bPermitExchange = bPermitExchange;
        this._mileageBonus = mileageBonus;
        this._expiredRestrictedProducts = expiredRestrictedProducts;
        this._restrictedProductChange = restrictedProductChange;
        this._rewardFixedCmsId = rewardFixedCmsId;
        this._rewardAmount = rewardAmount;
        this._curTimeUtc = curTimeUtc;
        this._mailIdsForOverflow = mailIdsForOverflow;
        this._cashShopCmsId = cashShopCmsId;
    }
    //* BattleRewardSpec 처럼 super.accumulate 는 사용 않고 _tryElem(맴버 함수)만 사용
    accumulate(user, tryData, changes) {
        //* super.accumulate 를 사용하지 않기 때문에
        //* 맴버 변수로 user, tryData, changes 설정해줘야 하는 것 주의(_tryElem에서 사용됨)
        this._user = user;
        this._tryData = tryData;
        this._changes = changes;
        let res = CashShopOperatorUtil.executeCashShopBuyWithoutPurchaseOps(user, tryData, changes, this._costPointCmsId, this._costPointValue, this._bPermitExchange, this._mileageBonus, this._expiredRestrictedProducts, this._restrictedProductChange, this._cashShopCmsId, this._rewardAmount, this._curTimeUtc);
        if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            return res;
        }
        const disallowExceedRnpElem = {
            type: rewardAndPaymentChangeSpec_1.RNP_TYPE.REWARD_FIXED,
            bAllowExceed: false,
            bIsBound: true,
            bIsAccum: true,
            shipFixedRandomStat: cms_1.default.Const.CashShipBlueprintRatio.value,
        };
        const defaultRnpElem = {
            type: rewardAndPaymentChangeSpec_1.RNP_TYPE.REWARD_FIXED,
            bAllowExceed: true,
            bIsBound: true,
            bIsAccum: true,
            shipFixedRandomStat: cms_1.default.Const.CashShipBlueprintRatio.value,
        };
        const getRnpElem = (rewardType) => {
            switch (rewardType) {
                case rewardDesc_1.REWARD_TYPE.MATE_EQUIP:
                case rewardDesc_1.REWARD_TYPE.ITEM:
                case rewardDesc_1.REWARD_TYPE.SHIP:
                case rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM:
                    return disallowExceedRnpElem;
                default:
                    return defaultRnpElem;
            }
        };
        const rewardFixedCms = cms_1.default.RewardFixed[this._rewardFixedCmsId];
        const receivedMailIds = [];
        const curTimeUtc = mutil.curTimeUtc();
        for (let i = 0; i < this._rewardAmount; i += 1) {
            const equipmentsForMail = [];
            const itemsForMail = [];
            const shipsForMail = [];
            for (const elem of rewardFixedCms.rewardFixed) {
                res = this._tryElem(elem, getRnpElem(elem.Type), curTimeUtc);
                if (res === userChangeTask_1.CHANGE_TASK_RESULT.MATE_EQUIP_FULL) {
                    equipmentsForMail.push(elem);
                }
                else if (res === userChangeTask_1.CHANGE_TASK_RESULT.INVEN_FULL) {
                    itemsForMail.push(elem);
                }
                else if (res === userChangeTask_1.CHANGE_TASK_RESULT.DOCK_FULL) {
                    shipsForMail.push(elem);
                }
                else if (res <= userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
                    // do nothing
                }
                else {
                    return res;
                }
            }
            if (equipmentsForMail.length > 0 || itemsForMail.length > 0 || shipsForMail.length > 0) {
                if (!tryData.mails) {
                    tryData.mails = user.userMails.clone();
                }
                const oldLastMailId = tryData.mails.getLastDirectMailId();
                if (equipmentsForMail.length > 0) {
                    res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopCEquipMailId.value, this._curTimeUtc, equipmentsForMail, true);
                    if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                        return res;
                    }
                }
                if (itemsForMail.length > 0) {
                    res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopCEquipMailId.value, this._curTimeUtc, itemsForMail, true);
                    if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                        return res;
                    }
                }
                if (shipsForMail.length > 0) {
                    res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopShipMailId.value, this._curTimeUtc, shipsForMail, true);
                    if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                        return res;
                    }
                }
                const curLastMailId = tryData.mails.getLastDirectMailId();
                for (let mailId = oldLastMailId + 1; mailId <= curLastMailId; mailId += 1) {
                    receivedMailIds.push(mailId);
                }
            }
        }
        // 외부 변수 적용
        this._mailIdsForOverflow.push(...receivedMailIds);
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
class CashShopBuyWithoutPurchaseBuffSpec extends CashShopBuyWithoutPurchaseSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc, expiredFixedTermProducts, fixedTermProductCmsIdToDelete, newFixedTermProduct) {
        super(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc);
        this.expiredFixedTermProducts = expiredFixedTermProducts;
        this.fixedTermProductCmsIdToDelete = fixedTermProductCmsIdToDelete;
        this.newFixedTermProduct = newFixedTermProduct;
    }
    accumulate(user, tryData, changes) {
        const ops = [super.accumulate(user, tryData, changes)];
        if (this.expiredFixedTermProducts) {
            for (const cmsId of this.expiredFixedTermProducts) {
                ops.push((0, userChangeOperator_1.opDeleteCashShopFixedTermProduct)(user, tryData, changes, cmsId));
            }
        }
        if (this.fixedTermProductCmsIdToDelete) {
            ops.push((0, userChangeOperator_1.opDeleteCashShopFixedTermProduct)(user, tryData, changes, this.fixedTermProductCmsIdToDelete));
        }
        if (this.newFixedTermProduct) {
            ops.push((0, userChangeOperator_1.opSetCashShopFixedTermProduct)(user, tryData, changes, this.newFixedTermProduct));
        }
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
class CashShopBuyWithoutPurchaseGachaBoxSpec extends CashShopBuyWithoutPurchaseSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, cashShopCmsId, curTimeUtc, cashShopBoxRatioCmses, guaranteeAccum, gains, glogBoxData, 
    // private curTimeUtc: number,
    mailIdsForOverflow, bUseGachaTicket, gachaTicketToUses) {
        super(bUseGachaTicket ? undefined : costPointCmsId, bUseGachaTicket ? undefined : costPointValue, bPermitExchange, bUseGachaTicket ? 0 : mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc);
        this.cashShopBoxRatioCmses = cashShopBoxRatioCmses;
        this.guaranteeAccum = guaranteeAccum;
        this.gains = gains;
        this.glogBoxData = glogBoxData;
        this.mailIdsForOverflow = mailIdsForOverflow;
        this.bUseGachaTicket = bUseGachaTicket;
        this.gachaTicketToUses = gachaTicketToUses;
    }
    accumulate(user, tryData, changes) {
        let res = super.accumulate(user, tryData, changes);
        if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            return res;
        }
        if (this.bUseGachaTicket) {
            lodash_1.default.forOwn(this.gachaTicketToUses, (count, itemId) => {
                res = (0, userChangeOperator_1.opAddItem)(user, tryData, changes, parseInt(itemId, 10), -count, false, false, undefined, true);
                if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                    return res;
                }
            });
        }
        if (this.guaranteeAccum !== undefined) {
            const singleBoxCashShopId = cms_1.default.CashShop[this.cashShopCmsId].singleBoxCashShopId;
            res = (0, userChangeOperator_1.opSetCashShopGachaBoxGuaranteeAccum)(user, tryData, changes, singleBoxCashShopId ? singleBoxCashShopId : this.cashShopCmsId, this.guaranteeAccum);
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        const getLastIdsForActualGain = () => {
            if (!tryData.fleets) {
                tryData.fleets = user.userFleets.clone();
            }
            if (!tryData.mates) {
                tryData.mates = user.userMates.clone();
            }
            if (!tryData.inven) {
                tryData.inven = user.userInven.clone();
            }
            return {
                lastShipId: tryData.fleets.getLastShipId(),
                lastMateEquipmentId: tryData.mates.getLastMateEquipmentId(),
                lastShipSlotItemId: tryData.inven.getLastShipSlotItemId(),
                lastQuestItemId: tryData.inven.itemInven.getLastQuestItemId(),
            };
        };
        // 받을 것들
        const totalContentsToReceive = [];
        // 공간이 꽉차서 메일로 갈 것들
        const totalContentsToMailForEquipment = [];
        const totalContentsToMailForItem = [];
        const totalContentsToMailForShip = [];
        for (const cashShopBoxRatioCms of this.cashShopBoxRatioCmses) {
            const isBonusGachaRewardForGlog = cashShopBoxRatioCms.isBonusGachaRewardForGlog;
            const contentsToReceive = [];
            const contentsToMailForEquipment = [];
            const contentsToMailForItem = [];
            const contentsToMailForShip = [];
            totalContentsToReceive.push(contentsToReceive);
            totalContentsToMailForEquipment.push(contentsToMailForEquipment);
            totalContentsToMailForItem.push(contentsToMailForItem);
            totalContentsToMailForShip.push(contentsToMailForShip);
            for (const contentElem of cashShopBoxRatioCms.cashShopBoxRatioCms.contents) {
                const oldLastIds = getLastIdsForActualGain();
                let bDuplicated = false;
                res = this.executeOp(user, tryData, changes, contentElem.RewardType, contentElem.Id, contentElem.Amount);
                if (res === userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                    const gain = this.getActualGain(contentElem.RewardType, contentElem.Id, contentElem.Amount, oldLastIds, tryData);
                    contentsToReceive.push({
                        contentElem,
                        bDuplicated,
                        gain,
                        isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
                    });
                }
                else if (res === userChangeTask_1.CHANGE_TASK_RESULT.NOTHING &&
                    contentElem.DuplicateRewardType !== undefined) {
                    bDuplicated = true;
                    res = this.executeOp(user, tryData, changes, contentElem.DuplicateRewardType, contentElem.DuplicateId, contentElem.DuplicateAmount);
                    if (res === userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                        const gain = this.getActualGain(contentElem.DuplicateRewardType, contentElem.DuplicateId, contentElem.DuplicateAmount, oldLastIds, tryData);
                        contentsToReceive.push({
                            contentElem,
                            bDuplicated,
                            gain,
                            isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
                        });
                    }
                }
                if (res === userChangeTask_1.CHANGE_TASK_RESULT.MATE_EQUIP_FULL) {
                    contentsToMailForEquipment.push({
                        contentElem,
                        bDuplicated,
                        isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
                    });
                }
                else if (res === userChangeTask_1.CHANGE_TASK_RESULT.INVEN_FULL) {
                    contentsToMailForItem.push({
                        contentElem,
                        bDuplicated,
                        isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
                    });
                }
                else if (res === userChangeTask_1.CHANGE_TASK_RESULT.DOCK_FULL) {
                    contentsToMailForShip.push({
                        contentElem,
                        bDuplicated,
                        isBonusGachaRewardForGlog: isBonusGachaRewardForGlog,
                    });
                }
                else if (res <= userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
                    // do nothing
                }
                else {
                    return res;
                }
            }
        }
        // 메일로 갈 것들 있으면 처리
        const spreadAndConvertToRewardFixedElem = (totalContents) => {
            const ret = [];
            for (const contents of totalContents) {
                for (const { contentElem, bDuplicated } of contents) {
                    ret.push(CashShopBuyWithoutPurchaseGachaBoxSpec.convertContentElemToRewardFixedElem(contentElem, bDuplicated));
                }
            }
            return ret;
        };
        const receivedMailIds = [];
        const equipmentsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForEquipment);
        const itemsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForItem);
        const shipsForMail = spreadAndConvertToRewardFixedElem(totalContentsToMailForShip);
        if (equipmentsForMail.length > 0 || itemsForMail.length > 0 || shipsForMail.length > 0) {
            if (!tryData.mails) {
                tryData.mails = user.userMails.clone();
            }
            const oldLastMailId = tryData.mails.getLastDirectMailId();
            if (equipmentsForMail.length > 0) {
                res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopCEquipMailId.value, this.curTimeUtc, equipmentsForMail, true);
                if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                    return res;
                }
            }
            if (itemsForMail.length > 0) {
                res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopCEquipMailId.value, this.curTimeUtc, itemsForMail, true);
                if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                    return res;
                }
            }
            if (shipsForMail.length > 0) {
                res = CashShopOperatorUtil.executeAddDirectMailOp(user, tryData, changes, cms_1.default.Const.CashShopShipMailId.value, this.curTimeUtc, shipsForMail, true);
                if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                    return res;
                }
            }
            const curLastMailId = tryData.mails.getLastDirectMailId();
            for (let i = oldLastMailId + 1; i <= curLastMailId; i += 1) {
                receivedMailIds.push(i);
            }
        }
        // 외부 변수에 적용된 것들을 넣어준다.
        for (const contents of totalContentsToReceive) {
            const mergedGain = {};
            for (const content of contents) {
                lodash_1.default.merge(mergedGain, content.gain);
            }
            this.gains.push(mergedGain);
        }
        this.mailIdsForOverflow.push(...receivedMailIds);
        //* glogBoxData
        // CMS.CashShopBoxRatioDesc.contents의 RewardType/DuplicateRewardType은
        // Id/DuplicateId가 있는 상품(명성/경험치류X)들만 지급되는 듯함.this.executeOp 참고
        const addGlogBoxDataReceived = (contents) => {
            for (const content of contents) {
                if (content.bDuplicated) {
                    const contentElem = content.contentElem;
                    this.glogBoxData.push({
                        id: contentElem.Id,
                        uid: null,
                        name: (0, rewardDesc_1.getRewardNameCmsTableByRewardType)(contentElem.RewardType, contentElem.Id),
                        amt: contentElem.Amount,
                        type: rewardDesc_1.REWARD_TYPE[contentElem.RewardType],
                        is_duplicated: 1,
                        duplicated_id: contentElem.DuplicateId,
                        duplicated_name: (0, rewardDesc_1.getRewardNameCmsTableByRewardType)(contentElem.DuplicateRewardType, contentElem.DuplicateId),
                        duplicated_type: rewardDesc_1.REWARD_TYPE[contentElem.DuplicateRewardType],
                        duplicated_amt: contentElem.DuplicateAmount,
                        eleven_bonus: content.isBonusGachaRewardForGlog ? 1 : 0,
                    });
                }
                else {
                    // uid가 있는 것들을 위함인듯?
                    const arrRewawrdData = userChangeTask_1.UserChangeTask.covertActualGainToGLogRewardData(content.gain);
                    for (const rewardData of arrRewawrdData) {
                        this.glogBoxData.push({
                            id: rewardData.id,
                            uid: rewardData.uid,
                            name: (0, rewardDesc_1.getRewardNameCmsTableByRewardType)(rewardDesc_1.REWARD_TYPE[rewardData.type], rewardData.id),
                            amt: rewardData.amt,
                            type: rewardData.type,
                            is_duplicated: 0,
                            duplicated_id: null,
                            duplicated_name: null,
                            duplicated_type: null,
                            duplicated_amt: null,
                            eleven_bonus: content.isBonusGachaRewardForGlog ? 1 : 0,
                        });
                    }
                }
            }
        };
        //TODO 메일로 받은 것들
        const addGLogBoxDataMailed = (contents) => {
            for (const { contentElem, bDuplicated, isBonusGachaRewardForGlog: isBonusGachaReward, } of contents) {
                const glogBoxData = {
                    id: contentElem.Id,
                    uid: null,
                    name: (0, rewardDesc_1.getRewardNameCmsTableByRewardType)(contentElem.RewardType, contentElem.Id),
                    amt: contentElem.Amount,
                    type: rewardDesc_1.REWARD_TYPE[contentElem.RewardType],
                    is_duplicated: 0,
                    duplicated_id: null,
                    duplicated_name: null,
                    duplicated_type: null,
                    duplicated_amt: null,
                    eleven_bonus: isBonusGachaReward ? 1 : 0,
                };
                if (bDuplicated) {
                    glogBoxData.is_duplicated = 1;
                    glogBoxData.duplicated_id = contentElem.DuplicateId;
                    glogBoxData.duplicated_name = (0, rewardDesc_1.getRewardNameCmsTableByRewardType)(contentElem.DuplicateRewardType, contentElem.DuplicateId);
                    glogBoxData.duplicated_type = rewardDesc_1.REWARD_TYPE[contentElem.DuplicateRewardType];
                    glogBoxData.duplicated_amt = contentElem.DuplicateAmount;
                }
                this.glogBoxData.push(glogBoxData);
            }
        };
        for (let i = 0; i < this.cashShopBoxRatioCmses.length; i += 1) {
            addGlogBoxDataReceived(totalContentsToReceive[i]);
            addGLogBoxDataMailed(totalContentsToMailForEquipment[i]);
            addGLogBoxDataMailed(totalContentsToMailForItem[i]);
            addGLogBoxDataMailed(totalContentsToMailForShip[i]);
        }
        //
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
    executeOp(user, tryData, changes, rewardType, cmsId, amount) {
        switch (rewardType) {
            case rewardDesc_1.REWARD_TYPE.POINT:
                return (0, userChangeOperator_1.opAddPoint)(user, tryData, changes, cmsId, amount, false, false, undefined, false);
            case rewardDesc_1.REWARD_TYPE.ITEM:
                return (0, userChangeOperator_1.opAddItem)(user, tryData, changes, cmsId, amount, false, false, undefined, true);
            case rewardDesc_1.REWARD_TYPE.MATE_EQUIP:
                return (0, userChangeOperator_1.opAddMateEquip)(user, tryData, changes, cmsId, amount, false, false, undefined, true);
            case rewardDesc_1.REWARD_TYPE.SHIP:
                return (0, userChangeOperator_1.opAddShip)(user, tryData, changes, cmsId, false, null, false /* bAllowExceedDock */, undefined, null, true);
            case rewardDesc_1.REWARD_TYPE.MATE:
                return (0, userChangeOperator_1.opAddMate)(user, tryData, changes, cmsId, false);
            case rewardDesc_1.REWARD_TYPE.SHIP_BLUEPRINT:
                return (0, userChangeOperator_1.opUpgradeShipBlueprint)(user, tryData, changes, cmsId, true);
            case rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM:
                return (0, userChangeOperator_1.opAddShipSlotItem)(user, tryData, changes, cmsId, amount, false, false, undefined, true);
            // 타입이 추가될 때 this.getActualGain 도 신경써줘야 될듯 함.
            default:
                mlog_1.default.error('not implemented-cash-shop-gacha-box-reward-type', {
                    userId: user.userId,
                    rewardType,
                    cmsId,
                });
                return userChangeTask_1.CHANGE_TASK_RESULT.NOT_IMPLEMENTED;
        }
    }
    getActualGain(rewardType, cmsId, amount, lastIds, tryData) {
        const ids = [];
        if (rewardType === rewardDesc_1.REWARD_TYPE.SHIP) {
            const oldLastId = lastIds.lastShipId;
            const tryDataLastId = tryData.fleets.getLastShipId();
            for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
                ids.push(i);
            }
        }
        else if (rewardType === rewardDesc_1.REWARD_TYPE.MATE_EQUIP) {
            const oldLastId = lastIds.lastMateEquipmentId;
            const tryDataLastId = tryData.mates.getLastMateEquipmentId();
            for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
                ids.push(i);
            }
        }
        else if (rewardType === rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM) {
            const oldLastId = lastIds.lastShipSlotItemId;
            const tryDataLastId = tryData.inven.getLastShipSlotItemId();
            for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
                ids.push(i);
            }
        }
        else if (rewardType === rewardDesc_1.REWARD_TYPE.QUEST_ITEM) {
            const oldLastId = lastIds.lastQuestItemId;
            const tryDataLastId = tryData.inven.itemInven.getLastQuestItemId();
            for (let i = oldLastId + 1; i <= tryDataLastId; i++) {
                ids.push(i);
            }
        }
        switch (rewardType) {
            case rewardDesc_1.REWARD_TYPE.POINT:
                return {
                    points: {
                        [cmsId]: amount,
                    },
                };
            case rewardDesc_1.REWARD_TYPE.ITEM:
                return {
                    items: {
                        [cmsId]: amount,
                    },
                };
            case rewardDesc_1.REWARD_TYPE.MATE_EQUIP:
                return {
                    mateEquips: {
                        [cmsId]: amount,
                    },
                    mateEquipIds: {
                        [cmsId]: ids,
                    },
                };
            case rewardDesc_1.REWARD_TYPE.SHIP:
                return {
                    ships: {
                        [cmsId]: amount,
                    },
                    shipIds: {
                        [cmsId]: ids,
                    },
                };
            case rewardDesc_1.REWARD_TYPE.MATE:
                return {
                    mates: [cmsId],
                };
            case rewardDesc_1.REWARD_TYPE.SHIP_BLUEPRINT:
                return {
                    shipBlueprints: {
                        [cmsId]: {
                            level: 1,
                            exp: 0,
                        },
                    },
                };
            case rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM:
                return {
                    shipSlotItems: {
                        [cmsId]: amount,
                    },
                    shipSlotItemIds: {
                        [cmsId]: ids,
                    },
                };
        }
    }
    static convertContentElemToRewardFixedElem(elem, bDuplicated) {
        return bDuplicated
            ? {
                Type: elem.DuplicateRewardType,
                Id: elem.DuplicateId,
                Quantity: elem.DuplicateAmount,
            }
            : {
                Type: elem.RewardType,
                Id: elem.Id,
                Quantity: elem.Amount,
            };
    }
}
class CashShopBuyWithoutPurchaseQuestPassSpec extends CashShopBuyWithoutPurchaseSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, cashShopCmsId, curTimeUtc, researchPoint, expiredFixedTermProducts, newFixedTermProduct, mateCmsId) {
        super(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc);
        this.researchPoint = researchPoint;
        this.expiredFixedTermProducts = expiredFixedTermProducts;
        this.newFixedTermProduct = newFixedTermProduct;
        this.mateCmsId = mateCmsId;
    }
    accumulate(user, tryData, changes) {
        const ops = [super.accumulate(user, tryData, changes)];
        if (this.expiredFixedTermProducts) {
            for (const cmsId of this.expiredFixedTermProducts) {
                ops.push((0, userChangeOperator_1.opDeleteCashShopFixedTermProduct)(user, tryData, changes, cmsId));
            }
        }
        if (this.newFixedTermProduct) {
            ops.push((0, userChangeOperator_1.opSetCashShopFixedTermProduct)(user, tryData, changes, this.newFixedTermProduct));
        }
        if (this.mateCmsId) {
            ops.push((0, userChangeOperator_1.opAddMate)(user, tryData, changes, this.mateCmsId));
        }
        if (this.researchPoint) {
            ops.push((0, userChangeOperator_1.opAddPoint)(user, tryData, changes, cmsEx.ResearchPointCmsId, this.researchPoint, false, false, { itemId: rsn }, false, true));
        }
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
class CashShopBuyWithoutPurchaseSoundPackSpec extends CashShopBuyWithoutPurchaseSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, cashShopCmsId, curTimeUtc, soundPackCmsId) {
        super(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, cashShopCmsId, expiredRestrictedProducts, restrictedProductChange, curTimeUtc);
        this.soundPackCmsId = soundPackCmsId;
    }
    accumulate(user, tryData, changes) {
        const ops = [
            super.accumulate(user, tryData, changes),
            (0, userChangeOperator_1.opAddSoundPack)(user, tryData, changes, this.soundPackCmsId),
        ];
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
class CashShopBuyWithoutPurchaseDailySubscriptionSpec extends CashShopBuyWithoutPurchaseRewardFixedSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, dsCms, rewardAmount, curTimeUtc, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow, cashShopCmsId) {
        super(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, dsCms.purchaseRewardId, // rewardFixedCmsId
        rewardAmount, // rewardAmount
        curTimeUtc, mailIdsForOverflow, cashShopCmsId);
        this.dsCms = dsCms;
        this.rewardAmount = rewardAmount;
        this.curTimeUtc = curTimeUtc;
    }
    accumulate(user, tryData, changes) {
        //
        let res = super.accumulate(user, tryData, changes);
        if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            return res;
        }
        for (let i = 0; i < this.rewardAmount; i++) {
            res = (0, userChangeOperator_1.opBuyDailySubscription)(user, tryData, changes, this.dsCms.id, this.curTimeUtc);
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
class CashShopBuyWithoutPurchaseHotSpotSpec extends CashShopBuyWithoutPurchaseRewardFixedSpec {
    constructor(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, rewardFixedCmsId, rewardAmount, curTimeUtc, // 공간이 꽉 차 보상이 메일로 보내질 경우 메일의 시간
    mailIdsForOverflow, cashShopCmsId) {
        super(costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, rewardFixedCmsId, rewardAmount, curTimeUtc, mailIdsForOverflow, cashShopCmsId);
        this.curTimeUtc = curTimeUtc;
        this.cashShopCmsId = cashShopCmsId;
    }
    accumulate(user, tryData, changes) {
        const ops = [
            super.accumulate(user, tryData, changes),
            (0, userChangeOperator_1.opSetHotSpotCoolTimeUtc)(user, tryData, changes, this.cashShopCmsId, this.curTimeUtc),
        ];
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
// class CashShopBuyWithoutPurchaseEncountShieldSpec extends CashShopBuyWithoutPurchaseSpec {
//   constructor(
//     costPointCmsId: number,
//     costPointValue: number,
//     bPermitExchange: boolean,
//     mileageBonus: number | undefined,
//     expiredRestrictedProducts: Set<number>,
//     restrictedProductChange: RestrictedProduct,
//     cashShopCmsId: number,
//     private amount: number
//   ) {
//     super(
//       costPointCmsId,
//       costPointValue,
//       bPermitExchange,
//       mileageBonus,
//       cashShopCmsId,
//       expiredRestrictedProducts,
//       restrictedProductChange
//     );
//   }
//   accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
//     const ops = [super.accumulate(user, tryData, changes)];
//     if (!tryData.encount) {
//       tryData.encount = user.userEncount.clone();
//     }
//     if (!tryData.shield) {
//       tryData.shield = user.userShield.clone();
//     }
//     // 보호막 구매 시 보호막을 켜준다.
//     if (!tryData.shield.isActivated(SHIELD_CMS_ID.ENCOUNT)) {
//       ops.push(opSetIsEncountShieldCountUsed(user, tryData, changes, 1));
//     }
//     ops.push(opAddEncountShieldCount(user, tryData, changes, this.amount));
//     for (const res of ops) {
//       if (res !== CHANGE_TASK_RESULT.OK) {
//         return res;
//       }
//     }
//     return CHANGE_TASK_RESULT.OK;
//   }
// }
var CashShopOperatorUtil;
(function (CashShopOperatorUtil) {
    /**
     *! 캐시샵에서 구매할 때 필수적으로 사용해야하는 오퍼레이터들을 실행
     */
    function executeCashShopBuyWithoutPurchaseOps(user, tryData, changes, costPointCmsId, costPointValue, bPermitExchange, mileageBonus, expiredRestrictedProducts, restrictedProductChange, cashShopCmsId, buyCount, curTimeUtc) {
        const ops = [];
        if (costPointValue && costPointValue > 0) {
            const cashShopCms = cms_1.default.CashShop[cashShopCmsId];
            const lgCashParam = {};
            if (costPointCmsId === cmsEx.RedGemPointCmsId && cashShopCms.isToBuy) {
                lgCashParam.productId =
                    mconf_1.default.binaryCode === 'GL' ? `uwogl_${cashShopCmsId}` : `uwo_${cashShopCmsId}`;
            }
            else if (costPointCmsId === cmsEx.RedGemPointCmsId) {
                lgCashParam.itemId =
                    mconf_1.default.binaryCode === 'GL' ? `uwogl_${cashShopCmsId}` : `uwo_${cashShopCmsId}`;
            }
            else {
                lgCashParam.itemId = rsn;
            }
            if (cashShopCms.isToBuy && buyCount > 1) {
                lgCashParam.buyCount = buyCount;
            }
            if (costPointCmsId === cmsEx.CashShopMileage) {
                ops.push((0, userChangeOperator_1.opAddMileage)(user, tryData, changes, -costPointValue, curTimeUtc));
            }
            else {
                ops.push((0, userChangeOperator_1.opAddPoint)(user, tryData, changes, costPointCmsId, -costPointValue, false, bPermitExchange, lgCashParam, false));
            }
        }
        if (mileageBonus !== undefined && mileageBonus !== 0) {
            (0, assert_1.default)(mileageBonus > 0);
            ops.push((0, userChangeOperator_1.opAddMileage)(user, tryData, changes, mileageBonus, curTimeUtc));
        }
        if (expiredRestrictedProducts) {
            for (const cmsId of expiredRestrictedProducts) {
                ops.push((0, userChangeOperator_1.opDeleteCashShopRestrictedProduct)(user, tryData, changes, cmsId));
            }
        }
        if (restrictedProductChange) {
            ops.push((0, userChangeOperator_1.opSetCashShopRestrictedProduct)(user, tryData, changes, restrictedProductChange));
        }
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
    CashShopOperatorUtil.executeCashShopBuyWithoutPurchaseOps = executeCashShopBuyWithoutPurchaseOps;
    function executeAddDirectMailOp(user, tryData, changes, mailCmsId, curTimeUtc, elems, bIsAccum) {
        const { expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment } = getMailExpirationParamForOperator(mailCmsId, curTimeUtc);
        return opAddAttachmentDirectMail(user, tryData, changes, mailCmsId, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, elems, bIsAccum);
    }
    CashShopOperatorUtil.executeAddDirectMailOp = executeAddDirectMailOp;
    function getMailExpirationParamForOperator(mailCmsId, curTimeUtc) {
        const mailCms = cms_1.default.Mail[mailCmsId];
        let expireTimeUtc = null;
        let bShouldSetExpirationWhenReceiveAttachment = 0;
        if (mailCms.mailKeepTime > 0) {
            expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
        }
        else if (mailCms.mailKeepTime === -1) {
            bShouldSetExpirationWhenReceiveAttachment = 1;
        }
        return {
            expireTimeUtc,
            bShouldSetExpirationWhenReceiveAttachment,
        };
    }
    /**
     * opAddDirectmail Wrapper
     */
    function opAddAttachmentDirectMail(user, tryData, changes, mailCmsId, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, elem, bIsAccum) {
        return (0, userChangeOperator_1.opAddDirectmail)(user, tryData, changes, mailCmsId, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, null, null, null, cmsEx.convertRewardFixedElemsToCustomAttachmentStr(elem, bIsAccum, curTimeUtc));
    }
})(CashShopOperatorUtil || (CashShopOperatorUtil = {}));
//# sourceMappingURL=cashShopBuyWithoutPurchase.js.map