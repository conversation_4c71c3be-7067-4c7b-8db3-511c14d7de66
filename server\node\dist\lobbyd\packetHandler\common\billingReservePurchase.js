"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingReservePurchase = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const linegamesBillingApiClient_1 = require("../../../motiflib/mhttp/linegamesBillingApiClient");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const merror_1 = require("../../../motiflib/merror");
const userConnection_1 = require("../../userConnection");
const cms_1 = __importDefault(require("../../../cms"));
const cmsEx = __importStar(require("../../../cms/ex"));
const userCashShop_1 = require("../../userCashShop");
const cashShopDesc_1 = require("../../../cms/cashShopDesc");
const iPlatformBillingApiClient_1 = require("../../../motiflib/mhttp/iPlatformBillingApiClient");
const ERROR_CODE_NOT_EXIST_DATA = iPlatformBillingApiClient_1.LGBillingErrorCode[iPlatformBillingApiClient_1.LGBillingErrorCode.NOT_EXIST_DATA];
class BillingApiFailError extends Error {
    constructor(api, billingApiResp) {
        super();
        this.step = api;
        this.resp = billingApiResp;
    }
}
// ----------------------------------------------------------------------------
class Cph_Common_BillingReservePurchase {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const userConn = user.userConn;
        // getIp 를 사용할 수 있도록 UserRealConnection 검증
        // (UserBotConnection 에는 소켓이 없음)
        if (!(userConn instanceof userConnection_1.UserRealConnection)) {
            throw new merror_1.MError('invalid-user-connection', merror_1.MErrorCode.INVALID_CONN_STATE);
        }
        if (!linegamesBillingApiClient_1.LineGamesBillingApiClient.isCompleteReservedPurchaseImplemented(user.storeCode)) {
            throw new merror_1.MError('invalid-store-code', merror_1.MErrorCode.INTERNAL_ERROR, {
                storeCode: user.storeCode,
            });
        }
        const reqBody = packet.bodyObj;
        const { productId, price, currency, microPrice } = reqBody;
        const curTimeUtc = mutil.curTimeUtc();
        const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
        if (!cashShopCms) {
            throw new merror_1.MError('failed-to-get-cash-shop-cms', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE, { productId, storeCode: user.storeCode });
        }
        if (cashShopCms.salePointType !== cashShopDesc_1.CASH_SHOP_SALE_POINT_TYPE.CASH) {
            throw new merror_1.MError('invalid-sale-point-type', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE, { productId, cashShopCmsId: cashShopCms.id });
        }
        if (cashShopCms.shopCase === cashShopDesc_1.SHOP_CASE.WEB_SHOP) {
            throw new merror_1.MError('can-not-buy-webshop-product', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE, { productId, cashShopCmsId: cashShopCms.id });
        }
        user.userContentsTerms.ensureContentsTerms(cashShopCms.contentsTerms, user);
        const curDate = new Date(curTimeUtc * 1000);
        const reason = user.userCashShop.isBuyableProduct(user, cashShopCms.id, curDate, user.userCashShop.getExpiredRestrictedProducts(curTimeUtc), 1, productId);
        if (reason !== userCashShop_1.UNBUYABLE_REASON.BUYABLE) {
            throw new merror_1.MError(`can-not-buy-cash-shop-biiling-product, reason: ${userCashShop_1.UNBUYABLE_REASON[reason]}`, merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE, {
                productId,
                curTimeUtc,
                restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
            });
        }
        return Promise.resolve()
            .then(() => {
            // 보관함에 많이 안 쌓이도록.
            return mhttp_1.default.platformBillingApi
                .queryInventoryPurchaseList(user.userId.toString())
                .then((billingApiResp) => {
                if (billingApiResp.success !== true) {
                    if (billingApiResp.errorCd === ERROR_CODE_NOT_EXIST_DATA) {
                        // 보관함에 아무것도 없을 때도 위 에러가 나온다.
                        return null;
                    }
                    throw new BillingApiFailError('QUERY_INVEN_PURCHASE_LIST', billingApiResp);
                }
                const invenPurchases = billingApiResp.data;
                if (!invenPurchases) {
                    return null;
                }
                if (!Array.isArray(invenPurchases)) {
                    throw new merror_1.MError('query-inven-purchase-resp-data-array-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { billingApiResp });
                }
                // 빌링 보관함에서 제공하는 개수와 무관한, 게임 서버에서의 제한
                const bEnoughSpace = invenPurchases.length < cms_1.default.Define.BillingInventorySlotSize;
                if (!bEnoughSpace) {
                    throw new merror_1.MError('billing-inventory-not-enough-slot', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE, { billingApiResp, slotSize: cms_1.default.Define.MaxBillingInventorySize });
                }
                return null;
            });
        })
            .then(() => {
            // 등록된 개별 품목들이 지급 가능한 것인지 확인해본다.
            return mhttp_1.default.platformBillingApi
                .queryProductGiveItemDetail(user.storeCode, productId)
                .then((billingApiResp) => {
                var _a;
                if (billingApiResp.success !== true) {
                    throw new BillingApiFailError('QUERY_PRODUCT_GIVE_ITEM', billingApiResp);
                }
                const giveItemList = billingApiResp.data.giveItemList;
                (0, iPlatformBillingApiClient_1.ensureGiveItems)(giveItemList);
                for (const elem of giveItemList) {
                    if (elem.coinManageBalanceYn === 'Y') {
                        continue;
                    }
                    const ret = userCashShop_1.BillingUtil.buildEnsuredGiveItem(elem);
                    if (ret.bOk !== true) {
                        throw new merror_1.MError(`failed to ensure give item(${userCashShop_1.BillingUtil.giveItemToString(elem)}). reason: ${(_a = ret.err) === null || _a === void 0 ? void 0 : _a.reason}.`, merror_1.MErrorCode.BILLING_API_UNEXPECTED_GIVE_ITEM, { billingApiResp, elem });
                    }
                }
            });
        })
            .then(() => {
            return mhttp_1.default.platformBillingApi.reservePurchase(user.userId.toString(), userConn.getIp(), productId, user.accountId, user.storeCode, price, currency, user.appVersion, user.deviceType.toUpperCase(), user.countryCreated, microPrice);
        })
            .then((billingApiResp) => {
            var _a, _b, _c, _d, _e;
            if (billingApiResp.success === true) {
                // 빌링 API 에서 인자들의 타입(RequestBody) 및 유효성이 검증됐다고 가정.
                user.glog('common_prepurchase', {
                    pn: productId,
                    spn: (_a = reqBody.productName) !== null && _a !== void 0 ? _a : null,
                    pr: Number(price),
                    currency: currency,
                    os: user.deviceType.toUpperCase(),
                    osv: (_b = user.osv) !== null && _b !== void 0 ? _b : null,
                    lang: (_c = user.deviceLang) !== null && _c !== void 0 ? _c : null,
                    lang_game: (_d = user.lineLangCultre) !== null && _d !== void 0 ? _d : null,
                    sk: user.storeCode,
                    country_ip: (_e = user.countryIp) !== null && _e !== void 0 ? _e : null,
                });
            }
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                step: 'RESERVE_PURCHASE',
                billingApiResp,
            });
        })
            .catch((err) => {
            if (err instanceof BillingApiFailError) {
                mlog_1.default.warn('[BillingReservePurchase] billing-api-failed', {
                    userId: user.userId,
                    step: err.step,
                    resp: err.resp,
                });
                // 빌링 API success 가 false 인 경우, resp 를 그대로 클라로 전송하도록 한다.
                // 클라에서 빌링 에러를 알 수 있도록함.
                return user.sendJsonPacket(packet.seqNum, packet.type, {
                    step: err.step,
                    billingApiResp: err.resp,
                });
            }
            throw err;
        });
    }
}
exports.Cph_Common_BillingReservePurchase = Cph_Common_BillingReservePurchase;
//# sourceMappingURL=billingReservePurchase.js.map