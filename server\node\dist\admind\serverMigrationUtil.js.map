{"version": 3, "file": "serverMigrationUtil.js", "sourceRoot": "", "sources": ["../../src/admind/serverMigrationUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;AAC/E,oDAAuB;AACvB,oDAA+B;AAE/B,qCAAwC;AAExC,qDAAyD;AACzD,8DAAsC;AACtC,2FAAiF;AACjF,yDAA2C;AAC3C,2CAA6C;AAC7C,mGAA2E;AAC3E,yGAAiF;AACjF,uHAA+F;AAC/F,iGAAyE;AACzE,+GAAuF;AACvF,6FAAqE;AACrE,yFAAiE;AACjE,4DAAoC;AAEpC,IAAY,qBAUX;AAVD,WAAY,qBAAqB;IAC/B,iEAAQ,CAAA;IACR,mGAAyB,CAAA;IACzB,6FAAsB,CAAA;IACtB,iGAAwB,CAAA;IACxB,+GAA+B,CAAA;IAC/B,yGAA4B,CAAA;IAC5B,+HAAuC,CAAA;IACvC,mGAAyB,CAAA;IACzB,+FAAuB,CAAA;AACzB,CAAC,EAVW,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAUhC;AAED,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,uDAAa,CAAA;IACb,2EAAuB,CAAA;IACvB,mGAAmC,CAAA;IACnC,yEAAsB,CAAA;IACtB,yEAAsB,CAAA;AACxB,CAAC,EANW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAMtB;AAED,MAAM,qBAAqB,GAAG;IAC5B,CAAC,YAAY,CAAC,EAAE,IAAI;IACpB,CAAC,gBAAgB,CAAC,EAAE,IAAI;IACxB,CAAC,wBAAwB,CAAC,EAAE,IAAI;IAChC,CAAC,wBAAwB,CAAC,EAAE,IAAI;IAChC,CAAC,cAAc,CAAC,EAAE,IAAI;IACtB,CAAC,6BAA6B,CAAC,EAAE,IAAI;IACrC,CAAC,WAAW,CAAC,EAAE,IAAI;IACnB,CAAC,iBAAiB,CAAC,EAAE,IAAI;IACzB,CAAC,SAAS,CAAC,EAAE,IAAI;IACjB,CAAC,uBAAuB,CAAC,EAAE,IAAI;IAC/B,CAAC,uBAAuB,CAAC,EAAE,IAAI;IAC/B,CAAC,sBAAsB,CAAC,EAAE,IAAI;IAC9B,CAAC,kCAAkC,CAAC,EAAE,IAAI;CAC3C,CAAC;AAEF,IAAI,UAAoB,CAAC;AAEzB,SAAgB,aAAa,CAAC,MAAkB;IAC9C,IAAI,UAAU,EAAE;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;KACpC;IAED,UAAU,GAAG,EAAE,CAAC;IAChB,OAAO,IAAA,2BAAiB,EAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;QACtD,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;YACtB,MAAM,SAAS,GAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE;gBACpC,SAAS;aACV;YAED,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5B;QAED,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC;AAlBD,sCAkBC;AAED,yFAAyF;AACzF,SAAgB,iBAAiB,CAAC,SAAiB,EAAE,MAAc;IACjE,QAAQ,SAAS,EAAE;QACjB,KAAK,SAAS;YACZ,OAAO,oCAAoC,MAAM,GAAG,CAAC;QACvD,iCAAiC;QACjC,mOAAmO;QACnO;YACE,OAAO,iBAAiB,SAAS,mBAAmB,MAAM,GAAG,CAAC;KACjE;AACH,CAAC;AATD,8CASC;AAED,SAAgB,iBAAiB,CAAC,SAAiB,EAAE,MAAc;IACjE,QAAQ,SAAS,EAAE;QACjB,KAAK,SAAS;YACZ,OAAO,kCAAkC,MAAM,GAAG,CAAC;QACrD;YACE,OAAO,eAAe,SAAS,mBAAmB,MAAM,GAAG,CAAC;KAC/D;AACH,CAAC;AAPD,8CAOC;AAED,SAAgB,UAAU,CACxB,MAAc,EACd,OAAe,EACf,QAAiB,KAAK;IAEtB,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,gBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;IAE7E,MAAM,iBAAiB,GAAsB,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,aAAa,GAAG,IAAA,4BAAgB,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACxD,MAAM,UAAU,GAAe,iBAAiB,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;IACvF,MAAM,eAAe,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAClD,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IAEtC,IAAI,GAAG,GAAG,CAAC,CAAC;IAEZ,OAAO,OAAO,CAAC,OAAO,EAAE;SACrB,IAAI,CAAC,GAAG,EAAE;QACT,eAAe;QACf,OAAO,IAAA,gCAAsB,EAAC,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACvE,IAAI,GAAG,EAAE;gBACP,cAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC1D,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC;aACnC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,sBAAsB;QACtB,OAAO,IAAA,mCAAyB,EAAC,eAAe,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YAC/E,IAAI,GAAG,IAAI,gBAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,cAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;gBACpE,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,mBAAmB,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,mBAAmB;QACnB,OAAO,IAAA,0CAAgC,EAAC,eAAe,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACtF,IAAI,GAAG,IAAI,gBAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,cAAI,CAAC,IAAI,CAAC,+CAA+C,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;gBAChF,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,+BAA+B,CAAC;aACzD;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,kBAAkB;QAClB,IAAI,KAAK,EAAE;YACT,OAAO,IAAI,CAAC;SACb;QAED,MAAM,kBAAkB,GAAG,eAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC;QACvE,OAAO,kBAAkB,CAAC,0BAA0B,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACnF,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;gBAChB,IAAI,GAAG,CAAC,OAAO,KAAK,8CAAkB,CAAC,8CAAkB,CAAC,cAAc,CAAC,EAAE;oBACzE,6BAA6B;oBAC7B,IAAI,GAAG,KAAK,CAAC;iBACd;aACF;iBAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5D,IAAI,GAAG,KAAK,CAAC;aACd;YACD,IAAI,IAAI,EAAE;gBACR,cAAI,CAAC,IAAI,CAAC,+CAA+C,EAAE;oBACzD,MAAM;oBACN,OAAO;oBACP,IAAI,EAAE,GAAG;iBACV,CAAC,CAAC;gBACH,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC;aAC5C;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,kBAAkB;QAClB,OAAO,IAAA,+BAAqB,EAAC,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC;aACnE,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACZ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,cAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;gBACnE,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC;aAC5C;YAED,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACvD,OAAO,IAAA,sCAA4B,EAAC,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC/E;YAED,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACZ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,cAAI,CAAC,IAAI,CAAC,0CAA0C,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC3E,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC;aAC5C;YAED,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACvD,OAAO,IAAA,6BAAmB,EAAC,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC;aAC1D;YAED,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACZ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;oBACtB,IAAI,IAAI,CAAC,KAAK,KAAK,kBAAU,CAAC,OAAO,EAAE;wBACrC,SAAS;qBACV;oBAED,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;oBACvD,IAAI,aAAa,IAAI,aAAa,IAAI,UAAU,EAAE;wBAChD,SAAS;qBACV;oBAED,sEAAsE;oBACtE,cAAI,CAAC,IAAI,CAAC,uCAAuC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;oBACxE,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC,kBAAkB,CAAC;oBAC3C,MAAM;iBACP;aACF;QACH,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACP,CAAC;AAxHD,gCAwHC"}