"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReceiveProducts = exports.Cph_Common_BillingReceiveInvenPurchases = void 0;
const assert_1 = __importDefault(require("assert"));
const lodash_1 = __importDefault(require("lodash"));
const bluebird_1 = require("bluebird");
const typedi_1 = require("typedi");
const moment_1 = __importDefault(require("moment"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const merror_1 = require("../../../motiflib/merror");
const mutil = __importStar(require("../../../motiflib/mutil"));
const userConnection_1 = require("../../userConnection");
const displayNameUtil = __importStar(require("../../../motiflib/displayNameUtil"));
const cmsEx = __importStar(require("../../../cms/ex"));
const cms_1 = __importDefault(require("../../../cms"));
const cashShopDesc_1 = require("../../../cms/cashShopDesc");
const server_1 = require("../../server");
const tuBillingReceiveInvenPurchases_1 = __importDefault(require("../../../mysqllib/txn/tuBillingReceiveInvenPurchases"));
const ship_1 = __importStar(require("../../ship"));
const rewardDesc_1 = require("../../../cms/rewardDesc");
const fleet_1 = __importDefault(require("../../fleet"));
const enum_1 = require("../../../motiflib/model/lobby/enum");
const eventPageDesc_1 = require("../../../cms/eventPageDesc");
const userCashShop_1 = require("../../userCashShop");
var BILLING_GIVE_ITEM_TYPE = userCashShop_1.BillingUtil.BILLING_GIVE_ITEM_TYPE;
const mate_1 = __importStar(require("../../mate"));
const userBuffs_1 = require("../../userBuffs");
const mailBuilder_1 = require("../../../motiflib/mailBuilder");
const iPlatformBillingApiClient_1 = require("../../../motiflib/mhttp/iPlatformBillingApiClient");
// ----------------------------------------------------------------------------
// 빌링 보관함에서 수령
// 중복 수령 관련해서, 먼저 빌링 서버에 완료처리 해버린다.
// 당장은 아이템을 지급에 실패하는 경우 CS 처리 해야함.
// ----------------------------------------------------------------------------
const rsn = 'billing_receive_inven_purchases';
const add_rsn = null;
class BillingApiFailError extends Error {
    constructor(step, billingApiResp) {
        super();
        this.step = step;
        this.billingApiResp = billingApiResp;
    }
}
// ----------------------------------------------------------------------------
class Cph_Common_BillingReceiveInvenPurchases {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const reqBody = packet.bodyObj;
        const respBody = { sync: {} };
        const mailIds = [];
        const curTimeUtc = mutil.curTimeUtc();
        if (!Array.isArray(reqBody.invenIds)) {
            throw new merror_1.MError('invenIds-array-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { reqBody });
        }
        if (reqBody.invenIds.length === 0) {
            throw new merror_1.MError('invenIds-empty', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { reqBody });
        }
        for (const invenId of reqBody.invenIds) {
            if (!_isString(invenId) || invenId.length === 0) {
                throw new merror_1.MError('invenId-non-empty-string-expected', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { reqBody, invenId });
            }
        }
        const reqInvenIdsUniqueChecked = new Set(reqBody.invenIds);
        if (reqInvenIdsUniqueChecked.size !== reqBody.invenIds.length) {
            throw new merror_1.MError('invenIds-duplicated', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { reqBody });
        }
        if (reqInvenIdsUniqueChecked.size > cms_1.default.Define.BillingInventorySlotSize) {
            // 인벤 공간에 대한 Define 이긴 하지만 편의상.
            // 수량을 조정하게 된다면 빌링 API 에서 요청 가능한 개수도 확인해봐야한다.
            throw new merror_1.MError('invenIds-exceeded-allowed-request-quantity', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { requestBody: reqBody, slotSize: cms_1.default.Define.MaxBillingInventorySize });
        }
        const productNames = {};
        const coinManageBalanceItemsByProductId = {};
        const ensuredGiveItemsByProductId = {};
        const completeWithChargeCoin = [];
        const completeOnly = [];
        const invenRemoveds = [];
        return Promise.resolve()
            .then(() => {
            // #. 상품보관함에서 조회.
            return mhttp_1.default.platformBillingApi.queryInventoryPurchaseList(user.userId.toString());
        })
            .then((billingApiResp) => {
            if (billingApiResp.success !== true) {
                if (billingApiResp.errorCd === iPlatformBillingApiClient_1.LGBillingErrorCode[iPlatformBillingApiClient_1.LGBillingErrorCode.NOT_EXIST_DATA]) {
                    // 보관함에 아무것도 없을 때도 위 에러가 나온다.
                    throw new merror_1.MError('billing-inven-empty', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { requestBody: reqBody, billingApiResp });
                }
                throw new BillingApiFailError('QUERY_INVEN_LIST', billingApiResp);
            }
            if (!Array.isArray(billingApiResp.data)) {
                throw new merror_1.MError('query-inven-purchase-resp-data-array-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { reqBody, billingApiResp });
            }
            const allInvenPurchases = billingApiResp.data;
            const allInvenPurchasesByInvenId = {};
            for (const invenPurchase of allInvenPurchases) {
                // 일단은 방어적으로..
                if (typeof invenPurchase !== 'object' || !invenPurchase) {
                    throw new merror_1.MError('query-inven-purchase-resp-data-array-elem-object-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { billingApiResp });
                }
                if (invenPurchase.status !== 'READY') {
                    // READY 인 것들만 전달된다고는 함.
                    throw new merror_1.MError('query-inven-purchase-resp-data-array-elem-status-READY-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { billingApiResp });
                }
                // 꼭 필요한 정보들.
                _ensureInvenPurchase(invenPurchase, {
                    billingApiResp,
                });
                if (allInvenPurchasesByInvenId[invenPurchase.invenId]) {
                    throw new merror_1.MError('query-inven-purchase-resp-data-array-elem-invenId-duplicated', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { billingApiResp });
                }
                allInvenPurchasesByInvenId[invenPurchase.invenId] = invenPurchase;
            }
            const byStoreCd = new Map();
            for (const reqInvenId of reqInvenIdsUniqueChecked) {
                const invenPurchase = allInvenPurchasesByInvenId[reqInvenId];
                if (!invenPurchase) {
                    throw new merror_1.MError('not-found-in-billing-inventory', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES, { reqBody, billingApiResp });
                }
                if (!byStoreCd.has(invenPurchase.appStoreCd)) {
                    byStoreCd.set(invenPurchase.appStoreCd, new Map());
                }
                const byProductId = byStoreCd.get(invenPurchase.appStoreCd);
                if (!byProductId.has(invenPurchase.productId)) {
                    byProductId.set(invenPurchase.productId, []);
                }
                byProductId.get(invenPurchase.productId).push(invenPurchase);
            }
            const queriesForGiveItem = [];
            for (const [appStoreCd, toReceivesbyProductId] of byStoreCd) {
                for (const [productId] of toReceivesbyProductId) {
                    queriesForGiveItem.push({
                        appStoreCd,
                        productId,
                    });
                    // 빌링 서버에서 상품명을 조회하는 게 번거롭기 때문에 기획 테이블에서 조회하도록 한다.
                    // 추후 빌링 서버를 통해 가져와야 할 수 있음.
                    // 그리고 수령 진행 과정에서 별 것도 아닌 것에서 에러나는 상황이 없도록 미리 만들어 놓도록 함.
                    if (!productNames[appStoreCd]) {
                        productNames[appStoreCd] = {};
                    }
                    const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
                    productNames[appStoreCd][productId] = cashShopCms
                        ? displayNameUtil.getCashShopProductDisplayName(cashShopCms)
                        : null;
                }
            }
            // #. 상품 구성품 조회
            return (0, bluebird_1.reduce)(queriesForGiveItem, async (_, query) => {
                return mhttp_1.default.platformBillingApi
                    .queryProductGiveItemDetail(query.appStoreCd, query.productId)
                    .then((billingApiResp) => {
                    var _a;
                    if (billingApiResp.success !== true) {
                        throw new BillingApiFailError('QUERY_PRODUCT_GIVE_ITEM', billingApiResp);
                    }
                    const data = billingApiResp.data;
                    const giveItemList = data.giveItemList;
                    (0, iPlatformBillingApiClient_1.ensureGiveItems)(giveItemList);
                    // 빌링에서 관리하는 재화를 나눠 담는다.
                    const coinManageBalanceItems = [];
                    const ensuredGiveItems = [];
                    for (const giveItem of giveItemList) {
                        if (giveItem.coinManageBalanceYn === 'Y') {
                            coinManageBalanceItems.push(giveItem);
                        }
                        else {
                            const ret = userCashShop_1.BillingUtil.buildEnsuredGiveItem(giveItem);
                            if (ret.bOk !== true) {
                                throw new merror_1.MError(`failed to ensure give item (${userCashShop_1.BillingUtil.giveItemToString(giveItem)}). reason: ${(_a = ret.err) === null || _a === void 0 ? void 0 : _a.reason}.`, merror_1.MErrorCode.BILLING_API_UNEXPECTED_GIVE_ITEM, { billingApiResp, giveItem });
                            }
                            ensuredGiveItems.push(ret.value);
                        }
                    }
                    //
                    if (coinManageBalanceItems.length > 0) {
                        if (!coinManageBalanceItemsByProductId[query.appStoreCd]) {
                            coinManageBalanceItemsByProductId[query.appStoreCd] = {};
                        }
                        coinManageBalanceItemsByProductId[query.appStoreCd][query.productId] =
                            coinManageBalanceItems;
                    }
                    if (ensuredGiveItems.length > 0) {
                        if (!ensuredGiveItemsByProductId[query.appStoreCd]) {
                            ensuredGiveItemsByProductId[query.appStoreCd] = {};
                        }
                        ensuredGiveItemsByProductId[query.appStoreCd][query.productId] = ensuredGiveItems;
                    }
                    //
                    if (coinManageBalanceItems.length > 0) {
                        completeWithChargeCoin.push(...byStoreCd.get(query.appStoreCd).get(query.productId));
                    }
                    else {
                        completeOnly.push(...byStoreCd.get(query.appStoreCd).get(query.productId));
                    }
                });
            }, {});
        })
            .then(() => {
            var _a;
            if (completeWithChargeCoin.length === 0) {
                return null;
            }
            const billingQueries = [];
            for (const elem of completeWithChargeCoin) {
                const coinManageBalanceItems = (_a = coinManageBalanceItemsByProductId[elem.appStoreCd]) === null || _a === void 0 ? void 0 : _a[elem.productId];
                (0, assert_1.default)(coinManageBalanceItems && coinManageBalanceItems.length > 0); // 로직이 의도와 다르게 동작하는 상태.
                billingQueries.push({
                    coinChargeList: coinManageBalanceItems.map((elem) => {
                        return {
                            coinCd: elem.itemCd,
                            chargeTypeCd: elem.coinChargeTypeCd,
                            chargeAmt: elem.amount,
                        };
                    }),
                    orderId: elem.orderId,
                    invenDelYn: 'Y',
                    invenMemo: `${rsn}`,
                });
            }
            return Promise.resolve()
                .then(() => {
                return mhttp_1.default.platformBillingApi.chargeCoinWithInvenList(user.userId.toString(), user.accountId, user.countryCreated, billingQueries);
            })
                .then((billingApiResp) => {
                var _a, _b;
                if (billingApiResp.success !== true) {
                    throw new BillingApiFailError('CHARGE_WITH_INVEN', billingApiResp);
                }
                for (const invenPurchase of completeWithChargeCoin) {
                    user.glog('common_purchase_box', {
                        rsn,
                        add_rsn,
                        flag: 2,
                        type: cashShopDesc_1.CASH_SHOP_SALE_POINT_TYPE.CASH,
                        product_id: invenPurchase.productId,
                        product_name: (_b = (_a = productNames[invenPurchase.appStoreCd]) === null || _a === void 0 ? void 0 : _a[invenPurchase.productId]) !== null && _b !== void 0 ? _b : null,
                        order_id: invenPurchase.orderId,
                        inven_id: invenPurchase.invenId,
                    });
                }
                invenRemoveds.push(...completeWithChargeCoin);
                const chargedBillingCoins = billingApiResp.data;
                _ensureChargedCoinList(chargedBillingCoins, { billingApiResp }, user.userId);
                lodash_1.default.merge(respBody.sync, user.userPoints.onChargeByPurchaseProduct(chargedBillingCoins, {
                    user,
                    rsn,
                    add_rsn,
                }));
                return null;
            });
        })
            .then(() => {
            if (completeOnly.length === 0) {
                return null;
            }
            // #. 보관함 수령 완료 통보 API 를 사용하는 경우
            const queries = completeOnly.map((elem, index) => {
                var _a;
                const coinManageBalanceItems = (_a = coinManageBalanceItemsByProductId[elem.appStoreCd]) === null || _a === void 0 ? void 0 : _a[elem.productId];
                if (coinManageBalanceItems && coinManageBalanceItems.length > 0) {
                    assert_1.default.fail(`coinManageBalanceYn-should-not-be-Y`); // 로직이 의도와 다르게 동작하는 상태.
                }
                return {
                    invenId: elem.invenId,
                    memo: `${rsn}`,
                };
            });
            return Promise.resolve()
                .then(() => {
                // #. 수령 통보.
                return mhttp_1.default.platformBillingApi.completeReceiveInvenPurchaseBulk(user.userId.toString(), queries);
            })
                .then((billingApiResp) => {
                var _a, _b, _c;
                if (billingApiResp.success !== true) {
                    throw new BillingApiFailError('COMPLETE_RECEIVE', billingApiResp);
                }
                const results = billingApiResp.data;
                if (!Array.isArray(results)) {
                    throw new merror_1.MError('complete-receive-inven-purchase-resp-data-array-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, { billingApiResp });
                }
                const successResults = [];
                const failResults = [];
                const invalidResults = [];
                const dupCheck = {};
                //* 건당 처리임에 유의해야함. 뭐하나 실패시 롤백이 아님.
                // TODO '모두 받기'를 실질적으로 사용하게 된다면, 실패한 것에 대해 클라에 전달할 필요가 있을지
                for (const result of results) {
                    if (((_a = result.resJsonVO) === null || _a === void 0 ? void 0 : _a.success) !== true) {
                        failResults.push({ result });
                        continue;
                    }
                    const invenPurchase = completeOnly.find((invenPurchase) => invenPurchase.invenId === result.invenId);
                    if (!invenPurchase) {
                        invalidResults.push([result, 'invenId-not-matched']);
                        continue;
                    }
                    if (dupCheck[invenPurchase.invenId]) {
                        invalidResults.push([result, 'invenId-duplicated']);
                        continue;
                    }
                    dupCheck[invenPurchase.invenId] = true;
                    successResults.push(invenPurchase);
                }
                if (failResults.length > 0) {
                    mlog_1.default.error('complete-receive-inven-purchase-fail-exist', {
                        userId: user.userId,
                        queries,
                        failResults,
                    });
                }
                if (invalidResults.length > 0) {
                    mlog_1.default.error('complete-receive-inven-purchase-invalid-resp-data-received', {
                        userId: user.userId,
                        queries,
                        invalidResults,
                    });
                }
                // 보관함에서 성공적으로 제거된 것에 관해 먼저 로그를 남기도록 한다.
                for (const invenPurchase of successResults) {
                    user.glog('common_purchase_box', {
                        rsn,
                        add_rsn,
                        flag: 2,
                        type: cashShopDesc_1.CASH_SHOP_SALE_POINT_TYPE.CASH,
                        product_id: invenPurchase.productId,
                        product_name: (_c = (_b = productNames[invenPurchase.appStoreCd]) === null || _b === void 0 ? void 0 : _b[invenPurchase.productId]) !== null && _c !== void 0 ? _c : null,
                        order_id: invenPurchase.orderId,
                        inven_id: invenPurchase.invenId,
                    });
                }
                invenRemoveds.push(...successResults);
                return null;
            });
        })
            .then(() => {
            var _a;
            const ensuredGiveItemsList = [];
            for (const invenPurchase of invenRemoveds) {
                const giveItems = (_a = ensuredGiveItemsByProductId[invenPurchase.appStoreCd]) === null || _a === void 0 ? void 0 : _a[invenPurchase.productId];
                if (giveItems) {
                    ensuredGiveItemsList.push(giveItems);
                }
            }
            if (ensuredGiveItemsList.length === 0) {
                return null;
            }
            return _receiveProducts(ensuredGiveItemsList, respBody, mailIds, user, curTimeUtc);
        })
            .then(() => {
            if (mailIds.length > 0) {
                respBody.mailIds = mailIds;
            }
            return user.sendJsonPacket(packet.seqNum, packet.type, respBody);
        })
            .catch((err) => {
            if (err instanceof BillingApiFailError) {
                mlog_1.default.error(`[${rsn}] billing-api-failed`, {
                    userId: user.userId,
                    reqBody,
                    failStep: err.step,
                    billingApiResp: err.billingApiResp,
                });
                // 빌링 API success 가 false 인 경우, resp 를 그대로 클라로 전송하도록 한다.
                // 클라에서 빌링 에러를 알 수 있도록함.
                return user.sendJsonPacket(packet.seqNum, packet.type, {
                    failStep: err.step,
                    billingApiRespForFail: err.billingApiResp,
                });
            }
            throw err;
        });
    }
}
exports.Cph_Common_BillingReceiveInvenPurchases = Cph_Common_BillingReceiveInvenPurchases;
function _isString(x) {
    return x !== undefined && x !== null && typeof x === 'string';
}
function _ensureInvenPurchase(x, extra) {
    const appStoreCd = x.appStoreCd;
    if (!_isString(appStoreCd)) {
        throw new merror_1.MError('appStoreCd-string-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }
    const productId = x.productId;
    if (!_isString(productId)) {
        throw new merror_1.MError('productId-string-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }
    const orderId = x.orderId;
    if (!_isString(orderId)) {
        throw new merror_1.MError('orderId-string-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }
    const invenId = x.invenId;
    if (!_isString(invenId)) {
        throw new merror_1.MError('invenId-string-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }
}
function _ensureChargedCoinList(x, extra, userId) {
    if (!Array.isArray(x)) {
        new merror_1.MError('array-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }
    for (const elem of x) {
        if (!Number.isInteger(elem.balance)) {
            new merror_1.MError('balance-integer-expected', merror_1.MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
        }
        // 이런 경우가 없긴 할 텐데 문서에 bigint 라고 되어있어서.
        const bInMaxSafeInteger = Math.abs(elem.balance) <= Number.MAX_SAFE_INTEGER;
        if (!bInMaxSafeInteger) {
            mlog_1.default.warn('balance, Precision may be lost!', {
                userId,
                elem,
            });
        }
    }
}
function ReceiveProducts(ensuredGiveItemsList, resp, mailIds, user, curTimeUtc, isBound = 1) {
    return _receiveProducts(ensuredGiveItemsList, resp, mailIds, user, curTimeUtc, isBound);
}
exports.ReceiveProducts = ReceiveProducts;
/**
 * 먼저 빌링 서버에 완료 통보를 하기 때문에, 이 함수에 진입하게 된다면 어떻게든 지급해야한다.
 * 못 받는 경우는 메일로 이동시키다.
 *
 * user RDB, 메모리, 업적 적용.
 * * UserChangeTask 사용을 고려해봐야함.
 */
async function _receiveProducts(ensuredGiveItemsList, resp, mailIds, user, curTimeUtc, isBound = 1) {
    var _a, _b;
    const yard = {
        itemChanges: {},
        newLastShipId: undefined,
        addedShips: [],
        eventPageProduct: undefined,
        newLastMateEquipmentId: undefined,
        addedMateEquipments: [],
        newLastShipSlotId: undefined,
        addedShipSlots: [],
        newLastMailId: undefined,
        addedMailParams: [],
        dailySubscriptions: {},
        addedMateIllusts: [],
        addedMates: [],
        addedUserTitles: {},
        pointChanges: {},
        fixedTermProducts: [],
        worldBuffNubs: [],
        addedMatePassives: [],
        addedPets: [],
    };
    const tryData = {};
    const lastMailId = user.userMails.getLastDirectMailId();
    const lastShipId = user.userFleets.getLastShipId();
    const lastMateEquipmentId = user.userMates.getLastMateEquipmentId();
    const lastShipSlotId = user.userInven.getLastShipSlotItemId();
    for (const ensuredGiveItems of ensuredGiveItemsList) {
        for (const ensuredGiveItem of ensuredGiveItems) {
            switch (ensuredGiveItem.type) {
                case BILLING_GIVE_ITEM_TYPE.EVENT_PAGE: {
                    if (!yard.eventPageProduct) {
                        yard.eventPageProduct = {
                            eventPageCmses: [],
                            changes: {},
                        };
                    }
                    const eventPageCms = cms_1.default.EventPage[ensuredGiveItem.id];
                    if ((eventPageCms.type === eventPageDesc_1.EventPageType.PASS_EVENT &&
                        user.userCashShop.hasPassEventTicket(eventPageCms)) ||
                        (eventPageCms.type === eventPageDesc_1.EventPageType.PACKAGE_EVENT &&
                            user.userCashShop.isPackageEventUnlocked(eventPageCms))) {
                        // TODO 특권 같은 상품이 활성화된 상태에서 구입되어 보관함에 들어간 경우에 대해 고려해봐야함.
                        mlog_1.default.error(`[${rsn}] already-bought-event-page-product-so-ignored`, {
                            userId: user.userId,
                            eventPgaeCmsId: eventPageCms.id,
                        });
                        break;
                    }
                    yard.eventPageProduct.eventPageCmses.push(eventPageCms);
                    const offset = Math.floor(eventPageCms.id / 32);
                    if (yard.eventPageProduct.changes[offset] === undefined) {
                        yard.eventPageProduct.changes[offset] =
                            user.userCashShop.getEventPageProductIdxField(offset);
                    }
                    yard.eventPageProduct.changes[offset] =
                        (yard.eventPageProduct.changes[offset] | (1 << eventPageCms.id % 32)) >>> 0;
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.ITEM: {
                    if (!tryData.userInven) {
                        tryData.userInven = user.userInven.clone();
                    }
                    const itemCms = cms_1.default.Item[ensuredGiveItem.id];
                    const amount = ensuredGiveItem.amount;
                    const bIsBound = isBound !== 0 ? true : false;
                    const ret = tryData.userInven.itemInven.calcReceivable(itemCms.id, amount, bIsBound);
                    if (ret.receivable === amount) {
                        yard.itemChanges[itemCms.id] = tryData.userInven.itemInven.buildItemChange(itemCms.id, amount, true, bIsBound);
                    }
                    else {
                        const extra = {
                            isBound,
                            isAccum: 1,
                        };
                        const attachment = {
                            Type: rewardDesc_1.REWARD_TYPE.ITEM,
                            Id: itemCms.id,
                            Quantity: amount,
                            Extra: JSON.stringify(extra),
                        };
                        const mail = _buildMailParam(lastMailId + yard.addedMailParams.length + 1, cms_1.default.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
                        curTimeUtc, JSON.stringify([attachment]));
                        yard.newLastMailId = mail.id;
                        yard.addedMailParams.push(mail);
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.SHIP: {
                    if (!tryData.userInven) {
                        tryData.userInven = user.userInven.clone();
                    }
                    const shipCms = cms_1.default.Ship[ensuredGiveItem.id];
                    const amount = 1; // elem.amount;
                    const rndStats = (0, ship_1.makeShipRndStats)(shipCms.shipBlueprintId, cms_1.default.Const.CashShipBlueprintRatio.value);
                    const defaultDurability = cmsEx.shipDefaultDurability(shipCms.shipBlueprintId, rndStats);
                    const defaultLife = cmsEx.shipDefaultLife(shipCms.shipBlueprintId);
                    const shipExtra = {
                        enchantCount: 0,
                        enchantedStats: [],
                        enchantResult: null,
                        life: defaultLife,
                        rndStats,
                        isBound,
                    };
                    const curDockShipsCount = user.userFleets.getShipsCountByAssignment(enum_1.SHIP_ASSIGNMENT.DOCK) +
                        yard.addedShips.length;
                    const maxDockShipCount = fleet_1.default.getMaxShipCount(enum_1.SHIP_ASSIGNMENT.DOCK, cmsEx.NoFleetIndex, user.level, tryData.userInven);
                    if (curDockShipsCount < maxDockShipCount) {
                        const shipId = lastShipId + yard.addedShips.length + 1;
                        const ship = {
                            id: shipId,
                            cmsId: shipCms.id,
                            assignment: enum_1.SHIP_ASSIGNMENT.DOCK,
                            fleetIndex: cmsEx.NoFleetIndex,
                            formationIndex: 0,
                            sailor: 0,
                            durability: defaultDurability,
                            permanentDamage: 0,
                            slots: {},
                            cargos: {},
                            name: null,
                            life: shipExtra.life,
                            isLocked: 0,
                            enchantedStats: shipExtra.enchantedStats,
                            enchantResult: shipExtra.enchantResult,
                            enchantCount: shipExtra.enchantCount,
                            rndStats: shipExtra.rndStats,
                            battleQuickSkills: ship_1.default.defaultBattleQuickSkills(),
                            isBound,
                            guid: `${user.userId}:${shipId}`,
                        };
                        yard.newLastShipId = ship.id;
                        yard.addedShips.push(ship);
                    }
                    else {
                        const extra = {
                            ...shipExtra,
                            guid: undefined,
                            isAccum: 1,
                        };
                        const attachment = {
                            Type: rewardDesc_1.REWARD_TYPE.SHIP,
                            Id: shipCms.id,
                            Quantity: amount,
                            Extra: JSON.stringify(extra),
                        };
                        const mail = _buildMailParam(lastMailId + yard.addedMailParams.length + 1, cms_1.default.Const.CashShopShipMailId.value, curTimeUtc, JSON.stringify([attachment]));
                        yard.newLastMailId = mail.id;
                        yard.addedMailParams.push(mail);
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.CEQUIP: {
                    if (!tryData.userInven) {
                        tryData.userInven = user.userInven.clone();
                    }
                    if (!tryData.mates) {
                        tryData.mates = user.userMates.clone();
                    }
                    const equipCms = cms_1.default.CEquip[ensuredGiveItem.id];
                    const amount = ensuredGiveItem.amount;
                    const addable = tryData.mates.calcMateEquipmentAddableForSpace(equipCms, amount, tryData.userInven, 0);
                    // 항해사 외형 장비 => 현금 결제 상품은  비귀속 처리
                    if (cmsEx.isCostumeEquipType(equipCms.type)) {
                        isBound = 0;
                    }
                    if (addable === amount) {
                        for (let i = 0; i < amount; i++) {
                            const newItem = tryData.mates.buildMateEquipmentNub(equipCms.id, 0, isBound, 0, curTimeUtc);
                            yard.newLastMateEquipmentId = newItem.id;
                            yard.addedMateEquipments.push(newItem);
                            tryData.mates.addMateEquipment(newItem, null);
                        }
                    }
                    else {
                        const extra = {
                            isBound,
                            isAccum: 1,
                            expireTimeUtc: equipCms.expireType === cmsEx.EXPIRE_TYPE.PROVIDE
                                ? curTimeUtc + equipCms.expireTime
                                : undefined,
                            enchantLv: 0,
                        };
                        const attachment = {
                            Type: rewardDesc_1.REWARD_TYPE.MATE_EQUIP,
                            Id: equipCms.id,
                            Quantity: amount,
                            Extra: JSON.stringify(extra),
                        };
                        const mail = _buildMailParam(lastMailId + yard.addedMailParams.length + 1, cms_1.default.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
                        curTimeUtc, JSON.stringify([attachment]));
                        yard.newLastMailId = mail.id;
                        yard.addedMailParams.push(mail);
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.SHIP_SLOT: {
                    if (!tryData.userInven) {
                        tryData.userInven = user.userInven.clone();
                    }
                    const shipSlotCms = cms_1.default.ShipSlot[ensuredGiveItem.id];
                    const amount = ensuredGiveItem.amount;
                    const added = tryData.userInven.calcShipSlotItemAddable(amount);
                    if (added === amount) {
                        for (let i = 0; i < amount; i++) {
                            const shipSlotItem = tryData.userInven.buildShipSlotItem(shipSlotCms.id, isBound, 0, curTimeUtc);
                            yard.addedShipSlots.push(shipSlotItem);
                            yard.newLastShipSlotId = shipSlotItem.id;
                            tryData.userInven.addShipSlotItem(shipSlotItem, null);
                        }
                    }
                    else {
                        const extra = {
                            isBound,
                            isAccum: 1,
                            expireTimeUtc: shipSlotCms.expireType === cmsEx.EXPIRE_TYPE.PROVIDE
                                ? curTimeUtc + shipSlotCms.expireTime
                                : undefined,
                            enchantLv: 0,
                        };
                        const attachment = {
                            Type: rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM,
                            Id: shipSlotCms.id,
                            Quantity: amount,
                            Extra: JSON.stringify(extra),
                        };
                        const mail = _buildMailParam(lastMailId + yard.addedMailParams.length + 1, cms_1.default.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
                        curTimeUtc, JSON.stringify([attachment]));
                        yard.newLastMailId = mail.id;
                        yard.addedMailParams.push(mail);
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.DAILY_SUBSCRIPTION: {
                    if (!tryData.cashShop) {
                        tryData.cashShop = user.userCashShop.clone();
                    }
                    const dailySubscriptionCms = cms_1.default.DailySubscription[ensuredGiveItem.id];
                    tryData.cashShop.buyDailySubscription(dailySubscriptionCms, curTimeUtc);
                    const userDS = tryData.cashShop.getDailySubscription(ensuredGiveItem.id, curTimeUtc);
                    yard.dailySubscriptions[ensuredGiveItem.id] = {
                        cmsId: ensuredGiveItem.id,
                        createTimeUtc: userDS.createTimeUtc,
                        expireTimeUtc: userDS.expireTimeUtc,
                        lastReceiveTimeUtc: userDS.lastReceiveTimeUtc,
                    };
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.ILLUST_SKIN: {
                    yard.addedMateIllusts.push(ensuredGiveItem.id);
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.MATE: {
                    if (!tryData.mates) {
                        tryData.mates = user.userMates.clone();
                    }
                    const mateCms = cms_1.default.Mate[ensuredGiveItem.id];
                    if (tryData.mates.hasMate(ensuredGiveItem.id)) {
                        if (!tryData.userInven) {
                            tryData.userInven = user.userInven.clone();
                        }
                        const itemCms = cms_1.default.Item[mateCms.reRecruitRewardItemId];
                        const amount = cms_1.default.Const.CashShopDuplicatedMateToPiece.value +
                            ((_b = (_a = yard.itemChanges[itemCms.id]) === null || _a === void 0 ? void 0 : _a.count) !== null && _b !== void 0 ? _b : 0);
                        const ret = tryData.userInven.itemInven.calcReceivable(itemCms.id, amount, true);
                        if (ret.receivable === amount) {
                            yard.itemChanges[itemCms.id] = tryData.userInven.itemInven.buildItemChange(itemCms.id, amount, true, true);
                        }
                        else {
                            const extra = {
                                isBound: 1,
                                isAccum: 1,
                            };
                            const attachment = {
                                Type: rewardDesc_1.REWARD_TYPE.ITEM,
                                Id: itemCms.id,
                                Quantity: amount,
                                Extra: JSON.stringify(extra),
                            };
                            const mail = _buildMailParam(lastMailId + yard.addedMailParams.length + 1, cms_1.default.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
                            curTimeUtc, JSON.stringify([attachment]));
                            yard.newLastMailId = mail.id;
                            yard.addedMailParams.push(mail);
                        }
                    }
                    else {
                        const mateNub = mate_1.default.defaultNub(ensuredGiveItem.id);
                        yard.addedMates.push(mateNub);
                        tryData.mates.addNewMate(mateNub, null, null, undefined, undefined);
                        if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
                            for (const cequipCmsId of mateCms.CEquipId) {
                                const newItem = tryData.mates.buildMateEquipmentNub(cequipCmsId, mateCms.id, 1, 0, curTimeUtc);
                                yard.newLastMateEquipmentId = newItem.id;
                                yard.addedMateEquipments.push(newItem);
                                tryData.mates.addMateEquipment(newItem, null);
                                tryData.mates.equipMateEquipment(newItem.equippedMateCmsId, newItem.id, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined);
                            }
                        }
                        const addedPassives = mate_1.MateUtil.getRelationChronicleLearnablePassives(tryData.mates, user.questManager, mateCms.id);
                        if (addedPassives) {
                            yard.addedMatePassives.push(...addedPassives);
                        }
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.SERVER_TRANFER: {
                    // 서버 이전권 상품의 경우 라인측에 수령 완료 통보만 하고 구성품 수령 작업은 하지 않는다.
                    // 빌링 툴에서 상품을 등록하려면 최소 1개의 구성품을 등록해야하는 이슈가 있음.
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.USER_TITLE: {
                    if (!tryData.userTitles) {
                        tryData.userTitles = user.userTitles.clone();
                    }
                    const newUserTitle = tryData.userTitles.buildUserTitle(ensuredGiveItem.id, curTimeUtc);
                    tryData.userTitles.setUserTitle(newUserTitle, null);
                    yard.addedUserTitles[ensuredGiveItem.id] = {
                        cmsId: newUserTitle.cmsId,
                        expiredTimeUtc: newUserTitle.expiredTimeUtc,
                        isEquipped: newUserTitle.isEquipped,
                    };
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.POINT: {
                    const pointCms = cms_1.default.Point[ensuredGiveItem.id];
                    const amount = ensuredGiveItem.amount;
                    if (pointCms.id === cmsEx.EnergyPointCmsId) {
                        if (!tryData.userEnergy) {
                            tryData.userEnergy = user.userEnergy.clone();
                        }
                        yard.energyChange = tryData.userEnergy.buildEnergyChangeWithConsume(curTimeUtc, user.level, user.level, -amount, false);
                        tryData.userEnergy.applyEnergyChange(yard.energyChange, { user, rsn, add_rsn });
                    }
                    else {
                        if (!tryData.userPoints) {
                            tryData.userPoints = user.userPoints.clone();
                        }
                        if (!yard.pointChanges[pointCms.id]) {
                            yard.pointChanges[pointCms.id] = {
                                cmsId: pointCms.id,
                                value: tryData.userPoints.getPoint(pointCms.id),
                            };
                        }
                        yard.pointChanges[pointCms.id].value += amount;
                        tryData.userPoints.applyPointChanges(lodash_1.default.values(yard.pointChanges), {
                            user,
                            rsn,
                            add_rsn,
                        });
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.WORLD_BUFF: {
                    const cashShopCms = cms_1.default.CashShop[ensuredGiveItem.id];
                    const oldBuffProduct = user.userCashShop.getFixedTermProductByGroup(cashShopCms.buffGroup, curTimeUtc);
                    // 현금 상품에서 지급하는 버프는 보관함에서 수령하는 타이밍때문에 buffGroupLevel를 적용할 수 없어서 레벨에 관한 처리는 무시한다
                    let newFixedTermProduct = {
                        cmsId: cashShopCms.id,
                        startTimeUtc: oldBuffProduct ? oldBuffProduct.startTimeUtc : curTimeUtc,
                        endTimeUtc: oldBuffProduct ? oldBuffProduct.endTimeUtc : curTimeUtc,
                    };
                    // 만약에 기획 데이터 실수로 중복되는 버프가 이미 있어도 시간 더해 준다.
                    let addEndTimeUtc = (0, cashShopDesc_1.getWorldBuffAddTime)(cashShopCms);
                    newFixedTermProduct.endTimeUtc += addEndTimeUtc;
                    yard.fixedTermProducts.push(newFixedTermProduct);
                    for (const buffElem of cashShopCms.worldBuffElems) {
                        const worldBuffCms = cms_1.default.WorldBuff[buffElem.worldBuffId];
                        // worldBuffElem 에 각각 시간 타임 값을 추가 했지만..
                        // 기존 처럼 상점 시간을 사용한다.
                        const startTimeUtc = newFixedTermProduct.startTimeUtc;
                        const endTimeUtc = newFixedTermProduct.endTimeUtc;
                        const wbNub = userBuffs_1.WorldBuffUtil.makeNubWithCustomEndTime(worldBuffCms, cmsEx.FirstFleetIndex, cmsEx.WorldBuffSourceType.BILLING, cashShopCms.id, startTimeUtc, endTimeUtc);
                        yard.worldBuffNubs.push(wbNub);
                    }
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.PET: {
                    if (!tryData.userPets) {
                        tryData.userPets = user.userPets.clone();
                    }
                    const petCms = cms_1.default.Pet[ensuredGiveItem.id];
                    tryData.userPets.addNewPet(petCms.id);
                    yard.addedPets.push(petCms.id);
                    break;
                }
                case BILLING_GIVE_ITEM_TYPE.DUMMY:
                    break;
                default:
                    // 모든 case 가 직접 대응되어야 됨.
                    assert_1.default.fail('all-billing-give-item-type-must-be-mapped');
            }
        }
    }
    return Promise.resolve()
        .then(() => {
        var _a, _b;
        const { userDbConnPoolMgr } = typedi_1.Container.get(server_1.LobbyService);
        return (0, tuBillingReceiveInvenPurchases_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, lodash_1.default.values(yard.itemChanges), yard.newLastShipId, yard.addedShips, ((_a = yard.eventPageProduct) === null || _a === void 0 ? void 0 : _a.changes)
            ? lodash_1.default.map((_b = yard.eventPageProduct) === null || _b === void 0 ? void 0 : _b.changes, (idxField, strOffset) => {
                return {
                    offset: parseInt(strOffset, 10),
                    idxField,
                };
            })
            : undefined, yard.newLastMateEquipmentId, yard.addedMateEquipments, yard.newLastShipSlotId, yard.addedShipSlots, yard.newLastMailId, yard.addedMailParams, Object.values(yard.dailySubscriptions), yard.addedMateIllusts, yard.addedMates, Object.values(yard.addedUserTitles), yard.energyChange, Object.values(yard.pointChanges), yard.fixedTermProducts, yard.addedMatePassives, yard.addedPets);
    })
        .then(() => {
        const accums = [];
        if (yard.eventPageProduct) {
            for (const eventPageCms of yard.eventPageProduct.eventPageCmses) {
                user.userCashShop.addEventPageProduct(eventPageCms);
            }
            lodash_1.default.merge(resp.sync, {
                add: { eventPageProducts: yard.eventPageProduct.changes },
            });
        }
        lodash_1.default.forOwn(yard.itemChanges, (change) => {
            lodash_1.default.merge(resp.sync, user.userInven.itemInven.applyItemChange(change, accums, { user, rsn, add_rsn }));
        });
        for (const addedShip of yard.addedShips) {
            user.userFleets.addNewShip(addedShip, user.companyStat, user.userShipBlueprints, user.userMates, user.userFleets, user.userInven, user.userBuffs, user.userPassives, user.userCollection, user.questManager, user.userSailing, user.userTriggers, user.userNation, user.userResearch, { user, rsn, add_rsn }, resp.sync);
            lodash_1.default.merge(resp.sync, {
                add: {
                    ships: {
                        [addedShip.id]: addedShip,
                    },
                },
            });
        }
        for (const elem of yard.addedMateEquipments) {
            lodash_1.default.merge(resp.sync, user.userMates.addMateEquipment(elem, { user, rsn, add_rsn }));
        }
        for (const elem of yard.addedShipSlots) {
            user.userInven.addShipSlotItem(elem, { user, rsn, add_rsn });
            lodash_1.default.merge(resp.sync, {
                add: {
                    shipSlotItems: {
                        [elem.id]: {
                            id: elem.id,
                            isBound: elem.isBound,
                            shipSlotCmsId: elem.shipSlotCmsId,
                            isLocked: elem.isLocked,
                            expireTimeUtc: elem.expireTimeUtc,
                            enchantLv: elem.enchantLv,
                        },
                    },
                },
            });
        }
        for (const mailCreatingParam of yard.addedMailParams) {
            const mail = user.userMails.addDirectMail(mailCreatingParam, { user, rsn, add_rsn });
            lodash_1.default.merge(resp.sync, {
                add: { userDirectMails: { [mail.id]: user.userMails.getDirectMailSyncData(mail.id) } },
            });
            mailIds.push(mail.id);
        }
        lodash_1.default.forOwn(yard.dailySubscriptions, (elem) => {
            user.userCashShop.setDailySubscription(elem);
            const userDS = user.userCashShop.getDailySubscription(elem.cmsId, curTimeUtc);
            lodash_1.default.merge(resp.sync, {
                add: {
                    dailySubscriptions: {
                        [elem.cmsId]: userDS,
                    },
                },
            });
        });
        for (const illustCmsId of yard.addedMateIllusts) {
            const illustSkinCms = cms_1.default.IllustSkin[illustCmsId];
            const userMate = user.userMates.getMate(illustSkinCms.mateId);
            userMate.addIllust(user, illustCmsId, resp.sync);
        }
        for (const mateNub of yard.addedMates) {
            const mateCmsId = mateNub.cmsId;
            user.userMates.addNewMate(mateNub, user.companyStat, user, { user, rsn, add_rsn }, resp);
            lodash_1.default.merge(resp.sync, {
                add: {
                    mates: {
                        [mateCmsId]: user.userMates.getMate(mateCmsId).getNub(),
                    },
                },
            });
        }
        for (const elem of yard.addedMatePassives) {
            const userMate = user.userMates.getMate(elem.mateCmsId);
            userMate.addPassive(elem.passiveCmsId, 0, resp.sync);
        }
        lodash_1.default.forOwn(yard.addedUserTitles, (userTitle) => {
            if (userTitle.isEquipped) {
                user.userTitles.equipUserTitle(user, userTitle, curTimeUtc, resp);
                user.userTitles.notifyCurTitleChanged(user, curTimeUtc);
            }
            else {
                user.userTitles.setUserTitle(userTitle, resp);
            }
        });
        if (yard.energyChange) {
            lodash_1.default.merge(resp.sync, user.userEnergy.applyEnergyChange(yard.energyChange, { user, rsn, add_rsn }));
        }
        if (yard.pointChanges) {
            lodash_1.default.merge(resp.sync, user.userPoints.applyPointChanges(lodash_1.default.values(yard.pointChanges), { user, rsn, add_rsn }));
        }
        if (yard.fixedTermProducts.length > 0) {
            const cashShopFixedTermProducts = {};
            for (const fixedTermProduct of yard.fixedTermProducts) {
                user.userCashShop.setFixedTermProduct(fixedTermProduct);
                cashShopFixedTermProducts[fixedTermProduct.cmsId] = fixedTermProduct;
            }
            lodash_1.default.merge(resp.sync, {
                add: { cashShopFixedTermProducts: cashShopFixedTermProducts },
            });
        }
        for (const wbNub of yard.worldBuffNubs) {
            user.userBuffs.addSingleBuffByWBNub(wbNub, user, resp);
        }
        for (const petCmsId of yard.addedPets) {
            user.userPets.addNewPet(petCmsId);
            lodash_1.default.merge(resp.sync, {
                add: {
                    pets: {
                        [petCmsId]: {
                            cmsId: petCmsId,
                        },
                    },
                },
            });
        }
        // CMS.AchievementTerms
        const shipsGroupedByCmsId = lodash_1.default.groupBy(yard.addedShips, (v) => v.cmsId);
        lodash_1.default.forOwn(shipsGroupedByCmsId, (ships, strShipCmsId) => {
            const shipCms = cms_1.default.Ship[strShipCmsId];
            const shipCount = ships.length;
            accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SHIP,
                addedValue: shipCount,
            });
            accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_SHIP,
                targets: [shipCms.id],
                addedValue: shipCount,
            });
            const userBP = user.userShipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
            const userBpLv = userBP ? userBP.level : 1;
            for (let bpLv = 1; bpLv <= userBpLv; bpLv++) {
                accums.push({
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SHIP,
                    targets: [bpLv],
                    addedValue: shipCount,
                });
                accums.push({
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP,
                    targets: [shipCms.id, bpLv],
                    addedValue: shipCount,
                });
            }
        });
        for (const mateNub of yard.addedMates) {
            const mateCmsId = mateNub.cmsId;
            const mateCms = cms_1.default.Mate[mateCmsId];
            accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
                targets: [mateCms.nationId],
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
                targets: [mateCmsId],
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
                targets: [mateCmsId],
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
                targets: [mateCmsId],
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
                targets: [mateCmsId],
                addedValue: 1,
            }, {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
                targets: [mateCmsId],
                addedValue: 3,
            });
            // language
            for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(mateCmsId); i++) {
                const accum = {
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
                    targets: [i],
                    addedValue: 1,
                };
                accums.push(accum);
            }
            // admiral
            if (mateCms.character.charType === cmsEx.CHARACTER_TYPE.LEADERABLE_MATE) {
                accums.push({
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
                    addedValue: 1,
                }, {
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
                    targets: [mateCmsId],
                    addedValue: 1,
                });
                // royal title
                const admiralCms = cmsEx.getAdmiralByMateCmsId(mateCmsId);
                const mateRoyalTitle = admiralCms.startRoyalTitelId;
                let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);
                if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
                    for (const target of targets) {
                        if (mateRoyalTitle < target) {
                            break;
                        }
                        if (target !== mateRoyalTitle) {
                            continue;
                        }
                        accums.push({
                            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
                            targets: [target],
                            addedValue: 1,
                        });
                    }
                }
                targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL, 1);
                if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
                    for (const target of targets) {
                        if (mateRoyalTitle < target) {
                            break;
                        }
                        if (target !== mateRoyalTitle) {
                            continue;
                        }
                        accums.push({
                            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
                            targets: [mateCmsId, target],
                            addedValue: 1,
                        });
                    }
                }
            }
        }
        // glog
        lodash_1.default.forOwn(yard.dailySubscriptions, (elem) => {
            const expireDate = new Date(elem.expireTimeUtc * 1000);
            user.glog('common_active_iap', {
                rsn,
                add_rsn,
                id: elem.cmsId,
                expire_date: (0, moment_1.default)(expireDate).format('YYYY-MM-DD HH:mm:ss'),
            });
        });
        return user.userAchievement.accumulate(accums, user, resp.sync, {
            user,
            rsn,
            add_rsn,
        });
    });
}
function _buildMailParam(mailId, mailCmsId, curTimeUtc, attachment) {
    const mailCms = cms_1.default.Mail[mailCmsId];
    let expireTimeUtc = null;
    let bShouldSetExpirationWhenReceiveAttachment = 0;
    if (mailCms.mailKeepTime > 0) {
        expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
    }
    else if (mailCms.mailKeepTime === -1) {
        bShouldSetExpirationWhenReceiveAttachment = 1;
    }
    return new mailBuilder_1.BuilderMailCreateParams(mailId, mailCms.id, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, null, null, null, attachment).getParam();
}
//# sourceMappingURL=billingReceiveInvenPurchases.js.map