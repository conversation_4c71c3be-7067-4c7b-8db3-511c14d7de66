{"version": 3, "file": "changeNation.js", "sourceRoot": "", "sources": ["../../../../src/lobbyd/packetHandler/common/changeNation.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;;;;;;;;;;;;;;;;;;;;AAE/E,mCAAmC;AACnC,oDAAuB;AAGvB,uDAA+B;AAC/B,uDAAyC;AACzC,oEAA4C;AAC5C,qDAA8D;AAC9D,yCAA4C;AAG5C,yDAAiE;AACjE,wEAM6C;AAC7C,4EAAsE;AACtE,gFAOiD;AAEjD,kEAA0C;AAC1C,mDAAgD;AAChD,+DAAiD;AAGjD,wDAAsD;AAEtD,+CAA4C;AAC5C,8CAA8D;AAE9D,yEAAsE;AACtE,iDAAuD;AACvD,+DAA4D;AAC5D,sEAA8C;AAE9C,+EAA+E;AAC/E,qCAAqC;AACrC,+EAA+E;AAE/E,MAAM,GAAG,GAAG,eAAe,CAAC;AAC5B,MAAM,OAAO,GAAG,IAAI,CAAC;AAYrB,+EAA+E;AAC/E,MAAa,uBAAuB;IAClC,6EAA6E;IAC7E,gBAAe,CAAC;IAEhB,6EAA6E;IAC7E,aAAa,CAAC,IAAU;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6EAA6E;IAC7E,IAAI,CAAC,IAAU,EAAE,MAAe;QAC9B,4BAA4B;QAC5B,IAAI,CAAC,eAAe,CAAC,iCAAgB,CAAC,SAAS,CAAC,CAAC;QAEjD,MAAM,IAAI,GAAY,MAAM,CAAC,OAAO,CAAC;QAErC,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAE7B,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,kBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;QAC9E,MAAM,WAAW,GAAG,kBAAS,CAAC,GAAG,CAAC,yBAAW,CAAC,CAAC;QAE/C,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnC,wBAAwB;QACxB,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACxD,MAAM,IAAI,eAAM,CAAC,sBAAsB,EAAE,mBAAU,CAAC,aAAa,CAAC,CAAC;SACpE;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9C,gCAAgC;QAChC,cAAc;QACd,+FAA+F;QAC/F,IAAI;QAEJ,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,cAAc,KAAK,CAAC,IAAI,cAAc,KAAK,WAAW,EAAE;YAC1D,MAAM,IAAI,eAAM,CAAC,2BAA2B,EAAE,mBAAU,CAAC,kCAAkC,CAAC,CAAC;SAC9F;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE;YAC9D,MAAM,IAAI,eAAM,CAAC,iBAAiB,EAAE,mBAAU,CAAC,eAAe,EAAE;gBAC9D,IAAI;aACL,CAAC,CAAC;SACJ;QAED,2BAA2B;QAC3B,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,CACjD,KAAK,CAAC,+BAA+B,CAAC,aAAa,EACnD,IAAI,CACL,CAAC;QAEF,oBAAoB;QACpB,IACE,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,uBAAuB,GAAG,aAAG,CAAC,KAAK,CAAC,6BAA6B,CAAC,KAAK,GAAG,OAAO,EACtF;YACA,MAAM,IAAI,eAAM,CAAC,6BAA6B,EAAE,mBAAU,CAAC,qBAAqB,EAAE;gBAChF,IAAI;gBACJ,OAAO;aACR,CAAC,CAAC;SACJ;QAED,kBAAkB;QAClB,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,kBAAkB,GAAqC,EAAE,CAAC;QAChE,MAAM,yBAAyB,GAAa,EAAE,CAAC;QAC/C,MAAM,eAAe,GAAG,IAAA,oCAA0B,EAChD,OAAO,EACP,aAAG,CAAC,MAAM,CAAC,+BAA+B,CAC3C,CAAC;QAEF,sCAAsC;QACtC,IAAI,uBAAU,CAAC,gCAAgC,CAAC,OAAO,CAAC,EAAE;YACxD,MAAM,IAAI,eAAM,CACd,uCAAuC,EACvC,mBAAU,CAAC,qCAAqC,EAChD,EAAE,WAAW,EAAE,eAAe,EAAE,CACjC,CAAC;SACH;QAED,KAAK,MAAM,OAAO,IAAI,gBAAC,CAAC,MAAM,CAAC,aAAG,CAAC,IAAI,CAAC,EAAE;YACxC,IAAI,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;gBACvE,qBAAqB;gBACrB,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAClC,eAAe;gBACf,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACjC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;aACvC;iBAAM,IACL,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC,YAAY;gBACpD,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC,aAAa,EACrD;gBACA,sBAAsB;gBACtB,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aACnC;SACF;QAED,IAAI,QAAQ,GAAQ,EAAE,CAAC;QACvB,IACE,uBAAU,CAAC,wBAAwB,CACjC,aAAa,CAAC,8BAA8B,CAAC,WAAW,CAAC,EACzD,OAAO,EACP,QAAQ,CACT,EACD;YACA,MAAM,IAAI,eAAM,CACd,qCAAqC,EACrC,mBAAU,CAAC,mCAAmC,EAC9C;gBACE,OAAO;gBACP,QAAQ;aACT,CACF,CAAC;SACH;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC,cAAc,CAAW,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;SACzF;QAED,kBAAkB;QAClB,IAAI,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;YACtE,OAAO,IAAI,CAAC,cAAc,CAAW,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBAC/D,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;SACJ;QAED,kCAAkC;QAClC,0DAA0D;QAC1D,gCAAgC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,UAAU,GAAG,aAAa,CAAC,gCAAgC,EAAE;YAC/D,MAAM,IAAI,eAAM,CAAC,uBAAuB,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;SAC7E;QAED,0BAA0B;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,yBAAyB,CACjE,KAAK,CAAC,cAAc,CAAC,WAAW,CACjC,CAAC;QACF,IAAI,eAAe,EAAE;YACnB,MAAM,IAAI,eAAM,CACd,uCAAuC,EACvC,mBAAU,CAAC,qCAAqC,EAChD;gBACE,oBAAoB,EAAE,eAAe,CAAC,KAAK;aAC5C,CACF,CAAC;SACH;QAED,EAAE;QACF,MAAM,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAE3D,aAAa;QACb,MAAM,iBAAiB,GAA2B,EAAE,CAAC;QACrD,MAAM,yCAAyC,GAC7C,IAAI,CAAC,cAAc,CAAC,8BAA8B,CAChD,WAAW,EACX,cAAc,EACd,KAAK,CAAC,uBAAuB,CAAC,aAAa,EAC3C,iBAAiB,EACjB,OAAO,CACR,CAAC;QAEJ,WAAW;QACX,+DAA+D;QAC/D,MAAM,4BAA4B,GAAgD,CAAC,GAAG,EAAE;YACtF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBACtC,cAAI,CAAC,KAAK,CACR,IAAI,GAAG,wEAAwE,cAAc,EAAE,EAC/F,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CACxB,CAAC;gBACF,OAAO,SAAS,CAAC;aAClB;YACD,MAAM,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;gBACrE,cAAI,CAAC,IAAI,CAAC,IAAI,GAAG,wDAAwD,EAAE;oBACzE,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,WAAW;oBACX,UAAU;oBACV,cAAc;oBACd,aAAa;iBACd,CAAC,CAAC;gBACH,OAAO,SAAS,CAAC;aAClB;YACD,MAAM,gBAAgB,GAAG,UAAU,GAAG,aAAa,CAAC,CAAC,eAAe;YACpE,IAAI,CAAC,gBAAgB,EAAE;gBACrB,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,KAAK,GAAW,aAAG,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,aAAa;YAC9D,MAAM,cAAc,GAAG;gBACrB,KAAK,CAAC,QAAQ,CAAC,SAAS;gBACxB,KAAK,CAAC,QAAQ,CAAC,KAAK;gBACpB,KAAK,CAAC,QAAQ,CAAC,MAAM;aACb,CAAC;YACX,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEjE,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBACpC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC5C,OAAO;oBACL,OAAO;oBACP,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;iBACjE,CAAC;YACJ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,EAAE,CAAC;QAEL,iBAAiB;QACjB,IAAI,eAAuB,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,MAAM,sBAAsB,GAC1B,aAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;YAC5E,eAAe,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;SAC3E;QAED,IAAI,IAAI,GAAW,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAC7D,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,GAAG,CAAC,CAAC;SACV;QAED,uBAAuB;QACvB,MAAM,qBAAqB,GAAG,KAAK,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,CAAC,qBAAqB,EAAE;YAC1B,MAAM,IAAI,eAAM,CAAC,iCAAiC,EAAE,mBAAU,CAAC,YAAY,CAAC,CAAC;SAC9E;QAED,MAAM,yBAAyB,GAAG,aAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAElE,MAAM,WAAW,GAAiB,EAAE,CAAC;QACrC,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,IAAI,4BAA4B,GAAG,CAAC,CAAC;QACrC,IAAI,UAA0B,CAAC;QAC/B,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;YACxC,4CAA4C;YAC5C,QAAQ,CAAC,IAAI,CACX,SAAS,CAAC,4BAA4B,CAAC,CACrC,IAAI,CAAC,MAAM,EACX,SAAS,EACT,eAAe,EACf,IAAI,CAAC,SAAS,CAAC,OAAO,CACvB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;gBACb,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACtC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,aAAG,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;gBAC1E,IAAI,KAAK,GAAG,CAAC,EAAE;oBACb,4BAA4B,IAAI,IAAI,CAAC,IAAI,CACvC,CAAC,KAAK;wBACJ,aAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK;wBACrC,aAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK,CAAC;wBAC5C,IAAI;wBACJ,yBAAyB,CAAC,MAAM,CACnC,CAAC;oBACF,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;wBAClC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAC3C;iBACF;YACH,CAAC,CAAC,CACH,CAAC;SACH;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;aACzB,IAAI,CAAC,GAAG,EAAE;YACT,UAAU,GAAG,IAAI,+BAAc,CAC7B,IAAI,EACJ,mCAAkB,CAAC,aAAa,EAChC,IAAI,gBAAgB,CAClB,WAAW,EACX,OAAO,EACP,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EACnC,IAAI,EACJ,eAAe,EACf;gBACE,MAAM,EAAE,cAAc;gBACtB,MAAM,EAAE,WAAW;gBACnB,oBAAoB,EAAE,yCAAyC;aAChE,EACD,iBAAiB,EACjB,4BAA4B,EAC5B,WAAW,EACX,yBAAyB,CAAC,EAAE,EAC5B,4BAA4B,CAC7B,CACF,CAAC;YACF,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;YACjC,IAAI,GAAG,KAAK,mCAAkB,CAAC,EAAE,EAAE;gBACjC,MAAM,IAAI,eAAM,CACd,+BAA+B,EAC/B,mBAAU,CAAC,6BAA6B,EACxC;oBACE,GAAG;iBACJ,CACF,CAAC;aACH;YAED,gBAAgB;YAChB,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,cAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,WAAW;gBACX,IAAI;gBACJ,eAAe;gBACf,4BAA4B;gBAC5B,4BAA4B;gBAC5B,gBAAgB;gBAChB,eAAe;aAChB,CAAC,CAAC;YACH,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE;gBACvC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;aAClF;YACD,KAAK,MAAM,SAAS,IAAI,yBAAyB,EAAE;gBACjD,QAAQ,CAAC,IAAI,CACX,SAAS,CAAC,qCAAqC,CAAC,CAC9C,IAAI,CAAC,MAAM,EACX,SAAS,EACT,eAAe,CAChB,CACF,CAAC;aACH;YAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,UAAU,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,QAAQ,EAAE;gBACZ,MAAM,OAAO,GAAG,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACnD,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBACnF,cAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE;wBACvD,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YAED,eAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACnE,cAAI,CAAC,KAAK,CAAC,sCAAsC,EAAE;oBACjD,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,WAAW;oBACX,GAAG,EAAE,GAAG,CAAC,OAAO;iBACjB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,eAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE9C,OAAO,qBAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,SAAe,EAAE,EAAE;YACxB,gBAAC,CAAC,KAAK,CAAa,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAE1C,OAAO,IAAA,gCAAmB,EAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,MAAM,iBAAiB,GAAG,kBAAS,CAAC,GAAG,CAAC,qCAAiB,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,kBAAS,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,gBAAM,CAAC,CAAC;YAExD,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,OAAO,MAAM,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;aAC3D;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO;YACP,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC;YAClD,MAAM,aAAa,GAAG,aAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,aAAG,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,YAAY,GAAG,aAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,GAAG;gBACH,OAAO;gBACP,UAAU,EAAE,YAAY,CAAC,IAAI;gBAC7B,UAAU,EAAE,YAAY,CAAC,IAAI;gBAC7B,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK;wBACzC,GAAG,EAAE,IAAI;qBACV;iBACF;gBACD,WAAW,EAAE,WAAW;gBACxB,aAAa,EAAE,UAAU,CAAC,eAAe,EAAE;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,GAAG;gBACH,OAAO;gBACP,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC,UAAU;gBAClC,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC;aAChE,CAAC,CAAC;YAEH,SAAS;YACT,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE;gBACvC,gBAAC,CAAC,KAAK,CAAa,IAAI,CAAC,IAAI,EAAE;oBAC7B,GAAG,EAAE;wBACH,kBAAkB,EAAE;4BAClB,CAAC,SAAS,CAAC,EAAE;gCACX,KAAK,EAAE,CAAC;6BACT;yBACF;qBACF;iBACF,CAAC,CAAC;gBAEH,MAAM,GAAG,GAA2B;oBAClC,SAAS;oBACT,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,IAAI;oBACnB,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,OAAO;iBACvB,CAAC;gBACF,WAAW,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBAC3E,cAAI,CAAC,KAAK,CAAC,oDAAoD,EAAE;wBAC/D,GAAG,EAAE,GAAG,CAAC,OAAO;wBAChB,SAAS;wBACT,MAAM,EAAE,IAAI,CAAC,MAAM;qBACpB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YACD,KAAK,MAAM,SAAS,IAAI,yBAAyB,EAAE;gBACjD,gBAAC,CAAC,KAAK,CAAa,IAAI,CAAC,IAAI,EAAE;oBAC7B,GAAG,EAAE;wBACH,kBAAkB,EAAE;4BAClB,CAAC,SAAS,CAAC,EAAE;gCACX,KAAK,EAAE,CAAC;6BACT;yBACF;qBACF;iBACF,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAuB;oBACjC,SAAS;oBACT,eAAe,EAAE,KAAK,CAAC,gBAAgB,CAAC,QAAQ;oBAChD,mBAAmB,EAAE,WAAW,CAAC,mBAAmB,CAClD,SAAS,EACT,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAChC;oBACD,SAAS,EAAE,eAAe;oBAC1B,aAAa,EAAE,OAAO;iBACvB,CAAC;gBAEF,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBACzE,cAAI,CAAC,KAAK,CAAC,+CAA+C,EAAE;wBAC1D,GAAG,EAAE,GAAG,CAAC,OAAO;wBAChB,SAAS;wBACT,MAAM,EAAE,IAAI,CAAC,MAAM;qBACpB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,cAAc,CAAW,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAzcD,0DAycC;AAED,MAAM,gBAAiB,SAAQ,gCAAa;IAC1C,YACE,WAAmB,EACnB,uBAA+B,EACvB,UAAkB,EAClB,IAAY,EACZ,uBAA+B,EAC/B,WAIP,EACO,iBAAyC,EACzC,qBAA8E,EAC9E,WAAyB,EACzB,2BAAmC,EACnC,4BAAoC;QAE5C,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;QAdpC,eAAU,GAAV,UAAU,CAAQ;QAClB,SAAI,GAAJ,IAAI,CAAQ;QACZ,4BAAuB,GAAvB,uBAAuB,CAAQ;QAC/B,gBAAW,GAAX,WAAW,CAIlB;QACO,sBAAiB,GAAjB,iBAAiB,CAAwB;QACzC,0BAAqB,GAArB,qBAAqB,CAAyD;QAC9E,gBAAW,GAAX,WAAW,CAAc;QACzB,gCAA2B,GAA3B,2BAA2B,CAAQ;QACnC,iCAA4B,GAA5B,4BAA4B,CAAQ;IAG9C,CAAC;IACD,UAAU,CAAC,IAAU,EAAE,OAAgB,EAAE,OAAgB;QACvD,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACrD,IAAI,GAAG,GAAG,mCAAkB,CAAC,MAAM,EAAE;YACnC,OAAO,GAAG,CAAC;SACZ;QAED,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,iBAAiB;QACjB,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,GAAG,CAAC,IAAI,CACN,IAAA,+BAAU,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,CAAC,UAAU,EACf,CAAC,IAAI,CAAC,IAAI,EACV,KAAK,EACL,IAAI,EACJ,EAAE,MAAM,EAAE,GAAG,EAAE,EACf,KAAK,CACN,CACF,CAAC;SACH;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,GAAG,CAAC,IAAI,CACN,IAAA,+BAAU,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,aAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,KAAK,EACtC,IAAI,CAAC,uBAAuB,EAC5B,KAAK,EACL,KAAK,EACL,EAAE,UAAU,EAAE,GAAG,EAAE,EACnB,KAAK,CACN,CACF,CAAC;SACH;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,oBAAoB,KAAK,SAAS,EAAE;YACvD,GAAG,CAAC,IAAI,CACN,IAAA,6CAAwB,EACtB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,CAAC,WAAW,CAAC,MAAM,EACvB,IAAI,CAAC,WAAW,CAAC,MAAM,EACvB,IAAI,CAAC,WAAW,CAAC,oBAAoB,CACtC,CACF,CAAC;SACH;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACzC,GAAG,CAAC,IAAI,CACN,IAAA,oCAAe,EACb,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,CACnB,CACF,CAAC;SACH;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC7C,GAAG,CAAC,IAAI,CAAC,IAAA,8BAAS,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACtE;SACF;QAED,IACE,IAAI,CAAC,YAAY,CAAC,wBAAwB,EAAE;YAC5C,IAAI,CAAC,YAAY,CAAC,6BAA6B,EAAE,EACjD;YACA,GAAG,CAAC,IAAI,CAAC,IAAA,4CAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,GAAG,CAAC,IAAI,CACN,IAAA,8BAAS,EACP,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,CAAC,2BAA2B,EAChC,IAAI,CAAC,4BAA4B,EACjC,IAAI,EACJ,KAAK,EACL,SAAS,EACT,IAAI,EACJ,IAAI,CACL,CACF,CAAC;SACH;QAED,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;YACrB,IAAI,GAAG,KAAK,mCAAkB,CAAC,EAAE,EAAE;gBACjC,OAAO,GAAG,CAAC;aACZ;SACF;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,IAAI,EAAE,wBAAW,CAAC,wBAAW,CAAC,KAAK,CAAC;gBACpC,EAAE,EAAE,aAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,KAAK;gBAC1C,GAAG,EAAE,IAAI;gBACT,GAAG,EAAE,IAAI,CAAC,uBAAuB;aAClC,CAAC,CAAC;SACJ;QAED,OAAO,mCAAkB,CAAC,EAAE,CAAC;IAC/B,CAAC;CACF"}