{"environment":"development","type":"oceand","gitCommitHash":"836b50c3bac7","gitCommitMessage":"UWO FGT 과몰입, 결제 관련 기능 추가(결제는 추가 작업 필요)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:00:05+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:02:28.193Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.739Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.739Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.739Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.740Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.740Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.740Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.740Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.741Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:56.741Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:02:56.754Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:02:56.757Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:02:58.296Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:02:58.297Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:02:58.297Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:02:58.298Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:58.298Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:58.299Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:02:58.299Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:03:01.838Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:03:01.839Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:03:01.839Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:03:01.845Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:03:01.847Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:03:01.861Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:03:01.937Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:01.958Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:01.973Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:01.985Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:01.998Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:02.011Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:02.027Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:02.048Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:02.064Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:02.082Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:03:02.157Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:03:02.158Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:03:02.162Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:03:02.299Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:03:02.301Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:03:02.303Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:03:02.303Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:03:02.306Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:03:02.306Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:03:02.306Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:03:02.306Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:03:02.308Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:03:02.309Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:03:02.310Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:03:02.311Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:03:02.313Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:03:02.316Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:03:02.317Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-07-30T09:03:02.425Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:03:02.434Z"}
{"functionName":"acquireNationPromiseLock","sha1":"5b01109ee19356fd3d1a5b78b0b05663a9948483","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"addToNationCabinetApplicants","sha1":"674d5fc6bd2a3f7e43d5e97bc3fc51639fba09b3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"addNationAccumulatedTax","sha1":"d05172a9ca059757873c2a7c96601504f13804cc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"appointNationCabinetMember","sha1":"3a114d722a537241d5c3e68c83ff63476a202d88","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"closeElectionSession","sha1":"0e7f83cbffde5e481d509c29869dec78dc96461f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"buyNationSupportShop","sha1":"2ae699b35ef01b10338f3bbbf67ef91f3404c61c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"closeNationWageWeeklySession","sha1":"8d516d2079b4cc63b8fd563fa3ae9be192106e66","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"changeNationPopulation","sha1":"f9d4e98ba5b606398f925375b92bc6d13ed1475e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.532Z"}
{"functionName":"devChangeSessionId","sha1":"e31286f86dd0d7c7307bfbd3fb99ee59b826b7d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"devSetNationBudget","sha1":"81b812f5d19dc54efbc91f03621cb3c48e68dfe5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"devResetNationCabinetLastAppointedTimes","sha1":"12febbef79989fa946292899b058a58e213a66dc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"devSetNationPolicy","sha1":"812017a21acd296f9f8bc671690ff8edc315de51","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"closeNationWeeklyDonationRankSession","sha1":"b15a5d9c7733b21f096692ce7e80cd590b20eedb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"donateToNationBudget","sha1":"b2f69de9cc6a378fd62e98498ef4e90ed795fc6c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"devSetNationPrimeMinister","sha1":"5c1737c29a121fe590581e859fbcfe814542fe06","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"endElectionRewad","sha1":"1952a63dafb8f2a6a1fc277b473d743bae2fd6a5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getAllPop","sha1":"f2af6cde9924e4c0f054ca1cc982f6800888d599","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getElectionCandidateVotes","sha1":"a248c14a1275abc39cd3bff5db51d947507822ba","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getEventOccuranceCountBetweenNations","sha1":"19501e83528f8af86e25b49fc62b4615171291aa","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getNationAccumulatedTax","sha1":"509580eb6743fe406226c73112e1a7a488f205a2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getPopulationSnapshot","sha1":"d14819e593c6d818b2c846f94ec281b2823df0a8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.533Z"}
{"functionName":"getUserNationWeeklyDonationRank","sha1":"33535189b7bdb056c08ab77c17c7b129c4a12980","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"getPowerSnapshot","sha1":"ecae0479ae017bf952b642cfda6f3765c14d94df","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"getUserPrevElectionVotes","sha1":"fb9bb1c1c6a4132b767b050270a05732d9f6902d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"getUserElectionVotes","sha1":"d3527d45b08d4184b838d995da9b891490a49116","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadElection","sha1":"6acb498e4becf76d02255b08fd235656d397a1d2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"getUserNationWeeklyDonationRankAndReward","sha1":"9a09a301946d46e86dcf73ddb214701f1775cb66","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadElectionCandidate","sha1":"0a0cd6bcf934231598e67a4228e494b70d141689","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadElectionResults","sha1":"208fdf15d8659b2d932bc15d6f51374bab4f08a8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadLastClosedGlobalElectionSessionId","sha1":"c5b351e0875debe846f52694a8c529980507d6a4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadLastClosedElectionSessionId","sha1":"c19bc22dd796e3da27f114ba64d51f222d476456","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadNationCabinet","sha1":"e8a54da7dc92c2a712d27a4a37eccccd43eefd80","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadLastClosedGlobalNationWageWeeklySessionId","sha1":"13e6a358e3eb8f2012598f93c3f23b494b42f2dc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.534Z"}
{"functionName":"loadLastClosedGlobalNationWeeklyDonationRankSessionId","sha1":"0df8f0403a974e9d2729838c66daae6578e4d522","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadLastGlobalPolicyKeepCostPaidTimeUtc","sha1":"df945f36b6495ed83c948775692ce5713225d487","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationCabinetApplicants","sha1":"775a3ec16ca080e8203d186431671e6b054d0a95","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationCabinetMember","sha1":"90c927cb820b681b8cb73fa905107e785e12936b","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationCabinetMembersAll","sha1":"482d1fdb48302c7f9e83c47ccfacedfe7ecb9d71","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationWeeklyDonationRanks","sha1":"71fcdd5ba0804791e3e509cb68920a4ab4c1ea53","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationPrimeMinisters","sha1":"bb8c50296eee7097329cd0403bf70ad446f74c5d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadUserRewardedGoalPromiseCmsIds","sha1":"28194ff16f618695c4c1c054ce978e6e54583dcc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadUserNationCabinetOptions","sha1":"db3e15003f3463d3d874e07b7a6fd4945a9e0082","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadPrevNationCabinet","sha1":"7589b25a3213a3250cdf0cbed09665a9884cb5cf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadUserRewardedSupportShopCmsIds","sha1":"850fbad65f49ae4d9d551d0a816c2b657d2542d7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"loadNationWeeklyWageResults","sha1":"16f46fdcb72d88c1b1b9db8bb1536f7c91087aa1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"payDailyNationPolicyKeepCost","sha1":"9312fbcbdb5660fca76d7e669d80209d15785e12","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"modifyNationElectionCandidate","sha1":"2ee0bf230e814212bcd524cd466f8e49b7c906a0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"recordEventBetweenTwoNations","sha1":"5251630b655182d4dc64908fb9aee29f968a35d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.535Z"}
{"functionName":"reloadNationSupportShopPurchases","sha1":"01182a9f0b2752ec68a31c952de54bf49460237e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"registerElectionCandidate","sha1":"29c5e64bdfdb9a16879cf4055d7e33842eac3486","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"reloadNationGoalPromises","sha1":"3772c02e334ba649e5c9aac75140eea04990b119","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"removeNationCabinetApplicant","sha1":"c9a17db2c9631bf72ea04453d7172be758256ad0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"removeElectionCandidate","sha1":"edc67420b999353ca1013b3227deedc724107252","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"removeNationCabinet","sha1":"08aa8d419de4b16c1be1abae20c618372eb7d9b7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"removeElectionVotes","sha1":"c9b2cfc22fe302d9406b359872494fe66266fe54","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"setLastClosedGlobalElectionSessionId","sha1":"72cfd55b02046918f7e8dfa946950e3ad45355a2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.536Z"}
{"functionName":"setLastClosedGlobalNationWeeklyDonationRankSessionId","sha1":"f946fb02bfaa511d29dcf448ef1fd768bca086e7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.537Z"}
{"functionName":"setLastClosedGlobalNationWageRewardWeeklySessionId","sha1":"a43f1bed9d32d239579107a8bb00fb8c868e9ca5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.537Z"}
{"functionName":"setLastClosedGlobalNationWageWeeklySessionId","sha1":"a096bf391be9c4456e6613acd25aec84748c4639","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.537Z"}
{"functionName":"setLastGlobalPolicyKeepCostPaidTimeUtc","sha1":"41dfcb7299187b2193441afaccae1521e1d2d0de","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.537Z"}
{"functionName":"setNationElectionVoterReward","sha1":"e7275eda25c951a84f3d54972fc74e2ea3b7abdc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.538Z"}
{"functionName":"setNationSupportShopRewarded","sha1":"c0e224869669c4bb06bce1bbd9d8ddf09f93eac1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.538Z"}
{"functionName":"setNationWageRate","sha1":"0f2b928eb7638ba3b283d0ff4c0ff18e0a827832","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.538Z"}
{"functionName":"setPopulationSnapshot","sha1":"de6b695b5380758c9ead06285d5f5651e95974d3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.538Z"}
{"functionName":"setPowerSnapshot","sha1":"acbb055aebedc8ea2e8a80d5ef141abe0d466b2f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"setUserGoalPromiseRewaded","sha1":"111727b82b8c92a66e6f3e3995357393544c006d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"trySetLastClosedGlobalElectionSessionId","sha1":"b3a2498fb95988034ccfd17cff0521d2cbc2626e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"setUserNationWeeklyDonationRankRewarded","sha1":"4bfb6c5973a545287089174791afa752e6273b0e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updateElectionCandidateVotes","sha1":"c5d391683de5abbecf9838809dce5a55c6c5ff1f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updateGoalPromisesCleared","sha1":"7eaa09336ae84f99456ef776a1c9651ff2b71583","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updateNationElectionCandidateInvestScore","sha1":"90e804a9d63f5334b9bfd57338885bff21cfe8e8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updateNationElectionCandidateLevel","sha1":"981820d24bf91f57250e4d1507a9fc11f41a42bf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updateNationElectionCandidateMayorAndInvestScore","sha1":"f1e41ce8e15ffb7930da0f1218d365e08386cdac","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updatePopulationSnapshot","sha1":"34b6dc302149adda90c03f7dec9cc1a3bbef0d04","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"updatePromiseConditionProgress","sha1":"16bfef2af9c5f79cf49d7472e600d8e170627ff4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"upgradeNationPolicyStep","sha1":"9fd06d831983bb4f45513136e234301992d3f5ec","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.539Z"}
{"functionName":"voteToElectionCandidate","sha1":"859234e49b3b9a45d388f3946b1d0ad385643ec6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.540Z"}
{"functionName":"writeNationNotice","sha1":"06853a2e54ee0980953103be027eb9f1a0d0f359","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.540Z"}
{"functionName":"writePrimeMinisterThought","sha1":"cda496905f5b0bc2862e12332183e716548e2123","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:03:02.540Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-07-30T09:03:02.542Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:02.546Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:02.546Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:02.546Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:02.546Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:02.593Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:02.605Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:03:02.607Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-07-30T09:03:02.607Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:03:02.610Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-07-30T09:03:02.610Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-07-30T09:03:02.683Z"}
{"pingInterval":2000,"curDate":1753866182,"level":"info","message":"registration succeeded.","timestamp":"2025-07-30T09:03:02.687Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-07-30T09:03:12.873Z"}
{"level":"info","message":"[SessionManager] session created: D1sOVe-0, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:03:13.185Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:03:13.186Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-07-30T09:03:13.187Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-07-30T09:03:13.187Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.192Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.194Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.194Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.195Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.195Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.195Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.195Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:03:13.196Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:13:52.910Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:13:52.911Z"}
{"level":"info","message":"[Session] socket disposed, D1sOVe-0","timestamp":"2025-07-30T09:13:52.911Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-07-30T09:13:53.077Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:13:53.077Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:13:53.222Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-07-30T09:13:53.223Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:13:53.223Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-07-30T09:13:53.225Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-07-30T09:13:53.226Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:13:53.226Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:13:53.227Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:13:53.227Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:13:53.227Z"}
{"environment":"development","type":"oceand","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:13:57.026Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.718Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.718Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.718Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.718Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.719Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.719Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.719Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.719Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:24.719Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:24.734Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:14:24.737Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:14:26.103Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:14:26.104Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:14:26.105Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:14:26.106Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:26.106Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:26.107Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:14:26.107Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:14:27.057Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:14:27.058Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:14:27.059Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:14:27.065Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:14:27.066Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:14:27.083Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:14:27.170Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.196Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.213Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.224Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.238Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.250Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.267Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.288Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.307Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.326Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:14:27.407Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:14:27.408Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:14:27.411Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:14:27.537Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:14:27.540Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:14:27.542Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:14:27.542Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:14:27.545Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:14:27.546Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:14:27.546Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:14:27.546Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:14:27.548Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:14:27.548Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:14:27.549Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:14:27.549Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:14:27.551Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:14:27.553Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:14:27.554Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-07-30T09:14:27.650Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:14:27.659Z"}
{"functionName":"addNationAccumulatedTax","sha1":"d05172a9ca059757873c2a7c96601504f13804cc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.694Z"}
{"functionName":"addToNationCabinetApplicants","sha1":"674d5fc6bd2a3f7e43d5e97bc3fc51639fba09b3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"acquireNationPromiseLock","sha1":"5b01109ee19356fd3d1a5b78b0b05663a9948483","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"appointNationCabinetMember","sha1":"3a114d722a537241d5c3e68c83ff63476a202d88","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"changeNationPopulation","sha1":"f9d4e98ba5b606398f925375b92bc6d13ed1475e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"closeElectionSession","sha1":"0e7f83cbffde5e481d509c29869dec78dc96461f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"buyNationSupportShop","sha1":"2ae699b35ef01b10338f3bbbf67ef91f3404c61c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"closeNationWageWeeklySession","sha1":"8d516d2079b4cc63b8fd563fa3ae9be192106e66","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"closeNationWeeklyDonationRankSession","sha1":"b15a5d9c7733b21f096692ce7e80cd590b20eedb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"devResetNationCabinetLastAppointedTimes","sha1":"12febbef79989fa946292899b058a58e213a66dc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.695Z"}
{"functionName":"devChangeSessionId","sha1":"e31286f86dd0d7c7307bfbd3fb99ee59b826b7d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"devSetNationPolicy","sha1":"812017a21acd296f9f8bc671690ff8edc315de51","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"donateToNationBudget","sha1":"b2f69de9cc6a378fd62e98498ef4e90ed795fc6c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"devSetNationBudget","sha1":"81b812f5d19dc54efbc91f03621cb3c48e68dfe5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"endElectionRewad","sha1":"1952a63dafb8f2a6a1fc277b473d743bae2fd6a5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"devSetNationPrimeMinister","sha1":"5c1737c29a121fe590581e859fbcfe814542fe06","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getAllPop","sha1":"f2af6cde9924e4c0f054ca1cc982f6800888d599","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getEventOccuranceCountBetweenNations","sha1":"19501e83528f8af86e25b49fc62b4615171291aa","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getNationAccumulatedTax","sha1":"509580eb6743fe406226c73112e1a7a488f205a2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getElectionCandidateVotes","sha1":"a248c14a1275abc39cd3bff5db51d947507822ba","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getPowerSnapshot","sha1":"ecae0479ae017bf952b642cfda6f3765c14d94df","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getPopulationSnapshot","sha1":"d14819e593c6d818b2c846f94ec281b2823df0a8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getUserElectionVotes","sha1":"d3527d45b08d4184b838d995da9b891490a49116","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.696Z"}
{"functionName":"getUserNationWeeklyDonationRank","sha1":"33535189b7bdb056c08ab77c17c7b129c4a12980","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"getUserNationWeeklyDonationRankAndReward","sha1":"9a09a301946d46e86dcf73ddb214701f1775cb66","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"getUserPrevElectionVotes","sha1":"fb9bb1c1c6a4132b767b050270a05732d9f6902d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadElectionCandidate","sha1":"0a0cd6bcf934231598e67a4228e494b70d141689","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadElectionResults","sha1":"208fdf15d8659b2d932bc15d6f51374bab4f08a8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadElection","sha1":"6acb498e4becf76d02255b08fd235656d397a1d2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadLastClosedGlobalElectionSessionId","sha1":"c5b351e0875debe846f52694a8c529980507d6a4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadLastClosedElectionSessionId","sha1":"c19bc22dd796e3da27f114ba64d51f222d476456","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadLastClosedGlobalNationWageWeeklySessionId","sha1":"13e6a358e3eb8f2012598f93c3f23b494b42f2dc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadLastGlobalPolicyKeepCostPaidTimeUtc","sha1":"df945f36b6495ed83c948775692ce5713225d487","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadLastClosedGlobalNationWeeklyDonationRankSessionId","sha1":"0df8f0403a974e9d2729838c66daae6578e4d522","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadNationCabinetApplicants","sha1":"775a3ec16ca080e8203d186431671e6b054d0a95","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadNationCabinet","sha1":"e8a54da7dc92c2a712d27a4a37eccccd43eefd80","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.697Z"}
{"functionName":"loadNationCabinetMember","sha1":"90c927cb820b681b8cb73fa905107e785e12936b","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadNationCabinetMembersAll","sha1":"482d1fdb48302c7f9e83c47ccfacedfe7ecb9d71","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadNationPrimeMinisters","sha1":"bb8c50296eee7097329cd0403bf70ad446f74c5d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadNationWeeklyDonationRanks","sha1":"71fcdd5ba0804791e3e509cb68920a4ab4c1ea53","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadPrevNationCabinet","sha1":"7589b25a3213a3250cdf0cbed09665a9884cb5cf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadNationWeeklyWageResults","sha1":"16f46fdcb72d88c1b1b9db8bb1536f7c91087aa1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadUserNationCabinetOptions","sha1":"db3e15003f3463d3d874e07b7a6fd4945a9e0082","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadUserRewardedGoalPromiseCmsIds","sha1":"28194ff16f618695c4c1c054ce978e6e54583dcc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"loadUserRewardedSupportShopCmsIds","sha1":"850fbad65f49ae4d9d551d0a816c2b657d2542d7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"modifyNationElectionCandidate","sha1":"2ee0bf230e814212bcd524cd466f8e49b7c906a0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"payDailyNationPolicyKeepCost","sha1":"9312fbcbdb5660fca76d7e669d80209d15785e12","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"recordEventBetweenTwoNations","sha1":"5251630b655182d4dc64908fb9aee29f968a35d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"reloadNationGoalPromises","sha1":"3772c02e334ba649e5c9aac75140eea04990b119","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"registerElectionCandidate","sha1":"29c5e64bdfdb9a16879cf4055d7e33842eac3486","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.698Z"}
{"functionName":"reloadNationSupportShopPurchases","sha1":"01182a9f0b2752ec68a31c952de54bf49460237e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"removeElectionVotes","sha1":"c9b2cfc22fe302d9406b359872494fe66266fe54","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"removeNationCabinet","sha1":"08aa8d419de4b16c1be1abae20c618372eb7d9b7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"removeNationCabinetApplicant","sha1":"c9a17db2c9631bf72ea04453d7172be758256ad0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"removeElectionCandidate","sha1":"edc67420b999353ca1013b3227deedc724107252","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setLastClosedGlobalNationWageRewardWeeklySessionId","sha1":"a43f1bed9d32d239579107a8bb00fb8c868e9ca5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setLastClosedGlobalElectionSessionId","sha1":"72cfd55b02046918f7e8dfa946950e3ad45355a2","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setLastClosedGlobalNationWeeklyDonationRankSessionId","sha1":"f946fb02bfaa511d29dcf448ef1fd768bca086e7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setLastClosedGlobalNationWageWeeklySessionId","sha1":"a096bf391be9c4456e6613acd25aec84748c4639","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setNationSupportShopRewarded","sha1":"c0e224869669c4bb06bce1bbd9d8ddf09f93eac1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setNationElectionVoterReward","sha1":"e7275eda25c951a84f3d54972fc74e2ea3b7abdc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setLastGlobalPolicyKeepCostPaidTimeUtc","sha1":"41dfcb7299187b2193441afaccae1521e1d2d0de","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setNationWageRate","sha1":"0f2b928eb7638ba3b283d0ff4c0ff18e0a827832","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setPopulationSnapshot","sha1":"de6b695b5380758c9ead06285d5f5651e95974d3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setPowerSnapshot","sha1":"acbb055aebedc8ea2e8a80d5ef141abe0d466b2f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setUserGoalPromiseRewaded","sha1":"111727b82b8c92a66e6f3e3995357393544c006d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.699Z"}
{"functionName":"setUserNationWeeklyDonationRankRewarded","sha1":"4bfb6c5973a545287089174791afa752e6273b0e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"trySetLastClosedGlobalElectionSessionId","sha1":"b3a2498fb95988034ccfd17cff0521d2cbc2626e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updateElectionCandidateVotes","sha1":"c5d391683de5abbecf9838809dce5a55c6c5ff1f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updateGoalPromisesCleared","sha1":"7eaa09336ae84f99456ef776a1c9651ff2b71583","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updateNationElectionCandidateInvestScore","sha1":"90e804a9d63f5334b9bfd57338885bff21cfe8e8","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updateNationElectionCandidateLevel","sha1":"981820d24bf91f57250e4d1507a9fc11f41a42bf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updateNationElectionCandidateMayorAndInvestScore","sha1":"f1e41ce8e15ffb7930da0f1218d365e08386cdac","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updatePopulationSnapshot","sha1":"34b6dc302149adda90c03f7dec9cc1a3bbef0d04","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"upgradeNationPolicyStep","sha1":"9fd06d831983bb4f45513136e234301992d3f5ec","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"updatePromiseConditionProgress","sha1":"16bfef2af9c5f79cf49d7472e600d8e170627ff4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"writeNationNotice","sha1":"06853a2e54ee0980953103be027eb9f1a0d0f359","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"voteToElectionCandidate","sha1":"859234e49b3b9a45d388f3946b1d0ad385643ec6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"functionName":"writePrimeMinisterThought","sha1":"cda496905f5b0bc2862e12332183e716548e2123","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:27.700Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-07-30T09:14:27.702Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:27.707Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:27.707Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:27.707Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:27.707Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:27.766Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:27.781Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:14:27.782Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-07-30T09:14:27.783Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:27.785Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-07-30T09:14:27.786Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-07-30T09:14:27.874Z"}
{"pingInterval":2000,"curDate":1753866867,"level":"info","message":"registration succeeded.","timestamp":"2025-07-30T09:14:27.879Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-07-30T09:14:41.659Z"}
{"level":"info","message":"[SessionManager] session created: EAYyoZ-U, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:14:41.997Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:14:41.998Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-07-30T09:14:41.999Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-07-30T09:14:41.999Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.000Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.002Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.002Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.002Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.003Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.003Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.003Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:14:42.003Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:57:03.078Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:57:03.078Z"}
{"level":"info","message":"[Session] socket disposed, EAYyoZ-U","timestamp":"2025-07-30T09:57:03.078Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-07-30T09:57:03.113Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:03.114Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:57:03.639Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-07-30T09:57:03.639Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:57:03.640Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-07-30T09:57:03.642Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-07-30T09:57:03.643Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:57:03.643Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:57:03.644Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:57:03.644Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:57:03.644Z"}
{"environment":"development","type":"oceand","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:57:07.520Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.651Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.652Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.652Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.652Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.653Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.653Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.653Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.654Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:34.654Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:34.663Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:57:34.667Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:57:36.332Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:36.333Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:57:36.333Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:57:36.334Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:36.334Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:36.335Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:57:36.335Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:57:39.705Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:57:39.706Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:57:39.707Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:57:39.713Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:57:39.714Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:57:39.729Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:57:39.813Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.834Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.849Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.864Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.878Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.890Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.908Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.926Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.943Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:39.963Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:57:40.043Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:57:40.043Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:57:40.047Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:57:40.149Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:57:40.152Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:57:40.153Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:57:40.154Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:57:40.157Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:57:40.157Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:57:40.157Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:57:40.157Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:57:40.162Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:57:40.163Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:57:40.163Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:57:40.163Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:57:40.165Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:57:40.170Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:57:40.170Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-07-30T09:57:40.267Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:57:40.275Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-07-30T09:57:40.312Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:40.316Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:40.316Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:40.316Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:40.316Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:40.372Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:40.387Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:57:40.388Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-07-30T09:57:40.389Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:40.391Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-07-30T09:57:40.392Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-07-30T09:57:40.472Z"}
{"pingInterval":2000,"curDate":1753869460,"level":"info","message":"registration succeeded.","timestamp":"2025-07-30T09:57:40.476Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-07-30T09:57:52.386Z"}
{"level":"info","message":"[SessionManager] session created: w0EhVW7K, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:57:52.649Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:57:52.650Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-07-30T09:57:52.650Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-07-30T09:57:52.651Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.653Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.654Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.654Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.654Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.655Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.655Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.655Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-07-30T09:57:52.655Z"}
