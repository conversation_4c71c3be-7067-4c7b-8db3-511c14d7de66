CREATE PROCEDURE `mp_u_user_load_cash_cn`(
  IN inUserId INT
)
label_body:BEGIN

    SET @paid=(SELECT value FROM u_points WHERE u_points.userId = inUserId AND u_points.cmsId = 100009); -- 유료
    SET @free=(SELECT value FROM u_points WHERE u_points.userId = inUserId AND u_points.cmsId = 100004); -- 무료
    
    SELECT
      IFNULL(@paid, 0) as paidRedGemBalance,
      IFNULL(@free, 0) as freeRedGemBalance;

END
