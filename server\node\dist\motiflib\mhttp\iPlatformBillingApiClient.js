"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureGiveItems = exports.LGBillingCode = exports.LGBillingErrorCode = void 0;
const merrorCode_1 = require("../merrorCode");
const merror_1 = require("../merror");
/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275618
 */
var LGBillingErrorCode;
(function (LGBillingErrorCode) {
    LGBillingErrorCode[LGBillingErrorCode["SYSTEM_ERROR"] = 0] = "SYSTEM_ERROR";
    LGBillingErrorCode[LGBillingErrorCode["SYSTEM_MAINTENANCE"] = 1] = "SYSTEM_MAINTENANCE";
    LGBillingErrorCode[LGBillingErrorCode["INVALID_PARAMETER"] = 2] = "INVALID_PARAMETER";
    LGBillingErrorCode[LGBillingErrorCode["NOT_ALLOW_AUTH"] = 3] = "NOT_ALLOW_AUTH";
    LGBillingErrorCode[LGBillingErrorCode["NOT_EXIST_DATA"] = 4] = "NOT_EXIST_DATA";
    LGBillingErrorCode[LGBillingErrorCode["EXPIRE_AUTH_TOKEN"] = 5] = "EXPIRE_AUTH_TOKEN";
    LGBillingErrorCode[LGBillingErrorCode["EXIST_DUPL_RECEIPT"] = 6] = "EXIST_DUPL_RECEIPT";
    LGBillingErrorCode[LGBillingErrorCode["ALREADY_COMPLETE_RECEIPT"] = 7] = "ALREADY_COMPLETE_RECEIPT";
    LGBillingErrorCode[LGBillingErrorCode["NOT_EXIST_RECEIPT"] = 8] = "NOT_EXIST_RECEIPT";
    //
    LGBillingErrorCode[LGBillingErrorCode["UNKNOWN"] = 9] = "UNKNOWN";
})(LGBillingErrorCode = exports.LGBillingErrorCode || (exports.LGBillingErrorCode = {}));
/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275557
 */
var LGBillingCode;
(function (LGBillingCode) {
    let PurchaseStatus;
    (function (PurchaseStatus) {
        /** 구매 예약 중 */
        PurchaseStatus[PurchaseStatus["RESERVE"] = 0] = "RESERVE";
        PurchaseStatus[PurchaseStatus["RESERVED_CANCEL"] = 1] = "RESERVED_CANCEL";
        PurchaseStatus[PurchaseStatus["COMPLETE"] = 2] = "COMPLETE";
        PurchaseStatus[PurchaseStatus["CANCEL"] = 3] = "CANCEL";
        PurchaseStatus[PurchaseStatus["CALL_BACK_ING"] = 4] = "CALL_BACK_ING";
        PurchaseStatus[PurchaseStatus["ABUSE_BLOCK"] = 5] = "ABUSE_BLOCK";
        PurchaseStatus[PurchaseStatus["CANCEL_ING"] = 6] = "CANCEL_ING";
    })(PurchaseStatus = LGBillingCode.PurchaseStatus || (LGBillingCode.PurchaseStatus = {}));
    LGBillingCode.APP_STORE_CD = {
        GOOGLE_PLAY: 'GOOGLE_PLAY',
        APPLE_APP_STORE: 'APPLE_APP_STORE',
        FLOOR_STORE: 'FLOOR_STORE',
        STEAM: 'STEAM',
    };
})(LGBillingCode = exports.LGBillingCode || (exports.LGBillingCode = {}));
/**
 * @see {@link GiveItem}
 */
function isGiveItem(x) {
    if (x &&
        typeof x === 'object' &&
        typeof x.productItemType === 'string' &&
        typeof x.itemCd === 'string' &&
        (!x.coinChargeTypeCd || typeof x.coinChargeTypeCd === 'string') &&
        typeof x.amount === 'number' &&
        (x.coinManageBalanceYn === 'Y' || x.coinManageBalanceYn === 'N')) {
        return true;
    }
    return false;
}
/**
 * @see {@link GiveItem}
 */
function ensureGiveItems(x) {
    if (!Array.isArray(x)) {
        throw new merror_1.MError('array expected', merrorCode_1.MErrorCode.INTERNAL_ERROR, { x });
    }
    for (const elem of x) {
        if (!isGiveItem(elem)) {
            throw new merror_1.MError('invalid property', merrorCode_1.MErrorCode.INTERNAL_ERROR, { x });
        }
    }
}
exports.ensureGiveItems = ensureGiveItems;
//# sourceMappingURL=iPlatformBillingApiClient.js.map