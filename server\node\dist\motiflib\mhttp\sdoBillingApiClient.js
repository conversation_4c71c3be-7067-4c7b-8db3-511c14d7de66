"use strict";
// TODO: linesdk를 통해서 잔액을 조회하고 있다. 이 부분을 사용할수 없으므로, database에 있는 포인트를 조회하는 형태로 변경해줘야한다.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SdoBillingApiClient = void 0;
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
const assert_1 = __importDefault(require("assert"));
const iPlatformBillingApiClient_1 = require("./iPlatformBillingApiClient");
const typedi_1 = __importDefault(require("typedi"));
const server_1 = require("../../lobbyd/server");
const puUserLoadCash_cn_1 = __importDefault(require("../../mysqllib/sp/puUserLoadCash_cn"));
const puUserConsumeCash_cn_1 = __importDefault(require("../../mysqllib/sp/puUserConsumeCash_cn"));
const puUserAddCash_cn_1 = __importDefault(require("../../mysqllib/sp/puUserAddCash_cn"));
class SdoBillingApiClient {
    /**
     * 유상/무상 구분하여 잔액 조회
     *
     * @see https://developer.line.games/pages/viewpage.action?pageId=19597717
     */
    async queryCash(userId, appStoreCd, countryCreated) {
        const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
        const result = await (0, puUserLoadCash_cn_1.default)(userDbPool, userId);
        const paidRedGemBalance = result.paidRedGemBalance;
        const freeRedGemBalance = result.freeRedGemBalance;
        return [
            {
                paymentType: 'PAID',
                coinCd: 'red_gem',
                balance: paidRedGemBalance,
            },
            {
                paymentType: 'FREE',
                coinCd: 'red_gem',
                balance: freeRedGemBalance,
            },
        ];
    }
    async queryCashPair(userId, appStoreCd, countryCreated) {
        const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
        const result = await (0, puUserLoadCash_cn_1.default)(userDbPool, userId);
        return {
            paidRedGemBalance: result.paidRedGemBalance,
            freeRedGemBalance: result.freeRedGemBalance,
        };
    }
    /**
     * Cash(레드젬, 마일리지) 추가. 단, 무료 재화만 가능. 유료 재화는 빌링을 통해서만 가능하다.
     *
     * @see https://developer.line.games/pages/viewpage.action?pageId=7276168
     */
    async addCash(userId, gnid, appStoreCd, countryCreated, pointCmsId, amount, reason) {
        const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
        await (0, puUserAddCash_cn_1.default)(userDbPool, userId, pointCmsId, amount);
        return await this.queryCash(userId, appStoreCd, countryCreated);
    }
    /**
     * Cash(레드젬, 마일리지) 소모.
     *
     * @see https://developer.line.games/pages/viewpage.action?pageId=7276291 or
     * @see https://developer.line.games/pages/viewpage.action?pageId=19598027
     */
    async consumeCash(userId, gnid, appStoreCd, countryCreated, pointCmsId, amount, lgCashParam) {
        const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
        const result = await (0, puUserConsumeCash_cn_1.default)(userDbPool, userId, pointCmsId, amount);
        const response = [
            {
                paymentType: 'PAID',
                coinCd: 'red_gem',
                balance: result.paidRedGemBalance,
            },
            {
                paymentType: 'FREE',
                coinCd: 'red_gem',
                balance: result.freeRedGemBalance,
            },
        ];
        return response;
    }
    /**
     * 판매중인 상품 목록 조회
     *
     * @see https://developer.line.games/pages/viewpage.action?pageId=7275461
     */
    async querySalesList(appStoreCd) {
        // TODO
        return {
            success: true,
            msg: 'request success',
        };
    }
    /**
     * 상품 구매시 지급할 아이템의 상세정보 조회
     * @see https://developer.line.games/pages/viewpage.action?pageId=7276147
     */
    async queryProductGiveItemDetail(appStoreCd, productId) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 유저의 특정 결제 데이터 단건 조회(결제 상태 조건)
     * @see https://developer.line.games/pages/viewpage.action?pageId=43418520
     */
    async queryExistPurchaseForStatus(userId, status) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 결제 예약/완료 구매건의 상세정보 조회
     * @see https://developer.line.games/pages/viewpage.action?pageId=35849461
     */
    async queryPurchaseDetail(orderId) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 상품보관함 목록 조회
     * @see https://developer.line.games/pages/viewpage.action?pageId=43419619
     */
    async queryInventoryPurchaseList(userId) {
        // TODO
        return {
            success: true,
            msg: 'success',
            data: [],
        };
    }
    /**
     * 상품보관함 상세 조회
     * @see https://developer.line.games/pages/viewpage.action?pageId=43419623
     */
    async queryInventoryPurchase(userId, invenId) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 구매 예약 상태로 남아있는 최근 데이터 조회(상품ID 조건)
     * @see https://developer.line.games/pages/viewpage.action?pageId=40042541
     */
    async queryLatestReservedPurchaseByProductId(appStoreCd, productId, userId) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 구매요청 예약 작업
     * @see https://developer.line.games/pages/viewpage.action?pageId=7275505
     */
    async reservePurchase(userId, userIp, productId, gnid, appStoreCd, price, currency, appVersion, os, countryCreated, microPrice) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 구매 예약 상태를 '구매예약 취소' 상태로 변경
     * @see https://developer.line.games/pages/viewpage.action?pageId=40042519
     */
    async cancelReservedPurchase(userId, orderId) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 스토어에 따른 '구매건의 완료 처리'가 구현되었는지
     */
    static isCompleteReservedPurchaseImplemented(storeCode) {
        return SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES[storeCode]
            ? true
            : false;
    }
    /**
     * [구글] play store 구매건의 완료 처리
     * @see https://developer.line.games/pages/viewpage.action?pageId=35849377
     * @see {@link CompleteReservedPurchaseGoogleReqBody}
     */
    async completeReservedPurchaseGoogle(gnid, userId, appStoreCd, cfViewerCountry, adjustIdfa = '', adjustIdfv = '', adjustDeviceId = '', adjustAndroidId = '', adjustGpsAdid = '', aosPackageNm = '', orderId, receipt, ignoreReceiptYn = '', price, microPrice, currency, memo = '', googleSignature) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * [애플] app store 구매건의 완료 처리
     * @see https://developer.line.games/pages/viewpage.action?pageId=35849379
     * @see {@link CompleteReservedPurchaseAppleReqBody}
     */
    async completeReservedPurchaseApple(gnid, userId, appStoreCd, cfViewerCountry, adjustIdfa = '', adjustIdfv = '', adjustDeviceId = '', adjustAndroidId = '', adjustGpsAdid = '', orderId, receipt, ignoreReceiptYn = '', price, microPrice, currency, memo = '') {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * [Floor] Floor 스토어 구매 완료 처리
     * @see https://developer.line.games/pages/viewpage.action?pageId=43419517
     * @see {@link CompleteReservedPurchaseFloorReqBody}
     */
    async completeReservedPurchaseFloor(gnid, userId, appStoreCd, cfViewerCountry, adjustIdfa = '', adjustIdfv = '', adjustDeviceId = '', adjustAndroidId = '', adjustGpsAdid = '', orderId, receipt, ignoreReceiptYn = '', price, microPrice, currency, memo = '', signature) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * [Steam] 구매건 완료 처리
     * @see https://developer.line.games/pages/viewpage.action?pageId=27526099
     * @see {@link CompleteReservedPurchaseSteamReqBody}
     */
    async completeReservedPurchaseSteam(gnid, userId, appStoreCd, cfViewerCountry, adjustIdfa = '', adjustIdfv = '', adjustDeviceId = '', adjustAndroidId = '', adjustGpsAdid = '', orderId, receipt, ignoreReceiptYn = '', price, microPrice, currency, memo = '') {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * [Steam] 상품구입 초기화
     * @see https://developer.line.games/pages/viewpage.action?pageId=27525181
     */
    async steamPurchaseInitTxn(billingOrderId, steamId, steamAppId, steamLanguage, steamCurrency, steamItemInfos) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * @deprecated
     * 빌링 보관함을 사용하게 되면서, 빌링 상품 구매시 보관함에 기록되는데
     * 상품을 지급하면 통보를 해서 수령 완료 처리(보관함에서 제거)를 해야함.
     * 이 API 를 사용해서 코인만 충전하고 보관함에서 제거가 되지 않을 여지가 있고, 다른 프로젝트에서 관련 사례가 있었다고함.
     * 코인 충전과 동시에 보관함에서 제거할수 있는 API( {@link chargeCoinWithInven} )를 이용하도록 강제함.
     *
     * 상품 구입에 의한 유상 코인 충전
     * @see https://developer.line.games/pages/viewpage.action?pageId=7275510
     */
    chargeByPurchaseProduct() {
        const url = 'api/v1/purchase/product/coin/charge/byPurchaseProduct';
        assert_1.default.fail(`[${url}] deprecated`);
    }
    /**
     * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)
     * @see https://developer.line.games/pages/viewpage.action?pageId=43423046
     * @param invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
     */
    async chargeCoinWithInven(userId, orderId, gnid, countryCreated, invenDelYn = 'Y', invenMemo, coinChargeList) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)(복수건)
     * @see https://developer.line.games/pages/viewpage.action?pageId=43424654
     * @param orderList.invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
     */
    async chargeCoinWithInvenList(userId, gnid, countryCreated, orderList) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(단건)
     * @see https://developer.line.games/pages/viewpage.action?pageId=43419621
     */
    async completeReceiveInvenPurchase(userId, invenId, memo) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    /**
     * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(복수건) JSON
     * @see https://developer.line.games/pages/viewpage.action?pageId=43424705
     */
    async completeReceiveInvenPurchaseBulk(userId, invens) {
        // TODO
        return {
            success: true,
            msg: 'success',
        };
    }
    async tryBidding(blindCmsId, userId, appStoreCd, gnid, countryCreated, amount, reason) {
        // TODO
    }
    async bidResult(blindCmsId, userId, appStoreCd, gnid, countryCreated, bWinner, reason) {
        // TODO
    }
    async sendToSlack(message) {
    }
}
exports.SdoBillingApiClient = SdoBillingApiClient;
SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES = {
    [iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.GOOGLE_PLAY]: true,
    [iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.APPLE_APP_STORE]: true,
    [iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.FLOOR_STORE]: true,
    [iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.STEAM]: true,
};
//# sourceMappingURL=sdoBillingApiClient.js.map