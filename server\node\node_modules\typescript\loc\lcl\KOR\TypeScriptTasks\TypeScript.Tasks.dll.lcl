﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\TypeScript.Tasks.dll" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="ko-KR" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="RCCX" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";Managed Resources" ItemType="0" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
  <Item ItemId=";TypeScript.Tasks.Strings.resources" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" Path=" \ ;Managed Resources \ 0 \ 0" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";CompilerLogNotSpecified" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No compiler log specified, 'Clean' won't work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[컴파일러 로그가 지정되지 않았습니다. '정리'가 작동하지 않습니다.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfigFileFoundOnDisk" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This project is using one or more tsconfig.json files for the build that may not be properly used by IntelliSense. Please set the item type of each tsconfig.json file to TypeScriptCompile or Content to ensure they are used properly by the editor.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[이 프로젝트는 IntelliSense에서 적절하게 사용하고 있지 않을 수 있는 빌드에 대해 하나 이상의 tsconfig.json 파일을 사용하고 있습니다. 편집기에서 적절하게 사용하도록 각 tsconfig.json 파일의 항목 종류를 TypeScriptCompile 또는 Content로 설정하세요.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorListBuildPrefix" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[빌드:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorWritingLog" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to write compiler log to '{0}. Exception: '{1}']]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA['{0}에 컴파일러 로그를 쓰지 못했습니다. 예외: '{1}']]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FallbackVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using compiler version ({0}), if this is incorrect change the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[컴파일러 버전({0})을 사용합니다. 이 버전이 잘못된 경우 프로젝트 파일에서 <TypeScriptToolsVersion> 요소를 변경하세요.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LocatedReferenceFilesAt_0" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Located references file at: '{0}'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA['{0}'에서 참조 파일을 찾았습니다.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoCompilerError" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project file uses a different version of the TypeScript compiler and tools than is currently installed on this machine.  No compiler was found at {0}.  You may be able to fix this problem by changing the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[이 시스템에 현재 설치된 것과 다른 버전의 TypeScript 컴파일러 및 도구가 프로젝트 파일에 사용됩니다. {0}에서 컴파일러를 찾을 수 없습니다. 프로젝트 파일에서 <TypeScriptToolsVersion> 요소를 변경하여 이 문제를 해결할 수 있습니다.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeNotFound" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The build task could not find node.exe which is required to run the TypeScript compiler. Please install Node and ensure that the system path contains its location.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[빌드 작업에서 TypeScript 컴파일러를 실행하는 데 필요한 node.exe를 찾을 수 없습니다. 노드를 설치하고 시스템 경로에 해당 위치가 포함되어 있는지 확인하세요.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolsVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Couldn't locate the compiler version ({0}) specified in the project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[프로젝트 파일에 지정된 컴파일러 버전({0})을 찾을 수 없습니다.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptCompileBlockedSet" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeScript compile is skipped because the TypeScriptCompileBlocked property is set to 'true'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeScriptCompileBlocked 속성이 'true'로 설정되어 있으므로 TypeScript 컴파일을 건너뜁니다.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptNoVersionWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project does not specify a TypeScriptToolsVersion. The latest available TypeScript compiler will be used ({0}). To remove this warning, set TypeScriptToolsVersion to a specific version or "Latest" to always select the latest compiler.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[프로젝트에서 TypeScriptToolsVersion을 지정하지 않았습니다. 사용 가능한 최신 TypeScript 컴파일러({0})가 사용됩니다. 이 경고를 제거하려면 TypeScriptToolsVersion을 특정 버전으로 설정하거나 "Latest"로 설정하여 항상 최신 컴파일러를 선택하세요.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptVersionMismatchWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project specifies TypeScriptToolsVersion {0}, but a matching compiler was not found. The latest available TypeScript compiler will be used ({1}). To remove this warning, install the TypeScript {0} SDK or update the value of TypeScriptToolsVersion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[프로젝트에서 TypeScriptToolsVersion {0}을(를) 지정했지만 일치하는 컴파일러를 찾을 수 없습니다. 사용 가능한 최신 TypeScript 컴파일러({1})가 사용됩니다. 이 경고를 제거하려면 TypeScript {0} SDK를 설치하거나 TypeScriptToolsVersion 값을 업데이트하세요.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Ver" Disp="true" LocTbl="false" Path=" \ ;Version \ 8 \ 0" />
    <Item ItemId=";CompanyName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[Microsoft Corporation]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";FileDescription" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 빌드 작업]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";InternalName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";LegalCopyright" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[© Microsoft Corporation. All rights reserved.]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[© Microsoft Corporation. All rights reserved.]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";OriginalFilename" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";ProductName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 빌드 작업]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="8" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
</LCX>