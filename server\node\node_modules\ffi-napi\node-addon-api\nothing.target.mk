# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := nothing
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=nothing' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DDEBUG' \
	'-D_DEBUG' \
	'-DV8_ENABLE_CHECKS'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-exceptions \
	-std=gnu++14

INCS_Debug := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=nothing' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-DV8_DEPRECATION_WARNINGS' \
	'-DV8_IMMINENT_DEPRECATION_WARNINGS' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-exceptions \
	-std=gnu++14

INCS_Release := \
	-I/root/.cache/node-gyp/16.20.2/include/node \
	-I/root/.cache/node-gyp/16.20.2/src \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/config \
	-I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include \
	-I/root/.cache/node-gyp/16.20.2/deps/uv/include \
	-I/root/.cache/node-gyp/16.20.2/deps/zlib \
	-I/root/.cache/node-gyp/16.20.2/deps/v8/include

OBJS := \
	$(obj).target/$(TARGET)/../node-addon-api/nothing.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(obj).target/../node-addon-api/nothing.a: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/../node-addon-api/nothing.a: LIBS := $(LIBS)
$(obj).target/../node-addon-api/nothing.a: TOOLSET := $(TOOLSET)
$(obj).target/../node-addon-api/nothing.a: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,alink)

all_deps += $(obj).target/../node-addon-api/nothing.a
# Add target alias
.PHONY: nothing
nothing: $(obj).target/../node-addon-api/nothing.a

# Add target alias to "all" target.
.PHONY: all
all: nothing

# Add target alias
.PHONY: nothing
nothing: $(builddir)/nothing.a

# Copy this to the static library output path.
$(builddir)/nothing.a: TOOLSET := $(TOOLSET)
$(builddir)/nothing.a: $(obj).target/../node-addon-api/nothing.a FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/nothing.a
# Short alias for building this static library.
.PHONY: nothing.a
nothing.a: $(obj).target/../node-addon-api/nothing.a $(builddir)/nothing.a

# Add static library to "all" target.
.PHONY: all
all: $(builddir)/nothing.a

