{"version": 3, "file": "changeName.js", "sourceRoot": "", "sources": ["../../../../src/lobbyd/packetHandler/auth/changeName.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;;;;;;;;;;;;;;;;;;;;AAE/E,mCAAmC;AACnC,oDAAuB;AAGvB,yDAAiE;AACjE,8FAAsE;AAEtE,yCAA4C;AAC5C,uEAAqE;AACrE,oEAA4C;AAC5C,uDAA+B;AAC/B,qDAA8D;AAC9D,kEAAwE;AACxE,kEAA0C;AAC1C,oEAA4C;AAC5C,+DAAiD;AAEjD,mDAAgD;AAEhD,+EAAiE;AAEjE,+EAA+E;AAC/E,UAAU;AACV,+EAA+E;AAE/E,MAAM,GAAG,GAAG,aAAa,CAAC;AAC1B,MAAM,OAAO,GAAG,IAAI,CAAC;AAWrB,+EAA+E;AAC/E,MAAa,mBAAmB;IAC9B,6EAA6E;IAC7E,gBAAe,CAAC;IAEhB,6EAA6E;IAC7E,aAAa,CAAC,IAAU;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6EAA6E;IAC7E,IAAI,CAAC,IAAU,EAAE,MAAe;QAC9B,MAAM,IAAI,GAAgB,MAAM,CAAC,OAAO,CAAC;QACzC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC;QAEzD,cAAc;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9B,aAAa,CAAC,eAAe,CAC3B,OAAO,EACP,IAAI,EACJ,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,EAClC,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CACnC,CAAC;QAEF,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,kBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;QAErF,IAAI,CAAC,eAAe,CAAC,iCAAgB,CAAC,SAAS,CAAC,CAAC;QAEjD,gDAAgD;QAChD,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,eAAgC,CAAC;QACrC,IAAI,YAA2B,CAAC;QAChC,IAAI,YAA2B,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE;YACZ,UAAU,GAAG,IAAI,CAAC;YAClB,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,sBAAU,CAAC,QAAQ,CAAC,CAAC;SAC5E;aAAM;YACL,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,iCAAiC,CACjE;gBACE;oBACE,KAAK,EAAE,aAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK;oBAC/C,IAAI,EAAE,aAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK;iBAClD;aACF,EACD,eAAe,EACf,EAAE,MAAM,EAAE,GAAG,EAAE,EACf,IAAI,CACL,CAAC;YACF,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YACtC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YAEtC,oBAAoB;YACpB,MAAM,WAAW,GAAG,kBAAS,CAAC,GAAG,CAAC,yBAAW,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YACtC,KAAK,MAAM,OAAO,IAAI,gBAAC,CAAC,MAAM,CAAC,aAAG,CAAC,IAAI,CAAC,EAAE;gBACxC,IAAI,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;oBAC1E,MAAM,IAAI,eAAM,CACd,iDAAiD,EACjD,mBAAU,CAAC,6BAA6B,EACxC;wBACE,SAAS,EAAE,OAAO,CAAC,EAAE;qBACtB,CACF,CAAC;iBACH;aACF;SACF;QAED,MAAM,IAAI,GAAa;YACrB,WAAW,EAAE,KAAK;YAClB,IAAI,EAAE,EAAE;SACT,CAAC;QAEF,IAAI,kBAAkB,GAAG,KAAK,CAAC,CAAC,gBAAgB;QAChD,IAAI,kBAAkB,GAAG,KAAK,CAAC,CAAC,gBAAgB;QAEhD,MAAM,YAAY,GAAG,oBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACrB,OAAO,eAAK,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aAC3C;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,IAAI,IAAI,EAAE;gBACR,MAAM,IAAI,eAAM,CAAC,cAAc,EAAE,mBAAU,CAAC,YAAY,EAAE;oBACxD,IAAI;oBACJ,OAAO,EAAE,IAAI,CAAC,QAAQ;iBACvB,CAAC,CAAC;aACJ;YAED,OAAO,eAAK,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,eAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACxF,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;YACtB,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,kBAAkB,GAAG,IAAI,CAAC;gBAE1B,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;oBACpE,IAAI;oBACJ,GAAG;oBACH,OAAO;oBACP,YAAY;iBACb,CAAC,CAAC;aACJ;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,OAAO,IAAA,0BAAgB,EACrB,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAC3D,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,eAAe,EACf,YAAY,CACb,CAAC;aACH;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,kBAAkB,GAAG,IAAI,CAAC;gBAE1B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,QAAQ,EAAE,IAAI;oBACd,aAAa,EAAE,YAAY;iBAC5B,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAErB,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBACxD,cAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE;wBAC7C,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,IAAI;wBACJ,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,cAAc,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBAC7D,cAAI,CAAC,KAAK,CAAC,uCAAuC,EAAE;wBAClD,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,IAAI;wBACJ,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,gBAAC,CAAC,KAAK,CAAa,IAAI,CAAC,IAAI,EAAE;oBAC7B,GAAG,EAAE;wBACH,IAAI,EAAE;4BACJ,IAAI;yBACL;qBACF;iBACF,CAAC,CAAC;gBAEH,IAAI,eAAe,EAAE;oBACnB,gBAAC,CAAC,KAAK,CACL,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,CAC/D,CAAC;iBACH;gBAED,gBAAC,CAAC,KAAK,CACL,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CACxE,CAAC;aACH;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,cAAc;gBACd,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE;oBAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAC7C,IAAI,QAAQ,EAAE;wBACZ,MAAM,OAAO,GAAG,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;wBACnD,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;4BAC5E,cAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE;gCACvD,MAAM,EAAE,IAAI,CAAC,MAAM;gCACnB,GAAG,EAAE,GAAG,CAAC,OAAO;gCAChB,IAAI;6BACL,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;qBACJ;iBACF;qBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE;oBACrC,OAAO;iBACR;gBAED,eAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAC/C;YAED,cAAc;YACd,IAAI,gBAAgB,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBAClC,GAAG;oBACH,OAAO;oBACP,iBAAiB,EAAE,gBAAgB;iBACpC,CAAC,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,cAAc,CAAW,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,OAAO,OAAO,CAAC,OAAO,EAAE;iBACrB,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,EAAE;oBAC7C,cAAI,CAAC,IAAI,CAAC,4CAA4C,EAAE;wBACtD,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,OAAO;wBACP,IAAI;wBACJ,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;oBAEH,OAAO,eAAK,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,eAAK,CAAC,OAAO,CAAC,CAAC;iBACxE;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;gBACtB,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,IAAI,aAAa,EAAE;oBAC9D,uCAAuC;oBACvC,oDAAoD;oBAEpD,cAAI,CAAC,IAAI,CAAC,qCAAqC,EAAE;wBAC/C,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,OAAO;wBACP,IAAI;wBACJ,aAAa;wBACb,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;iBACJ;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAxOD,kDAwOC"}