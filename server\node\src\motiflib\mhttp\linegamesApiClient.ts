// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import os from 'os';
import _ from 'lodash';

import mconf from '../mconf';
import mlog from '../mlog';
import * as mutil from '../mutil';
import { BaseApiClient } from './baseApiClient';
import { MError, MErrorCode } from '../merror';
import { CreateSlackNotifier, ISlackNotifier } from '../slackNotifier';
import { AxiosResponse } from 'axios';
import moment from 'moment';
import { IPlatformApiClient } from './iPlatformApiClient';

export enum LGErrorCode {
  SYSTEM_ERROR,
  SYSTEM_MAINTENANCE,
  INVALID_PARAMETER,
  NOT_ALLOW_AUTH,
  EXPIRE_AUTH_TOKEN,
  EXPIRE_PF_SESSION_TOKEN,
  NOT_EXIST_DATA,
  NOT_LEAVE_USER,
  OVER_DAY_RECOVERY_LEAVE,
  UNKNOWN,
}

export enum LGClientVersionStatus {
  ONLINE, //	온라인
  OFFLINE, //	오프라인
  NEED_UPDATE, //	업데이트필요
  ING_REVIEW, //	리뷰중	IOS 등의 스토어에 점검용으로 사용하는 버전
  MAINTENANCE, //	점검중	점검이나 장애시 클라이언트에서 게임서버를 콜하지 않고 메시지를 뿌려주는 형태로 처리하여, 유저에게 커뮤니케이션 용도로 활용 가능
  ONLY_PATCH, //	접속불가 & 패치허용	CDN 등을 통한 패치는 다운로드 가능하지만 게임서버 접속은 불가한 상태. 용량이 큰 게임에 대해서 유저의 패치를 미리 다운로드하도록 유도 가능
}

export interface LGLoginResult {
  gameCd: string; // 게임코드
  gnidStatus: string; // GNID 상태값 NORMAL 일 경우만 로그인 처리
  gnid: string; // 서버 구분없이 해당 게임의 1개의 유저ID
  newGnidYn: string; // GNID 신규 생성여부
  nid: string;
  newNidYn: string; // NID 신규 생성여부
  countryCreated: string; // 계정 생성 시점의 국가코드(참고: 계정을 생성할 때의 IP를 기반으로 플랫폼서버에서 국가코드를 생성)
  gameServerId: string; // 게임서버ID (요청 파라미터 리턴)
  nidToken: string; // 플랫폼서버 내부 로직에 따라 생성된 hash토큰 값으로 호출시점마다 변경 됨. 채팅 등에 사용할 목적의 임시성 토큰
  shortNid: string; // 단축 NID, NID 자릿수가 길어서 인게임 또는 이벤트페이지 등에서 줄여서 표현하고자하는 니즈가 있어서 만들어짐
  blockDateStartUnixTS: null; // 제재일시(시작) 유닉스타임
  blockDateEndUnixTS: null; // 제재일시(종료) 유닉스타임
  blockReason: null; // 제재 사유
  leaveDateUnixTS: null; // 탈퇴일시 유닉스타임
  timeZoneId: string; // 타임존 Canonical ID
  timeZoneOffsetSec: number; // 타임존 offset 초단위값
  termsAgreeUnixTS: number; // 이용약관동의 일시 유닉스타임
  privacyAgreeUnixTS: number; // 개인정보보호정책동의 일시 유닉스타임
  pushAgreeYn: string; // 푸쉬 수신 동의여부
  pushAgreeUnixTS: number; // 푸쉬 수신 동의일시
  nightPushAgreeUnixTS: number; // 야간푸쉬 수신 동의일시
  needAgreePushYn: string; // 푸쉬수신 재 동의 필요여부, Y이면 유저에게 푸쉬수신 재 동의 요청을 보여주고, "푸쉬 수신 동의 여부 : 변경" API로 전달
  needReAgreePolicyYn: string; // 정책 재 동의 필요 여부
  whiteNidYn: string; // 해당 유저가 화이트NID로 설정되어있는지 여부. 일반적으로 게임서버 세션DB 등에 저장해두고 화이트 기능에 사용
  cmpKey: string;
  accountListCnt: number; // GNID에 연동된 플랫폼 계정 갯수
  accountList: LGAccountList[]; // GNID에 연동된 플랫폼 계정 리스트
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7277010
 */
export interface LGAccountDetail {
  nid: string;
  gameCd: string;
  gameServerId?: string;
  shortNid: string;
  nidRegUnixTS: number;
  nidLastLoignUnixTS: number;
  gnid: string;
  gnidStatus: string;
  gnidRegUnixTS: number;
  blockDateStartUnixTS?: number; // 제재일시(시작) 유닉스타임
  blockDateEndUnixTS?: number; // 제재일시(종료) 유닉스타임
  blockReason?: string; // 제한사유
  leaveDateUnixTS?: number; // 탈퇴일시 유닉스타임
  timeZoneId: string;
  timeZoneOffsetSec: number;
  lang?: string;
  locale?: string;
  termsAgreeUnixTS?: number;
  privacyAgreeUnixTS?: number;
  pushAgreeUnixTS?: number;
  nightPushAgreeUnixTS?: number;
  gnidPuserList: GnidPuserList[];
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7277010
 */
export interface GnidPuserList {
  gnid: string;
  platformUserId: string;
  platformId: number;
}

export interface LGAccountList {
  gnid: string;
  platformUserId: string;
  platformId: number;
}

export interface ResponseBody {
  isSuccess: boolean;
  errorCd?: string;
  msg: string;
  data?: any;
}

export interface LGServerListResponse {
  gnid: string;
  registeredGameServerList: RegisteredGameServer[];
  createdNidGameServerIdList: CreatedNidGameServerID[];
}

// 통합툴에 등록 & 사용 중으로 설정되어있는 게임서버 리스트
export interface RegisteredGameServer {
  gameServerId: string;
  gameServerNm: string;
}

// 기존에 생성되어져있는 nid와 게임서버 정보 리스트
export interface CreatedNidGameServerID {
  nid: string;
  gameServerId: string;
}

export interface LGAllClientVersion {
  versionList: ClientVersion[]; // JSON ArrayList 클라이언트 버전 정보 리스트
  white_nids_arr: string[]; // 화이트 NID Array
  white_gnids_arr: string[]; // 화이트 GNID Array(화이트 NID의 GNID들)
  // GNID를 사용하지 않는 게임은 제공되지 않음 2020년 12월 이전에 등록된 nid들의 gnid에 대해서는 제공되지 않음(통합툴에서 다시 등록하면 제공됨)
  // 동일유저(GNID)가 N개의 서버에서 play시 GNID는 중복이 될 수 있음
  white_ips_arr: string[]; //	화이트 IP Array
}

// client versionList 정보
export interface ClientVersion {
  game_cd: string; // (50)게임코드
  game_server_id: string; //(20)	게임서버아이디
  os: string; //OS	참고 문서 (OS)
  client_version: string; //(255)	클라이언트버전
  client_version_status: string; //클라이언트 버전상태 OFFLINE 상태값은 제외 참고 문서 (CLIENT_VERSION_STATUS)
  maintenance_msg: string; //	점검중일때의 메시지
}

export class LineGamesApiClient extends BaseApiClient implements IPlatformApiClient {
  private nfToken: string = null;
  private authPwd: string = null;
  private loginNumber: number = 0;
  private slackNotifier?: ISlackNotifier;
  private whiteServerIpList: string[] = []; // 우리 API 를 사용가능한 LG 서버 아이피들
  private whiteServerIpTTL: number = 0; // LG 서버 목록 캐싱 타임 (5분)
  private allClientVersionTTL: number = 0; // 전체 클라이언트 버전의 설정 정보 조회(서버용) 캐싱 타임 (2분)
  private allClientVersionData: LGAllClientVersion;
  // for mail
  private _timeZoneId: string = Intl.DateTimeFormat().resolvedOptions().timeZone;
  private _timeZoneOffsetSec: number = new Date().getTimezoneOffset() * -60;

  get gameCode() {
    return mconf.LineGameCode;
  }

  constructor() {
    super();
  }

  init(baseUrl: string, timeout?: number) {
    super.init(baseUrl, timeout);
    this.authPwd = mconf.http.lgd.authPwd;
  }

  /**
   * 로그인
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276655
   */
  async login(sessionToken: string): Promise<unknown> {
    try {
      this.loginNumber++;
      const body = {
        gameCd: mconf.LineGameCode,
        pfSessionToken: sessionToken,
        cmpKey: this.loginNumber.toString(),
      };

      const result = await this.request('/api/api_nid/v2/login/byPfSessionToken', body);
      return result as LGLoginResult;
    } catch (error) {
      throw new MError(error.message, MErrorCode.LGSDK_BY_PF_SESSION_TOKEN_IS_FAILED);
    }
  }

  /**
   * 특정 유저(들)에게 푸시 발송
   * @param message 보낼 메세지. 200글자가 넘어가는건 문자열이 잘림
   * @param nid 받을 사람
   */
  async sendPushNotification(message: string, nid: string): Promise<void>;
  async sendPushNotification(message: string, nids: string[]): Promise<void>;
  async sendPushNotification(message: string, arg: string | string[]): Promise<void> {
    const body = {
      gameCd: mconf.LineGameCode,
      nids: '',
      msg: message.substr(0, 200),
    };

    if ('string' == typeof arg) {
      body.nids = arg;
    } else {
      body.nids = (arg as string[]).join(',');
    }

    await this.request('/api/api_push/pushToNids', body);
  }

  /**
   * GNID로 NID 및 서버 정보 가져오기
   * @see https://developer.line.games/pages/viewpage.action?pageId=********
   */
  async getNidAndServerInfoByGnid(gnidSessionToken: string) {
    const body = {
      gameCd: mconf.LineGameCode,
      gnidSessionToken,
    };

    const result = await this.request('api/api_nid/v2/nid/list/byGnidSessionToken', body);
    return result as LGServerListResponse;
  }

  /**
   * 서버 목록 가져오기
   * @see https://developer.line.games/pages/viewpage.action?pageId=19595972
   */
  async getServerList() {
    const body = {
      gameCd: mconf.LineGameCode,
    };

    const result = await this.request('api/gameServer/list', body);
    return result as RegisteredGameServer[];
  }

  /**
   * NID로 LG 사용자 정보 가져오기
   * @see https://developer.line.games/pages/viewpage.action?pageId=7277010
   */
  async getUserInfoFromNid(nid: string) {
    const body = {
      gameCd: mconf.LineGameCode,
      nid,
    };

    const result = await this.request('api/api_nid/v2/nid/detail', body);
    return result as LGAccountDetail;
  }

  /**
   * GnidSessionToken으로 부터 Nid 획득하기
   * @see https://developer.line.games/pages/viewpage.action?pageId=********
   */
  async getNidFromSessionToken(gnidSessionToken: string, worldId?: string): Promise<string> {
    const body = {
      gameCd: mconf.LineGameCode,
      gnidSessionToken,
    };

    const reqUrl = 'api/api_nid/v2/nid/list/byGnidSessionToken';
    const lgServerId = worldId ?? mconf.serverdInstances.appId.worldId;
    const result = await this.request(reqUrl, body);

    const list = result?.data?.createdNidGameServerIdList;
    if (!list) {
      throw new MError(`unexpected result(data), '${reqUrl}'`, MErrorCode.LGSDK_ERROR);
    }

    const selected = list.filter((elem) => {
      return elem.gameServerId === lgServerId;
    });

    if (!selected) {
      throw new MError(
        `unexpected gameServerId(${lgServerId}), '${reqUrl}'`,
        MErrorCode.LGSDK_ERROR
      );
    }

    if (!selected.nid) {
      throw new MError(`unexpected result(nid not found), '${reqUrl}'`, MErrorCode.LGSDK_ERROR);
    }

    return selected.nid;
  }

  async getUserInfoFromSessionToken(gnidSessionToken: string, worldId?: string) {
    const nid = await this.getNidFromSessionToken(gnidSessionToken, worldId);
    return await this.getUserInfoFromNid(nid);
  }

  /**
   * 회원 탈퇴
   * @see https://developer.line.games/pages/viewpage.action?pageId=7277446
   */
  async revoke(nid: string): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      nid,
    };

    return this.request('/api/api_nid/v2/leave', body);
  }

  /**
   * 회원 탈퇴 취소
   * @see https://developer.line.games/pages/viewpage.action?pageId=********#
   */
  async cancelRevoke(gnidSessionToken: string): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      gnidSessionToken,
    };

    return this.request('/api/v1/gnid/leave/cancel', body);
  }

  /**
   * 게임서버에서 탈퇴유저 데이터 삭제처리 결과 보고
   * https://developer.line.games/pages/viewpage.action?pageId=********
   */
  async reportAccountDeletionResult(
    successNidList: string[],
    failNidList: { nid: string; reasonCd: string }[]
  ): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      successNidList,
      failNidList,
    };

    return this.request('/api/api_nid/leave/result/removeData/byGame', body, false, {
      'Content-type': 'application/json;charset=UTF-8',
    }).catch((err) => {
      mlog.error('/api/api_nid/leave/result/removeData/byGame is failed', err);
    });
  }

  async reportBulkMailResult(
    successUserList: string[],
    failUserList: { userTarget: string; reasonCd: string }[],
    configId: number
  ): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      configId,
      successUserList,
      failUserList,
    };

    return this.request('/api/mailboxG/bulk/callback', body, false, {
      'Content-type': 'application/json;charset=UTF-8',
    }).catch((err) => {
      mlog.error('/api/mailboxG/bulk/callback is failed', err);
    });
  }

  async sendToSlack(message: string) {
    if (this.slackNotifier === undefined) {
      this.slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    }

    await this.slackNotifier.notify({
      username: 'host: ' + os.hostname(),
      text: message,
      channel: '#sdk-error',
    });
  }

  /**
   * 금칙어 포함 여부
   * @see https://developer.line.games/pages/viewpage.action?pageId=7277564
   */
  async hasBadWord(text: string): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      targetWord: text,
    };

    const result = await this.request('api/abuse/badword/v2/name/check', body);
    return result.badWordYn === 'Y';
  }

  /**
   * 탈퇴 유저 데이터 삭제 보고
   * @see https://developer.line.games/pages/viewpage.action?pageId=********
   */
  async reportDeleteUser(
    successNidList: string[],
    failNidList: { nid: string; reasonCd: string }[]
  ): Promise<boolean> {
    const body = {
      gameCd: mconf.LineGameCode,
      successNidList,
      failNidList,
    };

    const result = await this.request('api/api_nid/leave/result/removeData/byGame', body, false);
    return result.isSuccess;
  }

  /**
   * 게임 서버에 API 호출하는 LG 서버들의 IP 받아오기
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276121
   */
  async reqWhiteServerIpList() {
    const now = moment.utc().valueOf();
    if (now > this.whiteServerIpTTL) {
      const body = {
        gameCd: mconf.LineGameCode,
      };

      const result = await this.request('/api/auth/gameApi/ip/allowList', body);
      this.whiteServerIpList = result.ipList;
      this.whiteServerIpTTL = moment.utc().add(5, 'minute').valueOf();
      mlog.info('received white ip list from LG', result);
    }

    return this.whiteServerIpList;
  }

  /**
   * 유저 메일함 리스트 조회(글로벌 원빌드 Ver)
   * @see https://developer.line.games/pages/viewpage.action?pageId=7277889
   */
  getMails(userId: number, langCulture: string, level: number): Promise<any> {
    const body = {
      gameCd: mconf.LineGameCode,
      nid: userId,
      langCulture,
      timeZoneId: this._timeZoneId,
      timeZoneOffsetSec: this._timeZoneOffsetSec,
      gameServerId: mconf.worldId,
      playerLevel: level,
    };

    return this.request('api/mailboxG/current/list', body);
  }

  /**
   * 전체 클라이언트 버전의 설정 정보 조회(서버용)
   * 실제 용도는 client_version_status 값이 MAINTENANCE (점검) 인지 체크 위함
   * @see https://developer.line.games/pages/viewpage.action?pageId=19599517
   */
  async reqGetAllClientVersion(worldId: string, bForce: boolean = false) {
    const now = moment.utc().valueOf();
    if (bForce || now > this.allClientVersionTTL) {
      const body = {
        gameCd: mconf.LineGameCode,
        gameServerId: worldId,
      };

      const result = await this.request(
        '/api/api_version/getAllClientVersion/configInfo/forServer',
        body
      );

      this.allClientVersionData = result;
      this.allClientVersionTTL = moment.utc().add(2, 'minute').valueOf();
      mlog.info('received allClientVersion from LG', result);
    }

    return this.allClientVersionData;
  }

  async isWorldInMaintenance(worldId: string, bForce: boolean = false) {
    const allClientVersion = await this.reqGetAllClientVersion(worldId, bForce);
    if (
      allClientVersion?.versionList.length > 0 &&
      allClientVersion.versionList[0].client_version_status
    ) {
      const status = allClientVersion.versionList[0].client_version_status;

      // mlog.info('isWorldInMaintenance', {
      //   status,
      //   compareVal: `${LGClientVersionStatus[LGClientVersionStatus.MAINTENANCE]}`,
      // });

      if (status === `${LGClientVersionStatus[LGClientVersionStatus.MAINTENANCE]}`) {
        return true;
      }
    }
    return false;
  }

  /**
   * 불건전 채팅 신고
   * @see https://developer.line.games/pages/viewpage.action?pageId=43421116#
   */
  async reportBadChat(
    reportUserId: number,
    targetUserId: number,
    reasonCd: string,
    chatMsg: string,
    addInfo: string
  ) {
    const body = {
      gameCd: mconf.LineGameCode,
      gameServerId: mconf.worldId,
      reporterUserIdType: 'GAME_USER_ID',
      reporterUserIdValue: reportUserId.toString(),
      targetUserIdType: 'GAME_USER_ID',
      targetUserIdValue: targetUserId.toString(),
      reasonCd: reasonCd,
      reportedContent: chatMsg,
      reasonAddInfo: addInfo,
    };

    const url = `/api/${mconf.LineGameCode}/report`;

    return this.request(url, body);
  }

  /**
   * 불건전 채팅 신고 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=43421130
   */
  async reportBadChatReasonList(langCulture: string) {
    const body = {
      gameCd: mconf.LineGameCode,
      langCulture,
    };

    return this.request(`/api/${mconf.LineGameCode}/report/reason/list`, body);
  }

  /**
   * https://developer.line.games/pages/viewpage.action?pageId=60326757
   */
  async changeNidForServerMigration(nid: string, toGameServerId: string) {
    const body = {
      gameCd: mconf.LineGameCode,
      nid,
      toGameServerId,
      existToGameSeverNidDeleteYn: 'Y',
      changeReason: 'SERVER_MIGRATION',
    };

    return this.request('/api/gameServerId/change', body);
  }

  protected async request(url: string, body?: any, postForm: boolean = true, headersToMerge?: any) {
    try {
      if (this.nfToken === null || this.nfToken === '') {
        await this.refreshNfToken();
      }

      const headers = {
        nfToken: this.nfToken,
        gameCd: mconf.LineGameCode,
      };
      if (headersToMerge) {
        _.merge(headers, headersToMerge);
      }

      // https://jira.line.games/browse/UWO-19927 원인 파악을 위해
      if (!headers.nfToken) {
        mlog.error('Invalid nfToken.', { url, body, headers });
      }

      let resp: AxiosResponse<ResponseBody>;
      if (postForm) {
        resp = await this.mrest.postForm<ResponseBody>(url, body, {
          headers,
        });
      } else {
        resp = await this.mrest.post(url, body, {
          headers,
        });
      }

      const result = resp
        ? resp.data
        : {
            isSuccess: false,
            data: null,
            errorCd: LGErrorCode.UNKNOWN,
            msg: 'null',
          };

      if (result.isSuccess) {
        return result.data;
      } else {
        if (LGErrorCode[result.errorCd] == LGErrorCode.EXPIRE_AUTH_TOKEN) {
          await this.refreshNfToken();
          return await this.request(url, body, postForm, headersToMerge);
        } else {
          this.sendToSlack(
            `[NTSDK API] response error, url: ${url}, body: ${JSON.stringify(
              body
            )}, result: ${JSON.stringify(result)}`
          );

          throw new MError(`"${result.errorCd}", ${result.msg}`, MErrorCode.LGSDK_ERROR, result);
        }
      }
    } catch (error) {
      this.rethrow(url, body, error);
    }
  }

  private async delay(ms: number) {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275297
   */
  private async refreshNfToken() {
    // 만료된 토큰을 사용해서 API를 호출하는 경우, 최초 만료가 갱신을 시도하고 나머지들은 여기서 일정시간 머물게 된다.
    // 토큰이 갱신 되면 빠져나가고 자연스럽게 postForm을 재 호출 하는 구조.
    if (this.nfToken === '') {
      let retry = 10;
      while (--retry >= 0) {
        await this.delay(500);
        if (this.nfToken !== '') {
          return; // OK
        }
      }
    }

    try {
      this.nfToken = ''; // important! for prevent recursion.
      const resp: AxiosResponse<ResponseBody> = await this.mrest.postForm<ResponseBody>(
        '/api/auth/token/getNewToken',
        {
          gameCd: mconf.LineGameCode,
          authPwd: mconf.http.lgd.authPwd,
        }
      );

      const result = resp
        ? resp.data
        : {
            isSuccess: false,
            data: null,
            errorCd: LGErrorCode.UNKNOWN,
            msg: 'null',
          };

      if (result.isSuccess) {
        this.nfToken = result.data.nfToken;
        mlog.info('refreshNfToken success', result);
      } else {
        this.nfToken = null;
        mlog.error('refreshNfToken fail', result);
        this.sendToSlack(
          `[NTSDK API] 'getNewToken' response error, result: ${JSON.stringify(result)}`
        );

        throw new MError(`"${result.errorCd}", ${result.msg}`, MErrorCode.LGSDK_ERROR, result);
      }
    } catch (error) {
      mlog.error('[getServerHealth] rejection', error);
      throw new MError(error.message, MErrorCode.LGSDK_ERROR);
    }
  }
}
