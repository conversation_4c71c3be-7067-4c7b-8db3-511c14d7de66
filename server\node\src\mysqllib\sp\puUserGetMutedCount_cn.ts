// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_muted_count_cn';

const spFunction = query.generateSPFunction(spName);

export default async function (
    connection: query.Connection,
    userId: number,
): Promise<number> {
    const qr = await spFunction(connection, userId);
    const rows = qr.rows;
    console.log('rows:', rows);
    return Number.parseInt(rows[0][0].count);
}
