// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import moment from 'moment';

import { KICK_REASON } from '../../../motiflib/const';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mhttp from '../../../motiflib/mhttp';
import { LGLoginResult } from '../../../motiflib/mhttp/linegamesApiClient';
import mconf, { World } from '../../../motiflib/mconf';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { PLATFORM, ACCOUNT_ACCESS_LEVEL } from '../../../motiflib/model/auth/enum';
import * as mutil from '../../../motiflib/mutil';
import { MRedisConnPool } from '../../../redislib/connPool';
import * as kicker from '../../kicker';
import taLogin, { Result as AuthLoginResult } from '../../../mysqllib/txn/taLogin';
import { DBConnPool } from '../../../mysqllib/pool';
import glog from '../../../motiflib/gameLog';
import { MysqlReqRepCounter } from '../../../mysqllib/mysqlReqRepCounter';
import _ from 'lodash';

interface RequestBody {
  platform: number; // dev 환경에서만 유효. 값이 있을 경우 mconf.platform 대신 사용하여 로그인 진행한다.
  sessionToken: string; // DevLogin 경우 로그인 창에서 입력하는 아이디
  clientVersion?: string;
  revision: string; // client build(app) git revision
  patchRevision: string; // client patch(data) git revision
  worldId: string; // 선택한 월드 아이디

  // for admin
  loginPlatform: string; // 구글/페이스북 등

  // for glog. 기기에 어플 설치 후 최초 로그인 시에만 보낸다.
  os?: string; // OS 구분 값.
  osv?: string; // OS 버전 값
  dm?: string; // 기기 값. 기기 모델 값.
  deviceLang?: string; // language-culture 값으로 디바이스 언어값
  lang?: string; // 인게임 언어값
  v?: string; // 클라이언트 마켓 바이너리 버전
  sk?: string; // 스토어 타입
  country_ip?: string; // IP 기반 국가 코드
  isNewGnid?: boolean;
  isFirstAfterInstall?: boolean;
  bPrologue?: boolean;

  // [SDO Only]
  cachedAccountId?: string;
}

interface Response {
  gnid: string;
  nid: string;
  enterWorldToken: string;
  isNewUser: boolean;
  accessLevelForRejectReason?: ACCOUNT_ACCESS_LEVEL; // TODO 클라 처리 후 제거
  blockTimeUtcByAdmin?: number; // 운영툴에 의한 락 종료 시간
  worldAddresss: string; // 선택한 월드의 접속 주소
  worldPort: number; // 선택한 월드의 접속 포트
  gnidStatus: string; // NORMAL, LEAVE, DELETE, BLOCK
  orderWait: number; // 대기열 남은 순서(순번표 - 입장허용순번), 0이면 즉시 입장
  errorCd?: number; //
  floorPlatformUserId: string;
}

function findWorld(worldId: string): World {
  for (const world of mconf.worlds) {
    if (world.id == worldId) {
      return world;
    }
  }
}

export = async (req: RequestAs<RequestBody>, res: ResponseAs<JsonEmpty>) => {
  const { sessionToken, loginPlatform, clientVersion, worldId, bPrologue }: RequestBody = req.body;

  // [TEMP] production 환경에서 에디터 로그인 가능하게 하는 코드
  // const platform = req.body.platform !== undefined ? req.body.platform : mconf.platform;

  const platform =
    mconf.isDev && req.body.platform !== undefined ? req.body.platform : mconf.platform;

  mlog.info('[RX] /login', { body: req.body, platform: PLATFORM[platform] });

  const dbConnPool = Container.get(DBConnPool);
  const world = findWorld(worldId);

  const resp: Response = {
    gnid: undefined,
    nid: undefined,
    enterWorldToken: undefined,
    isNewUser: false,
    worldAddresss: world?.address,
    worldPort: world?.port,
    gnidStatus: 'NORMAL',
    orderWait: 0,
    floorPlatformUserId: undefined,
  };

  const reqRepCounter = Container.get(MysqlReqRepCounter);
  if (reqRepCounter.isLimitOver()) {
    resp.errorCd = MErrorCode.AUTH_LOGIN_FAILED_WITH_SERVER_BUSY;

    const limit = reqRepCounter.getLimitCount();
    const cnt = reqRepCounter.getCount();
    mlog.warn(`request-respons-count over`, { cnt, limit });

    _rejectLogin(res, resp, undefined, 503);
    return;
  }

  // https://developer.line.games/pages/viewpage.action?pageId=7275349
  let accountId: string; // (lg:gnid, editor:로그인 창에서 입력하는 아이디)
  const curTimeUtc = mutil.curTimeUtc();
  let bWhitePassOk = false;

  // TODO admin 로그인 처리

  try {
    // Platform-specific authentication
    let response: LGLoginResult = null;
    if (platform === PLATFORM.LINE) {
      response = await mhttp.lgd.login(sessionToken);
    }

    if (platform === PLATFORM.LINE) {
      accountId = response.gnid;

      resp.gnid = response.gnid;
      resp.nid = response.nid;
      resp.gnidStatus = response.gnidStatus;

      if (resp.gnidStatus === 'LEAVE') {
        // 탈퇴 요청한 계정이므로 로그인 시키지 않고 복구 여부를 클라가 확인하는 과정으로 진행 되어야 합니다.
        return _send(res, resp);
      }

      if (resp.gnidStatus !== 'NORMAL') {
        // 삭제 또는 블럭된 유저이므로 로그인을 거부해야 합니다.
        return _send(res, resp);
      }

      const whiteYn = response.whiteNidYn.toLowerCase();
      if (whiteYn === 'y') {
        bWhitePassOk = true;
      }

      const accountList = response.accountList;
      if (accountList && _.isArray(accountList) && accountList.length > 0) {
        // https://developer.line.games/pages/viewpage.action?pageId=7275997 FLOOR: 130
        const floorAccount = accountList.find((elem) => elem.platformId === 130);
        if (floorAccount) {
          resp.floorPlatformUserId = floorAccount.platformUserId;
        }
      }
    } else if (platform === PLATFORM.SDO) {
      // SDO의 경우 여러번 로그인을 허용하지 않으므로, 캐싱된 accountId 로 로그인 여부를 판단해야함.
      const { cachedAccountId } = req.body;

      if (cachedAccountId && cachedAccountId.length > 0) {
        accountId = cachedAccountId;
      } else {
        const loginResponse = await mhttp.platformApi.login(sessionToken) as SdoLoginResponse;
        accountId = String(loginResponse.data.userid);
      }

      resp.gnid = accountId;
      resp.nid = accountId; //TODO
    } else if (platform === PLATFORM.DEV) {
      accountId = sessionToken;
      resp.gnid = accountId;

      const krPattern = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
      const blankPattern = /[\s]/g;
      if (krPattern.test(accountId) || blankPattern.test(accountId)) {
        throw new MError('invalid-id', MErrorCode.AUTH_INVALID_PUB_ID, req.body);
      }
    } else {
      throw new MError('invalid-platform', MErrorCode.AUTH_INVALID_PLATFORM, req.body);
    }

    await _login(
      res,
      req.body,
      accountId,
      curTimeUtc,
      resp,
      loginPlatform,
      clientVersion,
      worldId,
      bWhitePassOk,
      bPrologue
    );
  } catch (error: any) {
    mlog.error('/login failed', {
      msg: error.message,
    });

    if (error instanceof MError) {
      throw new MError(`'/login' api err`, error.mcode, error.message);
    }
    throw new MError(error.message, MErrorCode.AUTH_LOGIN_ERROR, undefined, error.stack);
  }
};

async function _login(
  res: ResponseAs<JsonEmpty>,
  reqBody: RequestBody,
  accountId: string,
  curTimeUtc: number,
  resp: Response,
  loginPlatform: string,
  clientVersion: string,
  worldId: string,
  bWhitePassOk: boolean,
  bPrologue: boolean
): Promise<any> {
  resp.enterWorldToken = mutil.generateEnterWorldToken(accountId);
  const dbConnPool = Container.get(DBConnPool);

  const loginDbResult = await taLogin(
    dbConnPool.getPool(),
    accountId,
    curTimeUtc,
    mconf.defaultAccessLevel,
    loginPlatform,
    clientVersion
  );

  if (loginDbResult.bBlockedByAdmin) {
    return _rejectLogin(res, resp, loginDbResult, 200);
  }

  resp.isNewUser = loginDbResult.bIsNewUser;
  let lastLobbyForKick: any, userIdToKick: any;
  if (loginDbResult.isOnline === 1) {
    lastLobbyForKick = loginDbResult.lastLobby;
    for (const world of loginDbResult.worlds) {
      if (world.worldId === loginDbResult.lastWorldId) {
        userIdToKick = world.userId;
        break;
      }
    }
  }

  mlog.info('loginDbResult', { loginDbResult, reqBody });

  // Process order logic
  let orderId: number;
  if (bWhitePassOk) {
    // whiteip인경우 그냥 orderid는 0설정.
    resp.orderWait = 0;
    orderId = 0;
  } else if (bPrologue) {
    // 프롤로그 끝나고 로그인인 경우 대기열 통과처리
    // 프롤로그 유저여부 검증 (만약 프롤로그 정보가 없으면 일반 대기열 처리 적용)
    const minTs = curTimeUtc - mconf.prologueGnidTimeout;

    const orderRedis = Container.of('order-redis').get(MRedisConnPool);
    const retResult = await orderRedis['checkPrologueGnid'](accountId, worldId, minTs);

    if (!retResult) {
      mlog.info('login checkPrologueGnid expired..applying order', {
        accountId,
      });
      // 프롤로그 상태가 유효하지않은 경우 대기열 적용
      orderId = await _processOrder(worldId, resp);
    } else {
      // 유효한 경우 대기열 통과
      resp.orderWait = 0;
      orderId = 0;
    }
  } else {
    orderId = await _processOrder(worldId, resp);
  }

  // enterWorldToken, 순번표 redis 에 기록.
  const userCacheRedis = Container.get(MRedisConnPool);
  await userCacheRedis['setEnterWorldToken'](
    accountId,
    resp.enterWorldToken,
    curTimeUtc,
    reqBody.revision,
    reqBody.patchRevision,
    worldId,
    orderId
  );

  if (userIdToKick) {
    mlog.warn('/login user already online', {
      accountId,
      userId: userIdToKick,
      lastLobby: lastLobbyForKick,
    });

    // TODO jaykay: lastLobby
    await kicker.kick(userIdToKick, lastLobbyForKick, KICK_REASON.DUPLICATE_LOGIN);
  }

  //set user heartBeat
  await userCacheRedis['updateUserHeartBeat'](accountId, curTimeUtc);

  const minOffSailTs = curTimeUtc - mconf.offlineSailingHeartBeatInterval;
  const offlineSailingResult = await userCacheRedis['getOfflineSailingInfo'](accountId, minOffSailTs);
  const offlineSailingInfo = JSON.parse(offlineSailingResult);

  // mlog.warn('getOfflineSailingInfo result', {
  //   accountId,
  //   userId: offlineSailingInfo.userId,
  //   appId: offlineSailingInfo.appId,
  // });

  // offlineSailing 진행중인경우 중단시킨다
  if (offlineSailingInfo.userId && offlineSailingInfo.appId) {
    mlog.warn('/bot user is online', {
      accountId,
      userId: offlineSailingInfo.userId,
      appId: offlineSailingInfo.appId,
    });

    await kicker.kick(
      offlineSailingInfo.userId,
      offlineSailingInfo.appId,
      KICK_REASON.OFFLINE_SAILING_BOT
    );
  }

  // glog
  const {
    os,
    osv,
    dm,
    deviceLang,
    lang,
    v,
    sk,
    loginPlatform: reqLoginPlatform,
    country_ip,
    isNewGnid,
    isFirstAfterInstall,
  }: RequestBody = reqBody;

  if (isFirstAfterInstall) {
    // deviceType 을 클라에서 보내준 경우에만 common_installed 로그를 남긴다. (최초 로그인)
    glog('common_installed', {
      _time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      os,
      osv,
      dm,
      lang: deviceLang,
      v,
      sk,
    });
  }

  if (isNewGnid) {
    glog('common_gnid_register', {
      _time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      os,
      osv,
      dm,
      lang: deviceLang,
      lang_game: lang,
      v,
      sk,
      platform: reqLoginPlatform,
      country_ip,
      gnid: resp.gnid,
    });
  }

  _send(res, resp);
}

async function _processOrder(worldId: string, resp: Response): Promise<number> {
  // 현재 동접조회
  const monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
  const retuserCount = await monitorRedis['getUserCount']();
  const userCount = JSON.parse(retuserCount);

  let curWorldUsers: number = 0;
  if (userCount.user.world[worldId]) {
    curWorldUsers = userCount.user.world[worldId];
  }

  const orderIdCheckThreshold = (mconf.maxUsersPerWorld * mconf.orderIdCheckWorldUserRate) / 100;
  const curTimeMsec = new Date().getTime();
  const minTs = Math.floor(curTimeMsec / 1000) - mconf.prologueGnidTimeout;

  const orderRedis = Container.of('order-redis').get(MRedisConnPool);
  const prologueUserCount = await orderRedis['queryPrologueGnidsCount'](worldId, minTs);

  // 프롤로그 진행중인 유저수도 합산해서 계산한다
  curWorldUsers = curWorldUsers + prologueUserCount;

  let mode: number;
  if (curWorldUsers >= mconf.maxUsersPerWorld) {
    mode = 0; // 최대동접 초과된 경우. 순번표만 발급
  } else if (curWorldUsers < orderIdCheckThreshold) {
    // 대기열 적용 조건(임계값 미만 && 대기열에 다른 유저가 존재)이 충족되지 않는 경우에는
    // 순번표 발급 없이 월드 입장허용
    mode = 1;
  } else {
    mode = 2; // 임계값 이상인 경우 대기열 적용
  }

  // 동접이 임계값 이하이더라도 로그인 완료한 순번과 발급한 순번의 차이가 크다면
  // 대기열에 유저들이 남아 있다는 의미
  // (임계값을 넘어서 대기열이 작동한 상황에서 임계값이하로 떨어진 경우)
  // 이러한 경우 새로 들어오는 유저들도 대기열의 뒤에 넣어서 순서가 지켜지도록 한다
  const retResult = await orderRedis['generateOrderAndUpdateAllowedOrderId'](
    worldId,
    mode,
    curTimeMsec,
    mconf.maxUsersPerWorld,
    curWorldUsers,
    mconf.maxAllowOrderIdPerSec
  );

  if (retResult && retResult[0]) {
    const result = JSON.parse(retResult[0]);

    resp.orderWait = Math.max(result.orderId - result.lastAllowedOrderId, 0);

    // [임시] 대기열 디버깅용 정보
    if (retResult[1]) {
      const debug = JSON.parse(retResult[1]);
      mlog.info('[TEMP] login order', {
        accountId: resp.gnid,
        maxUsersPerWorld: mconf.maxUsersPerWorld,
        curWorldUsers,
        orderIdCheckThreshold,
        debug,
        orderId: result.orderId,
        lastAllowedOrderId: result.lastAllowedOrderId,
        orderWait: resp.orderWait,
      });
    }

    return result.orderId;
  }
}

function _rejectLogin(
  res: ResponseAs<JsonEmpty>,
  resp: Response,
  loginDbResult: AuthLoginResult,
  status: number
) {
  if (loginDbResult) {
    resp.accessLevelForRejectReason = ACCOUNT_ACCESS_LEVEL.UNDER_CONTROL_BY_ADMIN;
    resp.blockTimeUtcByAdmin = loginDbResult.blockTimeUtcByAdmin;
  }
  if (resp.enterWorldToken) {
    delete resp.enterWorldToken;
  }
  _send(res, resp, status);
}

function _send(res: ResponseAs<JsonEmpty>, resp: Response, status: number = 200) {
  mlog.info('[TX] /login', { body: resp });

  res.status(status).json(resp);
}
