"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const lodash_1 = __importDefault(require("lodash"));
const fp_1 = require("lodash/fp");
const assert_1 = __importDefault(require("assert"));
const crypto = __importStar(require("crypto"));
const bluebird_1 = require("bluebird");
const cms_1 = __importDefault(require("../cms"));
const cmsEx = __importStar(require("../cms/ex"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const formula_1 = require("../formula");
const merror_1 = require("../motiflib/merror");
const mutil = __importStar(require("../motiflib/mutil"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const pointDesc_1 = require("../cms/pointDesc");
const guildUtil_1 = require("./guildUtil");
const mconf_1 = __importDefault(require("../motiflib/mconf"));
// ----------------------------------------------------------------------------
// UserPoints object.
// ----------------------------------------------------------------------------
class UserPoints {
    constructor() {
        this._points = {};
        this._installmentSavings = {
            accumPoint: 0,
            accumRate: 0,
            accumCount: 0,
            lastCmsId: 0,
            lastDepositTimeUtc: 0,
        };
        this._insurance = {
            insuranceCmsId: 0,
            unpaidTradeGoods: 0,
            unpaidShip: 0,
            unpaidSailor: 0,
            unpaidDucat: 0,
        };
        this._userId = 0;
        this._freeRedGem = 0;
        this._paidRedGem = 0;
        this._mileages = [];
    }
    clone() {
        const c = new UserPoints();
        c.cloneSet(lodash_1.default.cloneDeep(this._points), lodash_1.default.cloneDeep(this._installmentSavings), lodash_1.default.cloneDeep(this._insurance), this._userId, this._freeRedGem, this._paidRedGem, lodash_1.default.cloneDeep(this._mileages));
        return c;
    }
    cloneSet(points, installmentSavings, insurance, userId, freeRedGem, paidRedGem, mileages) {
        this._points = points;
        this._installmentSavings = installmentSavings;
        this._insurance = insurance;
        this._userId = userId;
        this._freeRedGem = freeRedGem;
        this._paidRedGem = paidRedGem;
        this._mileages = mileages;
    }
    initWithLoginInfo(loginInfo) {
        for (const point of loginInfo.points) {
            const value = point.value;
            if (mutil.isNotANumber(value)) {
                mlog_1.default.error('points point.cmsId is NaN', { userId: loginInfo.userId, cmsId: point.cmsId });
                continue;
                // TODO: login should be failed.
            }
            this._points[point.cmsId] = point.value;
        }
        this._installmentSavings = (0, fp_1.update)('lastDepositTimeUtc', parseInt, loginInfo.installmentSavings);
        this._insurance = {
            insuranceCmsId: loginInfo.insurance.insuranceCmsId,
            unpaidTradeGoods: parseInt(loginInfo.insurance.unpaidTradeGoods, 10),
            unpaidShip: parseInt(loginInfo.insurance.unpaidShip, 10),
            unpaidSailor: parseInt(loginInfo.insurance.unpaidSailor, 10),
            unpaidDucat: parseInt(loginInfo.insurance.unpaidDucat, 10),
        };
        this._userId = loginInfo.userId;
        if (loginInfo.mileages) {
            for (const elem of loginInfo.mileages) {
                this._mileages.push({
                    month: elem.month,
                    value: elem.value,
                    bIsExpirationNotified: elem.isExpirationNotified === 1,
                });
            }
            this._mileages.sort((a, b) => {
                return a.month - b.month;
            });
        }
    }
    getPoint(cmsId) {
        (0, assert_1.default)(cmsId !== cmsEx.EnergyPointCmsId);
        (0, assert_1.default)(cmsId !== cmsEx.CashShopMileage);
        if (this._points[cmsId]) {
            return this._points[cmsId];
        }
        return 0;
    }
    getMileage(curTimeUtc) {
        let v = 0;
        const thisMonth = UserPoints.calcMileageMonth(curTimeUtc);
        for (const elem of this._mileages) {
            if (elem.month <= thisMonth - cms_1.default.Define.MileageExpirationMonth) {
                continue;
            }
            v += elem.value;
        }
        return v;
    }
    getExpireMileageInThisMonth(curTimeUtc) {
        const month = UserPoints.calcMileageMonth(curTimeUtc) - cms_1.default.Define.MileageExpirationMonth + 1;
        const mileage = this._mileages.find((elem) => elem.month === month);
        return mileage;
    }
    // battleParam 을 만들 때 사용함. 다른 경우에는 사용하지 않는걸 권장함 (write 하면 절대 안됨)
    getPointTable() {
        return this._points;
    }
    // ----------------------------------------------------------------------------
    // 추가로 소지 가능한 금액을 리턴
    // ----------------------------------------------------------------------------
    calcAddable(cmsId, value) {
        (0, assert_1.default)(cmsId !== cmsEx.EnergyPointCmsId);
        (0, assert_1.default)(cmsId !== cmsEx.CashShopMileage);
        const hardCap = cms_1.default.Point[cmsId].hardCap;
        if (!hardCap) {
            mlog_1.default.error('undefined hardCap.', { userId: this._userId, cmsId, value });
            return 0;
        }
        const myCurrentPoint = this.getPoint(cmsId);
        if (myCurrentPoint === undefined) {
            mlog_1.default.error('undefined _point.', { userId: this._userId, cmsId, value });
            return 0;
        }
        if (value < 0) {
            if (myCurrentPoint <= 0) {
                return 0;
            }
            else if (myCurrentPoint + value < 0) {
                return -myCurrentPoint;
            }
        }
        if (myCurrentPoint + value > hardCap) {
            return hardCap - myCurrentPoint;
        }
        return value;
    }
    // ----------------------------------------------------------------------------
    // 포인트의 값이 hardcap보다 많은 지 체크
    // ----------------------------------------------------------------------------
    isGreaterThanHardCap(cmsId, value) {
        (0, assert_1.default)(cmsId !== cmsEx.EnergyPointCmsId);
        const hardCap = cms_1.default.Point[cmsId].hardCap;
        if (!hardCap) {
            mlog_1.default.error('undefined hard cap.', { userId: this._userId, cmsId, value });
            return false;
        }
        return value > hardCap ? true : false;
    }
    // ----------------------------------------------------------------------------
    // 포인트가 마이너스 값인지 체크
    // ----------------------------------------------------------------------------
    isMinusPoint(point) {
        return point < 0 ? true : false;
    }
    // glog 과정이 없기에 userChangeOperator 와 같이 실 데이터 반영하지 않는 곳에서만 사용해야 됨.
    // applyPointChanges 사용 바람.
    setPointForChangeTask(cmsId, value) {
        (0, assert_1.default)(cmsId !== cmsEx.EnergyPointCmsId);
        (0, assert_1.default)(cmsId !== cmsEx.CashShopMileage);
        const hardCap = cms_1.default.Point[cmsId].hardCap;
        if (value > hardCap) {
            mlog_1.default.error('Point value is greater than hard cap.', { userId: this._userId, cmsId, value });
            value = hardCap;
        }
        this._points[cmsId] = value;
    }
    getInstallmentSavings() {
        return this._installmentSavings;
    }
    setInstallmentSavings(installmentSavings) {
        this._installmentSavings = installmentSavings;
    }
    setInstallmentSavingsLastDepositTimeUtc(timeUtc) {
        this._installmentSavings.lastDepositTimeUtc = timeUtc;
    }
    getInsuranceCmsId() {
        return this._insurance.insuranceCmsId;
    }
    setInsuranceCmsId(cmsId) {
        this._insurance.insuranceCmsId = cmsId;
    }
    getInsurance() {
        return this._insurance;
    }
    applyInsuranceChange(x) {
        if (x.unpaidTradeGoods !== undefined) {
            this._insurance.unpaidTradeGoods = x.unpaidTradeGoods;
        }
        if (x.unpaidShip !== undefined) {
            this._insurance.unpaidShip = x.unpaidShip;
        }
        if (x.unpaidSailor !== undefined) {
            this._insurance.unpaidSailor = x.unpaidSailor;
        }
        if (x.unpaidDucat !== undefined) {
            this._insurance.unpaidDucat = x.unpaidDucat;
        }
    }
    applyInsuranceUnpaidChange(x) {
        lodash_1.default.merge(this._insurance, x);
    }
    /**
     *
     * @param pointCosts
     * @param bPermitExchange 포인트가 부족할 경우 환전을 허용할 것인지.
     * @param lgCashParam lg cash 사용에 필요한 인자
     * @param ensurePointIsEnough
     * @returns null 인 경우 포인트 부족
     */
    buildPointAndCashChangesByPayment(pointCosts, bPermitExchange, lgCashParam, ensurePointIsEnough, pr_data) {
        if (!pointCosts || pointCosts.length === 0) {
            return undefined;
        }
        const pointChanges = [];
        const cashPayments = [];
        const prDataObj = {};
        for (const pointCost of pointCosts) {
            (0, assert_1.default)(pointCost.cmsId !== cmsEx.EnergyPointCmsId);
            (0, assert_1.default)(pointCost.cmsId !== cmsEx.CashShopMileage);
            if (pointCost.cost === 0) {
                continue;
            }
            (0, assert_1.default)(pointCost.cost > 0);
            if ((0, pointDesc_1.isCash)(pointCost.cmsId)) {
                (0, assert_1.default)(lgCashParam && (lgCashParam.itemId || lgCashParam.productId));
                cashPayments.push({
                    cmsId: pointCost.cmsId,
                    amount: pointCost.cost,
                    lgCashParam,
                });
                if (!prDataObj[pointCost.cmsId]) {
                    prDataObj[pointCost.cmsId] = {
                        type: pointCost.cmsId,
                        amt: 0,
                    };
                }
                prDataObj[pointCost.cmsId].amt += pointCost.cost;
            }
            else {
                const change = pointChanges.find((elem) => elem.cmsId === pointCost.cmsId);
                let oldPoint;
                if (change) {
                    oldPoint = change.value;
                }
                else {
                    oldPoint = this.getPoint(pointCost.cmsId);
                }
                let newPoint = oldPoint - pointCost.cost;
                if (newPoint < 0 &&
                    bPermitExchange &&
                    (pointCost.cmsId === cmsEx.DucatPointCmsId || pointCost.cmsId === cmsEx.BlueGemPointCmsId)) {
                    // 환전 처리
                    let ratio;
                    if (pointCost.cmsId === cmsEx.DucatPointCmsId) {
                        ratio = (0, formula_1.CalcDucatExchangeRatio)(-newPoint);
                    }
                    else if (pointCost.cmsId === cmsEx.BlueGemPointCmsId) {
                        ratio = cms_1.default.Const.BluegemExchangePer.value;
                    }
                    // 필요 레드젬
                    const exchangingCost = Math.ceil(-newPoint / ratio);
                    (0, assert_1.default)(lgCashParam && lgCashParam.itemId);
                    cashPayments.push({
                        cmsId: cmsEx.RedGemPointCmsId,
                        amount: exchangingCost,
                        lgCashParam,
                        glogPointExchangeData: {
                            target_type: pointCost.cmsId,
                            target_price: pointCost.cost,
                            cur_value: oldPoint,
                            exchange_value: -newPoint,
                            exchange_gem: exchangingCost,
                        },
                    });
                    if (!prDataObj[cmsEx.RedGemPointCmsId]) {
                        prDataObj[cmsEx.RedGemPointCmsId] = {
                            type: cmsEx.RedGemPointCmsId,
                            amt: 0,
                        };
                    }
                    prDataObj[cmsEx.RedGemPointCmsId].amt += exchangingCost;
                    newPoint = 0;
                }
                else if (newPoint < 0) {
                    if (ensurePointIsEnough) {
                        throw new merror_1.MError('not-enough-point', merror_1.MErrorCode.NOT_ENOUGH_POINT, {
                            pointCost,
                            oldPoint,
                            newPoint,
                            bPermitExchange,
                        });
                    }
                    return undefined;
                }
                if (oldPoint !== newPoint) {
                    if (change) {
                        change.value = newPoint;
                    }
                    else {
                        pointChanges.push({
                            cmsId: pointCost.cmsId,
                            value: newPoint,
                        });
                    }
                    if (!prDataObj[pointCost.cmsId]) {
                        prDataObj[pointCost.cmsId] = {
                            type: pointCost.cmsId,
                            amt: 0,
                        };
                    }
                    prDataObj[pointCost.cmsId].amt += oldPoint - newPoint;
                }
            }
        }
        if (pr_data) {
            lodash_1.default.forOwn(prDataObj, (elem) => {
                pr_data.push(elem);
            });
        }
        return { pointChanges, cashPayments };
    }
    applyPointChanges(pointChanges, glogParam) {
        if (!pointChanges || pointChanges.length === 0) {
            return {};
        }
        if (glogParam) {
            for (const change of pointChanges) {
                (0, assert_1.default)(change.cmsId !== cmsEx.EnergyPointCmsId);
                (0, assert_1.default)(change.cmsId !== cmsEx.CashShopMileage);
                (0, assert_1.default)(!(0, pointDesc_1.isCash)(change.cmsId));
                if (change.cmsId === cmsEx.DucatPointCmsId || change.cmsId === cmsEx.BlueGemPointCmsId) {
                    glogParam.user.glog(change.cmsId === cmsEx.DucatPointCmsId ? 'common_gamemoney' : 'common_bluegem', {
                        rsn: glogParam.rsn,
                        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                        fcv: change.value - this.getPoint(change.cmsId),
                        frv: change.value,
                        pcv: 0,
                        prv: 0,
                        sk: glogParam.user.storeCode,
                    });
                }
                else if (change.cmsId === cmsEx.ContributionPointCmsId ||
                    change.cmsId === cmsEx.ResearchPointCmsId) {
                    glogParam.user.glog(change.cmsId === cmsEx.ContributionPointCmsId ? 'contribution_point' : 'research_point', {
                        rsn: glogParam.rsn,
                        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                        cv: change.value - this.getPoint(change.cmsId),
                        rv: change.value,
                    });
                }
                else if (change.cmsId === cmsEx.GuildCoinCmsId) {
                    guildUtil_1.GuildLogUtil.gLog_GuildPoint(glogParam.user, glogParam.rsn, glogParam.add_rsn ? glogParam.add_rsn : null, change.value - this.getPoint(change.cmsId), // cv
                    change.value // rv
                    );
                }
            }
        }
        const sync = {};
        for (const elem of pointChanges) {
            this._points[elem.cmsId] = elem.value;
            lodash_1.default.merge(sync, {
                add: {
                    points: {
                        [elem.cmsId]: elem,
                    },
                },
            });
        }
        return sync;
    }
    queryCash(user) {
        return Promise.resolve().then(() => {
            if (user.isTestBot()) {
                return Promise.resolve();
            }
            else {
                return mhttp_1.default.platformBillingApi
                    .queryCash(user.userId, user.storeCode, user.countryCreated)
                    .then((ret) => {
                    this._handleCashResp(ret, undefined);
                });
            }
        });
    }
    static generateExchangeHash(userId) {
        return crypto
            .createHash('sha1')
            .update(`exchange_${userId}_${new Date().getTime()}_${Math.random()}`, 'utf8')
            .digest('hex');
    }
    tryConsumeCashs(cashPayments, sync, user, glogParam) {
        if (!cashPayments || cashPayments.findIndex((elem) => (0, pointDesc_1.isCash)(elem.cmsId)) === -1) {
            return Promise.resolve();
        }
        return bluebird_1.Promise
            .each(cashPayments, (change) => {
            (0, assert_1.default)(change.lgCashParam && (change.lgCashParam.itemId || change.lgCashParam.productId));
            return this.consumeCash(change.cmsId, change.amount, change.lgCashParam, user, {
                user: glogParam.user,
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn,
                exchangeHash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
                pointExchangeData: change.glogPointExchangeData,
            });
        })
            .then(() => {
            for (const change of cashPayments) {
                lodash_1.default.merge(sync, {
                    add: {
                        points: {
                            [change.cmsId]: {
                                cmsId: change.cmsId,
                                value: this.getPoint(change.cmsId),
                            },
                        },
                    },
                });
            }
        });
    }
    tryAddCashs(cashGains, sync, user, glogParam) {
        if (mconf_1.default.isSDO) {
            if (!cashGains || cashGains.findIndex((elem) => (0, pointDesc_1.isCash)(elem.cmsId)) === -1) {
                return Promise.resolve();
            }
        }
        else {
            if (!cashGains) {
                return Promise.resolve();
            }
        }
        const promises = [];
        for (const change of cashGains) {
            promises.push(this.addCash(change.cmsId, change.amount, change.reason, user, glogParam));
        }
        return Promise.all(promises).then(() => {
            for (const change of cashGains) {
                lodash_1.default.merge(sync, {
                    add: {
                        points: {
                            [change.cmsId]: {
                                cmsId: change.cmsId,
                                value: this.getPoint(change.cmsId),
                            },
                        },
                    },
                });
            }
        });
    }
    consumeCash(pointCmsId, amount, lgCashParam, user, glogParam) {
        if (!mconf_1.default.isSDO) {
            (0, assert_1.default)((0, pointDesc_1.isCash)(pointCmsId));
        }
        (0, assert_1.default)(amount > 0);
        return mhttp_1.default.platformBillingApi
            .consumeCash(user.userId, user.accountId, user.storeCode, user.countryCreated, pointCmsId, amount, lgCashParam)
            .then((ret) => {
            if (lgCashParam && lgCashParam.productId) {
                this._handleCashResp(ret ? ret.balanceList : undefined, glogParam);
            }
            else {
                this._handleCashResp(ret, glogParam);
            }
        });
    }
    addCash(pointCmsId, amount, reason, user, glogParam) {
        (0, assert_1.default)((0, pointDesc_1.isCash)(pointCmsId));
        (0, assert_1.default)(amount > 0);
        return mhttp_1.default.platformBillingApi
            .addCash(user.userId, user.accountId, user.storeCode, user.countryCreated, pointCmsId, amount, reason)
            .then((ret) => {
            this._handleCashResp(ret, glogParam);
        });
    }
    tryBidding(blindCmsId, user, amount, reason, sync, glogParam) {
        return mhttp_1.default.platformBillingApi
            .tryBidding(blindCmsId, user.userId, user.storeCode, user.accountId, user.countryCreated, amount, reason)
            .then((ret) => {
            lodash_1.default.merge(sync, this._handleCashResp(ret, glogParam));
        });
    }
    bidResult(blindCmsId, user, bWinner, sync, rsn, glogParam) {
        return mhttp_1.default.platformBillingApi
            .bidResult(blindCmsId, user.userId, user.storeCode, user.accountId, user.countryCreated, bWinner, rsn)
            .then((ret) => {
            lodash_1.default.merge(sync, this._handleCashResp(ret, glogParam));
        });
    }
    /**
     * 빌링으로 구입한 재화 충전 요청이 완료된 뒤에 호출.
     * 유저 데이터 적용 및 glog
     */
    onChargeByPurchaseProduct(data, glogParam) {
        // [빌링] 중국의 경우에는 처리 로직이 다름. (유료/무료 재화를 플랫폼에 의존하지 않고 서버에서 직접 처리)
        if (mconf_1.default.isSDO) {
            return this._handleCashResp_SDO(data, glogParam);
        }
        else {
            return this._handleCashResp(data, glogParam);
        }
    }
    _handleCashResp(data, glogParam) {
        if (!data) {
            return {};
        }
        const oldRedGem = this.getPoint(cmsEx.RedGemPointCmsId);
        const oldFreeRedGem = this._freeRedGem;
        const oldPaidRedGem = this._paidRedGem;
        this._points[cmsEx.RedGemPointCmsId] = 0;
        for (const elem of data) {
            if (elem.coinCd === 'red_gem') {
                const v = elem.balance;
                this._points[cmsEx.RedGemPointCmsId] += v;
                if (elem.paymentType === 'PAID') {
                    this._paidRedGem = v;
                }
                else {
                    this._freeRedGem = v;
                }
            }
        }
        const newRedGem = this.getPoint(cmsEx.RedGemPointCmsId);
        if (glogParam && oldRedGem !== newRedGem) {
            glogParam.user.glog('common_gem', {
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                fcv: this._freeRedGem - oldFreeRedGem,
                frv: this._freeRedGem,
                pcv: this._paidRedGem - oldPaidRedGem,
                prv: this._paidRedGem,
                sk: glogParam.user.storeCode,
                exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
            });
        }
        if (glogParam && glogParam.pointExchangeData) {
            // TODO 확인 필요
            // '만들어진 환전 정보(빌링 서버에 요청한 amount)'와
            // '빌링 서버에서 받은 수치(balance)'가 다를 경우가 있는 지
            // 레드젬 10을 소모하는 걸로 요청했지만 마일리지가 대신 깎여서 처리 된다는 등..
            const glogExchangeData = glogParam.pointExchangeData;
            glogParam.user.glog('common_gem_exchange', {
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                target_type: glogExchangeData.target_type,
                target_price: glogExchangeData.target_price,
                cur_value: glogExchangeData.cur_value,
                exchange_value: glogExchangeData.exchange_value,
                exchange_gem: glogExchangeData.exchange_gem,
                exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
            });
        }
        return {
            add: {
                points: {
                    [cmsEx.RedGemPointCmsId]: {
                        cmsId: cmsEx.RedGemPointCmsId,
                        value: newRedGem,
                    },
                },
            },
        };
    }
    // [빌링] 중국 전용 (재화를 직접 처리하기 때문에 로직 차이가 있음. 차후 리팩토링은 필요함.)
    _handleCashResp_SDO(data, glogParam) {
        if (!data) {
            return {};
        }
        const oldFreeRedGemPoint = this.getPoint(cmsEx.RedGemPointCmsId);
        const oldPaidRedGemPoint = this.getPoint(cmsEx.PaidRedGemPointCmsId);
        const oldFreeRedGem = this._freeRedGem;
        const oldPaidRedGem = this._paidRedGem;
        this._points[cmsEx.RedGemPointCmsId] = 0;
        this._points[cmsEx.PaidRedGemPointCmsId] = 0;
        for (const elem of data) {
            if (elem.coinCd === 'red_gem') {
                const v = elem.balance;
                if (elem.paymentType === 'PAID') {
                    this._points[cmsEx.PaidRedGemPointCmsId] += v;
                }
                else {
                    this._points[cmsEx.RedGemPointCmsId] += v;
                }
            }
        }
        this._paidRedGem = this._points[cmsEx.PaidRedGemPointCmsId];
        this._freeRedGem = this._points[cmsEx.RedGemPointCmsId];
        const newFreeRedGemPoint = this.getPoint(cmsEx.RedGemPointCmsId);
        const newPaidRedGemPoint = this.getPoint(cmsEx.PaidRedGemPointCmsId);
        if (glogParam && (oldFreeRedGemPoint !== newFreeRedGemPoint || oldPaidRedGemPoint !== newPaidRedGemPoint)) {
            glogParam.user.glog('common_gem', {
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                fcv: this._freeRedGem - oldFreeRedGem,
                frv: this._freeRedGem,
                pcv: this._paidRedGem - oldPaidRedGem,
                prv: this._paidRedGem,
                sk: glogParam.user.storeCode,
                exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
            });
        }
        if (glogParam === null || glogParam === void 0 ? void 0 : glogParam.pointExchangeData) {
            // TODO 확인 필요
            // '만들어진 환전 정보(빌링 서버에 요청한 amount)'와
            // '빌링 서버에서 받은 수치(balance)'가 다를 경우가 있는 지
            // 레드젬 10을 소모하는 걸로 요청했지만 마일리지가 대신 깎여서 처리 된다는 등..
            const glogExchangeData = glogParam.pointExchangeData;
            glogParam.user.glog('common_gem_exchange', {
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                target_type: glogExchangeData.target_type,
                target_price: glogExchangeData.target_price,
                cur_value: glogExchangeData.cur_value,
                exchange_value: glogExchangeData.exchange_value,
                exchange_gem: glogExchangeData.exchange_gem,
                exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
            });
        }
        return {
            add: {
                points: {
                    // 무료 레드젬
                    [cmsEx.RedGemPointCmsId]: {
                        cmsId: cmsEx.RedGemPointCmsId,
                        value: newFreeRedGemPoint,
                    },
                    // 유료 레드젬
                    [cmsEx.PaidRedGemPointCmsId]: {
                        cmsId: cmsEx.PaidRedGemPointCmsId,
                        value: newPaidRedGemPoint,
                    },
                },
            },
        };
    }
    static calcMileageMonth(curTimeUtc) {
        const curDate = new Date(curTimeUtc * 1000);
        return mutil.getLocalFullYear(curDate) * 12 + mutil.getLocalMonth(curDate);
    }
    /**
     * 마일리지 획득, 소모에 따른 변경 사항 반환
     * @param added 양수인 경우 획득, 음수인 경우 소모
     * @param curTimeUtc
     */
    buildMileageChanges(added, curTimeUtc) {
        const changes = [];
        const curMileageMonth = UserPoints.calcMileageMonth(curTimeUtc);
        for (const elem of this._mileages) {
            if (elem.month <= curMileageMonth - cms_1.default.Define.MileageExpirationMonth) {
                changes.push({
                    month: elem.month,
                    value: 0,
                });
            }
        }
        if (added < 0) {
            let remaining = -added;
            for (let i = 0; i < this._mileages.length; i++) {
                const mileage = this._mileages[i];
                if (mileage.month <= curMileageMonth - cms_1.default.Define.MileageExpirationMonth) {
                    continue;
                }
                const use = Math.min(remaining, mileage.value);
                changes.push({
                    month: mileage.month,
                    value: mileage.value - use,
                });
                remaining -= use;
                if (remaining === 0) {
                    break;
                }
            }
        }
        else if (added > 0) {
            const idx = this._mileages.findIndex((elem) => {
                return elem.month === curMileageMonth;
            });
            if (idx === -1) {
                changes.push({
                    month: curMileageMonth,
                    value: added,
                });
            }
            else {
                changes.push({
                    month: curMileageMonth,
                    value: this._mileages[idx].value + added,
                });
            }
        }
        return changes;
    }
    applyMileageChanges(changes, glogParam) {
        if (!changes) {
            return {};
        }
        // 시간 변화에 따라 기대 결과랑 다르게 기록될 수 있음.
        // TODO 정확한 기록을 위한 처리 필요( 변화량을 인자로 넣어주는 등 )
        const oldMileageForGlog = this.getMileage(mutil.curTimeUtc());
        for (const change of changes) {
            const idx = this._mileages.findIndex((elem) => {
                return elem.month === change.month;
            });
            if (change.value === 0) {
                if (idx !== -1) {
                    this._mileages.splice(idx, 1);
                }
            }
            else {
                if (idx !== -1) {
                    this._mileages[idx] = change;
                }
                else {
                    this._mileages.push(change);
                }
            }
        }
        let mileage = 0;
        for (const elem of this._mileages) {
            mileage += elem.value;
        }
        if (glogParam && oldMileageForGlog !== mileage) {
            glogParam.user.glog('mileage', {
                rsn: glogParam.rsn,
                add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
                cv: mileage - oldMileageForGlog,
                rv: mileage,
                flag: mileage - oldMileageForGlog > 0 ? 0 : 1,
            });
        }
        const sync = {
            add: {
                mileages: this._mileages,
                // [TEMP] 클라 마일리지 구현 후 제거한다.
                // https://jira.line.games/browse/UWO-16497
                points: {
                    [cmsEx.CashShopMileage]: {
                        cmsId: cmsEx.CashShopMileage,
                        value: mileage,
                    },
                },
            },
            remove: {
                mileages: true,
            },
        };
        return sync;
    }
    get paidRedGem() {
        return this._paidRedGem;
    }
    get freeRedGem() {
        return this._freeRedGem;
    }
    getSyncData() {
        const ret = {
            points: {},
        };
        lodash_1.default.forOwn(this._points, (value, cmsIdStr) => {
            const cmsId = parseInt(cmsIdStr, 10);
            ret.points[cmsIdStr] = {
                cmsId,
                value,
            };
        });
        ret.installmentSavings = this._installmentSavings;
        ret.insurance = this._insurance;
        ret.mileages = this._mileages;
        let mileage = 0;
        const curTimeUtc = mutil.curTimeUtc();
        const curMileageMonth = UserPoints.calcMileageMonth(curTimeUtc);
        for (const elem of this._mileages) {
            if (elem.month <= curMileageMonth - cms_1.default.Define.MileageExpirationMonth) {
                continue;
            }
            mileage += elem.value;
        }
        // [TEMP] 클라 마일리지 구현 후 제거한다.
        // https://jira.line.games/browse/UWO-16497
        if (mileage > 0) {
            ret.points[cmsEx.CashShopMileage] = {
                cmsId: cmsEx.CashShopMileage,
                value: mileage,
            };
        }
        return ret;
    }
    // ----------------------------------------------------------------------------
    // 이벤트 처리 결과에 따른 함수 호출
    // ----------------------------------------------------------------------------
    // point가 변경 되었을 경우 추가 처리 로직
    // cmsId:Point CmsId, prevPoint: 이전 금액 curPoint: 변경 된 현재 금액
    onChangedMoney(cmsId, prevPoint, curPoint) { }
}
// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------
exports.default = UserPoints;
//# sourceMappingURL=userPoints.js.map