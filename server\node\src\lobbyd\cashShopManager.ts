// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Service } from 'typedi';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import mhttp from '../motiflib/mhttp';
import mlog from '../motiflib/mlog';
import { LGBillingResponseBody } from '../motiflib/mhttp/iPlatformBillingApiClient';

@Service()
export class CashShopManager {
  private billingSalesList: {
    [appStoreCd: string]: {
      productList: {
        [productId: string]: {
          productId: string;
          productNm: string;
          saleStartUnixTS: number;
          saleEndUnixTS: number;
          price: number;
          memo: string;
        };
      };
      lastUpdateTimeUtc: number;
    };
  };

  public async getSalesList(appStoreCd: string, curTimeUtc: number) {
    if (
      !this.billingSalesList ||
      !this.billingSalesList[appStoreCd] ||
      this.billingSalesList[appStoreCd].lastUpdateTimeUtc +
        cms.Define.BillingSalesListCachingExpireSec <
        curTimeUtc
    ) {
      await mhttp.platformBillingApi.querySalesList(appStoreCd).then((billingApiResp) => {
        this.updateSalesList(billingApiResp, appStoreCd, curTimeUtc);
      });
    }

    return this.billingSalesList[appStoreCd].productList;
  }

  public updateSalesList(resp: LGBillingResponseBody, appStoreCd: string, curTimeUtc: number) {
    if (!resp.success) {
      mlog.error('[CashShopManager] update billing sales list failed', {
        msg: resp.msg,
        errorCd: resp.errorCd,
      });
      return;
    }

    if (!this.billingSalesList) {
      this.billingSalesList = {};
    }

    this.billingSalesList[appStoreCd] = {
      productList: {},
      lastUpdateTimeUtc: curTimeUtc,
    };

    const productList = this.billingSalesList[appStoreCd].productList;
    for (const product of (resp.data as any).productList) {
      productList[product.productId] = product;
    }
  }
}
