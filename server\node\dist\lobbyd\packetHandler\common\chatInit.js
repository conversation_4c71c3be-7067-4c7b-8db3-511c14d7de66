"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_ChatInit = void 0;
const userConnection_1 = require("../../userConnection");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const merror_1 = require("../../../motiflib/merror");
// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------
/**
 * 볼란테 (채팅)서버에 클라이언트가 접속하기 전에 사전 준비해야 될 작업을 여기서 진행합니다.
 */
// ----------------------------------------------------------------------------
class Cph_Common_ChatInit {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        if (!user.userState.isCompanyCreated()) {
            throw new merror_1.MError('company-is-not-created-yet', merror_1.MErrorCode.TRY_INIT_CHAT_BEFORE_COMPANY_CREATED, {
                gameState: user.userState.getGameState(),
            });
        }
        const { userName, nationCmsId } = user;
        const { guildId } = user.userGuild;
        const guildName = user.userGuild.getGuildName();
        if (!userName) {
            throw new merror_1.MError('no-user-name', merror_1.MErrorCode.TRY_INIT_CHAT_BEFORE_COMPANY_CREATED, {
                gameState: user.userState.getGameState(),
            });
        }
        const volanteId = user.userId.toString();
        const volanteToken = mhttp_1.default.platformChatApi.getIdToken(volanteId);
        const extraData = { nationCmsId, guildId, guildName };
        return mhttp_1.default.platformChatApi
            .createUserIfNotExists(volanteId, userName)
            .then(() => mhttp_1.default.platformChatApi.updateUser(volanteId, userName, extraData))
            .then((channels) => user.sendJsonPacket(packet.seqNum, packet.type, { volanteToken, channels }));
    }
}
exports.Cph_Common_ChatInit = Cph_Common_ChatInit;
//# sourceMappingURL=chatInit.js.map