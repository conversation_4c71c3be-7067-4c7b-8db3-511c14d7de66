// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi/Container';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mhttp from '../../../motiflib/mhttp';
import mlog from '../../../motiflib/mlog';
import {
  GuildData,
  GuildMemberNub,
  GUILD_AUTO_NOTIFICATION_TYPE,
  GUILD_MAIL,
  GUILD_MEMBER_GRADE,
  GUILD_MEMBER_GRADE_ACCESS_CATEGORY,
} from '../../../motiflib/model/lobby';
import { curTimeUtc } from '../../../motiflib/mutil';
import { UserLightInfo } from '../../../motiflib/userCacheRedisHelper';
import { getUserDbShardId } from '../../../mysqllib/mysqlUtil';
import tuGuildLeave from '../../../mysqllib/txn/tuGuildLeave';
import { onGuildPublish } from '../../guildPubsub';
import { LobbyService } from '../../server';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CONNECTION_STATE, CPacket } from '../../userConnection';
import { GuildUtil, GuildLogUtil } from '../../guildUtil';
import { ClientPacketHandler } from '../index';

const rsn = 'guild_leave';
const add_rsn = 'kicked';

// ----------------------------------------------------------------------------
interface RequestBody {
  kickUserId: number;
}

/**
 * 길드원 추방(길드장전용)
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_ManagingKickMember implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { kickUserId } = body;
    const { guildRedis, monitorRedis, userCacheRedis, userDbConnPoolMgr, townRedis } =
      Container.get(LobbyService);

    const guildId: number = user.userGuild.guildId;
    if (!guildId) {
      throw new MError('there-is-no-guild-joined.', MErrorCode.GUILD_NOT_JOINED, {
        userId: user.userId,
      });
    }

    let guildData: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };
    let sync: Sync = {};

    let kickedMemberNid: string;
    let kickedMemberGrade: number;
    let kickedMemberNationCmsId: number;
    const now = curTimeUtc();
    //======================================================================================================
    return (
      GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
        .then((result) => {
          if (!result) {
            throw new MError('cannot-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
              userId: user.userId,
              guildId,
            });
          }

          guildData = result.guildData;
          userLightInfos = result.userLightInfos;

          // 길드장 검사.
          // GuildUtil.ensureMaster(user, guildId, guildData.members);
          GuildUtil.ensureGuildMemberAccess(
            GUILD_MEMBER_GRADE_ACCESS_CATEGORY.MEMBER_MANAGE,
            user,
            guildId,
            guildData
          );

          if (user.userId === kickUserId) {
            throw new MError('cannot-do-self-kick', MErrorCode.GUILD_FAILED_SELF_KICK, {
              userId: user.userId,
              guildId,
              kickUserId,
            });
          }
          // 길드원 확인.
          const member: GuildMemberNub = guildData.members[kickUserId];
          if (!member) {
            throw new MError('not-guild-member', MErrorCode.GUILD_NOT_MEMBER, {
              userId: user.userId,
              guildId,
              kickUserId,
            });
          }

          const manager: GuildMemberNub = guildData.members[user.userId];
          if (!member) {
            throw new MError('not-guild-member', MErrorCode.GUILD_NOT_MEMBER, {
              userId: user.userId,
              guildId,
              kickUserId,
            });
          }
          // --강퇴시행자보다 낮은 등급유저에게만 강퇴가능.
          if (manager.grade >= member.grade) {
            throw new MError(
              'unable-to-kick-higher-grade-member',
              MErrorCode.UNABLE_TO_KICK_HIGHER_GRADE_MEMBER,
              {
                userId: user.userId,
                guildId,
                kickUserId,
                managerGrade: manager.grade,
                kickMemberGrade: member.grade,
              }
            );
          }

          kickedMemberNid = userLightInfos[kickUserId].pubId;
          kickedMemberGrade = guildData.members[kickUserId].grade;
          kickedMemberNationCmsId = userLightInfos[kickUserId].nationCmsId;

          delete guildData.members[kickUserId];
          const newScore = GuildUtil.makeSearchScore(guildData);

          // 멤버삭제는 RDB 실패와 상관없이 제거해주자.
          return guildRedis['deleteGuildMember'](
            kickUserId,
            guildId,
            guildData.guild.nationCmsId,
            newScore
          );
        })

        //======================================================================================================
        // RDB 길드 제거 업데이트
        //======================================================================================================
        .then(() => {
          return tuGuildLeave(
            userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(kickUserId)),
            kickUserId,
            0, // guildId
            now
          );
        })
        //======================================================================================================
        // 길드 자동안내 업데이트
        //======================================================================================================
        .then(() => {
          if (userLightInfos[kickUserId]) {
            guildData.guild.autoNotificationType = GUILD_AUTO_NOTIFICATION_TYPE.KICKED;
            guildData.guild.autoNotificationParam1 = userLightInfos[kickUserId].name;
            guildData.guild.autoNotificationRegTimeUtc = now;
            return guildRedis['updateGuild'](guildId, JSON.stringify(guildData.guild));
          }
        })

        //======================================================================================================
        // 유저캐시 redis  업데이트
        //======================================================================================================
        .then(() => {
          userCacheRedis['setUserGuild'](kickUserId, 0).catch((err) => {
            mlog.error('userCacheRedis setUserGuild is failed at guildMemberKick.', {
              err: err.message,
              userId: kickUserId,
            });
          });
        })
        //======================================================================================================
        // 타운 길드 유저 점수 제거
        //======================================================================================================
        .then(() => {
          return townRedis['deleteTownGuildUserScore'](
            JSON.stringify(Object.keys(cms.Town)),
            guildId,
            kickUserId,
            cms.Const.InvestGuildPointPer.value,
            cms.Const.InvestGuildSharePointPer.value
          );
        })
        //======================================================================================================
        // 추방당한 유저가 접속 중일 경우 알림.
        //======================================================================================================
        .then((result: string) => {
          if (result) {
            mlog.info('[UPDATE-GUILD-SCORE-BY-LEAVING] kicked-by-master', {
              guildId,
              userId: user.userId,
              kickUserId: kickUserId,
              changes: JSON.parse(result),
            });
          }
          // 길드 채널에서 유저 삭제
          mhttp.platformChatApi
            .disallowGuildChannel(`GUILD_${guildId}`, kickUserId.toString())
            .catch((err) => {
              mlog.error('mhttp.chatd.disallowGuildChannel is failed.', {
                err: err.message,
                userId: user.userId,
                guildId,
              });
            });

          if (userLightInfos[kickUserId] && userLightInfos[kickUserId].isOnline) {
            return mhttp.authd
              .getLastLobbyOfOnlineUsers([kickUserId])
              .then((ret) => {
                if (ret && ret.userLobbies && ret.userLobbies.length > 0) {
                  const curLobby = ret.userLobbies[0].lastLobby;
                  return monitorRedis['getLobbydUrls'](JSON.stringify([curLobby]));
                }
                return null;
              })
              .then((ret) => {
                if (ret) {
                  const lobbydUrls = JSON.parse(ret);
                  if (lobbydUrls) {
                    const url = lobbydUrls[Object.keys(lobbydUrls)[0]];
                    const lobbyApi = mhttp.lobbypx.channel(url);
                    const notiPacket = {
                      guildId: guildId,
                    };
                    return lobbyApi.sendGuildMemberKickToKickedUser(kickUserId, notiPacket);
                  }
                }
              })
              .catch((err) => {
                mlog.error('[GUILD] /guildMemberKickToKickedUser is failed.', {
                  kickUserId,
                  guildId,
                  err: err.message,
                });
              });
          }
        })
        //======================================================================================================
        // 추방당한 유저에게 메일 전송.
        //======================================================================================================
        .then(() => {
          return GuildUtil.sendGuildMail([kickUserId], GUILD_MAIL.KICKED);
        })

        //======================================================================================================
        // 나머지 기존 길드원에게 추방정보를 알려준다.
        //======================================================================================================
        .then(() => {
          sync = {
            add: {
              userGuild: {
                guild: {
                  autoNotificationType: guildData.guild.autoNotificationType,
                  autoNotificationParam1: guildData.guild.autoNotificationParam1,
                  autoNotificationParam2: guildData.guild.autoNotificationParam2,
                },
              },
            },
            remove: {
              userGuild: {
                guild: {
                  members: {
                    [kickUserId]: true,
                  },
                },
              },
            },
          };
          onGuildPublish(guildData, userLightInfos, [user.userId, kickUserId], sync);
        })

        //======================================================================================================
        // 결과 응답.
        //======================================================================================================
        .then(() => {
          gLog_guildLeave(
            user,
            kickedMemberNid,
            kickedMemberGrade,
            kickedMemberNationCmsId,
            guildId,
            guildData,
            userLightInfos
          );
          return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        })
    );
  }
}

function gLog_guildLeave(
  user: User,
  kickedMemberNid: string,
  kickedMemberGrade: number,
  kickedMemberNationCmsId: number,
  guildId: number,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo }
) {
  const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);

  let nation: string = null;
  if (kickedMemberNationCmsId) {
    const nationCms = cms.Nation[kickedMemberNationCmsId];
    nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_leave', {
    rsn,
    add_rsn,
    leave_nid: kickedMemberNid,
    nation,
    type: 1, // 추방
    grade: kickedMemberGrade,
    guild_data,
  });
}
