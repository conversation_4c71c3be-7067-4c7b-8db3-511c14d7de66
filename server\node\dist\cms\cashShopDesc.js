"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processCashShops = exports.processCashShop = exports.isExistWorldBuffTimeField = exports.getWorldBuffAddTime = exports.getProductCodeByStoreCode = exports.getConsecutiveProductCodeByStoreCode = exports.isConsecutiveProductCode = exports.getCashShopMileageBonus = exports.SHOP_CASE = exports.HOT_SPOT_RESET_TYPE = exports.CASH_SHOP_PRODUCT_TYPE = exports.CASH_SHOP_SALE_POINT_TYPE = exports.CASH_SHOP_SALE_TYPE = exports.CASH_SHOP_PRODUCT_CATEGORY = void 0;
const cmsEx = __importStar(require("./ex"));
const cmsTable_1 = require("./cmsTable");
const worldBuffDesc_1 = require("./worldBuffDesc");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const formula_1 = require("../formula");
const iPlatformBillingApiClient_1 = require("../motiflib/mhttp/iPlatformBillingApiClient");
// ----------------------------------------------------------------------------
// https://wiki.line.games/display/MOTIF/CashShop
// ----------------------------------------------------------------------------
var CASH_SHOP_PRODUCT_CATEGORY;
(function (CASH_SHOP_PRODUCT_CATEGORY) {
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["POINT"] = 1] = "POINT";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["RECOMMEND"] = 2] = "RECOMMEND";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["SINGLE"] = 3] = "SINGLE";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["DAILY"] = 4] = "DAILY";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["BUFF"] = 5] = "BUFF";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["GACHA"] = 6] = "GACHA";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["QUEST_PASS"] = 7] = "QUEST_PASS";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["EXCHANGE"] = 8] = "EXCHANGE";
    CASH_SHOP_PRODUCT_CATEGORY[CASH_SHOP_PRODUCT_CATEGORY["BGM"] = 9] = "BGM";
})(CASH_SHOP_PRODUCT_CATEGORY = exports.CASH_SHOP_PRODUCT_CATEGORY || (exports.CASH_SHOP_PRODUCT_CATEGORY = {}));
var CASH_SHOP_SALE_TYPE;
(function (CASH_SHOP_SALE_TYPE) {
    CASH_SHOP_SALE_TYPE[CASH_SHOP_SALE_TYPE["UNLIMITED"] = 1] = "UNLIMITED";
    CASH_SHOP_SALE_TYPE[CASH_SHOP_SALE_TYPE["DAY"] = 2] = "DAY";
    CASH_SHOP_SALE_TYPE[CASH_SHOP_SALE_TYPE["WEEK"] = 3] = "WEEK";
    CASH_SHOP_SALE_TYPE[CASH_SHOP_SALE_TYPE["MONTH"] = 4] = "MONTH";
    CASH_SHOP_SALE_TYPE[CASH_SHOP_SALE_TYPE["LIMIT"] = 5] = "LIMIT";
})(CASH_SHOP_SALE_TYPE = exports.CASH_SHOP_SALE_TYPE || (exports.CASH_SHOP_SALE_TYPE = {}));
var CASH_SHOP_SALE_POINT_TYPE;
(function (CASH_SHOP_SALE_POINT_TYPE) {
    CASH_SHOP_SALE_POINT_TYPE[CASH_SHOP_SALE_POINT_TYPE["POINT"] = 1] = "POINT";
    CASH_SHOP_SALE_POINT_TYPE[CASH_SHOP_SALE_POINT_TYPE["CASH"] = 2] = "CASH";
})(CASH_SHOP_SALE_POINT_TYPE = exports.CASH_SHOP_SALE_POINT_TYPE || (exports.CASH_SHOP_SALE_POINT_TYPE = {}));
var CASH_SHOP_PRODUCT_TYPE;
(function (CASH_SHOP_PRODUCT_TYPE) {
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["REWARD_FIXED"] = 1] = "REWARD_FIXED";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["BUFF"] = 2] = "BUFF";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["TAX_FREE_PERMIT"] = 3] = "TAX_FREE_PERMIT";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["GACHA_BOX"] = 4] = "GACHA_BOX";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["QUEST_PASS"] = 5] = "QUEST_PASS";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["SOUND"] = 6] = "SOUND";
    // ENCOUNT_SHIELD = 7,
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["EVENT_PAGE"] = 8] = "EVENT_PAGE";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["DAILY_SUBSCRIPTION"] = 9] = "DAILY_SUBSCRIPTION";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["HOT_SPOT"] = 10] = "HOT_SPOT";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["ILLUST_SKIN"] = 11] = "ILLUST_SKIN";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["MATE"] = 12] = "MATE";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["SERVER_TRANSFER"] = 13] = "SERVER_TRANSFER";
    CASH_SHOP_PRODUCT_TYPE[CASH_SHOP_PRODUCT_TYPE["USER_TITLE"] = 14] = "USER_TITLE";
})(CASH_SHOP_PRODUCT_TYPE = exports.CASH_SHOP_PRODUCT_TYPE || (exports.CASH_SHOP_PRODUCT_TYPE = {}));
var HOT_SPOT_RESET_TYPE;
(function (HOT_SPOT_RESET_TYPE) {
    HOT_SPOT_RESET_TYPE[HOT_SPOT_RESET_TYPE["NONE"] = 0] = "NONE";
    HOT_SPOT_RESET_TYPE[HOT_SPOT_RESET_TYPE["RESET"] = 1] = "RESET";
})(HOT_SPOT_RESET_TYPE = exports.HOT_SPOT_RESET_TYPE || (exports.HOT_SPOT_RESET_TYPE = {}));
var SHOP_CASE;
(function (SHOP_CASE) {
    SHOP_CASE[SHOP_CASE["WEB_SHOP"] = 0] = "WEB_SHOP";
    SHOP_CASE[SHOP_CASE["CASH_SHOP"] = 1] = "CASH_SHOP";
    SHOP_CASE[SHOP_CASE["BOTH"] = 2] = "BOTH";
})(SHOP_CASE = exports.SHOP_CASE || (exports.SHOP_CASE = {}));
function getCashShopMileageBonus(cashShopCms, costPointValue) {
    if (cashShopCms.mileageBonusPer === undefined) {
        return undefined;
    }
    if (cashShopCms.salePointId === cmsEx.RedGemPointCmsId) {
        // RedGem으로 구매할 경우 마일리지 적립.
        // 구매 비용 * (mileageBonusPer/1000), 소수점버림
        return Math.floor(costPointValue * (cashShopCms.mileageBonusPer / 1000));
    }
    return undefined;
}
exports.getCashShopMileageBonus = getCashShopMileageBonus;
function isConsecutiveProductCode(cashShopCms, productCode) {
    if (!productCode ||
        !cashShopCms.consecutiveProductCodeGoogle ||
        !cashShopCms.consecutiveProductCodeApple ||
        !cashShopCms.consecutiveProductCodeFloor ||
        !cashShopCms.consecutiveProductCodeSteam) {
        return false;
    }
    switch (productCode) {
        case cashShopCms.consecutiveProductCodeGoogle:
        case cashShopCms.consecutiveProductCodeApple:
        case cashShopCms.consecutiveProductCodeFloor:
        case cashShopCms.consecutiveProductCodeSteam:
            return true;
        default:
            return false;
    }
}
exports.isConsecutiveProductCode = isConsecutiveProductCode;
function getConsecutiveProductCodeByStoreCode(cashShopCms, storeCode) {
    switch (storeCode) {
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.GOOGLE_PLAY:
            return cashShopCms.consecutiveProductCodeGoogle;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.APPLE_APP_STORE:
            return cashShopCms.consecutiveProductCodeApple;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.FLOOR_STORE:
            return cashShopCms.consecutiveProductCodeFloor;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.STEAM:
            return cashShopCms.consecutiveProductCodeSteam;
        default:
            return undefined;
    }
}
exports.getConsecutiveProductCodeByStoreCode = getConsecutiveProductCodeByStoreCode;
function getProductCodeByStoreCode(cashShopCms, storeCode) {
    switch (storeCode) {
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.GOOGLE_PLAY:
            return cashShopCms.productCodeGoogle;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.APPLE_APP_STORE:
            return cashShopCms.productCodeApple;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.FLOOR_STORE:
            return cashShopCms.productCodeFloor;
        case iPlatformBillingApiClient_1.LGBillingCode.APP_STORE_CD.STEAM:
            return cashShopCms.productCodeSteam;
        default:
            return undefined;
    }
}
exports.getProductCodeByStoreCode = getProductCodeByStoreCode;
function getWorldBuffAddTime(cashShopCms) {
    if (cashShopCms.expiredAt !== undefined) {
        return Math.max(0, cashShopCms.expiredAt - mutil.curTimeUtc());
    }
    if (cashShopCms.durationSec !== undefined) {
        return cashShopCms.durationSec;
    }
    return 0;
}
exports.getWorldBuffAddTime = getWorldBuffAddTime;
function isExistWorldBuffTimeField(cashShopCms) {
    return cashShopCms.expiredAt !== undefined || cashShopCms.durationSec !== undefined;
}
exports.isExistWorldBuffTimeField = isExistWorldBuffTimeField;
const processCashShop = (cms) => (rawCashShopDesc) => {
    var _a;
    let worldBuffElems = [];
    let durationSec = undefined;
    let expiredAt = undefined;
    let worldBuffTimeType = undefined;
    const rawCashShops = cms.CashShop;
    const eventPages = cms.EventPage;
    // 버프인 경우.
    if (rawCashShopDesc.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
        // 1. 시즌 패스 버프. (passGroup이 있고 productType이 BUFF이면)
        if (rawCashShopDesc.passGroup && rawCashShopDesc.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
            // passGroup 으로 연결된 상위 상품 찾고
            // 그 상품이 가진 eventPageId 로 EventPage를 찾고
            // 1. EventPage의 endDate를 expiredAt으로 사용
            // EventPage의 endDate 이 없을 경우
            // 2. durationDays을 사용. 없다면
            // 3. durationHours을 사용. 없다면
            // 4. saleEndDate를 사용. 없다면
            // 5. cmsEx.TheEndTimeUtc 사용
            // 미쳤따..
            const parentCashShops = Object.values(rawCashShops).filter((rawCashShop) => rawCashShop.passGroup === rawCashShopDesc.passGroup && rawCashShop.eventPageId);
            if (parentCashShops.length != 1) {
                mlog_1.default.error('multiple-or-missing-parent-cash-shops-for-pass-group', {
                    cashShopId: rawCashShopDesc.id,
                    passGroup: rawCashShopDesc.passGroup,
                    parentCashShopsCount: parentCashShops.length,
                });
                throw new Error(`multiple-or-missing-parent-cash-shops-for-pass-group`);
            }
            const parentCashShop = parentCashShops[0];
            if (!parentCashShop.eventPageId) {
                mlog_1.default.error('parent-cash-shop-doesnt-have-event-page-id', {
                    cashShopId: rawCashShopDesc.id,
                    passGroup: rawCashShopDesc.passGroup,
                    parentCashShopId: parentCashShop.id,
                });
                throw new Error(`parent-cash-shop-doesnt-have-event-page-id`);
            }
            const eventPage = eventPages[parentCashShop.eventPageId];
            if (!eventPage) {
                mlog_1.default.error('cant-find-event-page-for-pass-group', {
                    eventPageId: parentCashShop.eventPageId,
                    passGroup: parentCashShop.passGroup,
                });
                throw new Error(`cant-find-event-page-for-pass-group`);
            }
            if (eventPage.endDate) { // 1. EventPage의 endDate
                worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
                expiredAt = mutil.dateToUtc(mutil.newDateByCmsDateStr(eventPage.endDate));
            }
            else if (rawCashShopDesc.durationDays) { // 2. durationDays
                worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION;
                durationSec = rawCashShopDesc.durationDays * formula_1.SECONDS_PER_DAY;
            }
            else if (rawCashShopDesc.durationHours) { // 3. durationHours
                worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION;
                durationSec = rawCashShopDesc.durationHours * formula_1.SECONDS_PER_HOUR;
            }
            else if (rawCashShopDesc.saleEndDate) { // 4. cashShop의 saleEndDate
                worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
                expiredAt = mutil.dateToUtc(mutil.newDateByCmsDateStr(rawCashShopDesc.saleEndDate));
            }
            else { // 5. cmsEx.TheEndTimeUtc
                worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
                expiredAt = cmsEx.TheEndTimeUtc;
            }
        }
        else if (rawCashShopDesc.durationDays) { // 2. durationDays
            worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION;
            durationSec = rawCashShopDesc.durationDays * formula_1.SECONDS_PER_DAY;
        }
        else if (rawCashShopDesc.durationHours) { // 3. durationHours
            worldBuffTimeType = worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION;
            durationSec = rawCashShopDesc.durationHours * formula_1.SECONDS_PER_HOUR;
        }
        if (worldBuffTimeType === undefined) {
            mlog_1.default.error('cash-shop-has-no-world-buff-time-type', {
                cashShopId: rawCashShopDesc.id,
            });
            throw new Error(`cash-shop-has-no-world-buff-time-type`);
        }
        if (worldBuffTimeType === worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION && durationSec === undefined) {
            mlog_1.default.error('cash-shop-has-no-duration-sec', {
                cashShopId: rawCashShopDesc.id,
            });
            throw new Error(`cash-shop-has-no-duration-sec`);
        }
        if (worldBuffTimeType === worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.EXPIRED_AT && expiredAt === undefined) {
            mlog_1.default.error('cash-shop-has-no-expired-at', {
                cashShopId: rawCashShopDesc.id,
            });
            throw new Error(`cash-shop-has-no-expired-at`);
        }
        if (worldBuffTimeType === worldBuffDesc_1.WORLD_BUFF_TIME_TYPE.DURATION && durationSec === undefined) {
            mlog_1.default.error('cash-shop-has-zero-duration-sec', {
                cashShopId: rawCashShopDesc.id,
            });
            throw new Error(`cash-shop-has-zero-duration-sec`);
        }
    }
    // worldBuffElems
    worldBuffElems = ((_a = rawCashShopDesc.productWorldBuffId) === null || _a === void 0 ? void 0 : _a.map((worldBuffId) => {
        return {
            worldBuffId,
            worldBuffTimeType: worldBuffTimeType,
            expiredAt: expiredAt,
            durationSec: durationSec,
            needToExtend: false,
        };
    })) || [];
    return {
        ...rawCashShopDesc,
        worldBuffElems: worldBuffElems,
        durationSec: durationSec,
        expiredAt: expiredAt,
        needToExtend: expiredAt === undefined,
    };
};
exports.processCashShop = processCashShop;
const processCashShops = (cms) => {
    const process = (0, exports.processCashShop)(cms);
    return (rawCashShops, name) => (0, cmsTable_1.processTable)(process, rawCashShops, name);
};
exports.processCashShops = processCashShops;
//# sourceMappingURL=cashShopDesc.js.map