"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const moment_1 = __importDefault(require("moment"));
const const_1 = require("../../../motiflib/const");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const merror_1 = require("../../../motiflib/merror");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const enum_1 = require("../../../motiflib/model/auth/enum");
const mutil = __importStar(require("../../../motiflib/mutil"));
const connPool_1 = require("../../../redislib/connPool");
const kicker = __importStar(require("../../kicker"));
const taLogin_1 = __importDefault(require("../../../mysqllib/txn/taLogin"));
const pool_1 = require("../../../mysqllib/pool");
const gameLog_1 = __importDefault(require("../../../motiflib/gameLog"));
const mysqlReqRepCounter_1 = require("../../../mysqllib/mysqlReqRepCounter");
const lodash_1 = __importDefault(require("lodash"));
const sdoApiClient_1 = require("../../../motiflib/mhttp/sdoApiClient");
function findWorld(worldId) {
    for (const world of mconf_1.default.worlds) {
        if (world.id == worldId) {
            return world;
        }
    }
}
async function _login(res, reqBody, accountId, curTimeUtc, resp, loginPlatform, clientVersion, worldId, bWhitePassOk, bPrologue) {
    resp.enterWorldToken = mutil.generateEnterWorldToken(accountId);
    const dbConnPool = typedi_1.default.get(pool_1.DBConnPool);
    const loginDbResult = await (0, taLogin_1.default)(dbConnPool.getPool(), accountId, curTimeUtc, mconf_1.default.defaultAccessLevel, loginPlatform, clientVersion);
    if (loginDbResult.bBlockedByAdmin) {
        return _rejectLogin(res, resp, loginDbResult, 200);
    }
    resp.isNewUser = loginDbResult.bIsNewUser;
    let lastLobbyForKick, userIdToKick;
    if (loginDbResult.isOnline === 1) {
        lastLobbyForKick = loginDbResult.lastLobby;
        for (const world of loginDbResult.worlds) {
            if (world.worldId === loginDbResult.lastWorldId) {
                userIdToKick = world.userId;
                break;
            }
        }
    }
    mlog_1.default.info('loginDbResult', { loginDbResult, reqBody });
    // Process order logic
    let orderId;
    if (bWhitePassOk) {
        // whiteip인경우 그냥 orderid는 0설정.
        resp.orderWait = 0;
        orderId = 0;
    }
    else if (bPrologue) {
        // 프롤로그 끝나고 로그인인 경우 대기열 통과처리
        // 프롤로그 유저여부 검증 (만약 프롤로그 정보가 없으면 일반 대기열 처리 적용)
        const minTs = curTimeUtc - mconf_1.default.prologueGnidTimeout;
        const orderRedis = typedi_1.default.of('order-redis').get(connPool_1.MRedisConnPool);
        const retResult = await orderRedis['checkPrologueGnid'](accountId, worldId, minTs);
        if (!retResult) {
            mlog_1.default.info('login checkPrologueGnid expired..applying order', {
                accountId,
            });
            // 프롤로그 상태가 유효하지않은 경우 대기열 적용
            orderId = await _processOrder(worldId, resp);
        }
        else {
            // 유효한 경우 대기열 통과
            resp.orderWait = 0;
            orderId = 0;
        }
    }
    else {
        orderId = await _processOrder(worldId, resp);
    }
    const userCacheRedis = typedi_1.default.get(connPool_1.MRedisConnPool);
    await userCacheRedis['setEnterWorldToken'](accountId, resp.enterWorldToken, curTimeUtc, reqBody.revision, reqBody.patchRevision, worldId, orderId);
    if (userIdToKick) {
        mlog_1.default.warn('/login user already online', {
            accountId,
            userId: userIdToKick,
            lastLobby: lastLobbyForKick,
        });
        // TODO jaykay: lastLobby
        await kicker.kick(userIdToKick, lastLobbyForKick, const_1.KICK_REASON.DUPLICATE_LOGIN);
    }
    //set user heartBeat
    await userCacheRedis['updateUserHeartBeat'](accountId, curTimeUtc);
    const minOffSailTs = curTimeUtc - mconf_1.default.offlineSailingHeartBeatInterval;
    const offlineSailingResult = await userCacheRedis['getOfflineSailingInfo'](accountId, minOffSailTs);
    const offlineSailingInfo = JSON.parse(offlineSailingResult);
    // mlog.warn('getOfflineSailingInfo result', {
    //   accountId,
    //   userId: offlineSailingInfo.userId,
    //   appId: offlineSailingInfo.appId,
    // });
    // offlineSailing 진행중인경우 중단시킨다
    if (offlineSailingInfo.userId && offlineSailingInfo.appId) {
        mlog_1.default.warn('/bot user is online', {
            accountId,
            userId: offlineSailingInfo.userId,
            appId: offlineSailingInfo.appId,
        });
        await kicker.kick(offlineSailingInfo.userId, offlineSailingInfo.appId, const_1.KICK_REASON.OFFLINE_SAILING_BOT);
    }
    // glog
    const { os, osv, dm, deviceLang, lang, v, sk, loginPlatform: reqLoginPlatform, country_ip, isNewGnid, isFirstAfterInstall, } = reqBody;
    if (isFirstAfterInstall) {
        // deviceType 을 클라에서 보내준 경우에만 common_installed 로그를 남긴다. (최초 로그인)
        (0, gameLog_1.default)('common_installed', {
            _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            os,
            osv,
            dm,
            lang: deviceLang,
            v,
            sk,
        });
    }
    if (isNewGnid) {
        (0, gameLog_1.default)('common_gnid_register', {
            _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            os,
            osv,
            dm,
            lang: deviceLang,
            lang_game: lang,
            v,
            sk,
            platform: reqLoginPlatform,
            country_ip,
            gnid: resp.gnid,
        });
    }
    _send(res, resp);
}
async function _processOrder(worldId, resp) {
    // 현재 동접조회
    const monitorRedis = typedi_1.default.of('monitor-redis').get(connPool_1.MRedisConnPool);
    const retuserCount = await monitorRedis['getUserCount']();
    const userCount = JSON.parse(retuserCount);
    let curWorldUsers = 0;
    if (userCount.user.world[worldId]) {
        curWorldUsers = userCount.user.world[worldId];
    }
    const orderIdCheckThreshold = (mconf_1.default.maxUsersPerWorld * mconf_1.default.orderIdCheckWorldUserRate) / 100;
    const curTimeMsec = new Date().getTime();
    const minTs = Math.floor(curTimeMsec / 1000) - mconf_1.default.prologueGnidTimeout;
    const orderRedis = typedi_1.default.of('order-redis').get(connPool_1.MRedisConnPool);
    const prologueUserCount = await orderRedis['queryPrologueGnidsCount'](worldId, minTs);
    // 프롤로그 진행중인 유저수도 합산해서 계산한다
    curWorldUsers = curWorldUsers + prologueUserCount;
    let mode;
    if (curWorldUsers >= mconf_1.default.maxUsersPerWorld) {
        mode = 0; // 최대동접 초과된 경우. 순번표만 발급
    }
    else if (curWorldUsers < orderIdCheckThreshold) {
        // 대기열 적용 조건(임계값 미만 && 대기열에 다른 유저가 존재)이 충족되지 않는 경우에는
        // 순번표 발급 없이 월드 입장허용
        mode = 1;
    }
    else {
        mode = 2; // 임계값 이상인 경우 대기열 적용
    }
    // 동접이 임계값 이하이더라도 로그인 완료한 순번과 발급한 순번의 차이가 크다면
    // 대기열에 유저들이 남아 있다는 의미
    // (임계값을 넘어서 대기열이 작동한 상황에서 임계값이하로 떨어진 경우)
    // 이러한 경우 새로 들어오는 유저들도 대기열의 뒤에 넣어서 순서가 지켜지도록 한다
    const retResult = await orderRedis['generateOrderAndUpdateAllowedOrderId'](worldId, mode, curTimeMsec, mconf_1.default.maxUsersPerWorld, curWorldUsers, mconf_1.default.maxAllowOrderIdPerSec);
    if (retResult && retResult[0]) {
        const result = JSON.parse(retResult[0]);
        resp.orderWait = Math.max(result.orderId - result.lastAllowedOrderId, 0);
        // [임시] 대기열 디버깅용 정보
        if (retResult[1]) {
            const debug = JSON.parse(retResult[1]);
            mlog_1.default.info('[TEMP] login order', {
                accountId: resp.gnid,
                maxUsersPerWorld: mconf_1.default.maxUsersPerWorld,
                curWorldUsers,
                orderIdCheckThreshold,
                debug,
                orderId: result.orderId,
                lastAllowedOrderId: result.lastAllowedOrderId,
                orderWait: resp.orderWait,
            });
        }
        return result.orderId;
    }
}
function _rejectLogin(res, resp, loginDbResult, status) {
    if (loginDbResult) {
        resp.accessLevelForRejectReason = enum_1.ACCOUNT_ACCESS_LEVEL.UNDER_CONTROL_BY_ADMIN;
        resp.blockTimeUtcByAdmin = loginDbResult.blockTimeUtcByAdmin;
    }
    if (resp.enterWorldToken) {
        delete resp.enterWorldToken;
    }
    _send(res, resp, status);
}
function _send(res, resp, status = 200) {
    mlog_1.default.info('[TX] /login', { body: resp });
    res.status(status).json(resp);
}
module.exports = async (req, res) => {
    var _a, _b, _c;
    const { sessionToken, loginPlatform, clientVersion, worldId, bPrologue } = req.body;
    // [TEMP] production 환경에서 에디터 로그인 가능하게 하는 코드
    // const platform = req.body.platform !== undefined ? req.body.platform : mconf.platform;
    const platform = mconf_1.default.isDev && req.body.platform !== undefined ? req.body.platform : mconf_1.default.platform;
    mlog_1.default.info('[RX] /login', { body: req.body, platform: enum_1.PLATFORM[platform] });
    const world = findWorld(worldId);
    const resp = {
        gnid: undefined,
        nid: undefined,
        enterWorldToken: undefined,
        isNewUser: false,
        worldAddresss: world === null || world === void 0 ? void 0 : world.address,
        worldPort: world === null || world === void 0 ? void 0 : world.port,
        gnidStatus: 'NORMAL',
        orderWait: 0,
        floorPlatformUserId: undefined,
    };
    const reqRepCounter = typedi_1.default.get(mysqlReqRepCounter_1.MysqlReqRepCounter);
    if (reqRepCounter.isLimitOver()) {
        resp.errorCd = merror_1.MErrorCode.AUTH_LOGIN_FAILED_WITH_SERVER_BUSY;
        const limit = reqRepCounter.getLimitCount();
        const cnt = reqRepCounter.getCount();
        mlog_1.default.warn(`request-respons-count over`, { cnt, limit });
        _rejectLogin(res, resp, undefined, 503);
        return;
    }
    // https://developer.line.games/pages/viewpage.action?pageId=7275349
    let accountId; // (lg:gnid, editor:로그인 창에서 입력하는 아이디)
    const curTimeUtc = mutil.curTimeUtc();
    let bWhitePassOk = false;
    // TODO admin 로그인 처리
    try {
        // Platform-specific authentication
        if (platform === enum_1.PLATFORM.LINE) {
            const response = await mhttp_1.default.platformApi.login(sessionToken);
            accountId = response.gnid;
            resp.gnid = response.gnid;
            resp.nid = response.nid;
            resp.gnidStatus = response.gnidStatus;
            if (resp.gnidStatus === 'LEAVE') {
                // 탈퇴 요청한 계정이므로 로그인 시키지 않고 복구 여부를 클라가 확인하는 과정으로 진행 되어야 합니다.
                return _send(res, resp);
            }
            if (resp.gnidStatus !== 'NORMAL') {
                // 삭제 또는 블럭된 유저이므로 로그인을 거부해야 합니다.
                return _send(res, resp);
            }
            const whiteYn = response.whiteNidYn.toLowerCase();
            if (whiteYn === 'y') {
                bWhitePassOk = true;
            }
            const accountList = response.accountList;
            if (accountList && lodash_1.default.isArray(accountList) && accountList.length > 0) {
                // https://developer.line.games/pages/viewpage.action?pageId=7275997 FLOOR: 130
                const floorAccount = accountList.find((elem) => elem.platformId === 130);
                if (floorAccount) {
                    resp.floorPlatformUserId = floorAccount.platformUserId;
                }
            }
        }
        else if (platform === enum_1.PLATFORM.SDO) {
            // SDO의 경우 여러번 로그인을 허용하지 않으므로, 캐싱된 accountId 로 로그인 여부를 판단해야함.
            const { cachedAccountId } = req.body;
            let nid = undefined;
            if (cachedAccountId && cachedAccountId.length > 0) {
                accountId = cachedAccountId;
                nid = (0, sdoApiClient_1.sdoMakeNid)(accountId, worldId);
            }
            else {
                const loginResponse = await mhttp_1.default.platformApi.login(sessionToken);
                accountId = String((_a = loginResponse.data) === null || _a === void 0 ? void 0 : _a.userid);
                nid = (0, sdoApiClient_1.sdoMakeNid)(accountId, worldId);
                await mhttp_1.default.sdoaa.userOnline({
                    userId: accountId,
                    accountType: 25,
                    areaId: ((_b = mconf_1.default.sdo) === null || _b === void 0 ? void 0 : _b.areaId) || -1,
                    groupId: ((_c = mconf_1.default.sdo) === null || _c === void 0 ? void 0 : _c.groupId) || -1,
                    endpointIp: req.ip,
                    endpointPort: 0,
                    characterId: nid,
                    deviceId: '',
                });
            }
            resp.gnid = accountId;
            resp.nid = nid;
        }
        else if (platform === enum_1.PLATFORM.DEV) {
            accountId = sessionToken;
            resp.gnid = accountId;
            const krPattern = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
            const blankPattern = /[\s]/g;
            if (krPattern.test(accountId) || blankPattern.test(accountId)) {
                throw new merror_1.MError('invalid-id', merror_1.MErrorCode.AUTH_INVALID_PUB_ID, req.body);
            }
        }
        else {
            throw new merror_1.MError('invalid-platform', merror_1.MErrorCode.AUTH_INVALID_PLATFORM, req.body);
        }
        await _login(res, req.body, accountId, curTimeUtc, resp, loginPlatform, clientVersion, worldId, bWhitePassOk, bPrologue);
    }
    catch (error) {
        mlog_1.default.error('/login failed', {
            msg: error.message,
        });
        if (error instanceof merror_1.MError) {
            throw new merror_1.MError(`'/login' api err`, error.mcode, error.message);
        }
        throw new merror_1.MError(error.message, merror_1.MErrorCode.AUTH_LOGIN_ERROR, undefined, error.stack);
    }
};
//# sourceMappingURL=login.js.map