{"version": 3, "file": "iPlatformBillingApiClient.js", "sourceRoot": "", "sources": ["../../../src/motiflib/mhttp/iPlatformBillingApiClient.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;AAI/E,8CAA2C;AAC3C,sCAAmC;AAanC;;GAEG;AACH,IAAY,kBAaX;AAbD,WAAY,kBAAkB;IAC5B,2EAAY,CAAA;IACZ,uFAAkB,CAAA;IAClB,qFAAiB,CAAA;IACjB,+EAAc,CAAA;IACd,+EAAc,CAAA;IACd,qFAAiB,CAAA;IACjB,uFAAkB,CAAA;IAClB,mGAAwB,CAAA;IACxB,qFAAiB,CAAA;IAEjB,EAAE;IACF,iEAAO,CAAA;AACT,CAAC,EAbW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAa7B;AAED;;GAEG;AACH,IAAiB,aAAa,CAkB7B;AAlBD,WAAiB,aAAa;IAC5B,IAAY,cASX;IATD,WAAY,cAAc;QACxB,cAAc;QACd,yDAAO,CAAA;QACP,yEAAe,CAAA;QACf,2DAAQ,CAAA;QACR,uDAAM,CAAA;QACN,qEAAa,CAAA;QACb,iEAAW,CAAA;QACX,+DAAU,CAAA;IACZ,CAAC,EATW,cAAc,GAAd,4BAAc,KAAd,4BAAc,QASzB;IAEY,0BAAY,GAAG;QAC1B,WAAW,EAAE,aAAa;QAC1B,eAAe,EAAE,iBAAiB;QAClC,WAAW,EAAE,aAAa;QAC1B,KAAK,EAAE,OAAO;KACN,CAAC;AACb,CAAC,EAlBgB,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAkB7B;AA8FD;;GAEG;AACH,SAAS,UAAU,CAAC,CAAM;IACxB,IACE,CAAC;QACD,OAAO,CAAC,KAAK,QAAQ;QACrB,OAAO,CAAC,CAAC,eAAe,KAAK,QAAQ;QACrC,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;QAC5B,CAAC,CAAC,CAAC,CAAC,gBAAgB,IAAI,OAAO,CAAC,CAAC,gBAAgB,KAAK,QAAQ,CAAC;QAC/D,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;QAC5B,CAAC,CAAC,CAAC,mBAAmB,KAAK,GAAG,IAAI,CAAC,CAAC,mBAAmB,KAAK,GAAG,CAAC,EAChE;QACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,CAAM;IACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACrB,MAAM,IAAI,eAAM,CAAC,gBAAgB,EAAE,uBAAU,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;KACtE;IACD,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrB,MAAM,IAAI,eAAM,CAAC,kBAAkB,EAAE,uBAAU,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;SACxE;KACF;AACH,CAAC;AATD,0CASC"}