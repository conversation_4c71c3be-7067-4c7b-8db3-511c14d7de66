// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { BaseApiClient } from './baseApiClient';
import { IPlatformPayApiClient } from './iPlatformPayApiClient';

export class SdoPayApiClient extends BaseApiClient implements IPlatformPayApiClient {
  cancelFloorReserve(
    appStoreCd: string,
    floorOrderId: string,
    floorStoreOrderId: string,
    billingOrderId: string,
    productId: string,
    userId: number
  ): Promise<any> {
    return Promise.resolve();
  }
}
