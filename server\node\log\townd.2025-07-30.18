{"environment":"development","type":"townd","gitCommitHash":"836b50c3bac7","gitCommitMessage":"UWO FGT 과몰입, 결제 관련 기능 추가(결제는 추가 작업 필요)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:00:05+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:02:28.549Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:58.535Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:02:58.546Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:02:58.549Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:02:59.918Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:02:59.918Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:02:59.918Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:02:59.919Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:59.920Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:59.920Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:02:59.921Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:03:03.176Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:03:03.177Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:03:03.177Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:03:03.184Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:03:03.185Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:03:03.199Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:03:03.277Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.297Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.312Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.324Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.337Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.349Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.365Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.382Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.398Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:03:03.416Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:03:03.491Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:03:03.492Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:03:03.496Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:03:03.586Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:03:03.588Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:03:03.589Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:03:03.589Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:03:03.592Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:03:03.592Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:03:03.592Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:03:03.593Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:03:03.595Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:03:03.595Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:03:03.595Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:03:03.596Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:03:03.597Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:03:03.601Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:03:03.601Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.603Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.603Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.603Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.603Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.603Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:03:03.604Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:03:03.615Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.616Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.626Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.634Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.644Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.651Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.661Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.675Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.683Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.693Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.700Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.707Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.715Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.721Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.728Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.736Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.744Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.752Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.758Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:03:03.765Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:03:03.774Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-07-30T09:03:03.775Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:03:03.777Z"}
{"pingInterval":2000,"curDate":1753866183,"level":"info","message":"registerServerd succeeded","timestamp":"2025-07-30T09:03:03.782Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-07-30T09:03:03.782Z"}
{"level":"info","message":"[SessionManager] session created: 4aEfLp_B, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:03:13.185Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:03:13.187Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-07-30T09:03:13.188Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-07-30T09:03:13.188Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-07-30T09:04:07.581Z"}
{"townCmsId":11000000,"channelId":"qEKi1QYL","level":"info","message":"townZone created","timestamp":"2025-07-30T09:04:07.582Z"}
{"url":"/enter","status":"200","response-time":"78.267","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:04:07.584Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":16597553,"dye2":15862966,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-07-30T09:04:07.584Z"}
{"url":"/loadComplete","status":"200","response-time":"0.583","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:04:37.326Z"}
{"url":"/updateTownUserSyncData","status":"200","response-time":"0.615","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:04:47.233Z"}
{"url":"/updateTownUserSyncData","status":"200","response-time":"0.446","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:05:08.715Z"}
{"url":"/leave","status":"200","response-time":"0.595","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:06:27.337Z"}
{"url":"/enter","status":"200","response-time":"0.556","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:06:34.118Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":16597553,"dye2":15862966,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-07-30T09:06:34.118Z"}
{"url":"/loadComplete","status":"200","response-time":"0.346","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:06:44.321Z"}
{"url":"/leave","status":"200","response-time":"0.367","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:08:41.523Z"}
{"url":"/enter","status":"200","response-time":"0.531","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:10:46.805Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":16597553,"dye2":15862966,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-07-30T09:10:46.805Z"}
{"url":"/loadComplete","status":"200","response-time":"0.230","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:11:08.042Z"}
{"url":"/leave","status":"200","response-time":"0.392","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:13:00.321Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:13:52.912Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:13:52.912Z"}
{"level":"info","message":"[Session] socket disposed, 4aEfLp_B","timestamp":"2025-07-30T09:13:52.912Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-07-30T09:13:52.913Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-07-30T09:13:53.392Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:13:53.392Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:13:53.903Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:13:53.904Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:13:53.905Z"}
{"level":"info","message":"server stopped","timestamp":"2025-07-30T09:13:54.908Z"}
{"environment":"development","type":"townd","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:13:56.175Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:25.225Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:25.235Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:14:25.238Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:14:26.911Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:14:26.912Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:14:26.912Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:14:26.913Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:26.913Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:26.914Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:14:26.914Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:14:27.806Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:14:27.807Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:14:27.807Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:14:27.814Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:14:27.815Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:14:27.832Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:14:27.913Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.933Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.950Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.962Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.976Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:27.988Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:28.005Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:28.021Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:28.037Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:28.057Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:14:28.142Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:14:28.143Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:14:28.148Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:14:28.283Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:14:28.286Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:14:28.287Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:14:28.287Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:14:28.290Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:14:28.290Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:14:28.291Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:14:28.291Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:14:28.293Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:14:28.294Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:14:28.294Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:14:28.294Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:14:28.295Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:14:28.297Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:14:28.298Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.299Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.299Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.299Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.300Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:28.301Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.312Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.325Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.336Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:14:28.348Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.350Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.357Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.376Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.386Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.395Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.407Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.414Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.422Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.428Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.435Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.444Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.451Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.459Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.468Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.478Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:28.488Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:14:28.499Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-07-30T09:14:28.501Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:28.503Z"}
{"pingInterval":2000,"curDate":1753866868,"level":"info","message":"registerServerd succeeded","timestamp":"2025-07-30T09:14:28.509Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-07-30T09:14:28.509Z"}
{"level":"info","message":"[SessionManager] session created: E85r8QoS, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:14:41.997Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:14:42.000Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-07-30T09:14:42.000Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-07-30T09:14:42.001Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-07-30T09:15:11.975Z"}
{"townCmsId":11000000,"channelId":"Mnh-x8K5","level":"info","message":"townZone created","timestamp":"2025-07-30T09:15:11.977Z"}
{"url":"/enter","status":"200","response-time":"76.911","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:15:11.979Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":16597553,"dye2":15862966,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-07-30T09:15:11.980Z"}
{"url":"/loadComplete","status":"200","response-time":"0.647","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:15:25.734Z"}
{"url":"/leave","status":"200","response-time":"0.489","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-07-30T09:17:34.068Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:57:03.079Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:57:03.080Z"}
{"level":"info","message":"[Session] socket disposed, E85r8QoS","timestamp":"2025-07-30T09:57:03.080Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-07-30T09:57:03.080Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-07-30T09:57:03.727Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:03.727Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:57:04.519Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:57:04.520Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:57:04.523Z"}
{"environment":"development","type":"townd","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:57:09.347Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:38.575Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:38.588Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:57:38.592Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:57:40.199Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:40.200Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:57:40.200Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:57:40.200Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:40.201Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:40.202Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:57:40.202Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:57:43.463Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:57:43.464Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:57:43.464Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:57:43.471Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:57:43.472Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:57:43.486Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:57:43.566Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.587Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.603Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.615Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.629Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.641Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.657Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.675Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.691Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:43.709Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:57:43.785Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:57:43.786Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:57:43.790Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:57:43.903Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:57:43.907Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:57:43.908Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:57:43.908Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:57:43.911Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:57:43.911Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:57:43.912Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:57:43.912Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:57:43.914Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:57:43.914Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:57:43.915Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:57:43.915Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:57:43.916Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:57:43.918Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:57:43.919Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.920Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.920Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.920Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.920Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.921Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:43.922Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:43.931Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:43.964Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:57:43.971Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:43.972Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:43.980Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:43.986Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.005Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.012Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.020Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.031Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.038Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.046Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.053Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.060Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.067Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.075Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.082Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.086Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.092Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:44.097Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:57:44.103Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-07-30T09:57:44.105Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:44.107Z"}
{"pingInterval":2000,"curDate":1753869464,"level":"info","message":"registerServerd succeeded","timestamp":"2025-07-30T09:57:44.112Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-07-30T09:57:44.113Z"}
{"level":"info","message":"[SessionManager] session created: sw8uS9uG, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:57:52.649Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:57:52.652Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-07-30T09:57:52.652Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-07-30T09:57:52.653Z"}
