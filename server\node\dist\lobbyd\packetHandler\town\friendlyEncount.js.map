{"version": 3, "file": "friendlyEncount.js", "sourceRoot": "", "sources": ["../../../../src/lobbyd/packetHandler/town/friendlyEncount.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;;;;;;;;;;;;;;;;;;;;AAE/E,oEAA4C;AAG5C,uEAAqE;AAErE,qDAA8D;AAE9D,mEAAgE;AAEhE,gEAA+D;AAC/D,oDAAuB;AAIvB,+DAAiD;AAGjD,mCAAgC;AAEhC,mDAAgD;AAChD,yDAA2D;AAC3D,oDAA+B;AAC/B,yCAA4C;AAC5C,mEAAwF;AAaxF,+EAA+E;AAC/E,MAAa,wBAAwB;IACnC,6EAA6E;IAC7E,gBAAe,CAAC;IAEhB,6EAA6E;IAC7E,aAAa,CAAC,IAAU;QACtB,IACE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,sBAAU,CAAC,OAAO;YACpD,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,+BAAe,CAAC,OAAO,EAC7D;YACA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,6EAA6E;IAC7E,IAAI,CAAC,IAAU,EAAE,MAAe;QAC9B,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC;QAElD,MAAM,EAAE,cAAc,EAAE,GAAG,gBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;QACvD,MAAM,UAAU,GAAW,KAAK,CAAC,UAAU,EAAE,CAAC;QAE9C,IAAI,CAAC,cAAc,IAAI,CAAC,gBAAC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YACnD,MAAM,IAAI,eAAM,CAAC,iBAAiB,EAAE,mBAAU,CAAC,wCAAwC,EAAE;gBACvF,cAAc;aACf,CAAC,CAAC;SACJ;QAED,MAAM,aAAa,GAA0B,yCAAmB,CAAC,oBAAoB,CACnF,IAAI,EACJ,IAAI,CACL,CAAC;QAEF,IAAI,aAAa,KAAK,2CAAqB,CAAC,kBAAkB,EAAE;YAC9D,OAAO,IAAI,CAAC,cAAc,CAAe,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACnE,aAAa;aACd,CAAC,CAAC;SACJ;QACD,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC;QAExB,OAAO,eAAK,CAAC,eAAe;aACzB,cAAc,CAAC,cAAc,CAAC;aAC9B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;YACpB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE;oBAC9B,MAAM,IAAI,eAAM,CACd,0BAA0B,EAC1B,mBAAU,CAAC,qCAAqC,EAChD;wBACE,cAAc,EAAE,IAAI,CAAC,MAAM;wBAC3B,cAAc;wBACd,mBAAmB,EAAE,WAAW;qBACjC,CACF,CAAC;iBACH;aACF;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,cAAc,CAAC,gCAAgC,CAAC,CAAC,cAAc,CAAC,CAAC;QAC1E,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,mBAAmB,EAAE,EAAE;YAC5B,uBAAuB;YACvB,IAAI,mBAAmB,KAAK,CAAC,EAAE;gBAC7B,MAAM,IAAI,eAAM,CACd,iCAAiC,EACjC,mBAAU,CAAC,wCAAwC,EACnD;oBACE,cAAc;oBACd,mBAAmB;iBACpB,CACF,CAAC;aACH;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,MAAM,iBAAiB,GAAG,yBAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,kBAAU,CAAC,QAAQ,CAAC,CAAC;YAErF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAEnD,OAAO,OAAO,CAAC,eAAe,CAC5B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,KAAK,EACV,iBAAiB,EACjB,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,EAC7B,cAAc,EACd,KAAK,CACN,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,IAAI,IAAI,CAAC,aAAa,KAAK,2CAAqB,CAAC,kBAAkB,EAAE;gBACnE,MAAM,YAAY,GAAyB;oBACzC,OAAO,EAAE,IAAI;oBACb,aAAa,EAAE,2CAAqB,CAAC,kBAAkB;oBACvD,OAAO,EAAE,UAAU;oBACnB,QAAQ,EAAE,KAAK;oBACf,SAAS,EAAE,IAAI,CAAC,iBAAiB;oBACjC,MAAM,EAAE,cAAc;oBACtB,KAAK,EAAE,IAAI,CAAC,aAAa;oBACzB,KAAK;oBACL,oBAAoB,EAClB,IAAI,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,SAAS;oBACvF,0BAA0B,EACxB,IAAI,CAAC,kCAAkC,GAAG,CAAC;wBACzC,CAAC,CAAC,IAAI,CAAC,kCAAkC;wBACzC,CAAC,CAAC,SAAS;oBACf,kBAAkB,EAAE,IAAI,CAAC,eAAe;iBACzC,CAAC;gBAEF,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;aACpC;YAED,OAAO,IAAI,CAAC,cAAc,CAAe,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACnE,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAtHD,4DAsHC"}