// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { LGCashParam } from '../../lobbyd/userPoints';
import { DbConnPoolManager } from '../../mysqllib/pool';
import { MErrorCode } from '../merrorCode';
import { MError } from '../merror';

/**
 * 명시적인 관련 링크 있으면 추가하면 좋을 듯..
 * ? https://developer.line.games/pages/viewpage.action?pageId=35849324
 */
export interface LGBillingResponseBody {
  success: boolean;
  msg: string;
  errorCd?: string;
  data?: unknown;
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275618
 */
export enum LGBillingErrorCode {
  SYSTEM_ERROR,
  SYSTEM_MAINTENANCE,
  INVALID_PARAMETER,
  NOT_ALLOW_AUTH,
  NOT_EXIST_DATA,
  EXPIRE_AUTH_TOKEN,
  EXIST_DUPL_RECEIPT,
  ALREADY_COMPLETE_RECEIPT,
  NOT_EXIST_RECEIPT,

  //
  UNKNOWN,
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275557
 */
export namespace LGBillingCode {
  export enum PurchaseStatus {
    /** 구매 예약 중 */
    RESERVE,
    RESERVED_CANCEL,
    COMPLETE,
    CANCEL,
    CALL_BACK_ING,
    ABUSE_BLOCK,
    CANCEL_ING,
  }

  export const APP_STORE_CD = {
    GOOGLE_PLAY: 'GOOGLE_PLAY',
    APPLE_APP_STORE: 'APPLE_APP_STORE',
    FLOOR_STORE: 'FLOOR_STORE',
    STEAM: 'STEAM',
  } as const;
}

/**
 * userId 종류를 구분
 */
export type GAME_USER_ID_TYPE =
  /* platform 에서 발급한 nid가 입력될 경우 */
  | 'NID'
  /* platform 에서 발급한 gnid가 입력될 경우 */
  | 'GNID'
  /* 게임에서 발급/관리하는 id(고유값)이 입력될 경우 */
  | 'GAME_USER_ID';

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=27528237
 */
export interface CompleteReservedPurchaseReqBody {
  orderId: string;
  svcCd: string;
  userId: string;
  gameUserIdType: GAME_USER_ID_TYPE;
  appStoreCd: string;
  receipt: string;
  /**
   * 영수증검증 무시할지 여부.
   * 171024기준으로 개발 및 QA 환경에서만 작동하는 개발자 편의기능
   */
  ignoreReceiptYn?: 'Y' | 'N' | '';
  price: number; // double
  microPrice: number; // long
  currency: string;
  /**
   * 결제 복구 같은 특수 상황에서만 기입
   */
  memo?: string;
  /**
   * IP 기반의 국가.
   * 빌링 서버에서 default 설정이 있다고 함.
   * 없는 경우(개발 환경)는 null 이 아닌 빈 문자열로 보내야 하는 것 참고
   */
  cfViewerCountry: string | '';
  /**
   * gnid 제재 정보에 사용
   * gnid 란? https://developer.line.games/pages/viewpage.action?pageId=7275349
   */
  gnid: string;
  /**
   * 게임 서버 아이디(상품보관함의 데이터, 영수증 검증에 사용)
   */
  serverId: string;

  adjustIdfa?: string;
  adjustIdfv?: string;
  adjustDeviceId?: string;
  adjustAndroidId?: string;
  adjustGpsAdid?: string;
}

export interface CompleteReservedPurchaseGoogleReqBody extends CompleteReservedPurchaseReqBody {
  /** 동일 게임코드인데 AOS에서 패키지명이 다른경우 분기처리 필요할 경우(사전 협의 필요) */
  aosPackageNm?: string;
  googleSignature: string;
}

export interface CompleteReservedPurchaseAppleReqBody extends CompleteReservedPurchaseReqBody {
  //
  transactionId: string;
}

export interface CompleteReservedPurchaseFloorReqBody extends CompleteReservedPurchaseReqBody {
  signature: string;
}

export interface CompleteReservedPurchaseSteamReqBody extends CompleteReservedPurchaseReqBody {
  //
}

/**
 * 3-1-3. 예약된 구매건의 완료 처리
 * ( https://developer.line.games/pages/viewpage.action?pageId=7275508 )
 * 응답 데이터의 giveItemList 원소
 * @see https://developer.line.games/pages/viewpage.action?pageId=35849324
 */
export interface GiveItem {
  /** 아이템 종류 */
  productItemType: string;
  itemCd: string;
  /** 코인충전 형태 */
  coinChargeTypeCd: string;
  /** 빌링에서 관리하는 재화 여부 */
  coinManageBalanceYn: 'Y' | 'N';
  amount: number;
}

/**
 * @see {@link GiveItem}
 */
function isGiveItem(x: any): x is GiveItem {
  if (
    x &&
    typeof x === 'object' &&
    typeof x.productItemType === 'string' &&
    typeof x.itemCd === 'string' &&
    (!x.coinChargeTypeCd || typeof x.coinChargeTypeCd === 'string') &&
    typeof x.amount === 'number' &&
    (x.coinManageBalanceYn === 'Y' || x.coinManageBalanceYn === 'N')
  ) {
    return true;
  }
  return false;
}

/**
 * @see {@link GiveItem}
 */
export function ensureGiveItems(x: any): asserts x is GiveItem[] {
  if (!Array.isArray(x)) {
    throw new MError('array expected', MErrorCode.INTERNAL_ERROR, { x });
  }
  for (const elem of x) {
    if (!isGiveItem(elem)) {
      throw new MError('invalid property', MErrorCode.INTERNAL_ERROR, { x });
    }
  }
}

/**
 * 플랫폼 빌링 API 공통 인터페이스
 */
export interface IPlatformBillingApiClient {
  /**
   * 유상/무상 구분하여 잔액 조회
   */
  queryCash(userId: number, appStoreCd: string, countryCreated: string): Promise<any>;

  queryCashPair(userId: number, appStoreCd: string, countryCreated: string): Promise<{
    paidRedGemBalance: number;
    freeRedGemBalance: number;
  }>;

  /**
   * Cash(레드젬, 마일리지) 추가. 단, 무료 재화만 가능. 유료 재화는 빌링을 통해서만 가능하다.
   */
  addCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    reason: string
  ): Promise<any>;

  /**
   * Cash(레드젬, 마일리지) 소모
   */
  consumeCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    lgCashParam: LGCashParam
  ): Promise<any>;

  /**
   * 판매중인 상품 목록 조회
   */
  querySalesList(appStoreCd: string): Promise<LGBillingResponseBody>;

  /**
   * 상품 구매시 지급할 아이템의 상세정보 조회
   */
  queryProductGiveItemDetail(
    appStoreCd: string,
    productId: string
  ): Promise<LGBillingResponseBody>;

  /**
   * 유저의 특정 결제 데이터 단건 조회(결제 상태 조건)
   */
  queryExistPurchaseForStatus(
    userId: string,
    status: LGBillingCode.PurchaseStatus
  ): Promise<LGBillingResponseBody>;

  /**
   * 결제 예약/완료 구매건의 상세정보 조회
   */
  queryPurchaseDetail(orderId: string): Promise<LGBillingResponseBody>;

  /**
   * 상품보관함 목록 조회
   */
  queryInventoryPurchaseList(userId: string): Promise<LGBillingResponseBody>;

  /**
   * 상품보관함 상세 조회
   */
  queryInventoryPurchase(userId: string, invenId: string): Promise<LGBillingResponseBody>;

  /**
   * 구매 예약 상태로 남아있는 최근 데이터 조회(상품ID 조건)
   */
  queryLatestReservedPurchaseByProductId(
    appStoreCd: string,
    productId: string,
    userId: string
  ): Promise<LGBillingResponseBody>;

  /**
   * 구매요청 예약 작업
   */
  reservePurchase(
    userId: string,
    userIp: string,
    productId: string,
    gnid: string,
    appStoreCd: string,
    price: number,
    currency: string,
    appVersion: string,
    os: string,
    countryCreated: string,
    microPrice: number
  ): Promise<LGBillingResponseBody>;

  /**
   * 구매 예약 상태를 '구매예약 취소' 상태로 변경
   */
  cancelReservedPurchase(userId: string, orderId: string): Promise<LGBillingResponseBody>;

  /**
   * 스토어에 따른 '구매건의 완료 처리'가 구현되었는지
   */
  isCompleteReservedPurchaseImplemented?(storeCode: string): boolean;

  /**
   * [구글] play store 구매건의 완료 처리
   */
  completeReservedPurchaseGoogle(...args: any[]): Promise<LGBillingResponseBody>;

  /**
   * [애플] app store 구매건의 완료 처리
   */
  completeReservedPurchaseApple(...args: any[]): Promise<LGBillingResponseBody>;

  /**
   * [Floor] Floor 스토어 구매 완료 처리
   */
  completeReservedPurchaseFloor(...args: any[]): Promise<LGBillingResponseBody>;

  /**
   * [Steam] 구매건 완료 처리
   */
  completeReservedPurchaseSteam(...args: any[]): Promise<LGBillingResponseBody>;

  /**
   * [Steam] 상품구입 초기화
   */
  steamPurchaseInitTxn(
    billingOrderId: number,
    steamId: BigInt,
    steamAppId: number,
    steamLanguage: string,
    steamCurrency: string,
    steamItemInfos: {
      steamItemId: number;
      steamQty: number;
      steamAmount: number;
      steamDescription: string;
    }[]
  ): Promise<LGBillingResponseBody>;

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)
   */
  chargeCoinWithInven(
    userId: string,
    orderId: string,
    gnid: string,
    countryCreated: string,
    invenDelYn: 'Y' | 'N',
    invenMemo: string,
    coinChargeList: {
      coinCd: string;
      chargeTypeCd: string;
      chargeAmt: number;
    }[]
  ): Promise<LGBillingResponseBody>;

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)(복수건)
   */
  chargeCoinWithInvenList(
    userId: string,
    gnid: string,
    countryCreated: string,
    orderList: {
      orderId: string;
      invenDelYn: 'Y' | 'N';
      invenMemo: string;
      coinChargeList: {
        coinCd: string;
        chargeTypeCd: string;
        chargeAmt: number;
      }[];
    }[]
  ): Promise<LGBillingResponseBody>;

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(단건)
   */
  completeReceiveInvenPurchase(
    userId: string,
    invenId: string,
    memo: string
  ): Promise<LGBillingResponseBody>;

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(복수건) JSON
   */
  completeReceiveInvenPurchaseBulk(
    userId: string,
    invens: {
      invenId: string;
      memo: string;
    }[]
  ): Promise<LGBillingResponseBody>;

  /**
   * 경매 입찰
   */
  tryBidding?(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    amount: number,
    reason: string
  ): Promise<any>;

  /**
   * 경매 결과 처리
   */
  bidResult?(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    bWinner: boolean,
    reason: string
  ): Promise<any>;

  /**
   * Slack 알림 전송
   */
  sendToSlack(message: string): Promise<void>;
}
