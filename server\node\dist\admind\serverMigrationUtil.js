"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.canMigrate = exports.userDbDeleteQuery = exports.userDbSelectQuery = exports.getUserTables = exports.FAIL_REASON = exports.SERVER_MIGRATION_STEP = void 0;
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
const lodash_1 = __importDefault(require("lodash"));
const typedi_1 = __importDefault(require("typedi"));
const server_1 = require("./server");
const mysqlUtil_1 = require("../mysqllib/mysqlUtil");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const iPlatformBillingApiClient_1 = require("../motiflib/mhttp/iPlatformBillingApiClient");
const mutil = __importStar(require("../motiflib/mutil"));
const const_1 = require("../lobbyd/const");
const puAdminUserLoadGuildId_1 = __importDefault(require("../mysqllib/sp/puAdminUserLoadGuildId"));
const pwAdminAuctionLoadProduct_1 = __importDefault(require("../mysqllib/sp/pwAdminAuctionLoadProduct"));
const pwAdminAuctionExpiredProductLoad_1 = __importDefault(require("../mysqllib/sp/pwAdminAuctionExpiredProductLoad"));
const puAdminDirectMailLoad_1 = __importDefault(require("../mysqllib/sp/puAdminDirectMailLoad"));
const puAdminDirectMailPendingLoad_1 = __importDefault(require("../mysqllib/sp/puAdminDirectMailPendingLoad"));
const puAdminLineMailLoad_1 = __importDefault(require("../mysqllib/sp/puAdminLineMailLoad"));
const puAdminShowTables_1 = __importDefault(require("../mysqllib/sp/puAdminShowTables"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
var SERVER_MIGRATION_STEP;
(function (SERVER_MIGRATION_STEP) {
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["NONE"] = 0] = "NONE";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["USER_REDIS_IS_DELETED"] = 1] = "USER_REDIS_IS_DELETED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["FRIENDS_IS_DELETED"] = 2] = "FRIENDS_IS_DELETED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["USER_RDB_IS_MIGRATED"] = 3] = "USER_RDB_IS_MIGRATED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["COLLECTOR_REDIS_IS_MIGRATED"] = 4] = "COLLECTOR_REDIS_IS_MIGRATED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["NATION_REDIS_IS_MIGRATED"] = 5] = "NATION_REDIS_IS_MIGRATED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["TOWN_REDIS_ACCUM_INVEST_IS_MIGRATED"] = 6] = "TOWN_REDIS_ACCUM_INVEST_IS_MIGRATED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["TOWN_REDIS_IS_DELETED"] = 7] = "TOWN_REDIS_IS_DELETED";
    SERVER_MIGRATION_STEP[SERVER_MIGRATION_STEP["AUTH_RDB_IS_UPDATED"] = 8] = "AUTH_RDB_IS_UPDATED";
})(SERVER_MIGRATION_STEP = exports.SERVER_MIGRATION_STEP || (exports.SERVER_MIGRATION_STEP = {}));
var FAIL_REASON;
(function (FAIL_REASON) {
    FAIL_REASON[FAIL_REASON["HAS_GUILD"] = 0] = "HAS_GUILD";
    FAIL_REASON[FAIL_REASON["HAS_ACUTION_PRODUCT"] = 1] = "HAS_ACUTION_PRODUCT";
    FAIL_REASON[FAIL_REASON["HAS_RECEIVABLE_AUCTION_PROCEEDS"] = 2] = "HAS_RECEIVABLE_AUCTION_PROCEEDS";
    FAIL_REASON[FAIL_REASON["HAS_CASH_SHOP_ITEM"] = 3] = "HAS_CASH_SHOP_ITEM";
    FAIL_REASON[FAIL_REASON["HAS_UNDELETED_MAIL"] = 4] = "HAS_UNDELETED_MAIL";
})(FAIL_REASON = exports.FAIL_REASON || (exports.FAIL_REASON = {}));
const UNMIGRATE_USER_TABLES = {
    ['migrations']: true,
    ['u_direct_mails']: true,
    ['u_direct_mail_last_ids']: true,
    ['u_direct_mail_pendings']: true,
    ['u_line_mails']: true,
    ['u_last_reported_discoveries']: true,
    ['u_friends']: true,
    ['u_friend_points']: true,
    ['u_arena']: true,
    ['u_arena_grade_rewards']: true,
    ['u_waiting_join_guilds']: true,
    ['u_guild_raid_tickets']: true,
    ['u_guild_shop_restricted_products']: true,
};
let userTables;
function getUserTables(dbPool) {
    if (userTables) {
        return Promise.resolve(userTables);
    }
    userTables = [];
    return (0, puAdminShowTables_1.default)(dbPool.getPool()).then((ret) => {
        for (const elem of ret) {
            const tableName = Object.values(elem)[0];
            if (UNMIGRATE_USER_TABLES[tableName]) {
                continue;
            }
            userTables.push(tableName);
        }
        return userTables;
    });
}
exports.getUserTables = getUserTables;
// user db table 중 userId로 select 하지 않아야 되거나 select 문에 포함되지 않아야 되는 컬럼이 있는 경우 따로 정의 되어야 됨.
function userDbSelectQuery(tableName, userId) {
    switch (tableName) {
        case 'u_users':
            return `SELECT * FROM u_users WHERE id = ${userId};`;
        // case 'u_direct_mail_pendings':
        //   return `SELECT userId, cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, title, titleFormatValue, body, bodyFormatValue, attachment, isValid FROM u_direct_mail_pendings WHERE id = ${userId};`;
        default:
            return `SELECT * FROM ${tableName} WHERE userId = ${userId};`;
    }
}
exports.userDbSelectQuery = userDbSelectQuery;
function userDbDeleteQuery(tableName, userId) {
    switch (tableName) {
        case 'u_users':
            return `DELETE FROM u_users WHERE id = ${userId};`;
        default:
            return `DELETE FROM ${tableName} WHERE userId = ${userId};`;
    }
}
exports.userDbDeleteQuery = userDbDeleteQuery;
function canMigrate(userId, worldId, bTest = false) {
    const { userDbConnPoolMgrs, worldDbConnPools } = typedi_1.default.get(server_1.AdminService);
    const userDbConnPoolMgr = userDbConnPoolMgrs[worldId];
    const userDbShardId = (0, mysqlUtil_1.getUserDbShardId)(userId, worldId);
    const userDbPool = userDbConnPoolMgr.getDBConnPoolByShardId(userDbShardId);
    const worldDbConnPool = worldDbConnPools[worldId];
    const curTimeUtc = mutil.curTimeUtc();
    let rsn = 0;
    return Promise.resolve()
        .then(() => {
        // 상회 탈퇴 여부 검사.
        return (0, puAdminUserLoadGuildId_1.default)(userDbPool.getPool(), userId).then((ret) => {
            if (ret) {
                mlog_1.default.info('[can-migrate] has-guild', { userId, worldId });
                rsn += 1 << FAIL_REASON.HAS_GUILD;
            }
        });
    })
        .then(() => {
        // 거래소에 등록된 상품 있는지 검사.
        return (0, pwAdminAuctionLoadProduct_1.default)(worldDbConnPool.getPool(), userId).then((ret) => {
            if (ret && lodash_1.default.isArray(ret) && ret.length > 0) {
                mlog_1.default.info('[can-migrate] has-acution-product', { userId, worldId });
                rsn += 1 << FAIL_REASON.HAS_ACUTION_PRODUCT;
            }
        });
    })
        .then(() => {
        // 거래소 정산 완료 여부 검사.
        return (0, pwAdminAuctionExpiredProductLoad_1.default)(worldDbConnPool.getPool(), userId).then((ret) => {
            if (ret && lodash_1.default.isArray(ret) && ret.length > 0) {
                mlog_1.default.info('[can-migrate] has-receivable-auction-proceeds', { userId, worldId });
                rsn += 1 << FAIL_REASON.HAS_RECEIVABLE_AUCTION_PROCEEDS;
            }
        });
    })
        .then(() => {
        // 유료 보관함 수령 여부 검사
        if (bTest) {
            return null;
        }
        const platformBillingApi = mhttp_1.default.worldHttp[worldId].platformBillingApi;
        return platformBillingApi.queryInventoryPurchaseList(userId.toString()).then((ret) => {
            let bHas = true;
            if (!ret.success) {
                if (ret.errorCd === iPlatformBillingApiClient_1.LGBillingErrorCode[iPlatformBillingApiClient_1.LGBillingErrorCode.NOT_EXIST_DATA]) {
                    // 보관함에 아무것도 없을 때도 위 에러가 나온다.
                    bHas = false;
                }
            }
            else if (!Array.isArray(ret.data) || ret.data.length === 0) {
                bHas = false;
            }
            if (bHas) {
                mlog_1.default.info('[can-migrate] has-receivable-auction-proceeds', {
                    userId,
                    worldId,
                    resp: ret,
                });
                rsn += 1 << FAIL_REASON.HAS_CASH_SHOP_ITEM;
            }
        });
    })
        .then(() => {
        // 모든 우편 삭제되었는지 검사
        return (0, puAdminDirectMailLoad_1.default)(userDbPool.getPool(), userId, curTimeUtc)
            .then((ret) => {
            if (ret.length > 0) {
                mlog_1.default.info('[can-migrate] has-undeleted-mail', { userId, worldId });
                rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
            }
            if ((rsn & (1 << FAIL_REASON.HAS_UNDELETED_MAIL)) === 0) {
                return (0, puAdminDirectMailPendingLoad_1.default)(userDbPool.getPool(), userId, curTimeUtc);
            }
            return [];
        })
            .then((ret) => {
            if (ret.length > 0) {
                mlog_1.default.info('[can-migrate] has-undeleted-pending-mail', { userId, worldId });
                rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
            }
            if ((rsn & (1 << FAIL_REASON.HAS_UNDELETED_MAIL)) === 0) {
                return (0, puAdminLineMailLoad_1.default)(userDbPool.getPool(), userId);
            }
            return [];
        })
            .then((ret) => {
            if (ret.length > 0) {
                for (const mail of ret) {
                    if (mail.state === const_1.MAIL_STATE.DELETED) {
                        continue;
                    }
                    const expireTimeUtc = parseInt(mail.expireTimeUtc, 10);
                    if (expireTimeUtc && expireTimeUtc <= curTimeUtc) {
                        continue;
                    }
                    // 라인메일(URL:'api/mailboxG/current/list')에서 받아온 정보를 우리쪽 게임DB에 저장된 메일정보.
                    mlog_1.default.info('[can-migrate] has-undeleted-line-mail', { userId, worldId });
                    rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
                    break;
                }
            }
        });
    })
        .then(() => {
        return Promise.resolve(rsn);
    });
}
exports.canMigrate = canMigrate;
//# sourceMappingURL=serverMigrationUtil.js.map