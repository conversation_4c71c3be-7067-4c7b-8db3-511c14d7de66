# Copyright (C) 2003, 2006, 2009, 2010, 2014 Free Software Foundation, Inc.

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; see the file COPYING3.  If not see
# <http://www.gnu.org/licenses/>.

dg-init
libffi-init

global srcdir subdir

if { [string match $compiler_vendor "microsoft"] } {
    # -wd4005  macro redefinition
    # -wd4244  implicit conversion to type of smaller size
    # -wd4305  truncation to smaller type
    # -wd4477  printf %lu of uintptr_t
    # -wd4312  implicit conversion to type of greater size
    # -wd4311  pointer truncation to unsigned long
    # -EHsc    C++ Exception Handling (no SEH exceptions)
    set additional_options "-wd4005 -wd4244 -wd4305 -wd4477 -wd4312 -wd4311 -EHsc";
} else {
    set additional_options "";
}

set tlist [lsort [glob -nocomplain -- $srcdir/$subdir/*.c]]

run-many-tests $tlist $additional_options

set tlist [lsort [glob -nocomplain -- $srcdir/$subdir/*.cc]]

# No C++ for or1k
if { [istarget "or1k-*-*"] } {
    foreach test $tlist {
        unsupported "$test"
    }
} else {
    run-many-tests $tlist $additional_options
}

dg-finish

# Local Variables:
# tcl-indent-level:4
# End:
