// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_load_cash_cn';

export interface Result {
  userId: number;
  paidRedGemBalance: number;
  freeRedGemBalance: number;
}

const spFunction = query.generateSPFunction(spName);

export default async function (
  connection: query.Connection,
  userId: number,
): Promise<Result> {
  const qr = await spFunction(connection, userId);
  const rows = qr.rows;
  const paidRedGemBalance = Number.parseInt(rows[0][0].paidRedGemBalance);
  const freeRedGemBalance = Number.parseInt(rows[0][0].freeRedGemBalance);
  const result = {
    userId,
    paidRedGemBalance,
    freeRedGemBalance,
  };
  return result;
}
