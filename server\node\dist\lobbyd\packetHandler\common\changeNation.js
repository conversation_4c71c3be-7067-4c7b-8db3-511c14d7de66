"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_ChangeNation = void 0;
const typedi_1 = require("typedi");
const lodash_1 = __importDefault(require("lodash"));
const cms_1 = __importDefault(require("../../../cms"));
const cmsEx = __importStar(require("../../../cms/ex"));
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const merror_1 = require("../../../motiflib/merror");
const server_1 = require("../../server");
const userConnection_1 = require("../../userConnection");
const userChangeTask_1 = require("../../UserChangeTask/userChangeTask");
const commonChangeSpec_1 = require("../../UserChangeTask/commonChangeSpec");
const userChangeOperator_1 = require("../../UserChangeTask/userChangeOperator");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const townManager_1 = require("../../townManager");
const mutil = __importStar(require("../../../motiflib/mutil"));
const rewardDesc_1 = require("../../../cms/rewardDesc");
const guildUtil_1 = require("../../guildUtil");
const formula_1 = require("../../../formula");
const nationUtil_1 = require("../../../motiflib/model/lobby/nationUtil");
const userNation_1 = require("../../userNation");
const clashPrizeManager_1 = require("../../clashPrizeManager");
const pubsub_1 = __importDefault(require("../../../redislib/pubsub"));
// ----------------------------------------------------------------------------
// 이민 할때 호출되는 패킷 (첫 이민시  국가보상을 획득가능함)
// ----------------------------------------------------------------------------
const rsn = 'change_nation';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_ChangeNation {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        // validate connection state
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const body = packet.bodyObj;
        const { nationCmsId } = body;
        const { nationManager, townRedis, worldPubsub } = typedi_1.Container.get(server_1.LobbyService);
        const townManager = typedi_1.Container.get(townManager_1.TownManager);
        const curTime = mutil.curTimeUtc();
        // validate request body
        if (nationCmsId === 0 || !nationManager.has(nationCmsId)) {
            throw new merror_1.MError('no-key-in-nation-cms', merror_1.MErrorCode.NO_KEY_IN_CMS);
        }
        const nation = nationManager.get(nationCmsId);
        // 1. 유저의 조건이 국가 변경 조건과 맞아야 합니다.
        // if (!T()) {
        //   throw new MError('nation-change-not-allowed', MErrorCode.NATION_CHANGE_NOT_ALLOWED_ERROR);
        // }
        // 2. 유저가 국가를 변경할 수 있어야 합니다.
        const oldNationCmsId = user.nationCmsId;
        if (oldNationCmsId === 0 || oldNationCmsId === nationCmsId) {
            throw new merror_1.MError('invalid-change-nation-cms', merror_1.MErrorCode.NATION_INVALID_CHANGE_NATION_ERROR);
        }
        // 3. 왕궁이 있는 도시인지 체크
        if (user.userTown.getTownBldg() !== cmsEx.BUILDING_TYPE.PALACE) {
            throw new merror_1.MError('not-palace-town', merror_1.MErrorCode.NOT_PALACE_TOWN, {
                body,
            });
        }
        // 4. 왕궁의 언어레벨 조건을 만족 해야 한다
        user.userContentsTerms.ensureBuildingContentsUnlock(cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.CHANGE_NATION, user);
        // 5. 망명 갱신 시간 조건 체크
        if (user.lastUpdateNationTimeUtc &&
            user.lastUpdateNationTimeUtc + cms_1.default.Const.NationChooseSelectCooltimeSec.value > curTime) {
            throw new merror_1.MError('not-condition-change-nation', merror_1.MErrorCode.INVALID_CHANGE_NATION, {
                body,
                curTime,
            });
        }
        // 시장 포기, 투자 금액 반환
        const refundTownCmsIds = [];
        const mayorTownCmsIds = [];
        const mayorTownCmsIdsObj = {};
        const refundedUnmayorTownCmsIds = [];
        const weeklySessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(curTime, cms_1.default.Define.InvestmentWeeklySessionPivotDay);
        // 정산 시간에는 이민 불가(시즌 랭킹 도입하면서 이민 제한 확대)
        if (nationUtil_1.NationUtil.isClosingInvestmentWeeklySession(curTime)) {
            throw new merror_1.MError('it-is-weekly-investment-cleaning-time', merror_1.MErrorCode.IT_IS_WEEKLY_INVESTMENT_CLEANING_TIME, { nationCmsId, weeklySessionId });
        }
        for (const townCms of lodash_1.default.values(cms_1.default.Town)) {
            if (townManager.getTownMayorUserId(townCms.id, curTime) === user.userId) {
                // 시장인 경우 투자 금액 반환 됨.
                refundTownCmsIds.push(townCms.id);
                // 시장인 경우 포기 됨.
                mayorTownCmsIds.push(townCms.id);
                mayorTownCmsIdsObj[townCms.id] = true;
            }
            else if (townCms.ownType === cmsEx.TOWN_OWN_TYPE.CAPITAL_TOWN ||
                townCms.ownType === cmsEx.TOWN_OWN_TYPE.NATIONAL_TOWN) {
                // 본거지, 영지 투자 금액 반환 됨.
                refundTownCmsIds.push(townCms.id);
            }
        }
        let outTrace = {};
        if (nationUtil_1.NationUtil.isClosingElectionSession(nationManager.getLastClosedElectionSessionId(nationCmsId), curTime, outTrace)) {
            throw new merror_1.MError('it-is-nation-election-cleaning-time', merror_1.MErrorCode.IT_IS_NATION_ELECTION_CLEANING_TIME, {
                curTime,
                outTrace,
            });
        }
        // 해당 국가가 선택 제한 상태가 아니어야 합니다.
        if (nation.isRestricted(nationManager)) {
            return user.sendJsonPacket(packet.seqNum, packet.type, { bRestricted: true });
        }
        // 이민국가 인구 비율 OVER
        if (nationManager.isOverPopulationRatio(user.nationCmsId, nationCmsId)) {
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                bPopulationRatioOver: true,
            });
        }
        // TODO: reputationSpec 뭔지 알아봐야 됨.
        // const reputationSpec = CreateReputationSpec(cms.Const);
        // 해당 국가에 대한 평판이 +50% 이상이어야 합니다.
        const reputation = user.userReputation.get(nationCmsId, curTime);
        if (reputation < nationManager.requiredReputationToChangeNation) {
            throw new merror_1.MError('not-enough-reputation', merror_1.MErrorCode.NOT_ENOUGH_REPUTATION);
        }
        // 칙명이 있는 경우 국가를 변경할 수 없다.
        const royalOrderQuest = user.questManager.getContextByQuestCategory(cmsEx.QUEST_CATEGORY.ROYAL_ORDER);
        if (royalOrderQuest) {
            throw new merror_1.MError('cant-change-nation-if-has-royal-order', merror_1.MErrorCode.CANT_CHANGE_NATION_IF_HAS_ROYAL_ORDER, {
                royalOrderQuestCmsId: royalOrderQuest.cmsId,
            });
        }
        //
        const nationRank = nationManager.getPowerRank(nationCmsId);
        // 평판, 우호도 변동
        const reputationChanges = [];
        const nationDiplomacyCmsIdToRecordIntimacyEvent = user.userReputation.buildReputationIntimacyChanges(nationCmsId, oldNationCmsId, cmsEx.NATION_DIPLOMACY_CMS_ID.CHANGE_NATION, reputationChanges, curTime);
        // 제독 명성 변동
        // 강대국으로 이민 시 명성 차감( https://jira.line.games/browse/UWO-16270 )
        const leaderMateFameChangesForLoss = (() => {
            if (!nationManager.get(oldNationCmsId)) {
                mlog_1.default.error(`[${rsn}] failed-to-get-old-nation-so-fame-loss-wil-be-skipped. nationCmsId: ${oldNationCmsId}`, { userId: user.userId });
                return undefined;
            }
            const oldNationRank = nationManager.getPowerRank(oldNationCmsId);
            if (!Number.isInteger(nationRank) || !Number.isInteger(oldNationRank)) {
                mlog_1.default.warn(`[${rsn}] unexpected-nation-rank-so-fame-loss-will-be-skipped.`, {
                    userId: user.userId,
                    nationCmsId,
                    nationRank,
                    oldNationCmsId,
                    oldNationRank,
                });
                return undefined;
            }
            const bNewNationStrong = nationRank < oldNationRank; // 숫자가 작을수록 강대국
            if (!bNewNationStrong) {
                return undefined;
            }
            const RATIO = cms_1.default.Const.ExileFame.value; // 음수. 단위: %.
            const ALL_FAME_TYPES = [
                cmsEx.JOB_TYPE.ADVENTURE,
                cmsEx.JOB_TYPE.TRADE,
                cmsEx.JOB_TYPE.BATTLE,
            ];
            const leaderMate = user.userMates.getLeaderMate(user.userFleets);
            return ALL_FAME_TYPES.map((jobType) => {
                const curFame = leaderMate.getFame(jobType);
                return {
                    jobType,
                    fame: Math.max(0, curFame + Math.floor((curFame * RATIO) / 100)),
                };
            }).filter((elem) => elem.fame !== leaderMate.getFame(elem.jobType));
        })();
        // 첫 이민시 국가 보상 지급
        let nationRankBonus;
        if (!user.lastUpdateNationTimeUtc) {
            const nationRankingEffectCms = cms_1.default.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.SELECT_NATION];
            nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];
        }
        let cost = nation.GetChangeNationCost(nationManager);
        // 첫 이민시 무료 이민
        if (!user.lastUpdateNationTimeUtc) {
            cost = 0;
        }
        // 투자 점수 기반 시즌 투자 증서 지급
        const investSeasonItemCmsId = cmsEx.getRefundInvestItemCmsId(curTime);
        if (!investSeasonItemCmsId) {
            throw new merror_1.MError('empty-refund-invest-item-cms-id', merror_1.MErrorCode.INVALID_ITEM);
        }
        const investCompensationItemCms = cms_1.default.Item[investSeasonItemCmsId];
        const reward_data = [];
        const resp = {};
        let investCompensationItemAmount = 0;
        let changeTask;
        const promises = [];
        for (const townCmsId of refundTownCmsIds) {
            // [TODO] loadInvestmentMyAccumPoints 으로 교체.
            promises.push(townRedis['loadInvestmentMyAccumPoint'](user.userId, townCmsId, weeklySessionId, user.userGuild.guildId).then((ret) => {
                const rawScore = parseInt(ret[1], 10);
                const score = Math.floor(rawScore / cms_1.default.Define.InvestmentSocreMultiplier);
                if (score > 0) {
                    investCompensationItemAmount += Math.ceil((score *
                        cms_1.default.Const.InvestCompanyPointPer.value *
                        cms_1.default.Const.ImmigrationInvestmentRefund.value) /
                        1000 /
                        investCompensationItemCms.custom);
                    if (!mayorTownCmsIdsObj[townCmsId]) {
                        refundedUnmayorTownCmsIds.push(townCmsId);
                    }
                }
            }));
        }
        return Promise.all(promises)
            .then(() => {
            changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.CHANGE_NATION, new ChangeNationSpec(nationCmsId, curTime, cms_1.default.Const.NationChangePointId.value, cost, nationRankBonus, {
                cmsId1: oldNationCmsId,
                cmsId2: nationCmsId,
                nationDiplomacyCmsId: nationDiplomacyCmsIdToRecordIntimacyEvent,
            }, reputationChanges, leaderMateFameChangesForLoss, reward_data, investCompensationItemCms.id, investCompensationItemAmount));
            const res = changeTask.trySpec();
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                throw new merror_1.MError('try-change-nation-spec-failed', merror_1.MErrorCode.TRY_CHANGE_NATION_SPEC_FAILED, {
                    res,
                });
            }
            // 시장, 투자 점수 제거.
            const promises = [];
            mlog_1.default.info('Try change nation.', {
                userId: user.userId,
                nationCmsId,
                cost,
                nationRankBonus,
                leaderMateFameChangesForLoss,
                investCompensationItemAmount,
                refundTownCmsIds,
                mayorTownCmsIds,
            });
            for (const townCmsId of mayorTownCmsIds) {
                promises.push(townRedis['resignMayor'](user.userId, townCmsId, weeklySessionId));
            }
            for (const townCmsId of refundedUnmayorTownCmsIds) {
                promises.push(townRedis['removeTownUserWeeklyInvestmentScore'](user.userId, townCmsId, weeklySessionId));
            }
            return Promise.all(promises);
        })
            .then(() => {
            return changeTask.apply();
        })
            .then((sync) => {
            resp.sync = sync;
            const townInfo = user.userTown.getTownInfo();
            if (townInfo) {
                const townApi = mhttp_1.default.townpx.channel(townInfo.url);
                townApi.updateTownUserSyncData(user.userId, { user: { nationCmsId } }).catch((err) => {
                    mlog_1.default.error('Town api updateTownUserSyncData is failed.', {
                        userId: user.userId,
                        err: err.message,
                    });
                });
            }
            mhttp_1.default.authd.changeUserNation(user.userId, nationCmsId).catch((err) => {
                mlog_1.default.error('Auth api changeUserNation is failed.', {
                    userId: user.userId,
                    nationCmsId,
                    err: err.message,
                });
            });
            mhttp_1.default.platformChatApi.updateVolanteUser(user);
            return guildUtil_1.GuildUtil.onChangedUserNation(user);
        })
            .then((guildSync) => {
            lodash_1.default.merge(resp.sync, guildSync);
            return (0, userNation_1.onChangedUserNation)(user, oldNationCmsId, resp.sync);
        })
            .then(() => {
            const clashPrizeManager = typedi_1.Container.get(clashPrizeManager_1.ClashPrizeManager);
            const pubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
            if (clashPrizeManager.isRanker(user.userId)) {
                return pubsub.publish('clash_season_rankers_updated', {});
            }
            return null;
        })
            .then(() => {
            // glog
            const leaderMate = user.userMates.getLeaderMate(user.userFleets);
            const royalTitle = leaderMate.getNub().royalTitle;
            const royalTitleCms = cms_1.default.RoyalTitle[royalTitle];
            const oldNationCms = cms_1.default.Nation[oldNationCmsId];
            const curNationCms = cms_1.default.Nation[nationCmsId];
            user.glog('nation_change', {
                rsn,
                add_rsn,
                old_nation: oldNationCms.name,
                cur_nation: curNationCms.name,
                pr_data: [
                    {
                        type: cms_1.default.Const.NationChangePointId.value,
                        amt: cost,
                    },
                ],
                reward_data: reward_data,
                exchange_hash: changeTask.getExchangeHash(),
            });
            user.glog('admiral_royaltitle', {
                rsn,
                add_rsn,
                id: leaderMate.getNub().royalTitle,
                nation: nationCmsId,
                grade: royalTitleCms.grade,
                name: royalTitleCms.titleNames[curNationCms.royalTitleNameType],
            });
            // 시장 포기.
            for (const townCmsId of mayorTownCmsIds) {
                lodash_1.default.merge(resp.sync, {
                    add: {
                        allTownInvestments: {
                            [townCmsId]: {
                                score: 0,
                            },
                        },
                    },
                });
                const msg = {
                    townCmsId,
                    mayorUserId: null,
                    mayorUserName: null,
                    mayorNationCmsId: null,
                    updateTimeUtc: curTime,
                };
                worldPubsub.publish('town_mayor_changed', JSON.stringify(msg)).catch((err) => {
                    mlog_1.default.alert('changeNation town_mayor_changed publish is failed.', {
                        err: err.message,
                        townCmsId,
                        userId: user.userId,
                    });
                });
            }
            for (const townCmsId of refundedUnmayorTownCmsIds) {
                lodash_1.default.merge(resp.sync, {
                    add: {
                        allTownInvestments: {
                            [townCmsId]: {
                                score: 0,
                            },
                        },
                    },
                });
                const msgObj = {
                    townCmsId,
                    developmentType: cmsEx.DEVELOPMENT_TYPE.industry,
                    oldDevelopmentLevel: townManager.getDevelopmentLevel(townCmsId, cmsEx.DEVELOPMENT_TYPE.industry),
                    sessionId: weeklySessionId,
                    updateTimeUtc: curTime,
                };
                worldPubsub.publish('town_invested', JSON.stringify(msgObj)).catch((err) => {
                    mlog_1.default.alert('changeNation town_invested publish is failed.', {
                        err: err.message,
                        townCmsId,
                        userId: user.userId,
                    });
                });
            }
            return user.sendJsonPacket(packet.seqNum, packet.type, resp);
        });
    }
}
exports.Cph_Common_ChangeNation = Cph_Common_ChangeNation;
class ChangeNationSpec extends commonChangeSpec_1.SetNationSpec {
    constructor(nationCmsId, lastUpdateNationTimeUtc, pointCmsId, cost, nationRankBonusPointVal, nationEvent, reputationChanges, leaderMateFameChanges, reward_data, investCompensationItemCmsId, investCompensationItemAmount) {
        super(nationCmsId, lastUpdateNationTimeUtc);
        this.pointCmsId = pointCmsId;
        this.cost = cost;
        this.nationRankBonusPointVal = nationRankBonusPointVal;
        this.nationEvent = nationEvent;
        this.reputationChanges = reputationChanges;
        this.leaderMateFameChanges = leaderMateFameChanges;
        this.reward_data = reward_data;
        this.investCompensationItemCmsId = investCompensationItemCmsId;
        this.investCompensationItemAmount = investCompensationItemAmount;
    }
    accumulate(user, tryData, changes) {
        const res = super.accumulate(user, tryData, changes);
        if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
            return res;
        }
        const ops = [];
        // 첫 이민시 이민 비용 무료
        if (this.cost) {
            ops.push((0, userChangeOperator_1.opAddPoint)(user, tryData, changes, this.pointCmsId, -this.cost, false, true, { itemId: rsn }, false));
        }
        // 이민시 국가 보너스 제공
        if (this.nationRankBonusPointVal) {
            ops.push((0, userChangeOperator_1.opAddPoint)(user, tryData, changes, cms_1.default.Const.NationPickBonusPointId.value, this.nationRankBonusPointVal, false, false, { gainReason: rsn }, false));
        }
        if (this.nationEvent.nationDiplomacyCmsId !== undefined) {
            ops.push((0, userChangeOperator_1.opRecordNationEventOccur)(user, tryData, changes, this.nationEvent.cmsId1, this.nationEvent.cmsId2, this.nationEvent.nationDiplomacyCmsId));
        }
        for (const elem of this.reputationChanges) {
            ops.push((0, userChangeOperator_1.opSetReputation)(user, tryData, changes, elem.nationCmsId, elem.reputation, elem.updateTimeUtc));
        }
        if (this.leaderMateFameChanges) {
            for (const elem of this.leaderMateFameChanges) {
                ops.push((0, userChangeOperator_1.opSetFame)(user, tryData, changes, elem.jobType, elem.fame));
            }
        }
        if (user.questManager.getPalaceRoyalOrderCmsId() ||
            user.questManager.getPalaceRoyalTitleOrderCmsId()) {
            ops.push((0, userChangeOperator_1.opResetPalaceRoyalOrder)(user, tryData, changes));
        }
        if (this.investCompensationItemAmount) {
            ops.push((0, userChangeOperator_1.opAddItem)(user, tryData, changes, this.investCompensationItemCmsId, this.investCompensationItemAmount, true, false, undefined, true, true));
        }
        for (const res of ops) {
            if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                return res;
            }
        }
        if (this.nationRankBonusPointVal) {
            this.reward_data.push({
                type: rewardDesc_1.REWARD_TYPE[rewardDesc_1.REWARD_TYPE.POINT],
                id: cms_1.default.Const.NationPickBonusPointId.value,
                uid: null,
                amt: this.nationRankBonusPointVal,
            });
        }
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    }
}
//# sourceMappingURL=changeNation.js.map