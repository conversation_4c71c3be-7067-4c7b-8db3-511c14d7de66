// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_muted_list_cn';

export interface Result {
  userId: number;
  mutedUserIds: number[];
}

const spFunction = query.generateSPFunction(spName);

export default async function (
  connection: query.Connection,
  userId: number,
): Promise<Result> {
  const qr = await spFunction(connection, userId);
  const rows = qr.rows;

  const result = {
    userId,
    mutedUserIds: [],
  }

  for (const row of rows[0]) {
    result.mutedUserIds.push(row.mutedUserId);
  }

  return result;
}
