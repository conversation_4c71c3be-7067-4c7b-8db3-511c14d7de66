<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for winston-transport/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> winston-transport/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">68% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>17/25</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">55.56% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>10/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/4</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">68% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>17/25</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="index.js"><a href="index.js.html">index.js</a></td>
	<td data-value="68" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 68%;"></div><div class="cover-empty" style="width:32%;"></div></div></td>
	<td data-value="68" class="pct medium">68%</td>
	<td data-value="25" class="abs medium">17/25</td>
	<td data-value="55.56" class="pct medium">55.56%</td>
	<td data-value="18" class="abs medium">10/18</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="68" class="pct medium">68%</td>
	<td data-value="25" class="abs medium">17/25</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Mar 18 2017 18:17:55 GMT-0400 (EDT)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
