// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Client<PERSON><PERSON><PERSON>Handler } from '../index';
import { getUserLightInfos, UserLightInfo } from '../../../motiflib/userCacheRedisHelper';
import { LobbyService } from '../../server';
import { Container } from 'typedi/Container';
import mconf from '../../../motiflib/mconf';

const rsn = 'friend_enemy';
const add_rsn = null;

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

interface RequestBody {
  targetUserId: number;
}

interface ResponseBody {
  //
}

// ----------------------------------------------------------------------------
export class Cph_Common_ChatUnmuteUser implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;
    const { targetUserId } = reqBody;

    if (targetUserId === undefined || !Number.isInteger(targetUserId)) {
      throw new MError(
        'targetUserId-integer-expected',
        MErrorCode.INVALID_REQ_BODY_CHAT_UNMUTE_USER,
        { reqBody }
      );
    }

    const volanteUserId: string = user.userId.toString();
    const volanteTargetUserId: string = targetUserId.toString();

    return Promise.resolve()
      .then(() => {
        // 에러 절차를 명확하게 하기 위해.
        // chatInit 이 된 상태인지와 볼란테에 접속 중인지를 알기 위함.
        // 무거운 조회는 아닐 거라 보고..
        return mhttp.platformChatApi.getSessions(volanteUserId);
      })
      .then(() => {
        return mhttp.platformChatApi.unmuteUser(volanteUserId, volanteTargetUserId);
      })
      .then(() => {
        const { userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr } =
          Container.get(LobbyService);
        const worldConfg = mconf.getWorldConfig();
        return getUserLightInfos(
          [targetUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          const userLightInfo = userLightInfos[targetUserId];
          if (userLightInfo) {
            user.glog('friend_enemy', {
              rsn,
              add_rsn,
              friend_nid: userLightInfo.pubId,
              friend_gameUserId: targetUserId,
              type: 0, // 차단:1 해제:0
            });
          }
          return;
        });
      })

      .then(() => {
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {});
      });
  }
}
