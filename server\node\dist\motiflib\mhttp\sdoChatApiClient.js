"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SdoChatApiClient = exports.PublicChannle = exports.getAliasName = exports.CHANNEL_TYPE = void 0;
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
const os_1 = __importDefault(require("os"));
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
const crypto = __importStar(require("crypto"));
const mconf_1 = __importDefault(require("../mconf"));
const mlog_1 = __importDefault(require("../mlog"));
const baseApiClient_1 = require("./baseApiClient");
const merror_1 = require("../merror");
const slackNotifier_1 = require("../slackNotifier");
const cms_1 = __importDefault(require("../../cms"));
const typedi_1 = require("typedi");
const server_1 = require("../../lobbyd/server");
const puUserQueryMutedList_cn_1 = __importDefault(require("../../mysqllib/sp/puUserQueryMutedList_cn"));
const puUserGetMutedCount_cn_1 = __importDefault(require("../../mysqllib/sp/puUserGetMutedCount_cn"));
const puUserMute_cn_1 = __importDefault(require("../../mysqllib/sp/puUserMute_cn"));
const puUserUnmute_cn_1 = __importDefault(require("../../mysqllib/sp/puUserUnmute_cn"));
const pubsub_1 = __importDefault(require("../../redislib/pubsub"));
const AxiosTimeout = 5000;
var CHANNEL_TYPE;
(function (CHANNEL_TYPE) {
    CHANNEL_TYPE[CHANNEL_TYPE["ALL"] = 0] = "ALL";
    CHANNEL_TYPE[CHANNEL_TYPE["SYSTEM"] = 1] = "SYSTEM";
    CHANNEL_TYPE[CHANNEL_TYPE["WORLD"] = 2] = "WORLD";
    CHANNEL_TYPE[CHANNEL_TYPE["NATION"] = 3] = "NATION";
    CHANNEL_TYPE[CHANNEL_TYPE["GUILD"] = 4] = "GUILD";
    CHANNEL_TYPE[CHANNEL_TYPE["REGION"] = 5] = "REGION";
    CHANNEL_TYPE[CHANNEL_TYPE["MAX"] = 5] = "MAX";
})(CHANNEL_TYPE = exports.CHANNEL_TYPE || (exports.CHANNEL_TYPE = {}));
function getAliasName(channelType, channelName) {
    if (channelType === CHANNEL_TYPE.SYSTEM) {
        return '안내';
    }
    if (channelType === CHANNEL_TYPE.WORLD) {
        return '세계';
    }
    if (channelType === CHANNEL_TYPE.NATION) {
        return `국가_${cms_1.default.Nation[channelName].name}`;
    }
    if (channelType === CHANNEL_TYPE.REGION) {
        if (cms_1.default.Region[channelName]) {
            return `지역_${cms_1.default.Region[channelName].name}`;
        }
        if (cms_1.default.Town[channelName]) {
            return `지역_${cms_1.default.Town[channelName].name}`;
        }
    }
    if (channelType === CHANNEL_TYPE.GUILD) {
        return `상회_${channelName.replace('GUILD_', '')}`;
    }
    return null;
}
exports.getAliasName = getAliasName;
var PublicChannle;
(function (PublicChannle) {
})(PublicChannle = exports.PublicChannle || (exports.PublicChannle = {}));
class SdoChatApiClient extends baseApiClient_1.BaseApiClient {
    init(baseUrl, timeout) {
        super.init(baseUrl, timeout);
        mlog_1.default.info(`chatd endpoint: ${baseUrl}`);
    }
    setSalt(salt) {
        this.salt = salt || '0';
    }
    async sendToSlack(message) {
        if (this.slackNotifier === undefined) {
            this.slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        }
        await this.slackNotifier.notify({
            username: `host: ${os_1.default.hostname()}`,
            text: message,
            channel: '#sdk-error',
        });
    }
    getIdToken(id) {
        const hash = crypto.createHash('SHA256');
        hash.update(id + this.salt);
        return hash.digest('hex');
    }
    async getAllChannels() {
        const args = await this._get('/channels');
        return args.channels;
    }
    async getChannel(channelName) {
        const args = await this._get(`/channels/${channelName}`);
        return args;
    }
    async existChannel(channelName) {
        // const axios = this.mrest.axios();
        // const resp = await axios.get(`/channels/${channelName}`, {
        //   headers: { Authorization: `custom ${this.salt}` },
        //   timeout: AxiosTimeout,
        //   validateStatus: () => true,
        // });
        // const result = resp.data;
        // return result.success && result.args && result.args.channel_name;
        // 채널이 없으면 생성하는 로직이 있으므로, 무조건 채널이 있는것처럼 만들어서 기존 로직을 수정하지 않아도 되도록함.
        return true;
    }
    async getAllowUserList(channelName) {
        const axios = this.mrest.axios();
        const resp = await axios.get(`/channels/${channelName}/allows`, {
            headers: { Authorization: `custom ${this.salt}` },
            timeout: AxiosTimeout,
            validateStatus: () => true,
        });
        const result = resp.data;
        if (result.success && result.args) {
            mlog_1.default.info('[TEMP] [DEBUG] chat allow user list.', {
                channelName,
                list: result.args.allow_users,
            });
            return result.args.allow_users;
        }
        return [];
    }
    /**
     * 공용 채널 생성 (월드 / 지역 / 국가).
     *
     * public & persistent & !group channel
     */
    async createPublicChannel(name, alias) {
        const body = {
            type: 'persistent',
            name,
            alias,
            group: false,
            public: true,
            user_limit: 100000,
        };
        const args = await this._post('/channels', body, [33 /* ALREADY_EXIST_CHANNEL */]);
        return args;
    }
    /**
     * 길드 채널 생성 (월드 / 지역 / 국가)
     *
     * private & persistent & !group channel
     */
    async createGuildChannel(channelName, userId, bShouldAllowChannel) {
        const body = {
            type: 'persistent',
            name: channelName,
            alias: getAliasName(CHANNEL_TYPE.GUILD, channelName),
            group: false,
            public: false,
            user_limit: 1000,
        };
        const args = await this._post('/channels', body);
        if (bShouldAllowChannel) {
            await this.allowGuildChannel(channelName, userId);
        }
        return args;
    }
    /**
     * 길드 채널 입장 허용
     */
    async allowGuildChannel(channelName, userId) {
        // const body = {
        //   user_id: userId,
        // };
        // return this._post(`/channels/${channelName}/allows`, body);
    }
    /**
     * 길드 채널 허용 사용자 삭제
     */
    async disallowGuildChannel(channelName, userId) {
        // return await this._delete(`/channels/${channelName}/allows/${userId}`);
    }
    /**
     * 채널 삭제
     */
    async deleteChannel(channelName) {
        // return await this._delete(`/channels/${channelName}`);
    }
    /**
     * 인증 시점에 채팅 서버에 등록되지 않은 유저면 추가한다.
     * (월드 선택 시점에는 채팅 서버에 접속해야 되므로 그 전 단계에서 필수로 진행해야 함)
     */
    async createUserIfNotExists(userId, nickName) {
        let url = `/users/${userId}`;
        try {
            // 조회
            const axios = this.mrest.axios();
            const resp = await axios.get(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            const result = resp.data;
            if (result.success) {
                return; // result.args;
            }
            if (result.error) {
                // 사용자 없는 경우에 생성
                const errcode = result.error.error_code;
                if (errcode === 45 /* NOT_EXIST_USER */) {
                    url = '/users';
                    await this._post('/users', { id: userId, nickname: nickName }, [
                        46 /* ALREADY_EXIST_USER */,
                    ]);
                }
            }
            else {
                throw new merror_1.MError('chat request error', merror_1.MErrorCode.LGSDK_ERROR, {
                    request: url,
                    response: result,
                });
            }
        }
        catch (error) {
            this.handleError(url, null, error);
        }
    }
    /**
     * 사용자 정보 받아오기
     */
    async getUser(userId) {
        const result = await this.get(`/users/${userId}`);
        return result;
    }
    /**
     * 사용자 세션 정보 조회
     */
    async getSessions(userId) {
        // const result = await this._get(`/users/${userId}/session`, [ChatErrorCode.SESSION_NOT_FOUND]);
        // if (result.hasIgnoredError) {
        //   throw new MError('session not found', MErrorCode.LG_VOLANTE_INVALID_SESSION);
        // }
        // return result as VolanteUserSession;
        return undefined;
    }
    /**
     * 유저 정보 수정
     */
    async updateUser(userId, nickName, extraData) {
        // await this._patch(`/users/${userId}`, { nickname: nickName, extra_data: extraData });
    }
    /**
     * 유저 채널 입장
     * 지역 이동 / 국가 변경시에 채널 Leave와 함께 Join 되어야 합니다. 채널 이름은 CmsId 사용
     */
    async channelJoin(channelName, userId) {
        // try {
        //   await this._post(
        //     `/users/${userId}/join`,
        //     {
        //       channel_name: channelName,
        //       options: 'DO_NOT_ANNOUNCE',
        //     },
        //     [ChatErrorCode.ALREADY_JOINED_CHANNEL]
        //   );
        // } catch (error) {
        //   if (error.extra?.error_code) {
        //     const errcode = error.extra.error_code;
        //     if (errcode === ChatErrorCode.INVALID_SESSION) {
        //       throw new MError('Join failed', MErrorCode.LG_VOLANTE_INVALID_SESSION);
        //     }
        //   }
        //   throw error;
        // }
    }
    /**
     * 유저 채널 퇴장
     * 지역 이동 / 국가 변경시에 채널에서 나가야 합니다. 채널 이름은 CmsId 사용
     */
    async channelLeave(channelName, userId) {
        // return await this._post('/users/${userId}/leave', { channel_name: channelName });
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43424902
     *
     * 사용자가 채팅을 보이지 않도록 등록한 사용자의 수를 조회합니다.
     */
    async getUserMuteUserCount(userId) {
        // TODO database로 처리해야함.
        // const args = await this.get(`/users/${userId}/mute/count`);
        // const muteUserCount = args?.mute_user_count;
        // if (!Number.isInteger(muteUserCount)) {
        //   throw new MError(
        //     '[GET /users/{userId}/mute/count] unexpected-resp-received',
        //     MErrorCode.LG_VOLANTE_ERROR,
        //     { args }
        //   );
        // }
        // return muteUserCount;
        const userIdAsNumber = Number.parseInt(userId);
        const { userDbConnPoolMgr } = typedi_1.Container.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
        const result = await (0, puUserGetMutedCount_cn_1.default)(userDbPool, userIdAsNumber);
        return result;
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421870
     *
     * 차단한 사용자 목록을 가져옵니다.
     * @param userId 차단 주체 volante user id
     */
    async getMuteUserIds(userId) {
        const userIdAsNumber = Number.parseInt(userId);
        const { userDbConnPoolMgr } = typedi_1.Container.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
        const result = await (0, puUserQueryMutedList_cn_1.default)(userDbPool, userIdAsNumber);
        return result.mutedUserIds;
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421866
     *
     * 사용자가 다른 사용자의 채팅을 보이지 않도록 차단 등록합니다.
     * @param userId 차단 주체 volante user id
     * @param targetUserId 차단 당할 volante user id
     */
    async muteUser(userId, targetUserId) {
        const userIdAsNumber = Number.parseInt(userId);
        const targetUserIdAsNumber = Number.parseInt(targetUserId);
        const { userDbConnPoolMgr } = typedi_1.Container.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
        const result = await (0, puUserMute_cn_1.default)(userDbPool, userIdAsNumber, targetUserIdAsNumber);
        if (result === 1) {
            // already muted.
            throw new merror_1.MError('[Chat] already-muted-user', merror_1.MErrorCode.CHAT_ALREADY_MUTED_USER);
        }
        await this.notifyMutedListChanged('mute', userIdAsNumber, targetUserIdAsNumber);
        // const IGNORE_ERROR_CODES = [
        //   ChatErrorCode.NOT_EXIST_USER,
        //   ChatErrorCode.ALREADY_MUTED_USER,
        //   ChatErrorCode.EXCEED_MAX_MUTE_USER_COUNT,
        // ] as const;
        // // TODO database로 처리해야함.
        // const result = await this._post(
        //   `/users/${userId}/mute`,
        //   { mute_user_id: targetUserId },
        //   IGNORE_ERROR_CODES
        // );
        // if (result.hasIgnoredError) {
        //   const params = {
        //     userId,
        //     targetUserId,
        //   } as const;
        //   switch (result.ignoredErrorCode as (typeof IGNORE_ERROR_CODES)[number]) {
        //     case ChatErrorCode.NOT_EXIST_USER:
        //       throw new MError(
        //         '[Volante] not-exist-user',
        //         MErrorCode.LG_VOLANTE_NOT_EXIST_USER,
        //         params
        //       );
        //     case ChatErrorCode.ALREADY_MUTED_USER:
        //       throw new MError(
        //         '[Volante] already-muted-user',
        //         MErrorCode.CHAT_ALREADY_MUTED_USER,
        //         params
        //       );
        //     case ChatErrorCode.EXCEED_MAX_MUTE_USER_COUNT:
        //       throw new MError(
        //         '[Volante] exceed-max-mute-count',
        //         MErrorCode.CHAT_EXCEED_MAX_MUTE_USER_COUNT,
        //         params
        //       );
        //     default:
        //       assert.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
        //   }
        // }
        // return result;
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421868
     *
     * 사용자가 다른 사용자의 채팅을 보이도록 등록된 차단을 해제합니다.
     * @param userId 차단 주체 volante user id
     * @param targetUserId 차단 당할 volante user id
     */
    async unmuteUser(userId, targetUserId) {
        // const IGNORE_ERROR_CODES = [
        //   ChatErrorCode.NOT_EXIST_USER,
        //   ChatErrorCode.NOT_MUTED_USER,
        // ] as const;
        // // TODO database로 처리해야함.
        // const result = await this._delete(`/users/${userId}/mute/${targetUserId}`, IGNORE_ERROR_CODES);
        // if (result.hasIgnoredError) {
        //   const params = {
        //     userId,
        //     targetUserId,
        //   } as const;
        //   switch (result.ignoredErrorCode as (typeof IGNORE_ERROR_CODES)[number]) {
        //     case ChatErrorCode.NOT_EXIST_USER:
        //       throw new MError(
        //         '[Volante] not-exist-user',
        //         MErrorCode.LG_VOLANTE_NOT_EXIST_USER,
        //         params
        //       );
        //     case ChatErrorCode.NOT_MUTED_USER:
        //       throw new MError('[Volante] not-muted-user', MErrorCode.CHAT_NOT_MUTED_USER, params);
        //     default:
        //       assert.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
        //   }
        // }
        // return result;
        const userIdAsNumber = Number.parseInt(userId);
        const targetUserIdAsNumber = Number.parseInt(targetUserId);
        const { userDbConnPoolMgr } = typedi_1.Container.get(server_1.LobbyService);
        const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
        const result = await (0, puUserUnmute_cn_1.default)(userDbPool, userIdAsNumber, targetUserIdAsNumber);
        if (result === 1) {
            throw new merror_1.MError('[Chat] not-muted-user', merror_1.MErrorCode.CHAT_NOT_MUTED_USER);
        }
        await this.notifyMutedListChanged('unmute', userIdAsNumber, targetUserIdAsNumber);
        return {};
    }
    async notifyMutedListChanged(op, userId, targetUserId) {
        const pubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
        if (pubsub) {
            const payload = {
                op,
                userId,
                targetUserId,
            };
            await pubsub.publish('sdo-user-mute-list-changed', JSON.stringify(payload));
        }
    }
    async updateVolanteUser(user) {
        // const volanteId = user.userId.toString();
        // const extraData = {
        //   nationCmsId: user.nationCmsId,
        //   guildId: user.userGuild.guildId,
        //   guildName: user.userGuild.getGuildName(),
        // };
        // try {
        //   return await this.updateUser(volanteId, user.userName, extraData);
        // } catch (err) {
        //   mlog.error('[VOLANTE] update user is failed', { userId: user.userId, err });
        // }
    }
    async _get(url, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.get(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, {}, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, null, error);
        }
    }
    async _post(url, body, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.post(url, body, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, body, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, body, error);
        }
    }
    async _patch(url, body, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.patch(url, body, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, body, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, body, error);
        }
    }
    async _delete(url, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.delete(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, {}, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, null, error);
        }
    }
    handleError(url, body, error) {
        var _a, _b;
        if (error instanceof merror_1.MError) {
            this.sendToSlack(`[Volante] handle reponse error, url: ${url}, error: ${error.message}`).catch();
            const volanteErrorCode = ((_b = (_a = error.extra) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.error_code) || undefined;
            throw new merror_1.MError('chatApiClient, volante api err', error.mcode, {
                url,
                volanteErrorCode,
                merror: {
                    mcode: error.mcode,
                    message: error.message,
                    extra: error.extra,
                },
            });
        }
        this.sendToSlack(`[Volante] exception, url: ${url}, error: ${error.message}, code: ${error.code ? error.code : 'undefined'}`).catch();
        throw new merror_1.MError('chatApiClient, error occured', merror_1.MErrorCode.INTERNAL_ERROR, `'${url}', message: ${error.message}`);
    }
    async handleResponse(url, body, resp, ignoreErrors) {
        var _a;
        const result = resp.data;
        if (result.success) {
            return result.args;
        }
        // if skippable error, return empty body result
        if ((_a = result.error) === null || _a === void 0 ? void 0 : _a.error_code) {
            const errcode = result.error.error_code;
            if (ignoreErrors.indexOf(errcode) !== -1) {
                return {
                    hasIgnoredError: true,
                    ignoredErrorCode: errcode,
                };
            }
        }
        else {
            this.sendToSlack(`[Volante] response error, url: ${url}, body: ${JSON.stringify(body)}, result: ${JSON.stringify(result)}`).catch();
        }
        throw new merror_1.MError('Volante-api-failed', merror_1.MErrorCode.LG_VOLANTE_ERROR, result);
    }
}
exports.SdoChatApiClient = SdoChatApiClient;
//# sourceMappingURL=sdoChatApiClient.js.map