// TODO: linesdk를 통해서 잔액을 조회하고 있다. 이 부분을 사용할수 없으므로, database에 있는 포인트를 조회하는 형태로 변경해줘야한다.

// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import assert from 'assert';
import { type ISlackNotifier } from '../slackNotifier';
import type { LGCashParam } from '../../lobbyd/userPoints';
import {
  IPlatformBillingApiClient,
  LGBillingCode,
  LGBillingResponseBody
} from './iPlatformBillingApiClient';
import Container from 'typedi';
import { LobbyService } from '../../lobbyd/server';
import puUserLoadCash_CN from '../../mysqllib/sp/puUserLoadCash_cn';
import puUserConsumeCash_CN from '../../mysqllib/sp/puUserConsumeCash_cn';
import puUserAddCash_CN from '../../mysqllib/sp/puUserAddCash_cn';

export class SdoBillingApiClient implements IPlatformBillingApiClient {
  /**
   * 유상/무상 구분하여 잔액 조회
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=19597717
   */
  async queryCash(userId: number, appStoreCd: string, countryCreated: string): Promise<any> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    const result = await puUserLoadCash_CN(userDbPool, userId);
    const paidRedGemBalance = result.paidRedGemBalance;
    const freeRedGemBalance = result.freeRedGemBalance;
    return [
      {
        paymentType: 'PAID',
        coinCd: 'red_gem',
        balance: paidRedGemBalance,
      },
      {
        paymentType: 'FREE',
        coinCd: 'red_gem',
        balance: freeRedGemBalance,
      },
    ];
  }

  async queryCashPair(userId: number, appStoreCd: string, countryCreated: string): Promise<{
    paidRedGemBalance: number;
    freeRedGemBalance: number;
  }> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    const result = await puUserLoadCash_CN(userDbPool, userId);
    return {
      paidRedGemBalance: result.paidRedGemBalance,
      freeRedGemBalance: result.freeRedGemBalance,
    };
  }

  /**
   * Cash(레드젬, 마일리지) 추가. 단, 무료 재화만 가능. 유료 재화는 빌링을 통해서만 가능하다.
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276168
   */
  async addCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    reason: string
  ): Promise<any> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    await puUserAddCash_CN(userDbPool, userId, pointCmsId, amount);
    return await this.queryCash(userId, appStoreCd, countryCreated);
  }

  /**
   * Cash(레드젬, 마일리지) 소모.
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276291 or
   * @see https://developer.line.games/pages/viewpage.action?pageId=19598027
   */
  async consumeCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    lgCashParam: LGCashParam,
  ): Promise<any> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    const result = await puUserConsumeCash_CN(userDbPool, userId, pointCmsId, amount);
    const response = [
      {
        paymentType: 'PAID',
        coinCd: 'red_gem',
        balance: result.paidRedGemBalance,
      },
      {
        paymentType: 'FREE',
        coinCd: 'red_gem',
        balance: result.freeRedGemBalance,
      },
    ]
    return response;
  }

  /**
   * 판매중인 상품 목록 조회
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275461
   */
  async querySalesList(appStoreCd: string): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'request success',
    }
  }

  /**
   * 상품 구매시 지급할 아이템의 상세정보 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276147
   */
  async queryProductGiveItemDetail(
    appStoreCd: string,
    productId: string
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 유저의 특정 결제 데이터 단건 조회(결제 상태 조건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43418520
   */
  async queryExistPurchaseForStatus(
    userId: string,
    status: LGBillingCode.PurchaseStatus
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 결제 예약/완료 구매건의 상세정보 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849461
   */
  async queryPurchaseDetail(orderId: string): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 상품보관함 목록 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419619
   */
  async queryInventoryPurchaseList(userId: string): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
      data: [],
    }
  }

  /**
   * 상품보관함 상세 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419623
   */
  async queryInventoryPurchase(userId: string, invenId: string): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매 예약 상태로 남아있는 최근 데이터 조회(상품ID 조건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=40042541
   */
  async queryLatestReservedPurchaseByProductId(
    appStoreCd: string,
    productId: string,
    userId: string
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매요청 예약 작업
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275505
   */
  async reservePurchase(
    userId: string,
    userIp: string,
    productId: string,
    gnid: string,
    appStoreCd: string,
    price: number,
    currency: string,
    appVersion: string,
    os: string,
    countryCreated: string,
    microPrice: number
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매 예약 상태를 '구매예약 취소' 상태로 변경
   * @see https://developer.line.games/pages/viewpage.action?pageId=40042519
   */
  async cancelReservedPurchase(userId: string, orderId: string): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  private static COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES = {
    [LGBillingCode.APP_STORE_CD.GOOGLE_PLAY]: true,
    [LGBillingCode.APP_STORE_CD.APPLE_APP_STORE]: true,
    [LGBillingCode.APP_STORE_CD.FLOOR_STORE]: true,
    [LGBillingCode.APP_STORE_CD.STEAM]: true,
  } as const;

  /**
   * 스토어에 따른 '구매건의 완료 처리'가 구현되었는지
   */
  static isCompleteReservedPurchaseImplemented(
    storeCode: string
  ): storeCode is keyof typeof SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES {
    return SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES[storeCode]
      ? true
      : false;
  }

  /**
   * [구글] play store 구매건의 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849377
   * @see {@link CompleteReservedPurchaseGoogleReqBody}
   */
  async completeReservedPurchaseGoogle(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    aosPackageNm: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = '',

    googleSignature: string
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [애플] app store 구매건의 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849379
   * @see {@link CompleteReservedPurchaseAppleReqBody}
   */
  async completeReservedPurchaseApple(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = ''
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Floor] Floor 스토어 구매 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419517
   * @see {@link CompleteReservedPurchaseFloorReqBody}
   */
  async completeReservedPurchaseFloor(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = '',

    signature: string
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Steam] 구매건 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=27526099
   * @see {@link CompleteReservedPurchaseSteamReqBody}
   */
  async completeReservedPurchaseSteam(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = ''
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Steam] 상품구입 초기화
   * @see https://developer.line.games/pages/viewpage.action?pageId=27525181
   */
  async steamPurchaseInitTxn(
    billingOrderId: number,
    steamId: BigInt,
    steamAppId: number,
    steamLanguage: string,
    steamCurrency: string,
    steamItemInfos: {
      steamItemId: number;
      steamQty: number;
      steamAmount: number;
      steamDescription: string;
    }[]
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * @deprecated
   * 빌링 보관함을 사용하게 되면서, 빌링 상품 구매시 보관함에 기록되는데
   * 상품을 지급하면 통보를 해서 수령 완료 처리(보관함에서 제거)를 해야함.
   * 이 API 를 사용해서 코인만 충전하고 보관함에서 제거가 되지 않을 여지가 있고, 다른 프로젝트에서 관련 사례가 있었다고함.
   * 코인 충전과 동시에 보관함에서 제거할수 있는 API( {@link chargeCoinWithInven} )를 이용하도록 강제함.
   *
   * 상품 구입에 의한 유상 코인 충전
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275510
   */
  chargeByPurchaseProduct(): never {
    const url = 'api/v1/purchase/product/coin/charge/byPurchaseProduct';
    assert.fail(`[${url}] deprecated`);
  }

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43423046
   * @param invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
   */
  async chargeCoinWithInven(
    userId: string,
    orderId: string,
    gnid: string,
    countryCreated: string,
    invenDelYn: 'Y' | 'N' = 'Y',
    invenMemo: string,
    coinChargeList: {
      coinCd: string;
      chargeTypeCd: string;
      chargeAmt: number;
    }[]
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)(복수건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43424654
   * @param orderList.invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
   */
  async chargeCoinWithInvenList(
    userId: string,
    gnid: string,
    countryCreated: string,
    orderList: {
      orderId: string;
      invenDelYn: 'Y' | 'N';
      invenMemo: string;
      coinChargeList: {
        coinCd: string;
        chargeTypeCd: string;
        chargeAmt: number;
      }[];
    }[]
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(단건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419621
   */
  async completeReceiveInvenPurchase(
    userId: string,
    invenId: string,
    memo: string
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(복수건) JSON
   * @see https://developer.line.games/pages/viewpage.action?pageId=43424705
   */
  async completeReceiveInvenPurchaseBulk(
    userId: string,
    invens: {
      invenId: string;
      memo: string;
    }[]
  ): Promise<LGBillingResponseBody> {
    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  async tryBidding(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    amount: number,
    reason: string
  ) {
    // TODO
  }

  async bidResult(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    bWinner: boolean,
    reason: string
  ) {
    // TODO
  }

  async sendToSlack(message: string) {
  }
}
