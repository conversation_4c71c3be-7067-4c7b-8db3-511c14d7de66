// TODO: linesdk를 통해서 잔액을 조회하고 있다. 이 부분을 사용할수 없으므로, database에 있는 포인트를 조회하는 형태로 변경해줘야한다.

// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import os from 'os';
import assert from 'assert';

import mconf from '../mconf';
import mlog from '../mlog';
import * as mutil from '../mutil';
import { BaseApiClient } from './baseApiClient';
import { MError, MErrorCode } from '../merror';
import { CreateSlackNotifier, type ISlackNotifier } from '../slackNotifier';
import { RedGemPointCmsId } from '../../cms/ex';
import { isCash } from '../../cms/pointDesc';
import type { LGCashParam } from '../../lobbyd/userPoints';
import Container from 'typedi';
import { LobbyService } from '../../lobbyd/server';
import puUserLoadCash from '../../mysqllib/sp/puUserLoadCash';
import puUserConsumeCash from '../../mysqllib/sp/puUserConsumeCash';
import { DbConnPoolManager } from '../../mysqllib/pool';
import puUserAddCash from '../../mysqllib/sp/puUserAddCash';

/**
 * 명시적인 관련 링크 있으면 추가하면 좋을 듯..
 * ? https://developer.line.games/pages/viewpage.action?pageId=35849324
 */
export interface LGBillingResponseBody {
  success: boolean;
  msg: string;
  errorCd?: string;
  data?: unknown;
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275618
 */
export enum LGBillingErrorCode {
  SYSTEM_ERROR,
  SYSTEM_MAINTENANCE,
  INVALID_PARAMETER,
  NOT_ALLOW_AUTH,
  NOT_EXIST_DATA,
  EXPIRE_AUTH_TOKEN,
  EXIST_DUPL_RECEIPT,
  ALREADY_COMPLETE_RECEIPT,
  NOT_EXIST_RECEIPT,

  //
  UNKNOWN,
}

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=7275557
 */
export namespace LGBillingCode {
  export enum PurchaseStatus {
    /** 구매 예약 중 */
    RESERVE,
    RESERVED_CANCEL,
    COMPLETE,
    CANCEL,
    CALL_BACK_ING,
    ABUSE_BLOCK,
    CANCEL_ING,
  }

  export const APP_STORE_CD = {
    GOOGLE_PLAY: 'GOOGLE_PLAY',
    APPLE_APP_STORE: 'APPLE_APP_STORE',
    FLOOR_STORE: 'FLOOR_STORE',
    STEAM: 'STEAM',
  } as const;
}

/**
 * userId 종류를 구분
 */
type GAME_USER_ID_TYPE =
  /* platform 에서 발급한 nid가 입력될 경우 */
  | 'NID'
  /* platform 에서 발급한 gnid가 입력될 경우 */
  | 'GNID'
  /* 게임에서 발급/관리하는 id(고유값)이 입력될 경우 */
  | 'GAME_USER_ID';

/**
 * @see https://developer.line.games/pages/viewpage.action?pageId=27528237
 */
interface CompleteReservedPurchaseReqBody {
  orderId: string;
  svcCd: string;
  userId: string;
  gameUserIdType: GAME_USER_ID_TYPE;
  appStoreCd: string;
  receipt: string;
  /**
   * 영수증검증 무시할지 여부.
   * 171024기준으로 개발 및 QA 환경에서만 작동하는 개발자 편의기능
   */
  ignoreReceiptYn?: 'Y' | 'N' | '';
  price: number; // double
  microPrice: number; // long
  currency: string;
  /**
   * 결제 복구 같은 특수 상황에서만 기입
   */
  memo?: string;
  /**
   * IP 기반의 국가.
   * 빌링 서버에서 default 설정이 있다고 함.
   * 없는 경우(개발 환경)는 null 이 아닌 빈 문자열로 보내야 하는 것 참고
   */
  cfViewerCountry: string | '';
  /**
   * gnid 제재 정보에 사용
   * gnid 란? https://developer.line.games/pages/viewpage.action?pageId=7275349
   */
  gnid: string;
  /**
   * 게임 서버 아이디(상품보관함의 데이터, 영수증 검증에 사용)
   */
  serverId: string;

  adjustIdfa?: string;
  adjustIdfv?: string;
  adjustDeviceId?: string;
  adjustAndroidId?: string;
  adjustGpsAdid?: string;
}

interface CompleteReservedPurchaseGoogleReqBody extends CompleteReservedPurchaseReqBody {
  /** 동일 게임코드인데 AOS에서 패키지명이 다른경우 분기처리 필요할 경우(사전 협의 필요) */
  aosPackageNm?: string;
  googleSignature: string;
}

interface CompleteReservedPurchaseAppleReqBody extends CompleteReservedPurchaseReqBody {
  //
}

interface CompleteReservedPurchaseFloorReqBody extends CompleteReservedPurchaseReqBody {
  signature: string;
}

interface CompleteReservedPurchaseSteamReqBody extends CompleteReservedPurchaseReqBody {
  //
}

/**
 * 3-1-3. 예약된 구매건의 완료 처리
 * ( https://developer.line.games/pages/viewpage.action?pageId=7275508 )
 * 응답 데이터의 giveItemList 원소
 * @see https://developer.line.games/pages/viewpage.action?pageId=35849324
 */
export interface GiveItem {
  /** 아이템 종류 */
  productItemType: string;
  itemCd: string;
  /** 코인충전 형태 */
  coinChargeTypeCd: string;
  /** 빌링에서 관리하는 재화 여부 */
  coinManageBalanceYn: 'Y' | 'N';
  amount: number;
}

/**
 * @see {@link GiveItem}
 */
function isGiveItem(x: any): x is GiveItem {
  if (
    x &&
    typeof x === 'object' &&
    typeof x.productItemType === 'string' &&
    typeof x.itemCd === 'string' &&
    (!x.coinChargeTypeCd || typeof x.coinChargeTypeCd === 'string') &&
    typeof x.amount === 'number' &&
    (x.coinManageBalanceYn === 'Y' || x.coinManageBalanceYn === 'N')
  ) {
    return true;
  }
  return false;
}

/**
 * @see {@link GiveItem}
 */
export function ensureGiveItems(x: any): asserts x is GiveItem[] {
  if (!Array.isArray(x)) {
    throw new MError('array expected', MErrorCode.INTERNAL_ERROR, { x });
  }
  for (const elem of x) {
    if (!isGiveItem(elem)) {
      throw new MError('invalid property', MErrorCode.INTERNAL_ERROR, { x });
    }
  }
}

export class SdoBillingApiClient extends BaseApiClient {
  private _authToken: string = null;
  private _bIsTokenRequested = false;
  private _authPwd: string;
  private _slackNotifier?: ISlackNotifier;

  constructor() {
    super();
  }

  init(baseUrl: string, timeout: number) {
    super.init(baseUrl, timeout);
  }

  setAuthPassword(passworld: string) {
    this._authPwd = passworld;
  }

  /**
   * 유상/무상 구분하여 잔액 조회
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=19597717
   */
  async queryCash(userId: number, appStoreCd: string, countryCreated: string, dbConnPoolManager?: DbConnPoolManager): Promise<any> {
    //   userId,
    //   appStoreCd,
    //   countryCreated,
    // };

    // const response = await this.requestAndUnwrapDataOrThrow('api/v1/coin/balance/get/byCountryCreated', body);
    // return response;

    if (dbConnPoolManager) {
      const userDbPool = dbConnPoolManager.getPoolByUserId(userId);
      const result = await puUserLoadCash(userDbPool, userId);

      return [
        {
          paymentType: 'PAID',
          coinCd: "red_gem",
          balance: result.paidRedGemBalance,
        },
        {
          paymentType: 'FREE',
          coinCd: "red_gem",
          balance: result.freeRedGemBalance,
        },
      ];
    }

    return [
      {
        paymentType: 'PAID',
        coinCd: 'red_gem',
        balance: 0,
      },
      {
        paymentType: 'FREE',
        coinCd: 'red_gem',
        balance: 0,
      },
    ];
  }

  async queryCash2(userId: number, appStoreCd: string, countryCreated: string): Promise<any> {
    // const body = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   appStoreCd,
    //   countryCreated,
    // };

    // const response = await this.requestAndUnwrapDataOrThrow('api/v1/coin/balance/get/byCountryCreated', body);
    // return response;

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);

    const result = await puUserLoadCash(userDbPool, userId);
    return {
      paidRedGemBalance: result.paidRedGemBalance,
      freeRedGemBalance: result.freeRedGemBalance,
    };
  }

  /**
   * Cash(레드젬, 마일리지) 추가. 단, 무료 재화만 가능. 유료 재화는 빌링을 통해서만 가능하다.
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276168
   */
  async addCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    reason: string
  ): Promise<any> {
    // assert(isCash(pointCmsId));

    // const body = {
    //   txId: `${userId}_${new Date().getTime()}_${Math.random()}`,
    //   userId,
    //   coinCd: pointCmsId === RedGemPointCmsId ? 'red_gem' : 'mileage',
    //   svcCd: mconf.LineGameCode,
    //   chargeTypeCd: 'FREE_SVC',
    //   appStoreCd,
    //   chargeAmt: amount,
    //   reason,
    //   memo: '',
    //   gnid,
    //   serverId: mconf.worldId,
    //   countryCreated,
    // };

    // mlog.info('requesting api/v1/coin/balance/charge/financeFree to line games...', body);

    // return this.requestAndUnwrapDataOrThrow('api/v1/coin/balance/charge/financeFree', body);

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    await puUserAddCash(userDbPool, userId, pointCmsId, amount);

    return await this.queryCash(userId, appStoreCd, countryCreated, userDbConnPoolMgr);
  }

  /**
   * Cash(레드젬, 마일리지) 소모.
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276291 or
   * @see https://developer.line.games/pages/viewpage.action?pageId=19598027
   */
  async consumeCash(
    userId: number,
    gnid: string,
    appStoreCd: string,
    countryCreated: string,
    pointCmsId: number,
    amount: number,
    lgCashParam: LGCashParam,
  ): Promise<any> {
    // assert(isCash(pointCmsId));
    // assert(lgCashParam);

    // // TODO: 유료재화가 있으면 유료 재화를 먼저 소모해줘야함.

    // if (lgCashParam.itemId) {
    //   const body = {
    //     txId: `${userId}_${new Date().getTime()}_${Math.random()}`,
    //     userId,
    //     coinCd: pointCmsId === RedGemPointCmsId ? 'red_gem' : 'mileage',
    //     svcCd: mconf.LineGameCode,
    //     appStoreCd,
    //     useAmt: amount,
    //     itemId: lgCashParam.itemId,
    //     memo: '',
    //     gnid,
    //     serverId: mconf.worldId,
    //     countryCreated,
    //   };

    //   mlog.info('requesting api/v1/coin/balance/use/toItem to line games...', body);

    //   return this.requestAndUnwrapDataOrThrow('api/v1/coin/balance/use/toItem', body);
    // } else {
    //   const body = {
    //     txId: `${userId}_${new Date().getTime()}_${Math.random()}`,
    //     userId,
    //     svcCd: mconf.LineGameCode,
    //     appStoreCd,
    //     productId: lgCashParam.productId,
    //     gnid,
    //     serverId: mconf.worldId,
    //     countryCreated,
    //     buyCount: lgCashParam.buyCount > 1 ? lgCashParam.buyCount : undefined,
    //   };

    //   const requestUrl =
    //     body.buyCount > 1
    //       ? 'api/v1/purchase/coinExchangeProduct/bulk/toBuy'
    //       : 'api/v1/purchase/coinExchangeProduct/toBuy';

    //   mlog.info('requesting ' + requestUrl + ' to line games...', body);

    //   return this.requestAndUnwrapDataOrThrow(requestUrl, body);
    // }

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    const result = await puUserConsumeCash(userDbPool, userId, pointCmsId, amount);

    const response = [
      {
        paymentType: 'PAID',
        coinCd: 'red_gem',
        balance: result.paidRedGemBalance,
      },
      {
        paymentType: 'FREE',
        coinCd: 'red_gem',
        balance: result.freeRedGemBalance,
      },
    ]
    return response;
  }

  /**
   * 판매중인 상품 목록 조회
   * 
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275461
   */
  async querySalesList(appStoreCd: string): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   appStoreCd: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   appStoreCd,
    // };

    // const url = 'api/v1/purchase/product/sales/list';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'request success',
    }
  }

  /**
   * 상품 구매시 지급할 아이템의 상세정보 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=7276147
   */
  async queryProductGiveItemDetail(
    appStoreCd: string,
    productId: string
  ): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   appStoreCd: string;
    //   productId: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   appStoreCd,
    //   productId,
    // };

    // const url = 'api/v1/product/giveItemDetail';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 유저의 특정 결제 데이터 단건 조회(결제 상태 조건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43418520
   */
  async queryExistPurchaseForStatus(
    userId: string,
    status: LGBillingCode.PurchaseStatus
  ): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   userId: string;
    //   status: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   status: LGBillingCode.PurchaseStatus[status],
    // };

    // const url = 'api/v1/purchase/info/status/exist/byUserId';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 결제 예약/완료 구매건의 상세정보 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849461
   */
  async queryPurchaseDetail(orderId: string): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   orderId: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   orderId,
    // };

    // const url = 'api/v1/purchase/detail';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 상품보관함 목록 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419619
   */
  async queryInventoryPurchaseList(userId: string): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   userId: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    // };

    // const url = 'api/v1/purchase/inventory/getList';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(
    //   url,
    //   body,
    //   undefined,
    //   undefined,
    //   LineGamesBillingApiClient.SLACK_NOTIFY_FILTER_NOT_EXIST_DATA
    // ).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
      data: [],
    }
  }

  /**
   * 상품보관함 상세 조회
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419623
   */
  async queryInventoryPurchase(userId: string, invenId: string): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   userId: string;
    //   invenId: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   invenId,
    // };

    // const url = 'api/v1/purchase/inventory/get';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매 예약 상태로 남아있는 최근 데이터 조회(상품ID 조건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=40042541
   */
  async queryLatestReservedPurchaseByProductId(
    appStoreCd: string,
    productId: string,
    userId: string
  ): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   productId: string;
    //   appStoreCd: string;
    //   userId: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   appStoreCd,
    //   productId,
    //   userId,
    // };

    // const url = 'api/v1/purchase/info/reserved/latest/byProductId';

    // mlog.verbose(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.verbose(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매요청 예약 작업
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275505
   */
  async reservePurchase(
    userId: string,
    userIp: string,
    productId: string,
    gnid: string,
    appStoreCd: string,
    price: number,
    currency: string,
    appVersion: string,
    os: string,
    countryCreated: string,
    microPrice: number
  ): Promise<LGBillingResponseBody> {
    // interface RequestBody {
    //   txId: string;
    //   svcCd: string;
    //   userId: string;
    //   userIp: string; // 별도의 형식은 없고, port 없이 ip 만 있으면 된다고함.
    //   productId: string; // 클라에서 보내주는 값
    //   appStoreCd: string;
    //   price: number; // double, 클라에서 보내주는 값
    //   currency: string; // 클라에서 보내주는 값
    //   appVersion: string; // 게임 로그의 v 라고 함, 개발 버전인 경우 간략하게 지정해서 보내면 된다고 함.
    //   os: string; // 게임 로그의 os 라고 함
    //   location: string; // countryCreated와 동일 필드, 하위 호환성을위해서 location 사용
    //   gnid: string;
    //   serverId: string;
    //   microPrice: number; // long 클라에서 보내주는 값
    //   gameUserIdType: GAME_USER_ID_TYPE;
    // }

    // const body: RequestBody = {
    //   txId: LineGamesBillingApiClient.makeTransactionId(userId),
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   userIp,
    //   productId,
    //   appStoreCd,
    //   price,
    //   currency,
    //   appVersion,
    //   os,
    //   location: countryCreated,
    //   gnid,
    //   serverId: mconf.worldId,
    //   microPrice,
    //   gameUserIdType: 'GAME_USER_ID',
    // };

    // const url = 'api/v1/purchase/product/reserve';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 구매 예약 상태를 '구매예약 취소' 상태로 변경
   * @see https://developer.line.games/pages/viewpage.action?pageId=40042519
   */
  async cancelReservedPurchase(userId: string, orderId: string): Promise<LGBillingResponseBody> {
    // const body: {
    //   orderId: string;
    //   svcCd: string;
    //   userId: string;
    // } = {
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   userId,
    // };

    // const url = 'api/v1/purchase/product/reserved/cancel';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  private static COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES = {
    [LGBillingCode.APP_STORE_CD.GOOGLE_PLAY]: true,
    [LGBillingCode.APP_STORE_CD.APPLE_APP_STORE]: true,
    [LGBillingCode.APP_STORE_CD.FLOOR_STORE]: true,
    [LGBillingCode.APP_STORE_CD.STEAM]: true,
  } as const;

  /**
   * 스토어에 따른 '구매건의 완료 처리'가 구현되었는지
   */
  static isCompleteReservedPurchaseImplemented(
    storeCode: string
  ): storeCode is keyof typeof SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES {
    return SdoBillingApiClient.COMPLETE_RESERVED_PURCHASE_IMPLEMENTED_STORE_CODES[storeCode]
      ? true
      : false;
  }

  /**
   * [구글] play store 구매건의 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849377
   * @see {@link CompleteReservedPurchaseGoogleReqBody}
   */
  async completeReservedPurchaseGoogle(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    aosPackageNm: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = '',

    googleSignature: string
  ): Promise<LGBillingResponseBody> {
    // const body: CompleteReservedPurchaseGoogleReqBody = {
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   appStoreCd,
    //   receipt,
    //   ignoreReceiptYn,
    //   price,
    //   microPrice,
    //   currency,
    //   memo,
    //   cfViewerCountry,
    //   gnid,
    //   serverId: mconf.worldId,
    //   adjustIdfa,
    //   adjustIdfv,
    //   adjustDeviceId,
    //   adjustAndroidId,
    //   adjustGpsAdid,

    //   aosPackageNm,
    //   googleSignature,
    // };

    // // 스토어에서 영수증 검증시 응답이 느려서 타임아웃을 20초 정도로 길게 설정해야 한다고 함.
    // const timeoutSec = 20;

    // const url = 'api/v1/purchase/product/google/reserve/complete';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, timeoutSec * 1000).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [애플] app store 구매건의 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=35849379
   * @see {@link CompleteReservedPurchaseAppleReqBody}
   */
  async completeReservedPurchaseApple(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = ''
  ): Promise<LGBillingResponseBody> {
    // const body: CompleteReservedPurchaseAppleReqBody = {
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   appStoreCd,
    //   receipt,
    //   ignoreReceiptYn,
    //   price,
    //   microPrice,
    //   currency,
    //   memo,
    //   cfViewerCountry,
    //   gnid,
    //   serverId: mconf.worldId,
    //   adjustIdfa,
    //   adjustIdfv,
    //   adjustDeviceId,
    //   adjustAndroidId,
    //   adjustGpsAdid,
    // };

    // // 스토어에서 영수증 검증시 응답이 느려서 타임아웃을 20초 정도로 길게 설정해야 한다고 함.
    // const timeoutSec = 20;

    // const url = 'api/v1/purchase/product/apple/reserve/complete';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, timeoutSec * 1000).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Floor] Floor 스토어 구매 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419517
   * @see {@link CompleteReservedPurchaseFloorReqBody}
   */
  async completeReservedPurchaseFloor(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = '',

    signature: string
  ): Promise<LGBillingResponseBody> {
    // const body: CompleteReservedPurchaseFloorReqBody = {
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   appStoreCd,
    //   receipt,
    //   ignoreReceiptYn,
    //   price,
    //   microPrice,
    //   currency,
    //   memo,
    //   cfViewerCountry,
    //   gnid,
    //   serverId: mconf.worldId,
    //   adjustIdfa,
    //   adjustIdfv,
    //   adjustDeviceId,
    //   adjustAndroidId,
    //   adjustGpsAdid,

    //   signature,
    // };

    // // 스토어에서 영수증 검증시 응답이 느려서 타임아웃을 20초 정도로 길게 설정해야 한다고 함.
    // const timeoutSec = 20;

    // const url = 'api/v1/purchase/product/floor/reserve/complete';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, timeoutSec * 1000).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Steam] 구매건 완료 처리
   * @see https://developer.line.games/pages/viewpage.action?pageId=27526099
   * @see {@link CompleteReservedPurchaseSteamReqBody}
   */
  async completeReservedPurchaseSteam(
    gnid: string,
    userId: string,
    appStoreCd: string,
    cfViewerCountry: string,

    adjustIdfa: string = '',
    adjustIdfv: string = '',
    adjustDeviceId: string = '',
    adjustAndroidId: string = '',
    adjustGpsAdid: string = '',

    orderId: string,
    receipt: string,
    ignoreReceiptYn: 'Y' | 'N' | '' = '',
    price: number,
    microPrice: number,
    currency: string,
    memo: string = ''
  ): Promise<LGBillingResponseBody> {
    // const body: CompleteReservedPurchaseSteamReqBody = {
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   appStoreCd,
    //   receipt,
    //   ignoreReceiptYn,
    //   price,
    //   microPrice,
    //   currency,
    //   memo,
    //   cfViewerCountry,
    //   gnid,
    //   serverId: mconf.worldId,
    //   adjustIdfa,
    //   adjustIdfv,
    //   adjustDeviceId,
    //   adjustAndroidId,
    //   adjustGpsAdid,
    // };

    // // 스토어에서 영수증 검증시 응답이 느려서 타임아웃을 20초 정도로 길게 설정해야 한다고 함.
    // const timeoutSec = 20;

    // const url = 'api/v1/purchase/product/steam/reserve/complete';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, timeoutSec * 1000).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * [Steam] 상품구입 초기화
   * @see https://developer.line.games/pages/viewpage.action?pageId=27525181
   */
  async steamPurchaseInitTxn(
    billingOrderId: number,
    steamId: BigInt,
    steamAppId: number,
    steamLanguage: string,
    steamCurrency: string,
    steamItemInfos: {
      steamItemId: number;
      steamQty: number;
      steamAmount: number;
      steamDescription: string;
    }[]
  ): Promise<LGBillingResponseBody> {
    // const body = {
    //   svcCd: mconf.LineGameCode,
    //   billingOrderId,
    //   steamId: steamId.toString(),
    //   steamAppId,
    //   steamLanguage,
    //   steamCurrency,
    //   steamItemInfos,
    // } as const;

    // const url = 'api/v1/purchase/product/steam/initTxn';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, undefined, 'json').then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * @deprecated
   * 빌링 보관함을 사용하게 되면서, 빌링 상품 구매시 보관함에 기록되는데
   * 상품을 지급하면 통보를 해서 수령 완료 처리(보관함에서 제거)를 해야함.
   * 이 API 를 사용해서 코인만 충전하고 보관함에서 제거가 되지 않을 여지가 있고, 다른 프로젝트에서 관련 사례가 있었다고함.
   * 코인 충전과 동시에 보관함에서 제거할수 있는 API( {@link chargeCoinWithInven} )를 이용하도록 강제함.
   *
   * 상품 구입에 의한 유상 코인 충전
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275510
   */
  chargeByPurchaseProduct(): never {
    const url = 'api/v1/purchase/product/coin/charge/byPurchaseProduct';
    assert.fail(`[${url}] deprecated`);
  }

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43423046
   * @param invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
   */
  async chargeCoinWithInven(
    userId: string,
    orderId: string,
    gnid: string,
    countryCreated: string,
    invenDelYn: 'Y' | 'N' = 'Y',
    invenMemo: string,
    coinChargeList: {
      coinCd: string;
      chargeTypeCd: string;
      chargeAmt: number;
    }[]
  ): Promise<LGBillingResponseBody> {
    // const body: {
    //   txId: string;
    //   userId: string;
    //   gameUserIdType: GAME_USER_ID_TYPE;
    //   orderId: string;
    //   svcCd: string;
    //   gnid: string;
    //   serverId: string;
    //   countryCreated: string;
    //   invenDelYn: string;
    //   invenMemo: string;
    //   coinChargeList: unknown[];
    // } = {
    //   txId: LineGamesBillingApiClient.makeTransactionId(userId),
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   orderId,
    //   svcCd: mconf.LineGameCode,
    //   gnid,
    //   serverId: mconf.worldId,
    //   countryCreated,
    //   invenDelYn,
    //   invenMemo,
    //   coinChargeList,
    // };

    // const url = 'api/v1/purchase/product/coin/charge/with/inven';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, undefined, 'json').then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 상품 구입에 의한 유상 코인 충전(인벤토리사용시)(복수건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43424654
   * @param orderList.invenDelYn 코인 충전과 함께 상품보관함 수령 완료 처리 여부.
   */
  async chargeCoinWithInvenList(
    userId: string,
    gnid: string,
    countryCreated: string,
    orderList: {
      orderId: string;
      invenDelYn: 'Y' | 'N';
      invenMemo: string;
      coinChargeList: {
        coinCd: string;
        chargeTypeCd: string;
        chargeAmt: number;
      }[];
    }[]
  ): Promise<LGBillingResponseBody> {
    // assert(Array.isArray(orderList));
    // assert(orderList.length > 0);
    // // 최대 건수는 API 페이지 참고.

    // const txId = LineGamesBillingApiClient.makeTransactionId(userId);

    // const body: {
    //   userId: string;
    //   gameUserIdType: GAME_USER_ID_TYPE;
    //   svcCd: string;
    //   gnid: string;
    //   serverId: string;
    //   countryCreated: string;
    //   orderList: {
    //     txId: string;
    //     orderId: string;
    //     invenDelYn: 'Y' | 'N';
    //     invenMemo: string;
    //     coinChargeList: {
    //       coinCd: string;
    //       chargeTypeCd: string;
    //       chargeAmt: number;
    //     }[];
    //   }[];
    // } = {
    //   userId,
    //   gameUserIdType: 'GAME_USER_ID',
    //   svcCd: mconf.LineGameCode,
    //   gnid,
    //   serverId: mconf.worldId,
    //   countryCreated,
    //   orderList: orderList.map((elem, index) => {
    //     return {
    //       ...elem,
    //       txId: `${txId}_${index}`,
    //     };
    //   }),
    // };

    // const url = 'api/v1/purchase/product/coin/charge/with/invenList';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body, undefined, 'json').then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(단건)
   * @see https://developer.line.games/pages/viewpage.action?pageId=43419621
   */
  async completeReceiveInvenPurchase(
    userId: string,
    invenId: string,
    memo: string
  ): Promise<LGBillingResponseBody> {
    // const body: {
    //   svcCd: string;
    //   userId: string;
    //   invenId: string;
    //   memo: string;
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   invenId,
    //   memo,
    // };

    // const url = 'api/v1/purchase/inventory/receive/complete';

    // mlog.info(`requesting to line games. [${url}]`, body);
    // return this.request(url, body).then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  /**
   * 게임서버에서 구매 상품에 대한 보상 지급 후 완료 통보 API(복수건) JSON
   * @see https://developer.line.games/pages/viewpage.action?pageId=43424705
   */
  async completeReceiveInvenPurchaseBulk(
    userId: string,
    invens: {
      invenId: string;
      memo: string;
    }[]
  ): Promise<LGBillingResponseBody> {
    // assert(Array.isArray(invens));
    // assert(invens.length > 0);
    // // 최대 건수는 API 페이지 참고.

    // const body: {
    //   svcCd: string;
    //   userId: string;
    //   invens: {
    //     invenId: string;
    //     memo: string;
    //   }[];
    // } = {
    //   svcCd: mconf.LineGameCode,
    //   userId,
    //   invens,
    // };

    // const url = 'api/v1/purchase/inventory/receive/complete/bulk/json';

    // mlog.info(`requesting to line games. [${url}] `, body);
    // return this.request(url, body, undefined, 'json').then((resp) => {
    //   mlog.info(`received response. [${url}]`, resp);

    //   // resp 의 data 는 사용하는 곳에서 검증 필요..
    //   return resp;
    // });

    // TODO
    return {
      success: true,
      msg: 'success',
    }
  }

  private static makeTransactionId(userId: number | string) {
    return `${userId}_${new Date().getTime()}_${Math.random()}`;
  }

  private static SLACK_NOTIFY_FILTER_NOT_EXIST_DATA = {
    [LGBillingErrorCode[LGBillingErrorCode.NOT_EXIST_DATA]]: true,
  } as const;

  protected async request(
    url: string,
    body?: any,
    timeoutMs?: number,
    contentType: 'x-www-form-urlencoded' | 'json' = 'x-www-form-urlencoded',
    slackNotifyFilter?: { [errorCd: string]: true }
  ): Promise<LGBillingResponseBody> {
    return Promise.resolve()
      .then(() => {
        if (!this._authToken) {
          return this.refreshAuthToken();
        }
        return null;
      })
      .then(() => {
        // MRestClient 에서 지원하도록 하는게 좋을 듯 싶음? 일단..
        switch (contentType) {
          case 'json':
            // By default, axios serializes JavaScript objects to JSON
            return this.mrest.post<LGBillingResponseBody>(url, body, {
              headers: {
                authToken: this._authToken,
                svcCd: mconf.LineGameCode,
                'Content-type': 'application/json',
                // TODO? charset=UTF-8 로 명시적으로 할 필요 있을 지 확인.
              },
              timeout: timeoutMs,
            });
          case 'x-www-form-urlencoded':
          default:
            return this.mrest.postForm<LGBillingResponseBody>(url, body, {
              headers: {
                authToken: this._authToken,
                svcCd: mconf.LineGameCode,
              },
              timeout: timeoutMs,
            });
        }
      })
      .then((axiosResp) => {
        const result: LGBillingResponseBody = axiosResp
          ? axiosResp.data
          : {
              success: false,
              data: null,
              errorCd: LGBillingErrorCode[LGBillingErrorCode.UNKNOWN],
              msg: 'null',
            };

        if (result.success === true) {
          return result;
        } else {
          if (result.errorCd === LGBillingErrorCode[LGBillingErrorCode.EXPIRE_AUTH_TOKEN]) {
            this._authToken = null;
            return this.refreshAuthToken().then(() => {
              return this.request(url, body, timeoutMs, contentType, slackNotifyFilter);
            });
          }
          if (!slackNotifyFilter?.[result.errorCd]) {
            this.sendToSlack(
              `[NTSDK BILLING API] response error, url: ${url}, body: ${JSON.stringify(
                body
              )}, result: ${JSON.stringify(result)}`
            );
          }
          return result;
        }
      })
      .catch((err) => {
        this.rethrow(url, body, err);
      });
  }

  /**
   * 빌링 API 에서 success 가 true 인 경우 data 반환, 아닌 경우 throw. 편의 함수
   */
  protected async requestAndUnwrapDataOrThrow(
    url: string,
    body?: any,
    timeoutMs?: number,
    ignoreErrorCode?: string,
    contentType: 'x-www-form-urlencoded' | 'json' = 'x-www-form-urlencoded'
  ): Promise<any> {
    return this.request(url, body, timeoutMs, contentType).then((res) => {
      if (res.success === true) {
        return res.data;
      }

      if (ignoreErrorCode === res.errorCd) {
        return null;
      }

      this.rethrow(
        url,
        body,
        new MError(`"${res.errorCd}", ${res.msg}`, MErrorCode.LG_BILLING_ERROR, res)
      );
    });
  }

  async tryBidding(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    amount: number,
    reason: string
  ) {
    // const body = {
    //   auctionId: blindCmsId.toString(),
    //   svcCd: mconf.LineGameCode,
    //   txId: `${userId}_${new Date().getTime()}_${Math.random()}`,
    //   userId,
    //   coinCd: 'red_gem',
    //   appStoreCd,
    //   useAmt: amount,
    //   memo: 'BLIND_BID:' + blindCmsId,
    //   reason,
    //   gnid,
    //   serverId: mconf.worldId,
    //   countryCreated,
    // };

    // mlog.info('requesting api/v1/auction/coin/use/req/UWO to line games...', body);

    // return this.requestAndUnwrapDataOrThrow('api/v1/auction/coin/use/req/UWO', body);
  }

  async bidResult(
    blindCmsId: number,
    userId: number,
    appStoreCd: string,
    gnid: string,
    countryCreated: string,
    bWinner: boolean,
    reason: string
  ) {
    // const body = {
    //   auctionId: blindCmsId.toString(),
    //   svcCd: mconf.LineGameCode,
    //   txId: `${userId}_${new Date().getTime()}_${Math.random()}`,
    //   userId,
    //   appStoreCd,
    //   memo: 'BLIND_BID:' + blindCmsId,
    //   auctionStatus: bWinner ? 'SUCCESS_BIDDING' : 'FAIL_BIDDING',
    //   reason,
    //   gnid,
    //   serverId: mconf.worldId,
    //   countryCreated,
    // };

    // // 롤백 이슈 때문에 추가하게 됨.
    // // 낙찰/유찰 api와 userDB는 하나의 트랜잭션으로 동작할 수 없고,
    // // 낙찰/유찰 api 호출 이후 userDB 처리할때 userDB에서 에러가 나면 낙찰/유찰 api 롤백이 필요하나 롤백하는 기능이 없음.
    // // 해결 방법
    // // -> 보상 수령 패킷 호출
    // //   -> 낙찰/유찰 api 정상 처리
    // //   -> userDB 에러 (실제 보상 지급 안됨, u_blind_bids에는 입찰 내역 남아 있음)
    // // -> 보상 수령 패킷 재 호출 (u_blind_bids에는 입찰 내역 남아 있기 때문에 가능)
    // //   -> 낙찰/유찰 api에서 ALREADY_DONE_AUCTION_USER_ID 반환 시
    // //   -> 무시하고 userDB에 보상수령하도록 처리

    // // 다만, 낙찰 시에만 사용하는 이유
    // // 낙찰 시 보상은 userDB에서 지급하나,
    // // 유찰 시 레드젬 환급은 라인에서 하기 때문에 유찰도 동일하게 처리할 경우
    // // 레드젬 환급은 진행되나 보관함에는 입찰 내역이 남아있는게 문제가됨.
    // let ignoreErrorCode: string;
    // if (bWinner) {
    //   ignoreErrorCode = 'ALREADY_DONE_AUCTION_USER_ID';
    // }

    // mlog.info('requesting /api/v1/auction/coin/use/result to line games...', body);
    // return this.requestAndUnwrapDataOrThrow(
    //   '/api/v1/auction/coin/use/result',
    //   body,
    //   undefined,
    //   ignoreErrorCode
    // );
  }

  async sendToSlack(message: string) {
    if (this._slackNotifier === undefined) {
      this._slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    }

    await this._slackNotifier.notify({
      username: 'host: ' + os.hostname(),
      text: message,
      channel: '#sdk-error',
    });
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275297
   */
  private refreshAuthToken(): Promise<any> {
    // 만료된 토큰을 사용해서 API를 호출하는 경우, 최초 만료가 갱신을 시도하고 나머지들은 여기서 일정시간 머물게 된다.
    // 토큰이 갱신 되면 빠져나가고 자연스럽게 postForm을 재 호출 하는 구조.
    return Promise.resolve()
      .then(() => {
        if (this._bIsTokenRequested) {
          return this.waitForToken(0);
        }
        return null;
      })
      .then(() => {
        this._bIsTokenRequested = true;

        const body = {
          svcCd: mconf.LineGameCode,
          authPwd: this._authPwd,
        };

        return this.mrest.postForm('/api/v1/auth/token/getNewToken', body);
      })
      .then((resp) => {
        const result = resp.data;
        if (result.success && result.data && result.data.authToken) {
          this._authToken = result.data.authToken;
        } else {
          // TODO throw
        }
        this._bIsTokenRequested = false;
      });
  }

  private waitForToken(retries: number): Promise<any> {
    return this.delay(500).then(() => {
      if (this._authToken) {
        return Promise.resolve();
      }
      if (retries > 10) {
        return Promise.resolve();
      }
      return this.waitForToken(++retries);
    });
  }

  private delay(ms: number): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }
}
