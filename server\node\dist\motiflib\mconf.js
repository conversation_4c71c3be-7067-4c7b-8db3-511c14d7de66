"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * config module is organizes automatically from config/default.json5
 * to override, create config/production.json5
 */
const config_1 = __importDefault(require("config"));
const fs_1 = __importDefault(require("fs"));
const json5_1 = __importDefault(require("json5"));
const lodash_1 = __importDefault(require("lodash"));
const path_1 = __importDefault(require("path"));
const merror_1 = require("./merror");
const enum_1 = require("./model/auth/enum");
const resolveLocalDotJson5_1 = require("./resolveLocalDotJson5");
const appInfo_1 = require("./appInfo");
const _RequiredWorldConfig = {
    lobbyd: [
        'mysqlUserDb',
        'mysqlWorldDb',
        'worldPubsubRedis',
        'townRedis',
        'nationRedis',
        'collectorRedis',
        'sailRedis',
        'auctionRedis',
        'guildRedis',
        'guildPubsubRedis',
        'arenaRedis',
        'raidRedis',
        'userRedis',
        'clientVolanteUrl',
        'timezone',
        'countryCode',
        'rankingRedis',
        'blindBidRedis',
    ],
    realmd: [
        'mysqlUserDb',
        'mysqlWorldDb',
        'worldPubsubRedis',
        'townRedis',
        'nationRedis',
        'collectorRedis',
        'arenaRedis',
        'userRedis',
        'raidRedis',
        'timezone',
        'countryCode',
        'createDate',
        'rankingRedis',
        'blindBidRedis',
    ],
    townd: ['worldPubsubRedis', 'timezone', 'countryCode'],
    oceand: ['townRedis', 'nationRedis', 'worldPubsubRedis', 'timezone', 'countryCode'],
    saild: ['mysqlWorldDb', 'sailRedis', 'timezone', 'countryCode'],
    zonelbd: ['townLbRedis', 'oceanLbRedis', 'timezone', 'countryCode'],
};
const _OptionalWorldConfig = {
    lobbyd: ['bIsNonPK'],
    realmd: ['bIsNonPK'],
};
function _throwOnMissingConfig(cfgName) {
    throw new merror_1.MError('Missing required config!', merror_1.MErrorCode.INTERNAL_ERROR, { cfgName });
}
class MConf {
    constructor() {
        this.util = config_1.default.util;
        const appInfo = (0, appInfo_1.getAppInfo)();
        this.appInstanceId = appInfo.instanceId;
        this.isDev = appInfo.isDev;
        this.hostname = appInfo.hostname;
        this.appId = appInfo.appId;
    }
    get isSDO() {
        return this.platform === enum_1.PLATFORM.SDO;
    }
    append(others) {
        lodash_1.default.merge(this, others);
    }
    appendWorldConfig(worldConfig) {
        // HTTP clients.
        lodash_1.default.merge(this.http, worldConfig.http);
        // 서버마다 필요한 설정 머지.
        const requiredCfgList = _RequiredWorldConfig[process.name];
        if (!requiredCfgList) {
            return;
        }
        for (const cfgName of requiredCfgList) {
            this[cfgName] = worldConfig[cfgName];
            if (this[cfgName] === undefined) {
                _throwOnMissingConfig(cfgName);
            }
        }
        const optionalCfgList = _OptionalWorldConfig[process.name];
        if (optionalCfgList) {
            for (const cfgName of optionalCfgList) {
                this[cfgName] = worldConfig[cfgName];
            }
        }
    }
    get(setting) {
        return config_1.default.get(setting);
    }
    has(setting) {
        return config_1.default.has(setting);
    }
    getWorldConfig(inWorldId) {
        const worldId = inWorldId ? inWorldId : this.worldId;
        if (!worldId) {
            return null;
        }
        for (const worldConfig of this.worlds) {
            if (worldConfig.id === worldId) {
                return worldConfig;
            }
        }
        return null;
    }
    getServerdInstances(serverType) {
        let serverInstances = [];
        if (this.serverdInstances) {
            Object.keys(this.serverdInstances).forEach((appId) => {
                const nameTokens = appId.split('.');
                if (nameTokens[0] != serverType) {
                    return;
                }
                serverInstances[appId] = this.serverdInstances[appId];
            });
        }
        return serverInstances;
    }
    // base 1
    getWorldNumber() {
        if (mconf.platform === enum_1.PLATFORM.LINE) {
            // UWO-KR-01 와 같은 규칙.
            const arr = mconf.worldId.split('-');
            return parseInt(arr[arr.length - 1], 10);
        }
        else {
            return 1;
        }
    }
}
let mconf = new MConf();
lodash_1.default.merge(mconf, config_1.default);
// Merge local config.
try {
    const localFilePath = path_1.default.resolve(path_1.default.join(__dirname, '..', '..', 'config', (0, resolveLocalDotJson5_1.resolveLocalDotJson5)()));
    const localFile = fs_1.default.readFileSync(localFilePath, 'utf8');
    const localJson = json5_1.default.parse(localFile);
    lodash_1.default.merge(mconf, localJson);
}
catch (_a) { }
exports.default = mconf;
//# sourceMappingURL=mconf.js.map