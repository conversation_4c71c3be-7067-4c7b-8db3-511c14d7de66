{"environment":"development","type":"saild","gitCommitHash":"836b50c3bac7","gitCommitMessage":"UWO FGT 과몰입, 결제 관련 기능 추가(결제는 추가 작업 필요)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:00:05+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"saild.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:02:28.190Z"}
{"fileName":"sailLobbySailProxyHandler.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:36.537Z"}
{"fileName":"sailPacketHandlerCommon.js","mapSize":4,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:36.537Z"}
{"fileName":"sailPacketHandlerOffSail.js","mapSize":5,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:02:36.538Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:02:36.679Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:02:36.682Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:02:38.246Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:02:38.247Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:02:38.248Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:02:38.249Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:38.250Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:02:38.250Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:02:38.253Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:02:42.974Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:02:42.975Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:02:42.976Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:02:42.986Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:02:42.987Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:02:43.009Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:02:43.111Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.132Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.152Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.172Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.192Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.207Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.230Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.249Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.265Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:02:43.285Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:02:43.372Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:02:43.372Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:02:43.376Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:02:43.527Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:02:43.530Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:02:43.531Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:02:43.531Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:02:43.534Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:02:43.534Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:02:43.535Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:02:43.535Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:02:43.538Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:02:43.538Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:02:43.539Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:02:43.540Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:02:43.541Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:02:43.543Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:02:43.544Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-07-30T09:02:44.479Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:02:44.484Z"}
{"functionName":"cashShopConsumeTransaction_cn","sha1":"5c8b0dcc63e04ebfa6a9adeb811dae99cf362554","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"addAvoidableEncountUser","sha1":"b196b938e5b9b16ff5db9e6f5f445ec5743584fc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopCancelTransaction_cn","sha1":"f13938e14d396ea4b47d23cece89584480551f7c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopCompleteTransaction_cn","sha1":"5630498be74cd25acb30c51a10a29ab30fff09dd","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopGetTransactions_cn","sha1":"719999a559e79faf97c11b89b17398218cdfd5f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopGetTransaction_cn","sha1":"52f3c6f767bf58560d14fe20d41b04019f92a8ae","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopPaidTransaction_cn","sha1":"7ff7d0167cc4fb4f8aaecdd51a01a7c48fea439c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"cashShopStartTransaction_cn","sha1":"af10558b02faa87711d4079c511e39f5459ad81f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.587Z"}
{"functionName":"devResetMyFirstFleetInfo","sha1":"d383039c0e9663db8825cd24f9218f727725b3d4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getEnterWorldToken","sha1":"a7c8b82eef224789b6c1ce783fb30c6d9427072a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getEnterWorldTokenAndOrderId","sha1":"28a6698388f40727ee90e438308fde99aba52db0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getOpenedFleetPresets","sha1":"249d33794918d77c6444f397522bb92488e96f1c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getIsFriendlyBattleRequestable","sha1":"39b209f3120ff9f5bedba9d959321363140a80f4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getOfflineSailingInfo","sha1":"40e502ab74f8fed7209807d45fe556c583d784e5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getUserFirstFleetInfo","sha1":"b6dbca0981bf09715a09ce483696ec16424de12d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getUser","sha1":"39886d1bc995dc0f3339a0e46339b4c3b4c5cdef","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getUserHeartBeatWithLobbyInfo","sha1":"ff373b5e7ca9852372760374bd1a9f56f3dec8ea","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.588Z"}
{"functionName":"getUserIsFirstFleetProfileOpened","sha1":"d9b19aafddbd222624103314203b08bbe500edd7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"getUserHeartBeat","sha1":"74a3e91eb452f8fb68c11310e9ab459bc1e2c6cd","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"getUserName","sha1":"d2152a3c6005c8aaa310880c64f734af463d4c1e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"getUserIsOnline","sha1":"f0a6e042655d3c7e2b72ad4611318459cd5a836d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"getUserNationCmsId","sha1":"3af426bfda0cd72edfbe26ca42fcc9c848998e12","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"removeUser","sha1":"baa3789800f4a8b8a66bf965544c81d83dd439ba","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"loadAvoidableEncountUsers","sha1":"420ea589d033d6621d9df9ad66b01bfa2a4af9d0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"loadTownUserWeeklyInvestmentReport","sha1":"a49aa69fa7631c4a7e951e8ff26ea4b416fdd2d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"setEnterWorldToken","sha1":"4a50a9cab4ac52b6a8144218e274b3173e20fcc9","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"setDefault","sha1":"db47476fcca466b94407b559ea831d4da92f9a89","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.589Z"}
{"functionName":"setGameState","sha1":"f2fd1c918851237c33979240c26c17d877f1b8eb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setIsFriendlyBattleRequestable","sha1":"46c9b2295bf9205c6cff97cebd36b65e88f3613a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setLastLogoutTimeUtc","sha1":"7810b61105318db121b8a1f9f6dde07e9b54759c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setIsProfileOpened","sha1":"e3976b05e912d1074cbe0197044fbceb4ab38dc5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setOnline","sha1":"4bce7ba49c605602e2959642605513b80325116d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setLastTownAndGameState","sha1":"b9d2fcc2db186038d19a3e0485b6b534070f398f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setOpenedFleetPresetAndIsOpen","sha1":"082d3bec72f5e1366df172cd2d7ac595f0b555a6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setOpenedFleetPresetIsOpens","sha1":"9f3f68bf6ac48a894b7985a5519f92ca2589b7f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setOpenedFleetPresets","sha1":"468a259da9836480c489cb682a39bc247ca31128","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setUserFirstFleetInfoFlag","sha1":"5fa97527f569be043f00059a07056fc4bee3213d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.590Z"}
{"functionName":"setRepresentedMate","sha1":"517c404671501935e72cf302b8f27a595760fe64","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserFirstFleetInfos","sha1":"8c638ef90b7b09c73f30151166c5e5d8d63eca7a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserGuild","sha1":"2cc477983683875d06edd1d777eed9a56be82b9c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserIsFirstFleetProfileOpened","sha1":"ca3abfc3cba2db31043f10f861da22431f2640a1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateCmsIdAndAwakenLevel","sha1":"b3c325aad95b33b31064d4bf8f45bcec1dd66ada","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateAwakenLevel","sha1":"1e1f360eb7435c2aa9791f3e8e905ee63d3a5c3c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateIllustCmsId","sha1":"9a6a76cfd4b008c9c4f35ce2ef57a9af658f73f6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateLightInfo","sha1":"67296e5e55b742144d9cf4d64ebdb134b35fd0cb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLevel","sha1":"6186632c5e72ddfa3a571508b8ac82f9df73fbef","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateIsTranscended","sha1":"8a3c0425aec1b16b29795fc8f1d6c45ddff23707","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserLeaderMateTrainingGrade","sha1":"f61cc332c5c13de3e28ce3dedfc3400dd9f5b335","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserName","sha1":"9d49d0effbec966f9d3db1469770909dfc959871","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.591Z"}
{"functionName":"setUserPrimeMinisterElectedCount","sha1":"156eb239240fb860dc734a0b87d95b5cb5d27720","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"setUserNationCmsId","sha1":"f659fe5f750847d1c1d3a38d9f01ce300e91de95","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"tryRegisterOfflineSailingJob","sha1":"17c981faae4ffb1b649fe357eb0b4e41ce5bdc68","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"storeTownUserWeeklyInvestmentReport","sha1":"516fd6ee88fbd2d22b6070a8dcca9236c5c245cb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"updateAvoidableEncountUsers","sha1":"afed3ef981c1721580c813137f09f786212d7018","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"updateUserHeartBeatWhenEnterWorld","sha1":"b8d893a26f6870abc86c4fd67d4a08ad2ba8cf49","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"setUserPubId","sha1":"05f9d26d9a6529be6065ba4e89bd8d2547939410","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"updateUserHeartBeat","sha1":"87e1fa2d7eced2121e846eed7f34bf4cda20f578","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"updateOfflineSailing","sha1":"92d543f54500446d768bfe4d9c99777b2ff3fc5c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"functionName":"updateUserHeartBeatWhenLogout","sha1":"8c1a38f0303549cc73fb2e9f02537919563be237","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.592Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-07-30T09:02:44.595Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (sail-redis) initializing ...","timestamp":"2025-07-30T09:02:44.595Z"}
{"functionName":"endSailing","sha1":"1ebbb62b5a5ae6cbcc429fed1b961c0ff310d7f5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"getSailingId","sha1":"26a1fce9e9e18b6cf4138000ef096abb10ac1bb3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"acquireDbScanLockAndLastScanedUserId","sha1":"afbb36bd8910086eb096bbc1a005e2969e9feebb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"getOfflineBattleHistories","sha1":"e81fb10accec111c763dce298e1906d4a79124cf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"setSailing","sha1":"13e9f22839377d9d6b2e7032a68054d6acab6e31","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"releaseDbScanLockAndLastScanedUserId","sha1":"f9bee44e6346cf6e07fa0e3d5fce3702f71abd4f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"loadSailing","sha1":"c31f0e36dc2d7af75b3ffec2b9bc20b15be18e15","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"setOfflineBattleHistories","sha1":"4d0b589172547c831445c415a7cf9f5e187816f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"updateSailing","sha1":"5b87c65a93808d5c3d351eb4dfbdc2ea942e4143","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.605Z"}
{"functionName":"startSailing","sha1":"1d8c7b362c6a10626091f18a6b546e37539c481b","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.606Z"}
{"level":"info","message":"redis pool (sail-redis) initialized","timestamp":"2025-07-30T09:02:44.606Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-07-30T09:02:44.607Z"}
{"functionName":"getLobbydUrls","sha1":"86f5f508ef7e8f9f1e6109062d306028acc68f24","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"getMaintenace","sha1":"5b5e789f6ba2e63e2ac24541efba798f86404516","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"processLobbydPingTimeout","sha1":"7b29fa11f7410617e374d3e428b64509fc5adfae","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"getUserCount","sha1":"6129d2affc117871e4dfbb2f2661c212430a14b1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"registerLobbyd","sha1":"9650a9aa61fc1d5001e22859d502c5cba4ecd917","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"unregisterLobbyd","sha1":"a1727c960d932372ad5dcdb6d0bf2a54bf62a007","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"updateClientVersionInfo","sha1":"bf1aea2c58265f0e69610d9495b07951967ab70c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"updateLobbydPing","sha1":"3351213853fa026dcc461db4a804eeaec0d9f431","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"functionName":"updateMaintenance","sha1":"bf1aea2c58265f0e69610d9495b07951967ab70c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:02:44.619Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-07-30T09:02:44.620Z"}
{"ch":"kick:saild.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:02:44.620Z"}
{"path":"/endSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.629Z"}
{"path":"/getSailingId","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.651Z"}
{"path":"/loadSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.660Z"}
{"path":"/startSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.670Z"}
{"path":"/updateSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.679Z"}
{"path":"/admin/allKick","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:02:44.691Z"}
{"bindAddress":"0.0.0.0","port":11100,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:02:44.733Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 11109","timestamp":"2025-07-30T09:02:44.733Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:02:44.736Z"}
{"new":false,"level":"info","message":"init bMaintenance state ","timestamp":"2025-07-30T09:03:11.347Z"}
{"managerTickInterval":1000,"level":"info","message":"[offlineSailingManager] tick started","timestamp":"2025-07-30T09:03:11.348Z"}
{"level":"info","message":"[SessionManager] session created: vONDH9BX, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:03:13.185Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:03:13.187Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2SA_REQ_CONNECTED","timestamp":"2025-07-30T09:03:13.188Z"}
{"origin":{},"seq":2,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:SA2LB_RES_CONNECTED","timestamp":"2025-07-30T09:03:13.188Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:13:52.910Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:13:52.911Z"}
{"level":"info","message":"[Session] socket disposed, vONDH9BX","timestamp":"2025-07-30T09:13:52.911Z"}
{"level":"info","message":"[!] server is stopping: type=saild, signal=SIGINT","timestamp":"2025-07-30T09:13:53.225Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:13:53.225Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:13:53.241Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:13:53.242Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:13:53.242Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-07-30T09:13:53.244Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:13:53.245Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:13:53.245Z"}
{"level":"info","message":"redis pool (sail-redis) destroyed","timestamp":"2025-07-30T09:13:53.245Z"}
{"name":"sail-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:13:53.245Z"}
{"name":"sail-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:13:53.246Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-07-30T09:13:53.246Z"}
{"level":"info","message":"server stopped","timestamp":"2025-07-30T09:13:53.246Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:13:53.246Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:13:53.246Z"}
{"environment":"development","type":"saild","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"saild.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:13:56.174Z"}
{"fileName":"sailLobbySailProxyHandler.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:03.483Z"}
{"fileName":"sailPacketHandlerCommon.js","mapSize":4,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:03.484Z"}
{"fileName":"sailPacketHandlerOffSail.js","mapSize":5,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:14:03.484Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:03.494Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:14:03.497Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:14:04.993Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:14:04.993Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:14:04.994Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:14:04.994Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:04.995Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:14:04.995Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:14:04.996Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:14:09.416Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:14:09.417Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:14:09.417Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:14:09.424Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:14:09.425Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:14:09.440Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:14:09.521Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.545Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.564Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.576Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.591Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.603Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.621Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.639Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.655Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:14:09.674Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:14:09.753Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:14:09.754Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:14:09.757Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:14:09.897Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:14:09.899Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:14:09.900Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:14:09.901Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:14:09.903Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:14:09.903Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:14:09.904Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:14:09.904Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:14:09.906Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:14:09.906Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:14:09.906Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:14:09.907Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:14:09.908Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:14:09.910Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:14:09.910Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-07-30T09:14:10.840Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:14:10.845Z"}
{"functionName":"cashShopConsumeTransaction_cn","sha1":"5c8b0dcc63e04ebfa6a9adeb811dae99cf362554","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.880Z"}
{"functionName":"cashShopCompleteTransaction_cn","sha1":"5630498be74cd25acb30c51a10a29ab30fff09dd","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.880Z"}
{"functionName":"cashShopCancelTransaction_cn","sha1":"f13938e14d396ea4b47d23cece89584480551f7c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"addAvoidableEncountUser","sha1":"b196b938e5b9b16ff5db9e6f5f445ec5743584fc","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"cashShopPaidTransaction_cn","sha1":"7ff7d0167cc4fb4f8aaecdd51a01a7c48fea439c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"cashShopGetTransactions_cn","sha1":"719999a559e79faf97c11b89b17398218cdfd5f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"cashShopStartTransaction_cn","sha1":"af10558b02faa87711d4079c511e39f5459ad81f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"cashShopGetTransaction_cn","sha1":"52f3c6f767bf58560d14fe20d41b04019f92a8ae","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"devResetMyFirstFleetInfo","sha1":"d383039c0e9663db8825cd24f9218f727725b3d4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"getEnterWorldToken","sha1":"a7c8b82eef224789b6c1ce783fb30c6d9427072a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.881Z"}
{"functionName":"getEnterWorldTokenAndOrderId","sha1":"28a6698388f40727ee90e438308fde99aba52db0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getIsFriendlyBattleRequestable","sha1":"39b209f3120ff9f5bedba9d959321363140a80f4","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getOpenedFleetPresets","sha1":"249d33794918d77c6444f397522bb92488e96f1c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getOfflineSailingInfo","sha1":"40e502ab74f8fed7209807d45fe556c583d784e5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserFirstFleetInfo","sha1":"b6dbca0981bf09715a09ce483696ec16424de12d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUser","sha1":"39886d1bc995dc0f3339a0e46339b4c3b4c5cdef","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserHeartBeat","sha1":"74a3e91eb452f8fb68c11310e9ab459bc1e2c6cd","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserIsFirstFleetProfileOpened","sha1":"d9b19aafddbd222624103314203b08bbe500edd7","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserHeartBeatWithLobbyInfo","sha1":"ff373b5e7ca9852372760374bd1a9f56f3dec8ea","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserIsOnline","sha1":"f0a6e042655d3c7e2b72ad4611318459cd5a836d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserName","sha1":"d2152a3c6005c8aaa310880c64f734af463d4c1e","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"getUserNationCmsId","sha1":"3af426bfda0cd72edfbe26ca42fcc9c848998e12","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"loadAvoidableEncountUsers","sha1":"420ea589d033d6621d9df9ad66b01bfa2a4af9d0","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.882Z"}
{"functionName":"loadTownUserWeeklyInvestmentReport","sha1":"a49aa69fa7631c4a7e951e8ff26ea4b416fdd2d6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"removeUser","sha1":"baa3789800f4a8b8a66bf965544c81d83dd439ba","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setDefault","sha1":"db47476fcca466b94407b559ea831d4da92f9a89","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setEnterWorldToken","sha1":"4a50a9cab4ac52b6a8144218e274b3173e20fcc9","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setGameState","sha1":"f2fd1c918851237c33979240c26c17d877f1b8eb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setLastLogoutTimeUtc","sha1":"7810b61105318db121b8a1f9f6dde07e9b54759c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setIsProfileOpened","sha1":"e3976b05e912d1074cbe0197044fbceb4ab38dc5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setIsFriendlyBattleRequestable","sha1":"46c9b2295bf9205c6cff97cebd36b65e88f3613a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setLastTownAndGameState","sha1":"b9d2fcc2db186038d19a3e0485b6b534070f398f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setOnline","sha1":"4bce7ba49c605602e2959642605513b80325116d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setOpenedFleetPresetIsOpens","sha1":"9f3f68bf6ac48a894b7985a5519f92ca2589b7f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setOpenedFleetPresetAndIsOpen","sha1":"082d3bec72f5e1366df172cd2d7ac595f0b555a6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setOpenedFleetPresets","sha1":"468a259da9836480c489cb682a39bc247ca31128","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.883Z"}
{"functionName":"setRepresentedMate","sha1":"517c404671501935e72cf302b8f27a595760fe64","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserFirstFleetInfos","sha1":"8c638ef90b7b09c73f30151166c5e5d8d63eca7a","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserFirstFleetInfoFlag","sha1":"5fa97527f569be043f00059a07056fc4bee3213d","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserGuild","sha1":"2cc477983683875d06edd1d777eed9a56be82b9c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateCmsIdAndAwakenLevel","sha1":"b3c325aad95b33b31064d4bf8f45bcec1dd66ada","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserIsFirstFleetProfileOpened","sha1":"ca3abfc3cba2db31043f10f861da22431f2640a1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateAwakenLevel","sha1":"1e1f360eb7435c2aa9791f3e8e905ee63d3a5c3c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateIllustCmsId","sha1":"9a6a76cfd4b008c9c4f35ce2ef57a9af658f73f6","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateIsTranscended","sha1":"8a3c0425aec1b16b29795fc8f1d6c45ddff23707","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateLightInfo","sha1":"67296e5e55b742144d9cf4d64ebdb134b35fd0cb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLeaderMateTrainingGrade","sha1":"f61cc332c5c13de3e28ce3dedfc3400dd9f5b335","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserLevel","sha1":"6186632c5e72ddfa3a571508b8ac82f9df73fbef","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserName","sha1":"9d49d0effbec966f9d3db1469770909dfc959871","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserNationCmsId","sha1":"f659fe5f750847d1c1d3a38d9f01ce300e91de95","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserPrimeMinisterElectedCount","sha1":"156eb239240fb860dc734a0b87d95b5cb5d27720","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"storeTownUserWeeklyInvestmentReport","sha1":"516fd6ee88fbd2d22b6070a8dcca9236c5c245cb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.884Z"}
{"functionName":"setUserPubId","sha1":"05f9d26d9a6529be6065ba4e89bd8d2547939410","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"tryRegisterOfflineSailingJob","sha1":"17c981faae4ffb1b649fe357eb0b4e41ce5bdc68","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"updateOfflineSailing","sha1":"92d543f54500446d768bfe4d9c99777b2ff3fc5c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"updateAvoidableEncountUsers","sha1":"afed3ef981c1721580c813137f09f786212d7018","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"updateUserHeartBeatWhenEnterWorld","sha1":"b8d893a26f6870abc86c4fd67d4a08ad2ba8cf49","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"updateUserHeartBeat","sha1":"87e1fa2d7eced2121e846eed7f34bf4cda20f578","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"functionName":"updateUserHeartBeatWhenLogout","sha1":"8c1a38f0303549cc73fb2e9f02537919563be237","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.885Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-07-30T09:14:10.887Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (sail-redis) initializing ...","timestamp":"2025-07-30T09:14:10.887Z"}
{"functionName":"getOfflineBattleHistories","sha1":"e81fb10accec111c763dce298e1906d4a79124cf","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"acquireDbScanLockAndLastScanedUserId","sha1":"afbb36bd8910086eb096bbc1a005e2969e9feebb","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"getSailingId","sha1":"26a1fce9e9e18b6cf4138000ef096abb10ac1bb3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"endSailing","sha1":"1ebbb62b5a5ae6cbcc429fed1b961c0ff310d7f5","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"loadSailing","sha1":"c31f0e36dc2d7af75b3ffec2b9bc20b15be18e15","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"releaseDbScanLockAndLastScanedUserId","sha1":"f9bee44e6346cf6e07fa0e3d5fce3702f71abd4f","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"setSailing","sha1":"13e9f22839377d9d6b2e7032a68054d6acab6e31","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"setOfflineBattleHistories","sha1":"4d0b589172547c831445c415a7cf9f5e187816f3","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"startSailing","sha1":"1d8c7b362c6a10626091f18a6b546e37539c481b","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.897Z"}
{"functionName":"updateSailing","sha1":"5b87c65a93808d5c3d351eb4dfbdc2ea942e4143","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.898Z"}
{"level":"info","message":"redis pool (sail-redis) initialized","timestamp":"2025-07-30T09:14:10.898Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-07-30T09:14:10.898Z"}
{"functionName":"getMaintenace","sha1":"5b5e789f6ba2e63e2ac24541efba798f86404516","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.907Z"}
{"functionName":"getUserCount","sha1":"6129d2affc117871e4dfbb2f2661c212430a14b1","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.907Z"}
{"functionName":"getLobbydUrls","sha1":"86f5f508ef7e8f9f1e6109062d306028acc68f24","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"processLobbydPingTimeout","sha1":"7b29fa11f7410617e374d3e428b64509fc5adfae","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"unregisterLobbyd","sha1":"a1727c960d932372ad5dcdb6d0bf2a54bf62a007","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"registerLobbyd","sha1":"9650a9aa61fc1d5001e22859d502c5cba4ecd917","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"updateClientVersionInfo","sha1":"bf1aea2c58265f0e69610d9495b07951967ab70c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"updateLobbydPing","sha1":"3351213853fa026dcc461db4a804eeaec0d9f431","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"functionName":"updateMaintenance","sha1":"bf1aea2c58265f0e69610d9495b07951967ab70c","level":"info","message":"[redis-sha1-to-lua]","timestamp":"2025-07-30T09:14:10.908Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-07-30T09:14:10.908Z"}
{"ch":"kick:saild.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:14:10.909Z"}
{"path":"/endSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.916Z"}
{"path":"/getSailingId","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.933Z"}
{"path":"/loadSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.941Z"}
{"path":"/startSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.949Z"}
{"path":"/updateSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.957Z"}
{"path":"/admin/allKick","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:14:10.968Z"}
{"bindAddress":"0.0.0.0","port":11100,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:14:11.005Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 11109","timestamp":"2025-07-30T09:14:11.006Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:14:11.008Z"}
{"new":false,"level":"info","message":"init bMaintenance state ","timestamp":"2025-07-30T09:14:11.046Z"}
{"managerTickInterval":1000,"level":"info","message":"[offlineSailingManager] tick started","timestamp":"2025-07-30T09:14:11.047Z"}
{"level":"info","message":"[SessionManager] session created: PMqsmAKO, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:14:41.997Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:14:41.999Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2SA_REQ_CONNECTED","timestamp":"2025-07-30T09:14:41.999Z"}
{"origin":{},"seq":2,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:SA2LB_RES_CONNECTED","timestamp":"2025-07-30T09:14:42.000Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-07-30T09:57:03.078Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-07-30T09:57:03.078Z"}
{"level":"info","message":"[Session] socket disposed, PMqsmAKO","timestamp":"2025-07-30T09:57:03.079Z"}
{"level":"info","message":"[!] server is stopping: type=saild, signal=SIGINT","timestamp":"2025-07-30T09:57:03.240Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:03.240Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-07-30T09:57:04.115Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-07-30T09:57:04.115Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-07-30T09:57:04.115Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-07-30T09:57:04.119Z"}
{"level":"info","message":"redis pool (sail-redis) destroyed","timestamp":"2025-07-30T09:57:04.120Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:57:04.120Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:57:04.120Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-07-30T09:57:04.121Z"}
{"level":"info","message":"server stopped","timestamp":"2025-07-30T09:57:04.121Z"}
{"name":"sail-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:57:04.121Z"}
{"name":"sail-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:57:04.121Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-07-30T09:57:04.121Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-07-30T09:57:04.121Z"}
{"environment":"development","type":"saild","gitCommitHash":"8220b89bbdd9","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-07-30T18:09:23+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"saild.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-07-30T09:57:08.967Z"}
{"fileName":"sailLobbySailProxyHandler.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:18.340Z"}
{"fileName":"sailPacketHandlerCommon.js","mapSize":4,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:18.341Z"}
{"fileName":"sailPacketHandlerOffSail.js","mapSize":5,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-07-30T09:57:18.341Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:18.350Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-07-30T09:57:18.352Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-07-30T09:57:19.865Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-07-30T09:57:19.866Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-07-30T09:57:19.866Z"}
{"level":"info","message":"[linegames_log] refresh-token requesting","timestamp":"2025-07-30T09:57:19.867Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:19.867Z"}
{"level":"info","message":"chatd endpoint: https://dev-volante-chat-api.line.games","timestamp":"2025-07-30T09:57:19.868Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-07-30T09:57:19.868Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-07-30T09:57:24.285Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-07-30T09:57:24.286Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-07-30T09:57:24.286Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-07-30T09:57:24.293Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-07-30T09:57:24.294Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-07-30T09:57:24.309Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-07-30T09:57:24.386Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.407Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.424Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.436Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.449Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.462Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.480Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.497Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.513Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-07-30T09:57:24.531Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-07-30T09:57:24.611Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-07-30T09:57:24.612Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-07-30T09:57:24.615Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-07-30T09:57:24.706Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-07-30T09:57:24.708Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-07-30T09:57:24.709Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-07-30T09:57:24.709Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-07-30T09:57:24.712Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-07-30T09:57:24.712Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-07-30T09:57:24.712Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-07-30T09:57:24.713Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-07-30T09:57:24.715Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-07-30T09:57:24.715Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-07-30T09:57:24.716Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-07-30T09:57:24.716Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-07-30T09:57:24.717Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-07-30T09:57:24.721Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-07-30T09:57:24.722Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-07-30T09:57:25.535Z"}
{"message":"[linegames_log] refresh-token error connect ECONNREFUSED 127.0.0.1:80","stack":"Error: connect ECONNREFUSED 127.0.0.1:80\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1278:16)","level":"warn","timestamp":"2025-07-30T09:57:25.539Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-07-30T09:57:25.580Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (sail-redis) initializing ...","timestamp":"2025-07-30T09:57:25.580Z"}
{"level":"info","message":"redis pool (sail-redis) initialized","timestamp":"2025-07-30T09:57:25.592Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-07-30T09:57:25.592Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-07-30T09:57:25.603Z"}
{"ch":"kick:saild.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-07-30T09:57:25.603Z"}
{"path":"/endSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.610Z"}
{"path":"/getSailingId","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.631Z"}
{"path":"/loadSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.639Z"}
{"path":"/startSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.644Z"}
{"path":"/updateSailing","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.652Z"}
{"path":"/admin/allKick","level":"verbose","message":"Registering api ...","timestamp":"2025-07-30T09:57:25.662Z"}
{"bindAddress":"0.0.0.0","port":11100,"level":"info","message":"start listening ...","timestamp":"2025-07-30T09:57:25.701Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 11109","timestamp":"2025-07-30T09:57:25.701Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-07-30T09:57:25.703Z"}
{"new":false,"level":"info","message":"init bMaintenance state ","timestamp":"2025-07-30T09:57:32.722Z"}
{"managerTickInterval":1000,"level":"info","message":"[offlineSailingManager] tick started","timestamp":"2025-07-30T09:57:32.723Z"}
{"level":"info","message":"[SessionManager] session created: sjxMYeGI, for: 127.0.0.1, session count: 1","timestamp":"2025-07-30T09:57:52.649Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-07-30T09:57:52.651Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2SA_REQ_CONNECTED","timestamp":"2025-07-30T09:57:52.652Z"}
{"origin":{},"seq":2,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:SA2LB_RES_CONNECTED","timestamp":"2025-07-30T09:57:52.652Z"}
