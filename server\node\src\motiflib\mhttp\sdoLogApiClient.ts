// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import mconf from '../mconf';
import mlog from '../mlog';
import { BaseApiClient } from './baseApiClient';
import { MError, MErrorCode } from '../merror';
import { ResponseBody, LGErrorCode } from './linegamesApiClient';
import { curTimeUtc } from '../mutil';
import { SECONDS_PER_MINUTE } from '../../formula';

// dev, qa 에서만 사용될 임시 ApiClient.
// 이하 주석은 라인게임즈 김태준님 코멘트.
// 리얼환경에서는 uwo용 별도 몽고API를 제공해드리고 게임 내부 인프라에서만 통신.
// 리얼서비스때는 uwo 게임 네트워크 안에 별도로 몽고DB 드려서, 따로 토큰을 사용안하셔도 되요.

export interface LineLogData {
  collection: string;
  dataList: any[];
}

const refreshTokenWithinTimeMin: number = 5; // 5분이하일때 토큰 갱신가능

export class LineGamesLogApiClient extends BaseApiClient {
  private _nfToken: string = null;
  private _authPwd: string;

  // 토큰 만료시간.(만료시간 전에 갱신처리.)
  private _expireUnixTS: number = 0;

  // 토큰 갱신 중 여부.
  private _bIsRefreshing: boolean = false;

  private _forceRefresh: boolean = false;

  constructor() {
    super();
  }

  init(baseUrl: string, timeout?: number) {
    super.init(baseUrl, timeout);
    this._authPwd = mconf.http.lglogd.authPwd;

    this.refresh();

    setInterval(() => {
      this.refreshTokenIfExpired();
    }, 10000);
  }

  private _post(url: string, body?: any): Promise<ResponseBody> {
    return Promise.resolve()
      .then(() => {
        // CBT, REAL 환경에서는 token 사용 안 함.
        if (mconf.http.lglogd.authPwd && !this._nfToken) {
          throw new MError('no token', MErrorCode.INTERNAL_ERROR);
        }
        const headers: any = {
          gameCd: mconf.LineGameCode,
          'Content-Type': 'application/json',
        };
        if (mconf.http.lglogd.authPwd) {
          headers.nfToken = this._nfToken;
        }

        return this.mrest.post<ResponseBody>(url, body, {
          headers,
        });
      })
      .then((resp) => {
        const result = resp.data;
        if (!result.isSuccess && LGErrorCode[result.errorCd] === LGErrorCode.EXPIRE_AUTH_TOKEN) {
          this._nfToken = null;
          this._forceRefresh = true;
        }

        return result;
      })
      .catch((err) => {
        throw err;
      });
  }

  /*
  refreshNfToken(): Promise<void> {
    // 만료된 토큰을 사용해서 API를 호출하는 경우, 최초 만료가 갱신을 시도하고 나머지들은 여기서 일정시간 머물게 된다.
    // 토큰이 갱신 되면 빠져나가고 자연스럽게 postForm을 재 호출 하는 구조.

    if (!this._authPwd) {
      return Promise.resolve();
    }

    return Promise.resolve()
      .then(() => {
        if (this._bIsTokenRequested) {
          return this.waitForToken(0);
        }
        return null;
      })
      .then(() => {
        mlog.info('[refresh-token] request');

        this._bIsTokenRequested = true;

        const body = {
          gameCd: mconf.LineGameCode,
          authPwd: this._authPwd,
        };

        return this.mrest.postForm<ResponseBody>('/api/auth/token/getNewToken', body, {
          headers: {
            gameCd: mconf.LineGameCode,
          },
        });
      })
      .then((resp) => {
        const result = resp.data;

        if (result.isSuccess && result.data && result.data.nfToken) {
          this._nfToken = result.data.nfToken;
          this._expireUnixTS = result.data.expireUnixTS;
          mlog.info('[refresh-token-result] success', {
            result,
          });
        } else {
          // TODO throw
          mlog.warn('[refresh-token-result] failed', {
            result,
          });
        }
        this._bIsTokenRequested = false;
      })
      .catch((err) => {
        this._bIsTokenRequested = false;
        mlog.warn('[refresh-token-result] error ', {
          message: err.message,
          stack: err.stack,
        });
        throw err;
      });
  }

  
  private waitForToken(retries: number): Promise<any> {
    return this.delay(500).then(() => {
      if (this._nfToken) {
        return Promise.resolve();
      }
      if (retries > 10) {
        return Promise.resolve();
      }
      return this.waitForToken(++retries);
    });
  }

  private delay(ms: number): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }
  */

  // https://developer.line.games/pages/viewpage.action?pageId=43421605
  saveCommonLog(data: LineLogData[]): Promise<boolean> {
    if (!data) {
      return Promise.resolve(false);
    }

    const url = `/api/mongo/v2/game/${mconf.LineGameCode}/data/save`;
    const body = {
      gameCd: mconf.LineGameCode,
      saveCollectionDataList: data,
    };

    return this._post(url, body)
      .then((result) => {
        return result.isSuccess;
      })
      .catch((err) => {
        mlog.error('saveCommonLog is failed', { err: err.message, data });
        return false;
      });
  }

  isTokenRefreshing(): boolean {
    return this._bIsRefreshing;
  }

  _canRefreshToken(): boolean {
    if (this._forceRefresh) {
      return true;
    }
    if (!this._authPwd) {
      return false;
    }
    if (this._bIsRefreshing) {
      return false;
    }
    this._expireUnixTS;
    const now = curTimeUtc();
    let timeMinLeftToExpire: number = (this._expireUnixTS - now) / SECONDS_PER_MINUTE;

    if (refreshTokenWithinTimeMin <= timeMinLeftToExpire) {
      // 로그용
      /*
      const timeLeft = Math.floor(this._expireUnixTS - now);
      const days = Math.floor(timeLeft / SECONDS_PER_DAY);
      const hours = Math.floor((timeLeft % SECONDS_PER_DAY) / SECONDS_PER_HOUR);
      const mins = Math.floor((timeLeft % SECONDS_PER_HOUR) / SECONDS_PER_MINUTE);
      const seconds = Math.floor(timeLeft % SECONDS_PER_MINUTE);
      mlog.info('[token-remaining]', {
        timeLeft,
        time: `days:${days} hours:${hours} mins:${mins} seconds:${seconds}`,
      });
      */
      return false;
    }
    return true;
  }

  // https://developer.line.games/pages/viewpage.action?pageId=7275297
  async refresh() {
    // 토큰 갱신이 완료될 때까지 로그기록을 막고 queue에 기록 후,
    // 갱신 완료 시 queue에 쌓인 로그를 bulk형식으로 전송
    this._bIsRefreshing = true;

    const body = {
      gameCd: mconf.LineGameCode,
      authPwd: this._authPwd,
    };

    try {
      mlog.info('[linegames_log] refresh-token requesting');
      const resp = await this.mrest.postForm<ResponseBody>('/api/auth/token/getNewToken', body, {
        headers: {
          gameCd: mconf.LineGameCode,
        },
      });

      const result = resp.data;

      this._bIsRefreshing = false;
      this._forceRefresh = false;

      if (result.isSuccess && result.data && result.data.nfToken) {
        // 토큰 갱신 성공
        mlog.info('[linegames_log] refresh-token success', {
          result,
        });

        this._nfToken = result.data.nfToken;
        this._expireUnixTS = result.data.expireUnixTS;
      } else {
        // 토큰 갱신 실패
        mlog.warn('[linegames_log] refresh-token failed', {
          result,
        });
      }
    } catch (err) {
      this._bIsRefreshing = false;
      mlog.warn('[linegames_log] refresh-token error ', {
        message: err.message,
        stack: err.stack,
      });
    }
  }

  refreshTokenIfExpired(): Promise<void> {
    if (this._canRefreshToken()) {
      this.refresh();
    }
    return Promise.resolve();
  }
}
