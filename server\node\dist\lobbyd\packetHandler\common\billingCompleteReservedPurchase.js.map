{"version": 3, "file": "billingCompleteReservedPurchase.js", "sourceRoot": "", "sources": ["../../../../src/lobbyd/packetHandler/common/billingCompleteReservedPurchase.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;;;;;;;;;;;;;;;;;;;;AAE/E,oDAA4B;AAC5B,oDAAuB;AACvB,oDAA+B;AAE/B,kEAA0C;AAC1C,oEAA4C;AAC5C,+DAAiD;AACjD,qDAA8D;AAG9D,yDAAiE;AAEjE,iGAE2D;AAC3D,+HAAuG;AACvG,yCAA4C;AAC5C,qDAM4B;AAC5B,uDAAyC;AACzC,4DAMmC;AACnC,uDAA+B;AAC/B,8CAAqE;AAErE,mFAAqE;AAErE,4GAAoF;AAGpF,+EAA+E;AAC/E,wBAAwB;AACxB,+EAA+E;AAE/E,MAAM,GAAG,GAAG,oCAAoC,CAAC;AACjD,MAAM,OAAO,GAAG,IAAI,CAAC;AA4BrB,+EAA+E;AAC/E,MAAa,0CAA0C;IACrD,6EAA6E;IAC7E,gBAAe,CAAC;IAEhB,6EAA6E;IAC7E,aAAa,CAAC,IAAU;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6EAA6E;IAC7E,IAAI,CAAC,IAAU,EAAE,MAAe;;QAC9B,IAAI,CAAC,eAAe,CAAC,iCAAgB,CAAC,SAAS,CAAC,CAAC;QAEjD,MAAM,WAAW,GAAgB,MAAM,CAAC,OAAO,CAAC;QAChD,MAAM,QAAQ,GAAiB,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QACvE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,WAAW,CAAC;QACnF,MAAM,EAAE,iBAAiB,EAAE,GAAG,gBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;QAE1D,IAAI,CAAC,qDAAyB,CAAC,qCAAqC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACpF,mBAAmB;YACnB,MAAM,IAAI,eAAM,CAAC,oBAAoB,EAAE,mBAAU,CAAC,cAAc,EAAE;gBAChE,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;SACJ;QAED,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,KAAK,aAAa;YAC9B,CAAC,CAAC,MAAA,WAAW,CAAC,eAAe,mCAAI,IAAI;YACrC,CAAC,CAAC,MAAA,WAAW,CAAC,SAAS,mCAAI,IAAI,CAAC;QAEpC;;;WAGG;QACH,MAAM,WAAW,GAAG,aAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,eAAM,CACd,6BAA6B,EAC7B,mBAAU,CAAC,kDAAkD,EAC7D,EAAE,WAAW,EAAE,CAChB,CAAC;SACH;QACD,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QACtC,IAAI,YAAY,EAAE;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAC/C,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,CAAC,YAAY,CAAC,4BAA4B,CAAC,UAAU,CAAC,EAC1D,CAAC,CACF,CAAC;YACF,IACE,MAAM,KAAK,+BAAgB,CAAC,QAAQ;gBACpC,MAAM,KAAK,+BAAgB,CAAC,oBAAoB;gBAChD,MAAM,KAAK,+BAAgB,CAAC,iCAAiC;gBAC7D,MAAM,KAAK,+BAAgB,CAAC,mBAAmB;gBAC/C,MAAM,KAAK,+BAAgB,CAAC,kBAAkB,EAC9C;gBACA,OAAO,eAAK,CAAC,MAAM;qBAChB,kBAAkB,CACjB,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,iBAAiB,EACjB,OAAO,EACP,SAAS,EACT,IAAI,CAAC,MAAM,CACZ;qBACA,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBACb,MAAM,IAAI,eAAM,CAAC,4BAA4B,EAAE,mBAAU,CAAC,wBAAwB,EAAE;wBAClF,MAAM;wBACN,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5E,GAAG,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC,CAAC;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;oBACZ,IAAI,GAAG,CAAC,OAAO,EAAE;wBACf,MAAM,IAAI,eAAM,CAAC,4BAA4B,EAAE,mBAAU,CAAC,wBAAwB,EAAE;4BAClF,MAAM;4BACN,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;4BAC5E,GAAG;yBACJ,CAAC,CAAC;qBACJ;yBAAM;wBACL,MAAM,IAAI,eAAM,CACd,uCAAuC,EACvC,mBAAU,CAAC,yBAAyB,EACpC;4BACE,MAAM;4BACN,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;yBAC7E,CACF,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;aACN;SACF;QAED,kEAAkE;QAClE,IAAI,8BAAiD,CAAC;QACtD,IAAI,cAA8B,CAAC;QACnC,IAAI,WAAW,CAAC,WAAW,KAAK,qCAAsB,CAAC,QAAQ,EAAE;YAC/D,cAAc,GAAG,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;YAClF,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,aAAa,GAAG,UAAU,CAAC;gBAC1C,cAAc,CAAC,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC,YAAY,GAAG,yBAAe,CAAC;aACtF;SACF;QAED,IAAI,kBAAsC,CAAC;QAC3C,IAAI,IAAA,mDAAoC,EAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;YACrE,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACxE,MAAM,iBAAiB,GAAG,WAAW,CAAC,eAAe,GAAG,0BAAgB,CAAC;YACzE,MAAM,mBAAmB,GAAG,WAAW,CAAC,iBAAiB,GAAG,0BAAgB,CAAC;YAE7E,IAAI,CAAC,OAAO,IAAI,UAAU,GAAG,OAAO,CAAC,mBAAmB,EAAE;gBACxD,kBAAkB,GAAG;oBACnB,KAAK,EAAE,WAAW,CAAC,EAAE;oBACrB,iBAAiB,EAAE,UAAU,GAAG,iBAAiB;oBACjD,mBAAmB,EAAE,UAAU,GAAG,iBAAiB,GAAG,mBAAmB;iBAC1E,CAAC;aACH;iBAAM;gBACL,kBAAkB,GAAG,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,iBAAiB,GACrB,UAAU,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,UAAU,CAAC,GAAG,iBAAiB,CAAC;gBAC9E,kBAAkB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;gBACzD,kBAAkB,CAAC,mBAAmB,GAAG,iBAAiB,GAAG,mBAAmB,CAAC;aAClF;SACF;QAED,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,oCAAoC,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACjE,CAAC,CAAC;aACD,IAAI,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC7B,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC;YAEzC,IAAI,cAAc,CAAC,OAAO,KAAK,IAAI,EAAE;gBACnC,kDAAkD;gBAElD,MAAM,SAAS,GAAG,MAAA,MAAC,cAAc,CAAC,IAAY,0CAAE,SAAS,mCAAI,IAAI,CAAC;gBAElE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACtB,EAAE,EAAE,SAAS;oBACb,GAAG,EAAE,MAAA,WAAW,CAAC,WAAW,mCAAI,IAAI;oBACpC,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBAC7B,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,EAAE,EAAE,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,WAAW,EAAE,mCAAI,IAAI;oBAC1C,GAAG,EAAE,MAAA,IAAI,CAAC,GAAG,mCAAI,IAAI;oBACrB,IAAI,EAAE,MAAA,IAAI,CAAC,UAAU,mCAAI,IAAI;oBAC7B,SAAS,EAAE,MAAA,IAAI,CAAC,cAAc,mCAAI,IAAI;oBACtC,EAAE,EAAE,IAAI,CAAC,SAAS;oBAClB,UAAU,EAAE,MAAA,IAAI,CAAC,SAAS,mCAAI,IAAI;oBAClC,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,GAAG,EAAE,SAAS;oBACd,QAAQ,EAAE,WAAW,CAAC,OAAO;oBAC7B,cAAc,EAAE,WAAW,CAAC,OAAO;oBACnC,SAAS,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,IAAI;oBAChC,QAAQ,EAAE,MAAA,IAAI,CAAC,OAAO,mCAAI,IAAI;oBAC9B,IAAI,EAAE,MAAA,IAAI,CAAC,IAAI,mCAAI,IAAI;oBACvB,IAAI,EAAE,MAAA,IAAI,CAAC,IAAI,mCAAI,IAAI;iBACxB,CAAC,CAAC;gBACH,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;oBAC/B,GAAG;oBACH,OAAO;oBACP,IAAI,EAAE,CAAC;oBACP,IAAI,EAAE,wCAAyB,CAAC,IAAI;oBACpC,UAAU,EAAE,SAAS;oBACrB,YAAY,EAAE,MAAA,WAAW,CAAC,WAAW,mCAAI,IAAI;oBAC7C,QAAQ,EAAE,WAAW,CAAC,OAAO;oBAC7B,QAAQ,EAAE,MAAA,MAAC,cAAc,CAAC,IAAY,0CAAE,OAAO,mCAAI,IAAI;iBACxD,CAAC,CAAC;gBAEH,qBAAqB;gBAErB,eAAe;gBACf,uCAAuC;gBACvC,IAAI;oBACF,MAAM,MAAM,GACV,MAAM,uCAAuC,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;oBAE7E,IAAI,CAAC,MAAM,EAAE;wBACX,OAAO;qBACR;oBAED,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;oBAC5B,8BAA8B,GAAG,MAAM,CAAC,uBAAuB,CAAC;iBACjE;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,CAAU,CAAC;oBACf,IAAI,GAAG,YAAY,eAAM,EAAE;wBACzB,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC;qBACpF;yBAAM,IAAI,GAAG,YAAY,KAAK,EAAE;wBAC/B,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC;qBAChD;yBAAM;wBACL,CAAC,GAAG,GAAG,CAAC;qBACT;oBACD,cAAI,CAAC,KAAK,CAAC,IAAI,GAAG,uCAAuC,EAAE;wBACzD,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,WAAW;wBACX,cAAc;wBACd,KAAK,EAAE,CAAC;qBACT,CAAC,CAAC;iBACJ;aACF;iBAAM;gBACL,cAAI,CAAC,IAAI,CAAC,IAAI,GAAG,sBAAsB,EAAE;oBACvC,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,WAAW;oBACX,cAAc;iBACf,CAAC,CAAC;aACJ;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,IAAA,iCAAuB,EAC5B,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAC3D,IAAI,CAAC,MAAM,EACX,cAAc,EACd,kBAAkB,CACnB,CAAC,IAAI,CAAC,GAAG,EAAE;gBACV,IAAI,cAAc,EAAE;oBAClB,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;oBACpD,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;wBACrB,GAAG,EAAE,EAAE,eAAe,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,EAAE;qBACrE,CAAC,CAAC;oBAEH,IAAI,UAAU,GAAG,IAAI,CAAC;oBACtB,IAAI,8BAA8B,EAAE;wBAClC,UAAU,GAAG,WAAW,CAAC,WAAW,GAAG,8BAA8B,CAAC,MAAM,CAAC;qBAC9E;oBACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;wBAClC,GAAG;wBACH,OAAO;wBACP,EAAE,EAAE,KAAK;wBACT,IAAI,EAAE,eAAe,CAAC,6BAA6B,CAAC,WAAW,CAAC;wBAChE,UAAU,EAAE,WAAW,CAAC,QAAQ;wBAChC,UAAU,EACR,WAAW,CAAC,QAAQ,KAAK,kCAAmB,CAAC,SAAS;4BACpD,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,WAAW,CAAC,WAAW;wBAC3C,GAAG,EAAE,CAAC;wBACN,OAAO,EAAE,EAAE;wBACX,WAAW,EAAE,KAAK,CAAC,kCAAkC,CACnD,WAAW,CAAC,oBAAoB,CACjC;wBACD,WAAW,EAAE,KAAK;qBACnB,CAAC,CAAC;iBACJ;gBAED,IAAI,kBAAkB,EAAE;oBACtB,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;oBAC5D,gBAAC,CAAC,KAAK,CAAa,QAAQ,CAAC,IAAI,EAAE;wBACjC,GAAG,EAAE;4BACH,2BAA2B,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,kBAAkB,EAAE;yBAChF;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,MAAM,MAAM,GAAG;gBACb;oBACE,qBAAqB,EAAE,KAAK,CAAC,iBAAiB,CAAC,mBAAmB;oBAClE,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;oBACzB,UAAU,EAAE,CAAC;iBACd;aACF,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;QAC9F,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,cAAI,CAAC,IAAI,CAAC,0CAA0C,EAAE;gBACpD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,UAAU,EAAE,KAAK;aAClB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,cAAc,CAAe,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAjRD,gGAiRC;AAED,KAAK,UAAU,oCAAoC,CACjD,IAAU,EACV,OAA8B;;IAE9B,mFAAmF;IACnF,MAAM,eAAe,GAAW,MAAA,IAAI,CAAC,SAAS,mCAAI,EAAE,CAAC;IAErD,QAAQ,IAAI,CAAC,SAAS,EAAE;QACtB,KAAK,aAAa,CAAC,CAAC;YAClB,OAAO,eAAK,CAAC,kBAAkB,CAAC,8BAA8B,CAC5D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,IAAI,CAAC,SAAS,EACd,eAAe,EAEf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EAEZ,SAAS,EAAE,iCAAiC;YAE5C,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,eAAsB,EAC9B,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,EAEZ,OAAO,CAAC,eAAe,CACxB,CAAC;SACH;QAED,KAAK,iBAAiB,CAAC,CAAC;YACtB,OAAO,eAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAC3D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,IAAI,CAAC,SAAS,EACd,eAAe,EAEf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EAEZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,eAAsB,EAC9B,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,CACb,CAAC;SACH;QAED,KAAK,aAAa,CAAC,CAAC;YAClB,OAAO,eAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAC3D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,IAAI,CAAC,SAAS,EACd,eAAe,EAEf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EAEZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,eAAsB,EAC9B,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,EAEZ,OAAO,CAAC,SAAS,CAClB,CAAC;SACH;QAED,KAAK,OAAO,CAAC,CAAC;YACZ,OAAO,eAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAC3D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,IAAI,CAAC,SAAS,EACd,eAAe,EAEf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EAEZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,eAAsB,EAC9B,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,CACb,CAAC;SACH;QAED;YACE,gBAAM,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KACxD;AACH,CAAC;AAED,KAAK,UAAU,uCAAuC,CACpD,IAAU,EACV,SAAiB,EACjB,OAAe;IAEf,MAAM,WAAW,GAAG,KAAK,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;IACjE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,eAAM,CACd,6BAA6B,EAC7B,mBAAU,CAAC,kDAAkD,EAC7D,EAAE,SAAS,EAAE,CACd,CAAC;KACH;IACD,MAAM,uBAAuB,GAAG,0BAAW,CAAC,4BAA4B,CACtE,IAAI,EACJ,WAAW,EACX,OAAO,CACR,CAAC;IACF,IAAI,CAAC,uBAAuB,EAAE;QAC5B,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,EAAE,iBAAiB,EAAE,GAAG,gBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;IAC1D,OAAO,IAAA,2CAAiC,EACtC,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAC3D,IAAI,CAAC,MAAM,EACX,uBAAuB,CACxB,CAAC,IAAI,CAAC,GAAG,EAAE;QACV,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;QAChE,MAAM,IAAI,GAAS;YACjB,GAAG,EAAE;gBACH,0BAA0B,EAAE;oBAC1B,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,uBAAuB;iBACzD;aACF;SACF,CAAC;QAEF,OAAO,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC;IAC3C,CAAC,CAAC,CAAC;AACL,CAAC"}