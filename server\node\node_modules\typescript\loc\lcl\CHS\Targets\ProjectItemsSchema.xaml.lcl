﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\Targets\ProjectItemsSchema.xaml" PsrId="22" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";&lt;ContentType&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptcompile@ContentType@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[TypeScript file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 文件]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";&lt;ItemType&gt;" ItemType="0" PsrId="210" Leaf="false">
    <Disp Icon="Str" Disp="true" LocTbl="false" />
    <Item ItemId="0;typescriptcompile@ItemType@DisplayName" ItemType="47;XML:Attr:DisplayName" PsrId="210" Leaf="true">
      <Str Cat="AppData">
        <Val><![CDATA[TypeScript file]]></Val>
        <Tgt Cat="AppData" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 文件]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
</LCX>