"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const merror_1 = require("../../../motiflib/merror");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const server_1 = require("../../server");
const puAdminUserLoad_1 = __importDefault(require("../../../mysqllib/sp/puAdminUserLoad"));
const taAdminGetUserIdsByPubId_1 = __importDefault(require("../../../mysqllib/txn/taAdminGetUserIdsByPubId"));
const bluebird_1 = require("bluebird");
module.exports = (req, res) => {
    mlog_1.default.info('[RX] /user/getGameInfo_20220526', { body: req.body });
    req.url;
    const curTimeUtc = mutil.curTimeUtc();
    let { nids } = req.body;
    if (!nids) {
        return res.status(400).json({
            isSuccess: false,
            msg: 'Invalid your parameter',
            errorCd: 'INVALID_PARAMETER',
        });
    }
    const splitNids = nids.replace(/ /g, '').split(',');
    const { authDbConnPool, userDbConnPoolMgrs, serviceLayoutMgr } = typedi_1.default.get(server_1.AdminService);
    const resp = {
        isSuccess: true,
        msg: 'request success',
    };
    const userServerIds = {};
    const userPubIds = {};
    return Promise.resolve()
        .then(() => {
        const promises = splitNids.map((nid) => {
            return (0, taAdminGetUserIdsByPubId_1.default)(authDbConnPool.getPool(), nid);
        });
        return Promise.all(promises);
    })
        .then((results) => {
        if (!results) {
            throw new merror_1.MError(`not exist nid(${nids}).`, merror_1.MErrorCode.NOT_EXIST_NID, req.body);
        }
        let users = [];
        results.forEach((elem) => {
            users = users.concat(elem);
        });
        splitNids.forEach((nid) => {
            const index = users.findIndex((user) => {
                return user.pubId === nid;
            });
            if (index === -1) {
                throw new merror_1.MError(`not exist nid(${nid}).`, merror_1.MErrorCode.NOT_EXIST_NID, req.body);
            }
        });
        const promises = users.map((user) => {
            const worldId = user.worldId;
            const pubId = user.pubId;
            const userDbConnPoolMgr = userDbConnPoolMgrs[worldId];
            if (!userDbConnPoolMgr) {
                throw new merror_1.MError('no gameServerId in service layout', merror_1.MErrorCode.ADMIN_INVALID_PARAMETER, {
                    worldId,
                });
            }
            userServerIds[user.userId] = worldId;
            userPubIds[user.userId] = pubId;
            return (0, puAdminUserLoad_1.default)(userDbConnPoolMgr.getPoolByUserIdAndShardFuncName(user.userId, serviceLayoutMgr.getUserDbShardFunction(worldId), user.worldId), user.userId, curTimeUtc);
        });
        return Promise.all(promises);
    })
        .then((results) => {
        resp.data = [];
        return bluebird_1.Promise.reduce(results, (_, user) => {
            //라인게임즈에서 appStoreCd, countryCreated는  'FLOOR_STORE', 'KR'로 하도록 전달받음.
            // 히스토리 --> https://jira.line.games/browse/UWO-11578
            // return mhttp.platformBillingApi
            //   .queryCash(user.userId, 'FLOOR_STORE', 'KR')
            //   .then((data: { coinCd: string; balance: number; paymentType: string }[]) => {
            //     let coinBalance: number = 0;
            //     for (const elem of data) {
            //       if (elem.coinCd === 'red_gem') {
            //         coinBalance += elem.balance;
            //       }
            //     }
            return Promise.resolve().then(() => {
                resp.data.push({
                    nick: user.name,
                    level: user.level,
                    nid: userPubIds[user.userId],
                    serverId: userServerIds[user.userId],
                    gameUserId: user.userId,
                    coinCd: 'red_gem',
                });
            });
        }, {});
    })
        .then(() => {
        mlog_1.default.info('[TX] /user/getGameInfo_20220526', { body: resp });
        res.json(resp);
    });
};
//# sourceMappingURL=getGameInfo_20220526.js.map