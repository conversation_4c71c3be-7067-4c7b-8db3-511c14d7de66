// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import assert from 'assert';

import cms from '../../cms';
import * as cmsEx from '../../cms/ex';
import * as mutil from '../../motiflib/mutil';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from '../user';
import { TryData, Changes, CHANGE_TASK_RESULT, CHANGE_TASK_REASON } from './userChangeTask';
import Mate, { MateExpChange, MateUtil } from '../mate';
import mlog from '../../motiflib/mlog';
import { MError, MErrorCode } from '../../motiflib/merror';
import UserMates from '../userMates';
import Ship, { ShipNub, ShipCargoChange, makeShipRndStats } from '../ship';
import Fleet from '../fleet';
import { MAIL_STATE } from '../const';
import { BattleReward } from '../battleResult';
import { MateEquipmentNub, SHIP_ASSIGNMENT } from '../../motiflib/model/lobby';
import { RestrictedProduct, FixedTermProduct } from '../userCashShop';
import { ContributionShopRestrictedProduct } from '../userContributionShop';
import { GameOverLosses, SailingChange } from '../userSailing';
import { GAME_ENTER_STATE, GAME_STATE } from '../../motiflib/model/lobby/gameState';
import {
  All,
  Challenge,
  FleetDispatchActionSync,
  FleetDispatchEndViewSync,
  FleetDispatchRewardSync,
  QuestContext,
} from '../type/sync';
import { LobbyService } from '../server';
import { Attendance } from '../userAttendance';
import { PointChange, InsuranceUnpaidChange, LGCashParam } from '../userPoints';
import { BattleFreeTakebackChange, BattleQuickModeChange, BattleEndResult } from '../userBattle';
import { ITEM_TYPE } from '../../cms/itemDesc';
import { ItemChange, QuestItem } from '../userInven';
import { QuestCandidate, QuestUtil } from '../questUtil';
import { SHIP_SLOT_TYPE } from '../../cms/shipSlotDesc';
import {
  PickedRandomReward,
  PickedRandomRewardElem,
  RewardCmsElemItemExtra,
  RewardCmsElemMateEquipmentExtra,
  RewardCmsElemShipExtra,
  RewardCmsElemShipSlotItemExtra,
} from './rewardAndPaymentChangeSpec';
import { EnergyChange } from '../userEnergy';
import { GameStateChange } from '../userState';
import { SHIELD_CMS_ID } from '../../cms/shieldDesc';
import { isCash } from '../../cms/pointDesc';
import { Village } from '../userVillage';
import { StatHelper } from '../statHelper';
import { EventPageType } from '../../cms/eventPageDesc';
import UserPassEvent, { PassEventMission } from '../userPassEvent';
import { GuildShopRestrictedProduct } from '../userGuildShop';
import { ExploreQuickModeChange, ExploreTicketChange } from '../userExplore';
import { TradeAreaChange } from '../userTradeArea';
import { ShipBpSailMasteryExpLevelChange, SHIP_CULTURE_TYPE } from '../shipBlueprint';
import { UserShipBuildingChange } from '../userShipBlueprints';
import { EventGameChange } from '../userEventGames';
import { FleetDispatchRewardSyncResult } from '../dispatchRewardUtil';
import {
  GetMonthlyContentsResetCount,
  HasContentsResetTimePassed,
  SECONDS_PER_DAY,
} from '../../formula';
import { DailySubscriptionDesc } from '../../cms/dailySubscription';
import { REWARD_TYPE, RewardCmsElem } from '../../cms/rewardDesc';
import { UserTitleDesc } from '../../cms/userTitleDesc';
import { UserTitle } from '../userTitles';
import { Shield } from '../userShield';
import { AccumulateParam } from '../userAchievement';
import { ReentryType } from '../../cms/reentryCostDesc';
import { ClashSession } from '../clash';
import { Reentry } from '../userReentry';
import { BuilderMailCreateParams, MailCreatingParams } from '../../motiflib/mailBuilder';
import { ShipBuildUtil } from '../../motiflib/model/lobby/shipBuildUtil';

// -------------------------------------------------------------------------------------------------
// * operator 작성 시 CHANGE_TASK_RESULT 확인 필수! ( FULL_MAX 등 주의 사항 )
// -------------------------------------------------------------------------------------------------

// -------------------------------------------------------------------------------------------------
export function opAddRewardExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  jobType: cmsEx.JOB_TYPE,
  amount: number,
  fleetIndex: number,
  bOnlyMateExp: boolean = false
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.energy) {
    tryData.energy = user.userEnergy.clone();
  }

  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }
  if (!changes.syncAdd.points) {
    changes.syncAdd.points = {};
  }
  if (!changes.mateExp) {
    changes.mateExp = {};
  }
  if (!changes.mateExp[jobType]) {
    changes.mateExp[jobType] = [];
  }
  if (!changes.actualGainExp) {
    changes.actualGainExp = {};
  }

  let bChanged = false;

  // 국가 순위 보너스
  const { nationManager } = Container.get(LobbyService);
  const nationRank = nationManager.getPowerRank(user.nationCmsId);
  if (nationRank) {
    const nationRankingEffectCms = cms.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.EXP];
    const nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];

    amount = Math.floor((amount * nationRankBonus) / 1000);
  }

  // reward 를 통해 경험치를 얻는 경우 getShipSlotExpJobPanelty 적용하지 않는다.

  // mate
  const fleet = user.userFleets.getFleet(fleetIndex);
  const mateCmsIds = fleet.getAllMateCmsIds();
  for (const mateCmsId of mateCmsIds) {
    const mate = tryData.mates.getMate(mateCmsId);
    const oldLevel = mate.getLevel(jobType);
    const newLevelAndExp = mate.calcExpLevel(
      jobType,
      amount,
      tryData.stats,
      tryData.userLevel,
      fleetIndex
    );

    if (newLevelAndExp) {
      mate.setExpLevel(jobType, newLevelAndExp, tryData.stats, null);

      const change = changes.mateExp[jobType].find((elem) => {
        return elem.mateCmsId === mateCmsId;
      });

      if (change) {
        change.exp = newLevelAndExp.exp;
        change.level = newLevelAndExp.level;
      } else {
        changes.mateExp[jobType].push({
          mateCmsId,
          exp: newLevelAndExp.exp,
          level: newLevelAndExp.level,
          oldLevel,
        });
      }

      bChanged = true;
    }
  }

  // user exp, level
  if (!bOnlyMateExp) {
    const expLevelChange = User.calcExpLevel(
      amount,
      mutil.curTimeUtc(),
      tryData.userExp,
      tryData.userLevel,
      tryData.stats,
      tryData.energy
    );
    if (expLevelChange) {
      tryData.userExp = expLevelChange.exp;
      tryData.userLevel = expLevelChange.level;

      changes.syncAdd.user.exp = tryData.userExp;
      changes.syncAdd.user.level = tryData.userLevel;

      bChanged = true;
    }

    if (expLevelChange && expLevelChange.energyChange) {
      _.merge<All, All>(
        changes.syncAdd,
        tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add
      );

      bChanged = true;
    }
  }

  if (!bChanged) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!changes.actualGainExp[jobType]) {
    changes.actualGainExp[jobType] = 0;
  }
  changes.actualGainExp[jobType] += amount;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddAdventureExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }
  if (!tryData.energy) {
    tryData.energy = user.userEnergy.clone();
  }

  if (!changes.mateExp) {
    changes.mateExp = {};
  }
  if (!changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE]) {
    changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE] = [];
  }
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }
  if (!changes.syncAdd.points) {
    changes.syncAdd.points = {};
  }
  if (!changes.actualGainExp) {
    changes.actualGainExp = {};
  }

  const clearAdventurerExp = 0; // TODO: 재해 해소에 따른 보너스를 구현합니다.
  const mateExpChanges: MateExpChange[] = [];
  const admiralGainedExp = tryData.mates.buildMateAdventureExpChangesBySailing(
    amount,
    clearAdventurerExp,
    mateExpChanges,
    tryData.fleets,
    tryData.stats,
    tryData.shipBlueprints,
    user.nationCmsId,
    cmsEx.FirstFleetIndex,
    tryData.userLevel
  );

  for (const elem of mateExpChanges) {
    const tryDataMate = tryData.mates.getMate(elem.mateCmsId);
    const oldLevel = tryDataMate.getLevel(cmsEx.JOB_TYPE.ADVENTURE);
    tryDataMate.setExpLevel(cmsEx.JOB_TYPE.ADVENTURE, elem, tryData.stats, null);

    const change = changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].find((changesElem) => {
      return changesElem.mateCmsId === elem.mateCmsId;
    });

    if (change) {
      change.exp = elem.exp;
      change.level = elem.level;
    } else {
      changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].push({
        mateCmsId: elem.mateCmsId,
        exp: elem.exp,
        level: elem.level,
        oldLevel,
      });
    }
  }

  const expLevelChange = User.calcExpLevel(
    admiralGainedExp,
    mutil.curTimeUtc(),
    tryData.userExp,
    tryData.userLevel,
    tryData.stats,
    tryData.energy
  );

  if (expLevelChange) {
    tryData.userExp = expLevelChange.exp;
    tryData.userLevel = expLevelChange.level;

    changes.syncAdd.user.exp = tryData.userExp;
    changes.syncAdd.user.level = tryData.userLevel;
  }

  if (expLevelChange && expLevelChange.energyChange) {
    _.merge<All, All>(
      changes.syncAdd,
      tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add
    );
  }

  if (!changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE]) {
    changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] = 0;
  }
  changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] += amount;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddDiscoveryExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }
  if (!tryData.energy) {
    tryData.energy = user.userEnergy.clone();
  }

  if (!changes.mateExp) {
    changes.mateExp = {};
  }
  if (!changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE]) {
    changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE] = [];
  }
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }
  if (!changes.syncAdd.points) {
    changes.syncAdd.points = {};
  }
  if (!changes.actualGainExp) {
    changes.actualGainExp = {};
  }
  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }

  const mateExpChanges: MateExpChange[] = [];
  const admiralGainedExp = tryData.mates.buildMateAdventureExpChangesByDiscovery(
    amount,
    mateExpChanges,
    tryData.fleets,
    tryData.stats,
    tryData.shipBlueprints,
    user.nationCmsId,
    cmsEx.FirstFleetIndex,
    tryData.userLevel
  );

  for (const elem of mateExpChanges) {
    const tryDataMate = tryData.mates.getMate(elem.mateCmsId);
    const oldLevel = tryDataMate.getLevel(cmsEx.JOB_TYPE.ADVENTURE);
    tryDataMate.setExpLevel(cmsEx.JOB_TYPE.ADVENTURE, elem, tryData.stats, null);

    const change = changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].find((changesElem) => {
      return changesElem.mateCmsId === elem.mateCmsId;
    });

    if (change) {
      change.exp = elem.exp;
      change.level = elem.level;
    } else {
      changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].push({
        mateCmsId: elem.mateCmsId,
        exp: elem.exp,
        level: elem.level,
        oldLevel,
      });
    }
  }

  const expLevelChange = User.calcExpLevel(
    admiralGainedExp,
    mutil.curTimeUtc(),
    tryData.userExp,
    tryData.userLevel,
    tryData.stats,
    tryData.energy
  );

  if (expLevelChange) {
    tryData.userExp = expLevelChange.exp;
    tryData.userLevel = expLevelChange.level;

    changes.syncAdd.user.exp = tryData.userExp;
    changes.syncAdd.user.level = tryData.userLevel;
  }

  if (expLevelChange && expLevelChange.energyChange) {
    _.merge<All, All>(
      changes.syncAdd,
      tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add
    );
  }

  if (!changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE]) {
    changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] = 0;
  }
  changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] += amount;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// @param bIsBound 획득일 때만 사용 됨.
// @param bIfExceededSendMail 넘칠경우 메일로 보냄 (bAllowAddToLimitIfExceeded가 true면 메일로 안보냄)
// @param isForcedBound 강제로 귀속여부 결정
export function opAddItem(
  user: User,
  tryData: TryData,
  changes: Changes,
  itemCmsId: number,
  amount: number,
  bAllowOverInven: boolean,
  bAllowAddToLimitIfExceeded: boolean,
  extraStr: string,
  inBIsAccum: boolean,
  inBIsBound: boolean = true,
  bIfExceededAddMail: boolean = false,
  bIfExceededLimitAddMail: boolean = false,
  isForcedBound?: boolean
): CHANGE_TASK_RESULT {
  const itemCms = cms.Item[itemCmsId];
  if (!itemCms || itemCms.type === ITEM_TYPE.RANDOM_QUEST) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // try data
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const accums: AccumulateParam[] = [];
  let itemChange: ItemChange;

  if (amount > 0) {
    // 지급하는 경우

    const extra: RewardCmsElemItemExtra = extraStr ? JSON.parse(extraStr) : undefined;

    // 귀속 여부 결정.
    let bIsBound = true;
    if (itemCms.isCashMarket && ((extra && extra.isBound === 0) || !inBIsBound)) {
      bIsBound = false;
    }

    // 귀속 여부를 정하는 규칙이 위에 있지만.. 예외적인 상황(로그인 아이템 변경 changeItems)
    if (isForcedBound !== undefined) {
      bIsBound = isForcedBound;
    }

    let bIsAccum = false;
    if (extra) {
      if (extra.isAccum === 1 && inBIsAccum) {
        bIsAccum = true;
      }
    } else if (inBIsAccum) {
      bIsAccum = true;
    }

    const result = tryData.inven.itemInven.calcReceivable(
      itemCmsId,
      amount,
      bIsBound,
      bAllowOverInven
    );
    if (result.receivable !== amount) {
      if (bAllowAddToLimitIfExceeded) {
        if (bIfExceededLimitAddMail) {
          const exceededAmount = amount - result.receivable;
          const attachment: RewardCmsElem[] = [
            {
              Id: itemCmsId,
              Type: REWARD_TYPE.ITEM,
              Quantity: exceededAmount,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
        }

        amount = result.receivable;
        if (amount === 0) {
          return CHANGE_TASK_RESULT.OK;
        }
      } else {
        if (bIfExceededAddMail) {
          const exceededRewards: RewardCmsElem[] = [
            {
              Id: itemCmsId,
              Type: REWARD_TYPE.ITEM,
              Quantity: result.excessSpace,
              Extra: extraStr,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), exceededRewards);
        } else {
          mlog.warn('[RewardTask] opAddItem. Exceeds limit.', {
            userId: user.userId,
            cmsId: itemCmsId,
            unboundOption: !bIsBound ? { inBIsBound, extraStr } : undefined,
            toAdd: amount,
            receivableResult: result,
          });
          return result.excessMaxHavable
            ? CHANGE_TASK_RESULT.ITEM_MAX_HAVABLE
            : result.excessSpace
            ? CHANGE_TASK_RESULT.INVEN_FULL
            : CHANGE_TASK_RESULT.INTERNAL_ERROR;
        }
      }
    }

    itemChange = tryData.inven.itemInven.buildItemChange(itemCmsId, amount, bIsAccum, bIsBound);
  } else {
    // 차감하는 경우

    if (inBIsBound !== true) {
      mlog.debug('[RewardTask] opAddItem. Not supported option.', {
        userId: user.userId,
        cmsId: itemCmsId,
        inBIsBound,
      });
    }
    const curCount = tryData.inven.itemInven.getCount(itemCmsId);
    if (curCount + amount < 0) {
      mlog.warn('[RewardTask] opAddItem. Not enough item.', {
        userId: user.userId,
        cmsId: itemCmsId,
        curCount,
        toRemove: amount,
      });
      return CHANGE_TASK_RESULT.NOT_ENOUGH_ITEM;
    }
    itemChange = tryData.inven.itemInven.buildItemChange(itemCmsId, amount, inBIsAccum);
  }

  assert(itemChange);
  _.merge<All, All>(
    changes.syncAdd,
    tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add
  );

  if (accums && accums.length > 0) {
    if (!changes.itemGainAccumParams) {
      changes.itemGainAccumParams = [];
    }

    changes.itemGainAccumParams.push(
      ...accums.map((acc) => {
        return { targets: acc.targets, addedValue: acc.addedValue };
      })
    );
  }

  return CHANGE_TASK_RESULT.OK;
}

/**
 *
 * @param user
 * @param tryData
 * @param changes
 * @param cmsId
 * @param addedAmount
 * @param bCutOffLast3Digits 끝 3자리 절삭이 필요한지.
 * @param bPermitExchange 포인트가 부족할 경우 환전을 허용할 것인지.
 * @param lgCashParam lg cash 사용에 필요한 인자
 * @param bIsNotPermitAddToHardCapLimitLine 지급 전 포인트가 hard cap 을 넘지 않았으나 지급 후 포인트가 hard cap 을
 * 넘을 경우 지급 후 금액을 hard cap 금액으로 맞추지 않을지 여부
 * @param bIfExceededLimitAddMail 지급 후 포인트가 hard cap을 넘는다면 나머지 포인트는 메일로
 * @returns
 */
export function opAddPoint(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  addedAmount: number,
  bCutOffLast3Digits: boolean,
  bPermitExchange: boolean,
  lgCashParam: LGCashParam,
  bIsNotPermitAddToHardCapLimitLine: boolean,
  bIfExceededLimitAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (addedAmount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // 환전 허용할 경우 lgCashParam 값이 있어야 된다.
  assert(!bPermitExchange || lgCashParam);

  const pointCms = cms.Point[cmsId];
  if (!pointCms || cmsId === cmsEx.EnergyPointCmsId || cmsId === cmsEx.CashShopMileage) {
    mlog.error('[RewardTask] opAddPoint. Invalid point.', {
      userId: user.userId,
      cmsId: cmsId,
    });
    throw new MError('cannot-reward-point', MErrorCode.INVALID_POINT);
  }

  // try data
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }

  const oldPoint = tryData.points.getPoint(cmsId);

  if (cmsId === cmsEx.DucatPointCmsId && addedAmount > 0 && bCutOffLast3Digits) {
    let digit = 0;
    let temp = addedAmount;
    while (temp >= 1) {
      temp /= 10;
      digit++;
    }
    if (digit > 3) {
      const divide = Math.pow(10, digit - 3);
      addedAmount = Math.floor(addedAmount / divide) * divide;
    }
  }

  if (addedAmount > 0) {
    let newPoint = oldPoint + addedAmount;
    if (oldPoint >= pointCms.hardCap) {
      return CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP;
    } else if (newPoint > pointCms.hardCap) {
      if (bIsNotPermitAddToHardCapLimitLine) {
        return CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP;
      } else {
        if (bIfExceededLimitAddMail) {
          const exceededPoint = newPoint - pointCms.hardCap;
          const attachment: RewardCmsElem[] = [
            {
              Id: cmsId,
              Type: REWARD_TYPE.POINT,
              Quantity: exceededPoint,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);

          addedAmount -= exceededPoint;
        }

        newPoint = pointCms.hardCap;
      }
    }
    tryData.points.setPointForChangeTask(cmsId, newPoint);

    if (isCash(cmsId)) {
      if (!changes.cashGains) {
        changes.cashGains = [];
      }
      // TODO 중국 처리 코드에서 호출될때 사유가 넘겨져야함.
      assert(lgCashParam && lgCashParam.gainReason);
      changes.cashGains.push({
        cmsId: cmsId,
        amount: addedAmount,
        reason: lgCashParam.gainReason,
      });
    } else {
      if (!changes.syncAdd.points) {
        changes.syncAdd.points = {};
      }
      changes.syncAdd.points[cmsId] = {
        cmsId: cmsId,
        value: tryData.points.getPoint(cmsId),
      };
    }

    if (!changes.pointsGainAmount) {
      changes.pointsGainAmount = {};
    }
    if (!changes.pointsGainAmount[cmsId]) {
      changes.pointsGainAmount[cmsId] = addedAmount;
    } else {
      changes.pointsGainAmount[cmsId] += addedAmount;
    }
  } else {
    const pcChanges = tryData.points.buildPointAndCashChangesByPayment(
      [
        {
          cmsId,
          cost: -addedAmount,
        },
      ],
      bPermitExchange,
      lgCashParam,
      false
    );
    if (!pcChanges) {
      mlog.warn('[RewardTask] opAddPoint. Not enough point.', {
        userId: user.userId,
        cmsId,
        addedAmount,
        userValue: user.userPoints.getPoint(cmsId),
        trryDataValue: tryData.points.getPoint(cmsId),
      });
      return CHANGE_TASK_RESULT.NOT_ENOUGH_POINT;
    }
    for (const change of pcChanges.pointChanges) {
      tryData.points.setPointForChangeTask(change.cmsId, change.value);
      _.merge<All, All>(changes.syncAdd, {
        points: {
          [change.cmsId]: change,
        },
      });
    }

    if (pcChanges.cashPayments && pcChanges.cashPayments.length > 0) {
      assert(lgCashParam && (lgCashParam.itemId || lgCashParam.productId));
      if (!changes.cashPayments) {
        changes.cashPayments = [];
      }
      for (const cashChange of pcChanges.cashPayments) {
        tryData.points.setPointForChangeTask(
          cashChange.cmsId,
          tryData.points.getPoint(cashChange.cmsId) - cashChange.amount
        );
        changes.cashPayments.push({
          cmsId: cashChange.cmsId,
          amount: cashChange.amount,
          lgCashParam: cashChange.lgCashParam,
          glogPointExchangeData: cashChange.glogPointExchangeData,
        });
      }
    }

    if (!changes.pointsConsumeAmountForGlog) {
      changes.pointsConsumeAmountForGlog = {};
    }
    if (!changes.pointsConsumeAmountForGlog[cmsId]) {
      changes.pointsConsumeAmountForGlog[cmsId] = addedAmount;
    } else {
      changes.pointsConsumeAmountForGlog[cmsId] += addedAmount;
    }
  } // end of if (addedAmount < 0)

  return CHANGE_TASK_RESULT.OK;
}

export function opAddMileage(
  user: User,
  tryData: TryData,
  changes: Changes,
  addedAmount: number,
  curTimeUtc: number
): CHANGE_TASK_RESULT {
  if (addedAmount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // try data
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }

  // 차감되는 경우 충분한지 비교
  if (addedAmount < 0) {
    const mileage = tryData.points.getMileage(curTimeUtc);
    if (mileage < -addedAmount) {
      return CHANGE_TASK_RESULT.NOT_ENOUGH_POINT;
    }
  }

  const mileageChanges = tryData.points.buildMileageChanges(addedAmount, curTimeUtc);
  if (!changes.mileages) {
    changes.mileages = [];
  }
  for (const mileageChange of mileageChanges) {
    const idx = changes.mileages.findIndex((elem) => elem.month === mileageChange.month);
    if (idx === -1) {
      changes.mileages.push(mileageChange);
    } else {
      changes.mileages[idx].value = mileageChange.value;
    }
  }
  tryData.points.applyMileageChanges(mileageChanges, null);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opLoadDepartSupply(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number,
  bSelectLoadsIfExceededRatio?: boolean,
  bIfExceededAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (quantity < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  let remainQuantity: number = 0;
  if (bSelectLoadsIfExceededRatio === undefined) {
    // 현재 함대에 적재 가능한 양을 받아옴.
    remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
  } else {
    // 보급품의 적재 비율을 고려하여 현재 적재 가능한 양을 받아옴.
    remainQuantity = tryData.fleets
      .getFirstFleet()
      .calcRemainQuantityForLoadType(
        cmsEx.convertCargoCmsIdToLoadPresetType(cmsId),
        tryData.stats,
        tryData.fleets
      );

    if (bSelectLoadsIfExceededRatio === true && quantity > remainQuantity) {
      return CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
    }
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  quantity = Math.min(quantity, remainQuantity);

  // load
  const firstFleetShips: { [id: number]: Ship } = tryData.fleets.getFirstFleet().getShips();
  for (const ship of _.values(firstFleetShips)) {
    const curLoad = ship.getCurLoad();
    const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
    const avail = totalCapacity - curLoad;

    if (avail <= 0) {
      continue;
    }

    const loadAmount = Math.min(avail, quantity);

    const change = {
      shipId: ship.getNub().id,
      cmsId: cmsId,
      quantity: ship.getCargoQuantity(cmsId) + loadAmount,
      pointInvested: 0,
    };
    ship.applyCargoChange(change, null, null);

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
      };
    }
    if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
      changes.syncAdd.ships[ship.getNub().id].cargos = {};
    }
    changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = change;

    quantity -= loadAmount;
    if (quantity === 0) {
      break;
    }
  }

  if (quantity > 0 && bIfExceededAddMail) {
    const attachment: RewardCmsElem[] = [
      {
        Id: cmsId,
        Type: REWARD_TYPE.DEPART_SUPPLY,
        Quantity: quantity,
      },
    ];
    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opUnloadDepartSupply(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number
): CHANGE_TASK_RESULT {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (quantity < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  let remaining = quantity;
  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();

  if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
    return CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  for (const ship of _.values(firstFleetShips)) {
    const cargo = ship.getCargo(cmsId);
    if (cargo.quantity >= remaining) {
      cargo.quantity -= remaining;
      remaining = 0;
    } else {
      remaining -= cargo.quantity;
      cargo.quantity = 0;
    }

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
        cargos: {},
      };
    }
    if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
      changes.syncAdd.ships[ship.getNub().id].cargos = {};
    }
    changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;

    if (remaining === 0) {
      break;
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opLoadTradeGoods(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number,
  bSelectLoadsIfExceededRatio?: boolean,
  shipId?: number,
  pointInvested?: number,
  bIfExceededAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (quantity < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const tradeGoodsCms = cms.TradeGoods[cmsId];
  let tradeGoodsPrice = tradeGoodsCms.basisDucat;
  if (pointInvested) {
    tradeGoodsPrice = Math.floor(pointInvested / quantity);
  }

  let remainQuantity: number = 0;
  if (bSelectLoadsIfExceededRatio === undefined) {
    // 현재 함대에 적재 가능한 양을 받아옴.
    remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
  } else {
    // 교역품의 적재 비율을 고려하여 현재 적재 가능한 양을 받아옴.
    remainQuantity = tryData.fleets
      .getFirstFleet()
      .calcRemainQuantityForLoadType(
        cmsEx.convertCargoCmsIdToLoadPresetType(cmsId),
        tryData.stats,
        tryData.fleets
      );

    if (bSelectLoadsIfExceededRatio === true && quantity > remainQuantity) {
      return CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
    }
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  quantity = Math.min(quantity, remainQuantity);

  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
  let shipIds = [];
  if (shipId) {
    shipIds.push(shipId);
  } else {
    shipIds = Object.keys(firstFleetShips);
  }

  for (const shipIdStr of shipIds) {
    const shipId = parseInt(shipIdStr, 10);
    const ship = firstFleetShips[shipId];
    const curLoad = ship.getCurLoad();
    const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
    const avail = totalCapacity - curLoad;

    if (avail <= 0) {
      continue;
    }

    const loadAmount = Math.min(avail, quantity);

    const cargo = ship.getCargo(cmsId);
    cargo.quantity += loadAmount;
    cargo.pointInvested += loadAmount * tradeGoodsPrice;

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
        cargos: {},
      };
    }
    if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
      changes.syncAdd.ships[ship.getNub().id].cargos = {};
    }
    changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;

    quantity -= loadAmount;
    if (quantity === 0) {
      break;
    }
  }

  if (quantity > 0 && bIfExceededAddMail) {
    const attachment: RewardCmsElem[] = [
      {
        Id: cmsId,
        Type: REWARD_TYPE.TRADE_GOODS,
        Quantity: quantity,
        //shipId, //! 일단 주석
        pointInvested,
      },
    ];
    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opUnloadTradeGoods(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number,
  shipId?: number
): CHANGE_TASK_RESULT {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (quantity < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  let remaining = quantity;
  let shipIds = [];
  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
  if (shipId) {
    shipIds.push(shipId);

    if (tryData.fleets.getShip(shipId).getCargoQuantity(cmsId) < quantity) {
      return CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
    }
  } else {
    shipIds = Object.keys(firstFleetShips);

    if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
      return CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
    }
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  for (const shipIdStr of shipIds) {
    const shipId = parseInt(shipIdStr, 10);
    const ship = firstFleetShips[shipId];
    const cargo = ship.getCargo(shipId);
    if (cargo.quantity >= remaining) {
      cargo.pointInvested -= Math.floor((cargo.pointInvested / cargo.quantity) * remaining);
      cargo.quantity -= remaining;
      remaining = 0;
    } else {
      remaining -= cargo.quantity;
      cargo.quantity = 0;
      cargo.pointInvested = 0;
    }

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
        cargos: {},
      };
    }
    if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
      changes.syncAdd.ships[ship.getNub().id].cargos = {};
    }
    changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;

    if (remaining === 0) {
      break;
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opLoadSmuggleGoods(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number,
  bSelectLoadsIfExceededRatio?: boolean,
  shipId?: number,
  pointInvested?: number,
  bIfExceededAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const smuggleGoodsCms = cms.SmuggleGoods[cmsId];
  let smuggleGoodsPrice = smuggleGoodsCms.basisDucat;
  if (pointInvested) {
    smuggleGoodsPrice = Math.floor(pointInvested / quantity);
  }

  let remainQuantity: number = 0;
  if (bSelectLoadsIfExceededRatio === undefined) {
    remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
  } else {
    remainQuantity = tryData.fleets
      .getFirstFleet()
      .calcRemainQuantityForLoadType(
        cmsEx.convertCargoCmsIdToLoadPresetType(cmsId),
        tryData.stats,
        tryData.fleets
      );

    if (bSelectLoadsIfExceededRatio && quantity > remainQuantity) {
      return CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
    }
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  quantity = Math.min(quantity, remainQuantity);

  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
  let shipIds = [];
  if (shipId) {
    shipIds.push(shipId);
  } else {
    shipIds = Object.keys(firstFleetShips);
  }

  for (const shipIdStr of shipIds) {
    const shipId = parseInt(shipIdStr, 10);
    const ship = firstFleetShips[shipId];
    const curLoad = ship.getCurLoad();
    const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
    const avail = totalCapacity - curLoad;

    if (avail <= 0) {
      continue;
    }

    const loadAmount = Math.min(avail, quantity);

    const cargo = ship.getCargo(cmsId);
    cargo.quantity += loadAmount;
    cargo.pointInvested += loadAmount * smuggleGoodsPrice;

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
        cargos: {},
      };
    }
    if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
      changes.syncAdd.ships[ship.getNub().id].cargos = {};
    }
    changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;

    quantity -= loadAmount;
    if (quantity === 0) {
      break;
    }
  }

  if (quantity > 0 && bIfExceededAddMail) {
    const attachment: RewardCmsElem[] = [
      {
        Id: cmsId,
        Type: REWARD_TYPE.SMUGGLE_GOODS,
        Quantity: quantity,
        pointInvested,
      },
    ];
    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opUnloadSmuggleGoods(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  quantity: number,
  shipId?: number
) {
  if (quantity === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (quantity < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  let remaining = quantity;
  let shipIds = [];
  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
  if (shipId) {
    shipIds.push(shipId);

    if (tryData.fleets.getShip(shipId).getCargoQuantity(cmsId) < quantity) {
      return CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
    }
  } else {
    shipIds = Object.keys(firstFleetShips);

    if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
      return CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
    }
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  for (const shipIdStr of shipIds) {
    const shipId = parseInt(shipIdStr, 10);
    const ship = firstFleetShips[shipId];
    const cargo = ship.getCargo(shipId);
    const nub = ship.getNub();
    if (cargo.quantity >= remaining) {
      cargo.pointInvested -= Math.floor((cargo.pointInvested / cargo.quantity) * remaining);
      cargo.quantity -= remaining;
      remaining = 0;
    } else {
      remaining -= cargo.quantity;
      cargo.quantity = 0;
      cargo.pointInvested = 0;
    }

    if (!changes.syncAdd.ships[nub.id]) {
      changes.syncAdd.ships[nub.id] = {
        id: nub.id,
        cargos: {},
      };
    }

    if (!changes.syncAdd.ships[nub.id].cargos) {
      changes.syncAdd.ships[nub.id].cargos = {};
    }
    changes.syncAdd.ships[nub.id].cargos[cmsId] = cargo;

    if (remaining === 0) {
      break;
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddMateEquip(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  count: number,
  bAllowOverInven: boolean,
  bAllowAddToLimitIfExceeded: boolean,
  extraStr: string,
  bIsBound: boolean = true,
  bIfExceededAddMail: boolean = false,
  bIfExceededLimitAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (count === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (count < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const equipCms = cms.CEquip[cmsId];
  if (!equipCms) {
    throw new MError('cannot-reward-mate-equip', MErrorCode.INVALID_MATE_EQUIP);
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const extra: RewardCmsElemMateEquipmentExtra = extraStr ? JSON.parse(extraStr) : undefined;

  // Check 'havable'.
  const maxHavable = equipCms.havableCount;
  if (maxHavable !== undefined) {
    const curCount = tryData.mates.getMateEquipmentCountOfCmsId(cmsId);
    const addable = Math.max(maxHavable - curCount, 0);

    if (count > addable) {
      if (bAllowAddToLimitIfExceeded) {
        if (bIfExceededLimitAddMail) {
          const exceededCount = count - addable;
          const attachment: RewardCmsElem[] = [
            {
              Id: cmsId,
              Type: REWARD_TYPE.MATE_EQUIP,
              Quantity: exceededCount,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
        }

        count = addable;
        if (count === 0) {
          return CHANGE_TASK_RESULT.OK;
        }
      } else {
        mlog.warn('[REWARD] Max mate equip havable.', {
          userId: user.userId,
          cEquipCmsId: cmsId,
          maxHavable,
          curCount,
          toAdd: count,
        });
        return CHANGE_TASK_RESULT.MATE_EQUIP_MAX_HAVABLE;
      }
    }
  }

  // Check equip inven space.
  if (!bAllowOverInven) {
    // addable은 count보다 작거나 같을 수 밖에 없음
    const addable = tryData.mates.calcMateEquipmentAddableForSpace(
      equipCms,
      count,
      tryData.inven,
      0
    );
    if (addable !== count) {
      if (bAllowAddToLimitIfExceeded) {
        count = addable;
        if (count === 0) {
          return CHANGE_TASK_RESULT.OK;
        }
      } else {
        if (bIfExceededAddMail) {
          const exceededCount = count - addable;
          const attachment: RewardCmsElem[] = [
            {
              Id: cmsId,
              Type: REWARD_TYPE.MATE_EQUIP,
              Quantity: exceededCount,
              Extra: extraStr,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);

          count = addable;
        } else {
          mlog.warn('[REWARD] Not enough mate equip space.', {
            userId: user.userId,
            count,
            addable,
          });
          return CHANGE_TASK_RESULT.MATE_EQUIP_FULL;
        }
      }
    }
  }

  if (!changes.syncAdd.mateEquipments) {
    changes.syncAdd.mateEquipments = {};
  }

  const curTimeUtc = mutil.curTimeUtc();

  // 귀속 여부 결정.
  let isBound = 1;
  if (
    equipCms.isCashMarket &&
    ((extra && extra.isBound === 0) || !bIsBound) &&
    !equipCms.expireType &&
    !equipCms.exclusiveMateId
  ) {
    isBound = 0;
  }

  // save to try data and changes
  for (let i = 0; i < count; i++) {
    const newItem = tryData.mates.buildMateEquipmentNub(
      cmsId,
      0,
      isBound,
      0,
      curTimeUtc,
      extra?.expireTimeUtc,
      extra?.enchantLv
    );
    tryData.mates.addMateEquipment(newItem, null);
    changes.syncAdd.mateEquipments[newItem.id] = newItem;
    changes.lastMateEquipmentId = newItem.id;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddFame(
  user: User,
  tryData: TryData,
  changes: Changes,
  jobType: cmsEx.JOB_TYPE,
  amount: number
): CHANGE_TASK_RESULT {
  if (!Number.isInteger(amount)) {
    mlog.error('[RewardTask] opAddFame. amount integer expected.', {
      userId: user.userId,
      amount,
    });
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const leader = UserMates.getLeaderMate(tryData.fleets, tryData.mates);
  if (!leader) {
    mlog.error('[RewardTask] Cannot add fame without an admiral.', {
      userId: user.userId,
    });
    throw new MError('no-adirmal', MErrorCode.NEED_ADMIRAL);
  }

  // Check job type.
  if (jobType < cmsEx.JOB_TYPE.ADVENTURE || jobType > cmsEx.JOB_TYPE.BATTLE) {
    mlog.error('[RewardTask] Invalid fame job type.', {
      userId: user.userId,
      jobType: jobType,
    });
    throw new MError('invalid-job-type', MErrorCode.INVALID_JOB_TYPE);
  }

  const gainFame = Mate.calcGainFame(
    leader.getStat(tryData.stats),
    jobType,
    amount,
    user.nationCmsId,
    tryData.stats.getFleetStat(cmsEx.FirstFleetIndex)
  );

  if (gainFame > 0) {
    if (!changes.totalGainFames) {
      changes.totalGainFames = {};
    }
    if (changes.totalGainFames[jobType] === undefined) {
      changes.totalGainFames[jobType] = 0;
    }
    changes.totalGainFames[jobType] += gainFame;
  }

  const newFame = leader.calcFame(jobType, gainFame);
  if (newFame === undefined || newFame === leader.getFame(jobType)) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // Update the try data.
  leader.setFame(jobType, newFame, null);

  // Save the change.
  if (!changes.syncAdd.mates) {
    changes.syncAdd.mates = {};
  }
  if (!changes.syncAdd.mates[leader.getNub().cmsId]) {
    changes.syncAdd.mates[leader.getNub().cmsId] = {
      cmsId: leader.getNub().cmsId,
    };
  }

  switch (jobType) {
    case cmsEx.JOB_TYPE.ADVENTURE:
      changes.syncAdd.mates[leader.getNub().cmsId].adventureFame = leader.getFame(jobType);
      break;
    case cmsEx.JOB_TYPE.TRADE:
      changes.syncAdd.mates[leader.getNub().cmsId].tradeFame = leader.getFame(jobType);
      break;
    case cmsEx.JOB_TYPE.BATTLE:
      changes.syncAdd.mates[leader.getNub().cmsId].battleFame = leader.getFame(jobType);
      break;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 * @param user
 * @param tryData
 * @param changes
 * @param cmsId
 * @param bCaptured
 * @param name
 * @param bAllowExceedDock
 * @param fixedRandomStat 값이 존재할 경우 랜덤스탯이 해당 값으로 고정
 * @param extraStr
 * @param bIsBound
 * @param pickedRandomRewardElem null 이 아닐 경우 ship id 를 pickedRandomRewardElem.id 에 넣어주면 된다.
 * @returns
 */
export function opAddShip(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  bCaptured: boolean,
  name: string,
  bAllowExceedDock: boolean,
  fixedRandomStat: number,
  extraStr: string,
  bIsBound: boolean = true,
  pickedRandomRewardElem?: PickedRandomRewardElem,
  bIfExceededAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.userLevel) {
    tryData.userLevel = user.level;
  }
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  const shipCms = cms.Ship[cmsId];
  if (!shipCms) {
    throw new MError('cannot-reward-ship', MErrorCode.INVALID_SHIP);
  }

  const extra: RewardCmsElemShipExtra = extraStr ? JSON.parse(extraStr) : undefined;

  // Make a new ship nub and save it as a change.
  let rndStats;
  if (extra && extra.rndStats) {
    rndStats = extra.rndStats;
  } else {
    rndStats = makeShipRndStats(shipCms.shipBlueprintId, fixedRandomStat);
  }

  let defaultDurability = cmsEx.shipDefaultDurability(shipCms.shipBlueprintId, rndStats);
  let defaultLife = cmsEx.shipDefaultLife(shipCms.shipBlueprintId);
  if (bCaptured) {
    // 나포 선박은 내구도 비율을 조정한다
    defaultDurability = Math.floor(
      (defaultDurability * cms.Const.CapturedShipDurabilityRate.value) / 1000
    );
    defaultLife = Math.floor((defaultLife * cms.Const.CapturedShipLifeRate.value) / 1000);
  }

  // 귀속 여부 결정.
  let isBound = 1;
  // 나포 선박은 항상 귀속.
  if (!bCaptured && shipCms.isCashMarket && ((extra && extra.isBound === 0) || !bIsBound)) {
    isBound = 0;
  }

  const id = tryData.fleets.getNewShipId();
  const { shipNub: newShipNub, bpExpLevelChange } = ShipBuildUtil.buildShipNubAndShipBlueprint(
    tryData.stats,
    tryData.shipBlueprints,
    false,
    id,
    cmsId,
    bCaptured ? SHIP_ASSIGNMENT.CAPTURED : SHIP_ASSIGNMENT.DOCK,
    cmsEx.NoFleetIndex,
    0,
    extra?.life !== undefined ? extra.life : defaultLife,
    defaultDurability,
    rndStats,
    extra?.enchantedStats !== undefined ? extra.enchantedStats : [],
    extra?.enchantResult !== undefined ? extra.enchantResult : null,
    extra?.enchantCount !== undefined ? extra.enchantCount : 0,
    isBound,
    extra?.guid !== undefined ? extra.guid : `${user.userId}:${id}`,
    name
  );

  if (bpExpLevelChange) {
    if (!tryData.shipBlueprints.getUserShipBlueprint(bpExpLevelChange.cmsId)) {
      tryData.shipBlueprints.createUserShipBlueprint(
        tryData.stats,
        bpExpLevelChange.cmsId,
        bpExpLevelChange.level,
        bpExpLevelChange.exp,
        1,
        0,
        null,
        null
      );
    } else {
      tryData.shipBlueprints.setUserShipBlueprintLevel(
        bpExpLevelChange.cmsId,
        bpExpLevelChange.level,
        bpExpLevelChange.exp,
        tryData.stats,
        null
      );
    }

    if (!changes.syncAdd.shipBlueprints) {
      changes.syncAdd.shipBlueprints = {};
    }

    if (!changes.syncAdd.shipBlueprints[bpExpLevelChange.cmsId]) {
      changes.syncAdd.shipBlueprints[bpExpLevelChange.cmsId] = {
        cmsId: bpExpLevelChange.cmsId,
        level: bpExpLevelChange.level,
        exp: bpExpLevelChange.exp,
        slots: {},
      };
    }
  }

  let newCaptainCmsId; // 1함대에 배치될 경우 해당 배의 선장.
  const firstFleetShipsCount = tryData.fleets.getFirstFleet().getShipsCount();
  // 가능할 경우 지급할 배를 자동으로 1함대 배치.
  if (user.userState.isInTown()) {
    if (!bCaptured) {
      if (
        Fleet.canAddMoreToFleet(
          SHIP_ASSIGNMENT.FLEET,
          cmsEx.FirstFleetIndex,
          firstFleetShipsCount,
          1,
          tryData.userLevel,
          tryData.inven
        )
      ) {
        const firstCanditateMateCmsIds = []; // 첫 번째 후보군.
        const secondCanditateMateCmsIds = []; // 두 번째 후보군.
        for (const mateCmsIdStr of Object.keys(tryData.mates.getMates())) {
          const mateCmsId = parseInt(mateCmsIdStr, 10);
          const curShipSlot = tryData.fleets.getMateShipSlot(mateCmsId);
          if (curShipSlot.shipId) {
            // 선장이 아니고 잠기지 않은 선실에 배치된 항해사의 경우 두 번째 후보군에 넣는다.
            if (curShipSlot.slotIndex !== cmsEx.ShipSlotIndexCaptainRoom && !curShipSlot.isLocked) {
              secondCanditateMateCmsIds.push(mateCmsId);
            }
          } else {
            // 배치 되지 않은 항해사를 첫 번째 후보군에 넣는다.
            firstCanditateMateCmsIds.push(mateCmsId);
          }
        }

        const sortFunc = (a: number, b: number) => {
          const aMate = tryData.mates.getMate(a);
          const bMate = tryData.mates.getMate(b);
          let aMateSumLv = 0;
          let bMateSumLv = 0;
          for (let i = cmsEx.JOB_TYPE.NONE + 1; i <= cmsEx.JOB_TYPE.BATTLE; i++) {
            // 이번 트랜젝션에서 얻을 항해사는 undefined.
            if (aMate) {
              aMateSumLv += aMate.getLevel(i);
            }
            if (bMate) {
              bMateSumLv += bMate.getLevel(i);
            }
          }
          if (aMateSumLv !== bMateSumLv) {
            return bMateSumLv - aMateSumLv;
          }
          return a - b;
        };

        // 첫 번째 후보군에 아무도 없을 경우 두 번째 후보군에서 선장을 선택한다.
        if (firstCanditateMateCmsIds.length > 0) {
          firstCanditateMateCmsIds.sort(sortFunc);
          newCaptainCmsId = firstCanditateMateCmsIds[0];
        } else if (secondCanditateMateCmsIds.length > 0) {
          secondCanditateMateCmsIds.sort(sortFunc);
          newCaptainCmsId = secondCanditateMateCmsIds[0];
        }

        // TODO: 차후 2~4함대가 생길 경우 해당 함대에 배치된 항해사 고려 필요함.
      }
    }
  }

  // 선장으로 넣을 수 있는 항해사가 있을 경우 배를 도크가 아닌 1함대에 넣는다.
  if (newCaptainCmsId) {
    newShipNub.fleetIndex = cmsEx.FirstFleetIndex;
    newShipNub.assignment = SHIP_ASSIGNMENT.FLEET;
    newShipNub.formationIndex = firstFleetShipsCount + 1;
    newShipNub.slots[cmsEx.ShipSlotIndexCaptainRoom] = {
      slotIndex: cmsEx.ShipSlotIndexCaptainRoom,
      mateCmsId: newCaptainCmsId,
      isLocked: 0,
      shipSlotItemId: null,
    };
  } else if (bCaptured) {
    if (
      !Fleet.canAddMoreToFleet(
        SHIP_ASSIGNMENT.CAPTURED,
        cmsEx.NoFleetIndex,
        tryData.fleets.getShipsCountByAssignment(SHIP_ASSIGNMENT.CAPTURED),
        1,
        tryData.userLevel,
        tryData.inven
      )
    ) {
      if (bIfExceededAddMail) {
        const attachment: RewardCmsElem[] = [
          {
            Id: cmsId,
            Type: REWARD_TYPE.SHIP,
            Quantity: 1,
            Extra: extraStr,
          },
        ];
        AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
        return CHANGE_TASK_RESULT.NOTHING;
      } else {
        return CHANGE_TASK_RESULT.CAPTURED_FULL;
      }
    }
  } else {
    if (
      !Fleet.canAddMoreToFleet(
        SHIP_ASSIGNMENT.DOCK,
        cmsEx.NoFleetIndex,
        tryData.fleets.getShipsCountByAssignment(SHIP_ASSIGNMENT.DOCK),
        1,
        tryData.userLevel,
        tryData.inven
      ) &&
      !bAllowExceedDock
    ) {
      if (bIfExceededAddMail) {
        const attachment: RewardCmsElem[] = [
          {
            Id: cmsId,
            Type: REWARD_TYPE.SHIP,
            Quantity: 1,
            Extra: extraStr,
          },
        ];
        AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
        return CHANGE_TASK_RESULT.NOTHING;
      } else {
        return CHANGE_TASK_RESULT.DOCK_FULL;
      }
    }
  }

  if (newCaptainCmsId) {
    const oldShipSlot = tryData.fleets.getMateShipSlot(newCaptainCmsId);
    const oldShip = tryData.fleets.getShip(oldShipSlot.shipId);
    if (oldShip) {
      oldShip.setSlotMate(
        {
          slotIndex: oldShipSlot.slotIndex,
          mateCmsId: 0,
          isLocked: 0,
          shipSlotItemId: null,
        },
        tryData.shipBlueprints,
        tryData.mates,
        tryData.fleets,
        undefined, // companyStat
        undefined, // userPassives
        undefined, // questMgr
        undefined, // userSailing
        undefined, // userTriggers
        undefined, // userBuffs
        false, // bOnLogin
        undefined
      );

      if (!changes.syncAdd.ships[oldShipSlot.shipId]) {
        changes.syncAdd.ships[oldShipSlot.shipId] = {
          id: oldShipSlot.shipId,
          slots: {},
        };
      }
      if (!changes.syncAdd.ships[oldShipSlot.shipId].slots) {
        changes.syncAdd.ships[oldShipSlot.shipId].slots = {};
      }

      changes.syncAdd.ships[oldShipSlot.shipId].slots[oldShipSlot.slotIndex] = {
        slotIndex: oldShipSlot.slotIndex,
        mateCmsId: 0,
        isLocked: 0,
        shipSlotItemId: null,
      };
    }
  }

  tryData.fleets.addNewShip(
    newShipNub,
    tryData.stats,
    tryData.shipBlueprints,
    tryData.mates,
    tryData.fleets,
    tryData.inven,
    null, // userBuffs
    null, // userPassives
    null, // userCollection
    null, // questMgr
    null, // userSailing
    null, // userTriggers
    null, // userNation
    null, // userResearch
    null, // glogParam
    undefined
  );
  changes.syncAdd.ships[newShipNub.id] = newShipNub;
  changes.lastShipId = newShipNub.id;

  if (pickedRandomRewardElem) {
    pickedRandomRewardElem.id = newShipNub.id;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddMate(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  // 이 밑으론 항해사를 이미 가지고 있을때 계약서 지급 용도
  bIsProceedDuplicated: boolean = true,
  bAllowOverInven: boolean = false,
  bAllowAddToLimitIfExceeded: boolean = false,
  extraStr: string = undefined,
  inBIsBound: boolean = true,
  bIfExceededAddMail: boolean = false,
  bIfExceededLimitAddMail: boolean = false
): CHANGE_TASK_RESULT {
  const mateCms = cms.Mate[cmsId];
  if (!mateCms) {
    throw new MError('cannot-reward-mate', MErrorCode.INVALID_MATE);
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.questManager) {
    tryData.questManager = user.questManager.clone();
  }

  if (!tryData.cashShop) {
    tryData.cashShop = user.userCashShop.clone();
  }

  // Check duplicate mates.
  if (tryData.mates.hasMate(cmsId)) {
    if (bIsProceedDuplicated === false) {
      return CHANGE_TASK_RESULT.NOTHING;
    }

    return opAddItem(
      user,
      tryData,
      changes,
      mateCms.reRecruitRewardItemId,
      cms.Const.CashShopDuplicatedMateToPiece.value,
      bAllowOverInven,
      bAllowAddToLimitIfExceeded,
      extraStr,
      true,
      inBIsBound,
      bIfExceededAddMail,
      bIfExceededLimitAddMail
    );
  }

  if (!changes.syncAdd.mates) {
    changes.syncAdd.mates = {};
  }

  // Save change.
  const mateNub = Mate.defaultNub(cmsId);
  tryData.mates.addNewMate(mateNub, tryData.stats, null, null, undefined);
  changes.syncAdd.mates[mateNub.cmsId] = mateNub;

  if (tryData.mates.getUnemployedMate(mateCms.id)) {
    tryData.mates.deleteUnemployedMate(mateCms.id);
    _.merge(changes.syncRemove, {
      unemployedMates: {
        [mateCms.id]: true,
      },
    });
  }

  const curTimeUtc = mutil.curTimeUtc();
  if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
    for (const cequipCmsId of mateCms.CEquipId) {
      const newItem = tryData.mates.buildMateEquipmentNub(
        cequipCmsId,
        mateCms.id,
        1,
        0,
        curTimeUtc
      );
      tryData.mates.addMateEquipment(newItem, null);
      _.merge<All, All>(changes.syncAdd, {
        mateEquipments: {
          [newItem.id]: newItem,
        },
      });
      if (newItem.equippedMateCmsId) {
        tryData.mates.equipMateEquipment(
          newItem.equippedMateCmsId,
          newItem.id,

          // 스탯/패시브를 위한 인자로만 쓰이는 걸로 보장되는 지 확인 필요.
          undefined, // companyStat
          undefined, // userPassives
          undefined, // userFleets
          undefined, // userSailing
          undefined, // userTriggers
          undefined, // userBuffs
          undefined, // sync
          undefined // glog
        );
      }

      changes.lastMateEquipmentId = newItem.id;
    }
  }

  const addedPassives: { mateCmsId: number; passiveCmsId: number }[] =
    MateUtil.getRelationChronicleLearnablePassives(tryData.mates, tryData.questManager, mateCms.id);

  if (addedPassives && addedPassives.length > 0) {
    if (!changes.addedMatePassives) {
      changes.addedMatePassives = [];
    }

    for (const elem of addedPassives) {
      tryData.mates.getMate(cmsId).addPassive(elem.passiveCmsId, 0, null);
      changes.addedMatePassives.push(elem);
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// 넘치는 항해사는 버려짐.
export function opAddSailor(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number,
  bFillWreckedShip: boolean
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  if (!tryData.state) {
    tryData.state = user.userState.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  if (!changes.syncAdd.ships) {
    changes.syncAdd.ships = {};
  }

  const firstFleetShips = tryData.fleets.getFirstFleet().getShips();

  let remaining = amount;

  for (const ship of _.values(firstFleetShips)) {
    // 해상에 있을 경우 난파 중인 배에는 선원을 지급하지 않는다. (bFillWreckedShip 가 false 인 경우)
    if (!tryData.state.isInTown() && !bFillWreckedShip && ship.isWrecked()) {
      continue;
    }

    const max = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_MAX_SAILOR);
    const avail = max - ship.getSailor();

    if (avail <= 0) {
      continue;
    }

    const sailorToAdd = Math.min(avail, remaining);

    if (!changes.syncAdd.ships[ship.getNub().id]) {
      changes.syncAdd.ships[ship.getNub().id] = {
        id: ship.getNub().id,
        sailor: ship.getSailor(),
      };
    }
    if (changes.syncAdd.ships[ship.getNub().id].sailor === undefined) {
      changes.syncAdd.ships[ship.getNub().id].sailor = ship.getSailor();
    }

    changes.syncAdd.ships[ship.getNub().id].sailor += sailorToAdd;

    remaining -= sailorToAdd;
    if (remaining === 0) {
      break;
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opUpgradeShipBlueprint(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  bAllowOnlyOpen: boolean
): CHANGE_TASK_RESULT {
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }
  const blueprintCms = cms.ShipBlueprint[cmsId];
  const shipCms = cms.Ship[blueprintCms.shipId];
  const bpMaxLevel = cms.Const['ShipMaxEnchant' + shipCms.shipSize].value;

  const userBP = tryData.shipBlueprints.getUserShipBlueprint(cmsId);
  if (userBP && userBP.level >= bpMaxLevel) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  let newLevel;
  let newExp;
  if (userBP) {
    if (bAllowOnlyOpen) {
      return CHANGE_TASK_RESULT.NOTHING;
    }
    newLevel = userBP.level + 1;
    newExp = cmsEx.getShipBuildMasteryExpCmsAccumulateExp(newLevel - 1, shipCms.shipSize);
  } else {
    newLevel = 1;
    newExp = 0;
  }

  if (newLevel === 1) {
    tryData.shipBlueprints.createUserShipBlueprint(
      tryData.stats,
      cmsId,
      newLevel,
      newExp,
      1,
      0,
      null,
      null
    );
  } else {
    tryData.shipBlueprints.setUserShipBlueprintLevel(cmsId, newLevel, newExp, undefined, null);
  }

  if (!changes.syncAdd.shipBlueprints) {
    changes.syncAdd.shipBlueprints = {};
  }
  if (!changes.syncAdd.shipBlueprints[cmsId]) {
    changes.syncAdd.shipBlueprints[cmsId] = {
      cmsId,
      level: newLevel,
      exp: newExp,
      slots: {},
    };
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetDirectMailState(
  user: User,
  tryData: TryData,
  changes: Changes,
  mailId: number,
  state: MAIL_STATE
): CHANGE_TASK_RESULT {
  if (!tryData.mails) {
    tryData.mails = user.userMails.clone();
  }

  // validate
  const mail = tryData.mails.getDirectMail(mailId);
  if (!mail) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (mail.state === state) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // save
  tryData.mails.setDirectMailState(mailId, state);
  _.merge<All, All>(changes.syncAdd, {
    userDirectMails: {
      [mailId]: {
        id: mailId,
        state,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetDirectmailExpireTimeUtc(
  user: User,
  tryData: TryData,
  changes: Changes,
  mailId: number,
  expireTimeUtc: number
): CHANGE_TASK_RESULT {
  if (!tryData.mails) {
    tryData.mails = user.userMails.clone();
  }

  // validate
  const mail = tryData.mails.getDirectMail(mailId);
  if (!mail) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (mail.expireTimeUtc === expireTimeUtc) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  mail.expireTimeUtc = expireTimeUtc;

  _.merge<All, All>(changes.syncAdd, {
    userDirectMails: {
      [mailId]: {
        id: mailId,
        expireTimeUtc,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddDirectmail(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  createTimeUtc: number,
  expireTimeUtc: number,
  bShouldSetExpirationWhenReceiveAttachment: number,
  title: string = null,
  titleFormatValue: number = null,
  body: string = null,
  bodyFormatValue: number = null,
  attachment: string = null
): CHANGE_TASK_RESULT {
  if (!tryData.mails) {
    tryData.mails = user.userMails.clone();
  }

  const newMail = new BuilderMailCreateParams(
    tryData.mails.generateNewDirectMailId(),
    cmsId,
    createTimeUtc,
    expireTimeUtc,
    bShouldSetExpirationWhenReceiveAttachment,
    title,
    titleFormatValue,
    body,
    bodyFormatValue,
    attachment
  ).getParam();

  tryData.mails.addDirectMail(newMail, null);

  if (!changes.newMail) {
    changes.newMail = [];
  }
  changes.newMail.push(newMail);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetCompleteTask(
  user: User,
  tryData: TryData,
  changes: Changes,
  taskCmsId: number,
  category: cmsEx.TASK_CATEGORY,
  index: number
) {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  const task = tryData.achievements.getTasks(category).tasks[taskCmsId];
  if (task.isRewarded) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_REWARD;
  }

  const taskCms = cms.Task[taskCmsId];
  tryData.achievements.setTaskCount(category, taskCmsId, taskCms.achievementCount);

  if (!changes.syncAdd.tasks) {
    changes.syncAdd.tasks = {};
  }
  if (!changes.syncAdd.tasks[category]) {
    changes.syncAdd.tasks[category] = {
      tasks: {},
    };
  }
  if (!changes.syncAdd.tasks[category].tasks[taskCmsId]) {
    changes.syncAdd.tasks[category].tasks[taskCmsId] = {
      cmsId: taskCmsId,
      index,
    };
  }

  changes.syncAdd.tasks[category].tasks[taskCmsId].count = taskCms.achievementCount;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetTaskRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  taskCmsId: number,
  category: cmsEx.TASK_CATEGORY,
  index: number
): CHANGE_TASK_RESULT {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  const task = tryData.achievements.getTasks(category).tasks[taskCmsId];
  if (task.isRewarded) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_REWARD;
  }

  tryData.achievements.setTaskRewarded(category, taskCmsId);

  if (!changes.syncAdd.tasks) {
    changes.syncAdd.tasks = {};
  }
  if (!changes.syncAdd.tasks[category]) {
    changes.syncAdd.tasks[category] = {
      tasks: {},
    };
  }
  if (!changes.syncAdd.tasks[category].tasks[taskCmsId]) {
    changes.syncAdd.tasks[category].tasks[taskCmsId] = {
      cmsId: taskCmsId,
      index,
    };
  }

  changes.syncAdd.tasks[category].tasks[taskCmsId].isRewarded = 1;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetTaskCategoryRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  category: cmsEx.TASK_CATEGORY
): CHANGE_TASK_RESULT {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  const categoryTasks = tryData.achievements.getTasks(category);
  if (categoryTasks.isCategoryRewarded) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_CATEGORY_REWARD;
  }

  tryData.achievements.setTaskCategoryRewarded(category);

  if (!changes.syncAdd.tasks) {
    changes.syncAdd.tasks = {};
  }
  if (!changes.syncAdd.tasks[category]) {
    changes.syncAdd.tasks[category] = {
      tasks: {},
    };
  }

  changes.syncAdd.tasks[category].isCategoryRewarded = 1;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetAchievementRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  achievementCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  const a = tryData.achievements.getAchievement(achievementCmsId);
  if (a.isRewarded) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_ACHIEVEMENT_REWARD;
  }

  tryData.achievements.setAchievementRewarded(achievementCmsId);

  if (!changes.syncAdd.achievements) {
    changes.syncAdd.achievements = {};
  }
  if (!changes.syncAdd.achievements[achievementCmsId]) {
    changes.syncAdd.achievements[achievementCmsId] = {
      cmsId: achievementCmsId,
    };
  }

  changes.syncAdd.achievements[achievementCmsId].isRewarded = 1;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetAchievementPointRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  lastRewardedAchievementPointCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  if (
    tryData.achievements.lastRewardedAchievementPointCmsId === lastRewardedAchievementPointCmsId
  ) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_ACHIEVEMENT_POINT_REWARD;
  }

  tryData.achievements.lastRewardedAchievementPointCmsId = lastRewardedAchievementPointCmsId;

  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }

  changes.syncAdd.user.lastRewardedAchievementPointCmsId = lastRewardedAchievementPointCmsId;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetEventMissionRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventPageCmsId: number,
  eventMissionCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.achievements) {
    tryData.achievements = user.userAchievement.clone();
  }

  const eventMission = tryData.achievements.getEventMission(eventPageCmsId, eventMissionCmsId);
  if (eventMission) {
    if (eventMission.isRewarded) {
      return CHANGE_TASK_RESULT.ALREADY_RECEIVED_EVENT_MISSION_REWARD;
    }
    eventMission.isRewarded = 1;
  } else {
    // 카운팅 개념을 사용하지 않고, isRewarded 만 사용하는 형태의 CMS.EventMission 도 있음.
    // DB, 메모리에 없을 수 있는 것 참고.
    tryData.achievements.setEventMission(eventPageCmsId, eventMissionCmsId, 0, 1);
  }

  if (!changes.syncAdd.eventPages) {
    changes.syncAdd.eventPages = {};
  }
  if (!changes.syncAdd.eventPages[eventPageCmsId]) {
    changes.syncAdd.eventPages[eventPageCmsId] = {
      eventMissions: {},
    };
  }

  changes.syncAdd.eventPages[eventPageCmsId].eventMissions[eventMissionCmsId] = {
    isRewarded: 1,
  };

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// 막 우겨넣었는데 정리가 필요함.
// 이 오퍼레이터 안에서도 ALREADY_RECEIVED_EVENT_MISSION_REWARD 같은 검증을 하는 게 좋을 듯한데, 고민.
export function opSetPassEventMissions(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventPageCmsId: number,
  eventMissionCmsIdsToReset: number[], // 검증이 된 것들이어야함.
  repeatedRewardReceiveCount: { [eventMissionCmsId: number]: number },
  eventMissionCmsIdsToReward: number[] // 검증이 된 것들이어야함.
): CHANGE_TASK_RESULT {
  const passEventPageCms = cms.EventPage[eventPageCmsId];
  if (!passEventPageCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.passEvent) {
    tryData.passEvent = user.userPassEvent.clone();
  }

  type PassEventMissionChanges = Changes['passEventMissions'][number];
  type PassEventMissionChange = PassEventMissionChanges[number];
  const inEventMissionChanges: PassEventMissionChanges = {};

  // 리셋
  for (const eventMissionCmsId of eventMissionCmsIdsToReset) {
    inEventMissionChanges[eventMissionCmsId] = {
      eventPageCmsId: passEventPageCms.id,
      eventMissionCmsId,
      count: 0,
      repeatedRewardReceiveCount: 0,
      isRewarded: 0,
    };
  }
  // 보상 수령 여부.
  for (const eventMissionCmsId of eventMissionCmsIdsToReward) {
    if (!inEventMissionChanges[eventMissionCmsId]) {
      inEventMissionChanges[eventMissionCmsId] = {
        eventPageCmsId: passEventPageCms.id,
        eventMissionCmsId,
        isRewarded: 1,
      };
    } else {
      inEventMissionChanges[eventMissionCmsId].isRewarded = 1;
    }
  }
  // 반복보상 횟수 반영
  _.forOwn(repeatedRewardReceiveCount, (receiveCount, eventMissionCmsId) => {
    if (!inEventMissionChanges[eventMissionCmsId]) {
      inEventMissionChanges[eventMissionCmsId] = {
        eventPageCmsId: passEventPageCms.id,
        eventMissionCmsId: parseInt(eventMissionCmsId, 10),
        repeatedRewardReceiveCount: receiveCount,
      };
    } else {
      inEventMissionChanges[eventMissionCmsId].repeatedRewardReceiveCount = receiveCount;
    }
  });

  const tryDataPassEventMissions = tryData.passEvent.getPassEventMissions(passEventPageCms.id);
  const actualChangedPassEventMissions: PassEventMission[] = [];
  const actualChangedEventMissionChanges: PassEventMissionChange[] = [];
  _.forOwn(inEventMissionChanges, (change) => {
    const tryDataPassEventMission = tryDataPassEventMissions[change.eventMissionCmsId];
    const oldCount = tryDataPassEventMission ? tryDataPassEventMission.count : 0;
    const oldRepeatedRewardReceiveCount = tryDataPassEventMission
      ? tryDataPassEventMission.repeatedRewardReceiveCount
      : 0;
    const oldIsRewarded = tryDataPassEventMission ? tryDataPassEventMission.isRewarded : 0;
    if (
      (change.count === undefined || change.count === oldCount) &&
      (change.repeatedRewardReceiveCount === undefined ||
        change.repeatedRewardReceiveCount === oldRepeatedRewardReceiveCount) &&
      (change.isRewarded === undefined || change.isRewarded === oldIsRewarded)
    ) {
      return;
    }
    actualChangedPassEventMissions.push({
      eventMissionCmsId: change.eventMissionCmsId,
      count: change.count !== undefined ? change.count : oldCount,
      repeatedRewardReceiveCount:
        change.repeatedRewardReceiveCount !== undefined
          ? change.repeatedRewardReceiveCount
          : oldRepeatedRewardReceiveCount,
      isRewarded: change.isRewarded !== undefined ? change.isRewarded : oldIsRewarded,
    });
    actualChangedEventMissionChanges.push(change);
  });

  tryData.passEvent.applyPassEventMissions(passEventPageCms, actualChangedPassEventMissions);

  // 관련 오퍼레이터를 섞어서 사용하는 것도 이상한데, 괜히 복잡해지는 데 차라리 아예 막는게 어떨지?
  if (!changes.passEventMissions) {
    changes.passEventMissions = {};
  }
  if (!changes.passEventMissions[passEventPageCms.id]) {
    changes.passEventMissions[passEventPageCms.id] = {};
  }
  const accEventMissionChanges: PassEventMissionChanges =
    changes.passEventMissions[passEventPageCms.id];
  // 애매하긴 한데, 리셋된 다음 보상을 받아 최종적으로 DB 변경이 없는 경우를 방지하기 위함.
  // 메모리 apply 할 때도, 업데이트 함수에서 처리되지 않을텐데 문제없을지 모르겠음.
  actualChangedEventMissionChanges.forEach((elem) => {
    if (!accEventMissionChanges[elem.eventMissionCmsId]) {
      accEventMissionChanges[elem.eventMissionCmsId] = elem;
      return;
    }

    if (elem.count !== undefined) {
      accEventMissionChanges[elem.eventMissionCmsId].count = elem.count;
    }
    if (elem.repeatedRewardReceiveCount !== undefined) {
      accEventMissionChanges[elem.eventMissionCmsId].repeatedRewardReceiveCount =
        elem.repeatedRewardReceiveCount;
    }
    if (elem.isRewarded !== undefined) {
      accEventMissionChanges[elem.eventMissionCmsId].isRewarded = elem.isRewarded;
    }
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetBattleRewards(
  _user: User,
  _tryData: TryData,
  changes: Changes,
  battleRewardChanges: BattleReward[]
): CHANGE_TASK_RESULT {
  changes.syncAdd.battleRewards = battleRewardChanges;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetInsuranceCmsId(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }

  if (tryData.points.getInsuranceCmsId() === cmsId) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.points.setInsuranceCmsId(cmsId);

  if (!changes.syncAdd.insurance) {
    changes.syncAdd.insurance = {};
  }

  changes.syncAdd.insurance.insuranceCmsId = cmsId;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetNation(
  user: User,
  tryData: TryData,
  changes: Changes,
  nationCmsId: number,
  lastUpdateNationTimeUtc: number
): CHANGE_TASK_RESULT {
  // try data
  if (!tryData.nationCmsId) {
    tryData.nationCmsId = user.nationCmsId;
  }

  if (tryData.nationCmsId === nationCmsId) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.nationCmsId = nationCmsId;
  tryData.lastUpdateNationTimeUtc = lastUpdateNationTimeUtc;

  // changes
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }

  changes.syncAdd.user.nationCmsId = nationCmsId;
  changes.syncAdd.user.lastUpdateNationTimeUtc = lastUpdateNationTimeUtc;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opResetPalaceRoyalOrder(
  user: User,
  tryData: TryData,
  changes: Changes
): CHANGE_TASK_RESULT {
  changes.bResetPalaceRoyalOrder = true;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetReputation(
  user: User,
  tryData: TryData,
  changes: Changes,
  nationCmsId: number,
  reputation: number,
  updateTimeUtc: number
): CHANGE_TASK_RESULT {
  // try data
  if (!tryData.reputation) {
    tryData.reputation = user.userReputation.clone();
  }

  tryData.reputation.set(
    nationCmsId,
    {
      reputation: reputation,
      updateTimeUtc: updateTimeUtc,
    },
    null
  );

  // changes
  if (!changes.syncAdd.reputations) {
    changes.syncAdd.reputations = {};
  }

  changes.syncAdd.reputations[nationCmsId] = {
    reputation: reputation,
    updateTimeUtc: updateTimeUtc,
  };

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opModifyReputation(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  coefficient: number,
  updateTimeUtc: number
): CHANGE_TASK_RESULT {
  // try data
  if (!tryData.reputation) {
    tryData.reputation = user.userReputation.clone();
  }

  const oldReputation = tryData.reputation.get(cmsId, updateTimeUtc);
  const newReputation = tryData.reputation.getNewValue(cmsId, updateTimeUtc, coefficient);
  if (oldReputation === newReputation) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.reputation.set(
    cmsId,
    {
      reputation: newReputation,
      updateTimeUtc: updateTimeUtc,
    },
    null
  );

  // changes
  if (!changes.syncAdd.reputations) {
    changes.syncAdd.reputations = {};
  }

  changes.syncAdd.reputations[cmsId] = {
    reputation: newReputation,
    updateTimeUtc: updateTimeUtc,
  };

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opRecordNationEventOccur(
  _user: User,
  _tryData: TryData,
  changes: Changes,
  cmsId1: number,
  cmsId2: number,
  nationDiplomacyCmsId: cmsEx.NATION_DIPLOMACY_CMS_ID
): CHANGE_TASK_RESULT {
  if (!changes.nationEvents) {
    changes.nationEvents = [];
  }
  changes.nationEvents.push([cmsId1, cmsId2, nationDiplomacyCmsId]);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opDecreaseMateLoyalty(
  user: User,
  fleetIndex: number,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  amount: number // 양수가 넘어온다
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }

  const oldLoyalty = tryData.mates.getMate(cmsId).getLoyalty();
  if (!oldLoyalty) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // 충성도 감소가 발생할 수 있는 상황에서는 이 함수를 사용하여 충성도 감소 방지 효과 체크를 한다
  const added = StatHelper.calcChangeLoyalty(
    tryData.fleets.getFleet(fleetIndex),
    user.companyStat,
    -amount,
    oldLoyalty
  );
  if (0 === added) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!changes.mateLoyalty) {
    changes.mateLoyalty = [];
  }

  const newLoyalty = Math.max(0, oldLoyalty + added);

  tryData.mates.getMate(cmsId).setLoyalty(newLoyalty, null, null, null);

  const change = changes.mateLoyalty.find((elem) => {
    return elem.mateCmsId === cmsId;
  });

  if (change) {
    change.value = newLoyalty;
  } else {
    changes.mateLoyalty.push({
      mateCmsId: cmsId,
      value: newLoyalty,
    });
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
export function opDeleteCashShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (!changes.syncRemove.cashShopRestrictedProducts) {
    changes.syncRemove.cashShopRestrictedProducts = [];
  }

  changes.syncRemove.cashShopRestrictedProducts.push(cmsId.toString());

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetCashShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  restrictedProduct: RestrictedProduct
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.cashShopRestrictedProducts) {
    changes.syncAdd.cashShopRestrictedProducts = {};
  }

  changes.syncAdd.cashShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
export function opDeleteCashShopFixedTermProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (!changes.syncRemove.cashShopFixedTermProducts) {
    changes.syncRemove.cashShopFixedTermProducts = [];
  }

  changes.syncRemove.cashShopFixedTermProducts.push(cmsId.toString());

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetCashShopFixedTermProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  product: FixedTermProduct
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.cashShopFixedTermProducts) {
    changes.syncAdd.cashShopFixedTermProducts = {};
  }

  changes.syncAdd.cashShopFixedTermProducts[product.cmsId] = product;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetSailing(
  user: User,
  tryData: TryData,
  changes: Changes,
  sailingChange: SailingChange,
  sailedDays: number
): CHANGE_TASK_RESULT {
  // sailing 업데이트는 누적될 일이 없기 때문에 try data 사용하지 않음.
  changes.daysForLoyaltyDecrease = sailingChange.daysForLoyaltyDecrease;
  changes.daysForTownReset = sailingChange.daysForTownReset;

  changes.sailedDays = sailedDays;

  // reset
  changes.syncRemove.sailing = true;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddTotalSailedDays(
  user: User,
  tryData: TryData,
  changes: Changes,
  sailedDays: number
): CHANGE_TASK_RESULT {
  if (!tryData.sailing) {
    tryData.sailing = user.userSailing.clone();
  }

  tryData.sailing.totalSailedDays += sailedDays;

  changes.syncAdd.user.totalSailedDays = tryData.sailing.totalSailedDays;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opResetTownState(
  user: User,
  tryData: TryData,
  changes: Changes
): CHANGE_TASK_RESULT {
  changes.bResetTownState = true;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opResetGameOverLosses(
  user: User,
  tryData: TryData,
  changes: Changes
): CHANGE_TASK_RESULT {
  changes.syncRemove.gameOverLosses = true;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opResetGameOverLossesPvpLoss(
  user: User,
  tryData: TryData,
  changes: Changes
): CHANGE_TASK_RESULT {
  changes.syncRemove.multiPvpLoss = true;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetbGameOver(
  user: User,
  tryData: TryData,
  changes: Changes,
  bGameOver: boolean
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }
  changes.syncAdd.user.bGameOver = bGameOver;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetGameState(
  user: User,
  tryData: TryData,
  changes: Changes,
  gameState: GAME_STATE,
  gameEnterState: GAME_ENTER_STATE
): CHANGE_TASK_RESULT {
  if (!tryData.state) {
    tryData.state = user.userState.clone();
  }

  const gameStateChange = tryData.state.buildGameStateChange(gameState, gameEnterState);
  _.merge<All, All>(changes.syncAdd, tryData.state.applyGameStateChange(gameStateChange, null).add);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetLastGameState(
  user: User,
  tryData: TryData,
  changes: Changes,
  gameState: GAME_STATE
): CHANGE_TASK_RESULT {
  if (!tryData.state) {
    tryData.state = user.userState.clone();
  }

  tryData.state.forceSetLastGameState(gameState);
  _.merge<All, All>(changes.syncAdd, {
    user: {
      lastGameState: gameState,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyGameStateChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  gameStateChange: GameStateChange
): CHANGE_TASK_RESULT {
  if (!tryData.state) {
    tryData.state = user.userState.clone();
  }

  _.merge<All, All>(changes.syncAdd, tryData.state.applyGameStateChange(gameStateChange, null).add);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetLastTownCmsId(
  user: User,
  tryData: TryData,
  changes: Changes,
  lastTownCmsId: number
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }

  changes.syncAdd.user.lastTownCmsId = lastTownCmsId;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetArrivalTownCmsId(
  user: User,
  tryData: TryData,
  changes: Changes,
  arrivalTownCmsId: number
): CHANGE_TASK_RESULT {
  changes.arrivalTownCmsId = arrivalTownCmsId;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// 부락 arriveVillage 패킷 때 사용하던 기능 주석처리 현재 사용 안함
/*
export function opSetArrivalVillageCmsId(
  changes: Changes,
  arrivalVillageCmsId: number
): CHANGE_TASK_RESULT {
  changes.arrivalVillageCmsId = arrivalVillageCmsId;
  return CHANGE_TASK_RESULT.OK;
}
*/

// -------------------------------------------------------------------------------------------------
export function opSetCashShopGachaBoxGuaranteeAccum(
  user: User,
  tryData: TryData,
  changes: Changes,
  cashShopCmsId: number,
  guaranteeAccum: number
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.cashShopGachaBoxGuarantees) {
    changes.syncAdd.cashShopGachaBoxGuarantees = {};
  }

  changes.syncAdd.cashShopGachaBoxGuarantees[cashShopCmsId] = guaranteeAccum;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddSoundPack(
  user: User,
  tryData: TryData,
  changes: Changes,
  soundPackCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.cashShop) {
    tryData.cashShop = user.userCashShop.clone();
  }

  if (tryData.cashShop.isSoundPackBought(soundPackCmsId)) {
    return CHANGE_TASK_RESULT.ALREADY_BOUGHT;
  }

  tryData.cashShop.addSoundPack(soundPackCmsId);

  if (!changes.syncAdd.soundPacks) {
    changes.syncAdd.soundPacks = {};
  }

  const offset = Math.floor(soundPackCmsId / 32);
  changes.syncAdd.soundPacks[offset] = tryData.cashShop.getSoundPackIdxField(soundPackCmsId);

  if (!changes.addedSoundPacks) {
    changes.addedSoundPacks = [];
  }
  changes.addedSoundPacks.push(soundPackCmsId);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddMateIntimacyOrLoyalty(
  user: User,
  tryData: TryData,
  changes: Changes,
  mateCmsId: number,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const mateCms = cms.Mate[mateCmsId];
  if (!mateCms) {
    mlog.error('[RewardTask] opAddIntimacyOrLoyalty. Invalid mate cms id.', {
      userId: user.userId,
      mateCmsId,
    });
    throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_MATE_CMS_ID);
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }

  const mate = tryData.mates.getMate(mateCmsId);
  if (mate) {
    // 충성도 지급
    const oldLoyalty = mate.getLoyalty();

    if (oldLoyalty === null) {
      // 제독 가능 항해사인 경우.
      return CHANGE_TASK_RESULT.NOTHING;
    }

    const newLoyalty = Math.min(oldLoyalty + amount, cms.Const.MaxLoyalty.value);
    if (oldLoyalty === newLoyalty) {
      return CHANGE_TASK_RESULT.NOTHING;
    }

    _.merge<All, All>(changes.syncAdd, {
      mates: {
        [mateCmsId]: {
          cmsId: mateCmsId,
          loyalty: newLoyalty,
        },
      },
    });

    mate.setLoyalty(newLoyalty, null, null, null);

    if (
      newLoyalty >= cms.Const.MateManagementLoyaltyTerms.value &&
      mate.hasState(cmsEx.MATE_STATE_FLAG.SLOWDOWN)
    ) {
      _.merge<All, All>(changes.syncAdd, {
        mates: {
          [mateCmsId]: {
            cmsId: mateCmsId,
            stateFlags: mate.removeState(cmsEx.MATE_STATE_FLAG.SLOWDOWN, user.companyStat),
          },
        },
      });
    }
  } else {
    // 친밀도 지급
    const oldIntimacy = tryData.mates.getUnemployedMateIntimacy(mateCmsId);
    const newIntimacy = Math.min(oldIntimacy + amount, cms.Const.MaxLoyalty.value);
    if (oldIntimacy === newIntimacy) {
      return CHANGE_TASK_RESULT.NOTHING;
    }

    _.merge<All, All>(changes.syncAdd, {
      unemployedMates: {
        [mateCmsId]: {
          mateCmsId,
          isMet: 1,
          intimacy: newIntimacy,
        },
      },
    });

    tryData.mates.setUnemployedMateIntimacy(mateCmsId, newIntimacy, null);
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddMateInjuryState(
  user: User,
  tryData: TryData,
  changes: Changes,
  mateCmsId: number,
  curTimeUtc: number,
  injuryTimeSec: number
): CHANGE_TASK_RESULT {
  // 전투(항해)에서는 항해 틱이 안돌아서 괜찮지만
  // *항해에서는 부상틱과 겹칠 여지가 있을 지, 다른 스테이트에서는 문제 없을 지 확인 필요합니다.
  // 알림용으로 가드를 넣어놓습니다.
  if (!user.userState.isInOceanBattle() && !user.userState.isInOceanLand()) {
    mlog.error('opAddMateInjuryState. invalid state.', {
      userId: user.userId,
      gameState: user.userState.getGameState(),
    });
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (injuryTimeSec === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (injuryTimeSec < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const mateCms = cms.Mate[mateCmsId];
  if (!mateCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }

  const mate = tryData.mates.getMate(mateCmsId);
  if (!mate) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (mate.hasState(cmsEx.MATE_STATE_FLAG.INJURY)) {
    mlog.error('opAddMateInjuryState. already injured.', {
      userId: user.userId,
      mateCmsId,
    });
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!changes.injuryDuration) {
    changes.injuryDuration = {};
  }
  changes.injuryDuration[mateCmsId] = injuryTimeSec;
  const injuryExpireTimeUtc = curTimeUtc + injuryTimeSec;
  _.merge<All, All>(changes.syncAdd, {
    mates: {
      [mateCmsId]: {
        cmsId: mateCmsId,
        stateFlags: mate.addState(cmsEx.MATE_STATE_FLAG.INJURY, null),
        injuryExpireTimeUtc,
      },
    },
  });
  mate.setInjuryExpireTimeUtc(injuryExpireTimeUtc);
  mate.setInjuryImmuneTimeUtc(null);

  // apply 될 때는 부상 버프 적용되는 것 참고
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetAttendance(
  user: User,
  tryData: TryData,
  changes: Changes,
  attendance: Attendance
) {
  if (!tryData.attendance) {
    tryData.attendance = user.userAttendance.clone();
  }

  tryData.attendance.setAttendance(attendance);

  if (!changes.syncAdd.attendances) {
    changes.syncAdd.attendances = {};
  }

  changes.syncAdd.attendances[attendance.eventPageCmsId] = attendance;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddEnergy(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.energy) {
    tryData.energy = user.userEnergy.clone();
  }
  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }

  const curTimeUtc = mutil.curTimeUtc();
  const curEnergy = tryData.energy.getCurrentEnergy(curTimeUtc, tryData.userLevel);

  if (amount < 0 && curEnergy < -amount) {
    return CHANGE_TASK_RESULT.NOT_ENOUGH_ENERGY;
  }

  let energyChange: EnergyChange;
  if (amount < 0) {
    energyChange = tryData.energy.buildEnergyChangeWithConsume(
      curTimeUtc,
      tryData.userLevel,
      tryData.userLevel,
      -amount,
      false
    );

    if (changes.energyConsumeAmount) {
      changes.energyConsumeAmount += amount;
    } else {
      changes.energyConsumeAmount = amount;
    }
  } else {
    energyChange = tryData.energy.buildEnergyChange(
      curTimeUtc,
      tryData.userLevel,
      tryData.userLevel
    );
    if (!energyChange) {
      energyChange = {
        energy: tryData.energy.rawEnergy,
        lastUpdateTimeUtc: tryData.energy.lastUpdateEnergyTimeUtc,
      };
    }

    energyChange.energy += amount;
    if (energyChange.energy === curEnergy) {
      energyChange = undefined;
    }

    if (!changes.pointsGainAmount) {
      changes.pointsGainAmount = {};
    }
    if (!changes.pointsGainAmount[cmsEx.EnergyPointCmsId]) {
      changes.pointsGainAmount[cmsEx.EnergyPointCmsId] = amount;
    } else {
      changes.pointsGainAmount[cmsEx.EnergyPointCmsId] += amount;
    }
  }

  if (energyChange) {
    _.merge<All, All>(changes.syncAdd, tryData.energy.applyEnergyChange(energyChange, null).add);
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddShipSlotItem(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipSlotCmsId: number,
  amount: number,
  bAllowOverInven: boolean,
  bAllowAddToLimitIfExceeded: boolean,
  extraStr: string,
  bIsBound: boolean = true,
  bIfExceededAddMail: boolean = false
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  // try data
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const extra: RewardCmsElemShipSlotItemExtra = extraStr ? JSON.parse(extraStr) : undefined;

  if (!bAllowOverInven) {
    const added = tryData.inven.calcShipSlotItemAddable(amount);
    if (added !== amount) {
      if (bAllowAddToLimitIfExceeded) {
        amount = added;
        if (amount === 0) {
          return CHANGE_TASK_RESULT.OK;
        }
      } else {
        if (bIfExceededAddMail) {
          let exceededCount;
          if (added <= 0) {
            exceededCount = amount;
            amount = 0;
          } else {
            exceededCount = amount - added;
            amount = added;
          }

          const attachment: RewardCmsElem[] = [
            {
              Id: shipSlotCmsId,
              Type: REWARD_TYPE.SHIP_SLOT_ITEM,
              Quantity: exceededCount,
              Extra: extraStr,
            },
          ];
          AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
        } else {
          return CHANGE_TASK_RESULT.INVEN_FULL;
        }
      }
    }
  }

  const shipSlotCms = cms.ShipSlot[shipSlotCmsId];
  const curTimeUtc = mutil.curTimeUtc();
  const expireTimeUtc = extra?.expireTimeUtc;

  // 귀속 여부 결정.
  if (
    shipSlotCms.isCashMarket &&
    ((extra && extra.isBound === 0) || !bIsBound) &&
    !shipSlotCms.expireType
  ) {
    bIsBound = false;
  }

  for (let i = 0; i < amount; i++) {
    const shipSlotItem = tryData.inven.buildShipSlotItem(
      shipSlotCmsId,
      bIsBound ? 1 : 0,
      0,
      curTimeUtc,
      extra?.expireTimeUtc,
      extra?.enchantLv
    );
    tryData.inven.addShipSlotItem(shipSlotItem, null);

    _.merge<All, All>(changes.syncAdd, {
      shipSlotItems: {
        [shipSlotItem.id]: {
          id: shipSlotItem.id,
          shipSlotCmsId,
          isBound: bIsBound ? 1 : 0,
          isLocked: 0,
          expireTimeUtc,
          enchantLv: shipSlotItem.enchantLv,
        },
      },
    });

    changes.lastShipSlotItemId = shipSlotItem.id;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyMateExpChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  jobType: cmsEx.JOB_TYPE,
  mateExpChange: MateExpChange
): CHANGE_TASK_RESULT {
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }

  const mate = tryData.mates.getMate(mateExpChange.mateCmsId);
  const oldLevel = mate.getLevel(jobType);

  if (mate.getExp(jobType) === mateExpChange.exp && oldLevel === mateExpChange.level) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  mate.setExpLevel(jobType, mateExpChange, null, null);

  if (!changes.mateExp) {
    changes.mateExp = {};
  }
  if (!changes.mateExp[jobType]) {
    changes.mateExp[jobType] = [];
  }

  const change = changes.mateExp[jobType].find((changesElem) => {
    return changesElem.mateCmsId === mateExpChange.mateCmsId;
  });

  if (change) {
    change.exp = mateExpChange.exp;
    change.level = mateExpChange.level;
  } else {
    changes.mateExp[jobType].push({
      mateCmsId: mateExpChange.mateCmsId,
      exp: mateExpChange.exp,
      level: mateExpChange.level,
      oldLevel,
    });
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyEnergyChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  energyChange: EnergyChange
): CHANGE_TASK_RESULT {
  if (!energyChange) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.energy) {
    tryData.energy = user.userEnergy.clone();
  }

  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }

  const curTimeUtc = mutil.curTimeUtc();

  const curEnergy = tryData.energy.getCurrentEnergy(curTimeUtc, tryData.userLevel);
  const energyDiff = energyChange.energy - curEnergy;
  if (energyDiff < 0) {
    if (changes.energyConsumeAmount) {
      changes.energyConsumeAmount += energyDiff;
    } else {
      changes.energyConsumeAmount = energyDiff;
    }
  } else {
    if (!changes.pointsGainAmount) {
      changes.pointsGainAmount = {};
    }
    if (!changes.pointsGainAmount[cmsEx.EnergyPointCmsId]) {
      changes.pointsGainAmount[cmsEx.EnergyPointCmsId] = energyDiff;
    } else {
      changes.pointsGainAmount[cmsEx.EnergyPointCmsId] += energyDiff;
    }
  }

  _.merge<All, All>(changes.syncAdd, tryData.energy.applyEnergyChange(energyChange, null).add);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyPointChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  pointChange: PointChange
): CHANGE_TASK_RESULT {
  if (pointChange.cmsId === cmsEx.EnergyPointCmsId || isCash(pointChange.cmsId)) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }

  const curPoints = tryData.points.getPoint(pointChange.cmsId);
  if (curPoints === pointChange.value) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  const pointDiff = pointChange.value - curPoints;
  if (pointDiff > 0) {
    if (!changes.pointsGainAmount) {
      changes.pointsGainAmount = {};
    }
    if (!changes.pointsGainAmount[pointChange.cmsId]) {
      changes.pointsGainAmount[pointChange.cmsId] = pointDiff;
    } else {
      changes.pointsGainAmount[pointChange.cmsId] += pointDiff;
    }
  } else {
    if (!changes.pointsConsumeAmountForGlog) {
      changes.pointsConsumeAmountForGlog = {};
    }
    if (!changes.pointsConsumeAmountForGlog[pointChange.cmsId]) {
      changes.pointsConsumeAmountForGlog[pointChange.cmsId] = pointDiff;
    } else {
      changes.pointsConsumeAmountForGlog[pointChange.cmsId] += pointDiff;
    }
  }

  tryData.points.applyPointChanges([pointChange], null);
  _.merge<All, All>(changes.syncAdd, {
    points: {
      [pointChange.cmsId]: pointChange,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetUserExpLevel(
  user: User,
  tryData: TryData,
  changes: Changes,
  exp: number,
  level: number
): CHANGE_TASK_RESULT {
  if (tryData.userExp === undefined) {
    tryData.userExp = user.exp;
    tryData.userLevel = user.level;
  }
  if (mutil.isNotANumber(exp) || mutil.isNotANumber(level)) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (tryData.userExp === exp && tryData.userLevel === level) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.userExp = exp;
  tryData.userLevel = level;
  _.merge<All, All>(changes.syncAdd, {
    user: {
      exp,
      level,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyFreeTakebackChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  freeTakebackChange: BattleFreeTakebackChange
): CHANGE_TASK_RESULT {
  if (!tryData.battle) {
    tryData.battle = user.userBattle.clone();
  }

  if (
    tryData.battle.usedFreeTurnTakebackCount === freeTakebackChange.usedFreeTurnTakebackCount &&
    tryData.battle.usedFreePhaseTakebackCount === freeTakebackChange.usedFreePhaseTakebackCount &&
    tryData.battle.lastFreeTakebackUpdateTimeUtc === freeTakebackChange.updateTimeUtc
  ) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.battle.applyFreeTakebackChange(freeTakebackChange);
  _.merge<All, All>(changes.syncAdd, {
    user: {
      usedFreeTurnTakebackCount: freeTakebackChange.usedFreeTurnTakebackCount,
      usedFreePhaseTakebackCount: freeTakebackChange.usedFreePhaseTakebackCount,
      lastFreeTakebackUpdateTimeUtc: freeTakebackChange.updateTimeUtc,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyQuickModeChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  quickModeChange: BattleQuickModeChange
): CHANGE_TASK_RESULT {
  if (!tryData.battle) {
    tryData.battle = user.userBattle.clone();
  }

  if (
    tryData.battle.quickModeCount === quickModeChange.quickModeCount &&
    tryData.battle.lastQuickModeCountUpdateTimeUtc === quickModeChange.updateTimeUtc
  ) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.battle.applyQuickModeChange(quickModeChange);
  _.merge<All, All>(changes.syncAdd, {
    user: {
      quickModeCount: quickModeChange.quickModeCount,
      lastQuickModeCountUpdateTimeUtc: quickModeChange.updateTimeUtc,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 * 명성을 획득한다면, {@link cmsEx.ACHIEVEMENT_TERMS.GAIN_FAME | 명성 획득 업적}을 따로 누적해줘야 합니다.
 */
export function opSetFame(
  user: User,
  tryData: TryData,
  changes: Changes,
  jobType: cmsEx.JOB_TYPE,
  fame: number
): CHANGE_TASK_RESULT {
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  const leader = UserMates.getLeaderMate(tryData.fleets, tryData.mates);
  if (!leader) {
    mlog.error('[RewardTask] Cannot add fame without an admiral.', {
      userId: user.userId,
    });
    throw new MError('no-adirmal', MErrorCode.NEED_ADMIRAL);
  }

  // Check job type.
  if (jobType < cmsEx.JOB_TYPE.ADVENTURE || jobType > cmsEx.JOB_TYPE.BATTLE) {
    mlog.error('[RewardTask] Invalid fame job type.', {
      userId: user.userId,
      jobType: jobType,
    });
    throw new MError('invalid-job-type', MErrorCode.INVALID_JOB_TYPE);
  }

  if (leader.getFame(jobType) === fame) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // Update the try data.
  leader.setFame(jobType, fame, null);

  // Save the change.
  if (!changes.syncAdd.mates) {
    changes.syncAdd.mates = {};
  }
  if (!changes.syncAdd.mates[leader.getNub().cmsId]) {
    changes.syncAdd.mates[leader.getNub().cmsId] = {
      cmsId: leader.getNub().cmsId,
    };
  }

  switch (jobType) {
    case cmsEx.JOB_TYPE.ADVENTURE:
      changes.syncAdd.mates[leader.getNub().cmsId].adventureFame = fame;
      break;
    case cmsEx.JOB_TYPE.TRADE:
      changes.syncAdd.mates[leader.getNub().cmsId].tradeFame = fame;
      break;
    case cmsEx.JOB_TYPE.BATTLE:
      changes.syncAdd.mates[leader.getNub().cmsId].battleFame = fame;
      break;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetGameOverLosses(
  user: User,
  tryData: TryData,
  changes: Changes,
  gameOverLosses: GameOverLosses
): CHANGE_TASK_RESULT {
  if (!tryData.sailing) {
    tryData.sailing = user.userSailing.clone();
  }
  if (_.isEqual(tryData.sailing.getGameOverLosses(), gameOverLosses)) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.sailing.setGameOverLosses(gameOverLosses);

  changes.gameOverLosses = gameOverLosses;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetMultiPvpLoss(
  user: User,
  tryData: TryData,
  changes: Changes,
  multiPvpLoss: any
): CHANGE_TASK_RESULT {
  if (!tryData.sailing) {
    tryData.sailing = user.userSailing.clone();
  }

  if (_.isEqual(tryData.sailing.getMultiPvpLoss(), {})) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.sailing.setMultiPvpLoss(multiPvpLoss);

  changes.multiPvpLoss = multiPvpLoss;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetShipDurabilityAndSailor(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipId: number,
  durability: number,
  sailor: number
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  const ship = tryData.fleets.getShip(shipId);

  if (ship.getNub().durability === durability && ship.getNub().sailor === sailor) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (ship.getNub().durability !== durability) {
    ship.setDurability(durability, null, null);
    _.merge<All, All>(changes.syncAdd, {
      ships: {
        [shipId]: {
          id: shipId,
          durability,
        },
      },
    });
  }

  if (ship.getNub().sailor !== sailor) {
    ship.setSailor(sailor, null, false, null);
    _.merge<All, All>(changes.syncAdd, {
      ships: {
        [shipId]: {
          id: shipId,
          sailor,
        },
      },
    });
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetShipLife(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipId: number,
  life: number
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const ship = tryData.fleets.getShip(shipId);

  if (ship.getNub().life === life) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // 수명으로 인한 최대 내구도 감소된 상태에서 현재 내구도가 크다면 최대 내구도로 변경된다
  if (ship.getNub().life !== 0 && life === 0) {
    const expectMaxDurability = Math.floor(ship.getMaxDurability(tryData.stats) * 0.5);
    if (ship.getNub().durability > expectMaxDurability) {
      ship.setDurability(expectMaxDurability, null, null);
      _.merge<All, All>(changes.syncAdd, {
        ships: {
          [shipId]: {
            id: shipId,
            durability: expectMaxDurability,
          },
        },
      });
    }
  }

  ship.setLife(life, null, tryData.stats);
  _.merge<All, All>(changes.syncAdd, {
    ships: {
      [shipId]: {
        id: shipId,
        life,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetVillageLastDepartureTimeUtc(
  user: User,
  tryData: TryData,
  changes: Changes,
  villageCmsId: number,
  lastDepartureTimeUtc: number
): CHANGE_TASK_RESULT {
  if (!tryData.village) {
    tryData.village = user.userVillage.clone();
  }
  const village: Village = tryData.village.getVillage(villageCmsId);
  village.lastDepartureTimeUtc = lastDepartureTimeUtc;
  _.merge<All, All>(changes.syncAdd, {
    villages: {
      [villageCmsId]: village,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetVillageFriendshipRewarded(
  user: User,
  tryData: TryData,
  changes: Changes,
  villageCmsId: number,
  grade: number,
  bIsWeeklyReward: boolean
) {
  if (!tryData.village) {
    tryData.village = user.userVillage.clone();
  }

  const village = tryData.village.getVillage(villageCmsId);
  if (bIsWeeklyReward) {
    village.lastReceiveFriendshipWeeklyRewardGrade = grade;
    village.lastReceiveFriendshipWeeklyRewardTimeUtc = mutil.curTimeUtc();
  } else {
    village.friendshipFirstRewardReceiveBitflag += 1 << grade;
  }

  _.merge<All, All>(changes.syncAdd, {
    villages: {
      [villageCmsId]: village,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyShipCargoChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  cargoChange: ShipCargoChange
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  const ship = tryData.fleets.getShip(cargoChange.shipId);

  if (ship.getCargoQuantity(cargoChange.cmsId) === cargoChange.quantity) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  ship.applyCargoChange(cargoChange, null, null);
  _.merge<All, All>(changes.syncAdd, {
    ships: {
      [cargoChange.shipId]: {
        id: cargoChange.shipId,
        cargos: {
          [cargoChange.cmsId]: cargoChange,
        },
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyInsuranceUnpaidChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  insuranceUnpaidChange: InsuranceUnpaidChange
): CHANGE_TASK_RESULT {
  if (!tryData.points) {
    tryData.points = user.userPoints.clone();
  }

  const oldInsurance = tryData.points.getInsurance();
  if (
    oldInsurance.unpaidDucat === insuranceUnpaidChange.unpaidDucat &&
    oldInsurance.unpaidSailor === insuranceUnpaidChange.unpaidSailor &&
    oldInsurance.unpaidShip === insuranceUnpaidChange.unpaidShip &&
    oldInsurance.unpaidTradeGoods === insuranceUnpaidChange.unpaidTradeGoods
  ) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.points.applyInsuranceUnpaidChange(insuranceUnpaidChange);
  _.merge<All, All>(changes.syncAdd, {
    insurance: insuranceUnpaidChange,
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetQuestFlags(
  user: User,
  tryData: TryData,
  changes: Changes,
  questCmsId: number,
  uflags: number,
  lflags: number
): CHANGE_TASK_RESULT {
  if (!tryData.questManager) {
    tryData.questManager = user.questManager.clone();
  }

  const oldQuest = tryData.questManager.getContext(questCmsId);
  if (oldQuest.uflags === uflags && oldQuest.lflags === lflags) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  user.questManager.applyCtxChange({
    cmsId: questCmsId,
    uflags,
    lflags,
  });
  _.merge<All, All>(changes.syncAdd, {
    questData: {
      contexts: {
        [questCmsId]: {
          cmsId: questCmsId,
          uflags,
          lflags,
        },
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyItemChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  itemChange: ItemChange
): CHANGE_TASK_RESULT {
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const accums: AccumulateParam[] = [];

  _.merge<All, All>(
    changes.syncAdd,
    tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add
  );

  if (accums && accums.length > 0) {
    if (!changes.itemGainAccumParams) {
      changes.itemGainAccumParams = [];
    }

    changes.itemGainAccumParams.push(
      ...accums.map((acc) => {
        return { targets: acc.targets, addedValue: acc.addedValue };
      })
    );
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opBattleLogEnd(
  user: User,
  tryData: TryData,
  changes: Changes,
  battleId: string
): CHANGE_TASK_RESULT {
  changes.battleIdForLogEnd = battleId;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetChallenge(
  user: User,
  tryData: TryData,
  changes: Changes,
  challenge: Challenge
): CHANGE_TASK_RESULT {
  if (!tryData.challenge) {
    tryData.challenge = user.userChallenges.clone();
  }

  if (_.isEqual(challenge, tryData.challenge.getChallenge(challenge.cmsId))) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.challenge.updateChallenge(challenge);
  _.merge<All, All>(changes.syncAdd, {
    challenges: {
      [challenge.cmsId]: challenge,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opDropQuest(
  user: User,
  tryData: TryData,
  changes: Changes,
  questCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.questManager) {
    tryData.questManager = user.questManager.clone();
  }

  if (!tryData.questManager.getContext(questCmsId)) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.questManager.dropQuest(questCmsId);

  _.merge(changes.syncRemove, {
    questData: {
      contexts: {
        [questCmsId]: true,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// 수정시 UserQuestManager의 questDrop도 확인 필요
export function opQuestDropByReturn(
  user: User,
  tryData: TryData,
  changes: Changes,
  curTimeUtc: number
): CHANGE_TASK_RESULT {
  if (!tryData.questManager) {
    tryData.questManager = user.questManager.clone();
  }
  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const dropQuestCmsIds: number[] = [];

  const royalOrderQuestCtxs: QuestContext[] = [];
  const notRoyalOrderQuestCtxs: QuestContext[] = [];
  _.forOwn(tryData.questManager.contexts, (elem) => {
    const questCms = cms.Quest[elem.cmsId];
    if (!questCms?.isDropByReturn) {
      return;
    }

    // 명성 페널티
    // 어드민 일시 정지된 상태에 한해서 패널티를 적용하지 않는다.
    if (!elem.isAdminPaused) {
      if (questCms.category === cmsEx.QUEST_CATEGORY.ROYAL_ORDER) {
        royalOrderQuestCtxs.push(elem);
      } else {
        notRoyalOrderQuestCtxs.push(elem);
      }
    }

    if (questCms.category === cmsEx.QUEST_CATEGORY.ROYAL_ORDER && !questCms.isRoyalTitle) {
      // 칙명 취소하면 다음날까지 못함
      tryData.questManager.setLastRoyalOrderCompletedTimeUtc(curTimeUtc);

      _.merge<All, All>(changes.syncAdd, {
        user: {
          lastRoyalOrderCompletedTimeUtc: curTimeUtc,
        },
      });
    }

    // 종업원 퀘스트 포기 쿨타임
    if (questCms.category === cmsEx.QUEST_CATEGORY.PUB_STAFF) {
      if (!changes.pubStaffChanges) {
        changes.pubStaffChanges = [];
      }

      const townCmsId = cmsEx.getTownCmsIdForPubStaffQuestCmsId(questCms.id);
      const pubStaffChange = _.cloneDeep(user.userTown.getPubStaff(townCmsId));
      if (!pubStaffChange) {
        return CHANGE_TASK_RESULT.INTERNAL_ERROR;
      }
      pubStaffChange.questBlockTimeUtc = curTimeUtc + cms.Const.PubstaffQuestCooltimeSec.value;
      changes.pubStaffChanges.push(pubStaffChange);
    }

    // 퀘스트 포기로 인한 아이템 제거
    if (questCms.clearItemIds && questCms.clearItemIds.length > 0) {
      const itemChanges = tryData.inven.itemInven.buildItemChangesForRemovingAll(
        new Set(questCms.clearItemIds),
        false
      );
      for (const itemChange of itemChanges) {
        _.merge<All, All>(
          changes.syncAdd,
          tryData.inven.itemInven.applyItemChange(itemChange, null, null).add
        );
      }
    }

    if (!changes.questDropForGlogs) {
      changes.questDropForGlogs = [];
    }
    changes.questDropForGlogs.push({ questCmsId: questCms.id, step: elem.nodeIdx });

    dropQuestCmsIds.push(questCms.id);
  });

  // 칙명 먼저 계산 후 나머지 계산
  const leaderMate = tryData.mates.getLeaderMate(tryData.fleets);
  for (const ctx of royalOrderQuestCtxs.concat(notRoyalOrderQuestCtxs)) {
    const questCms = cms.Quest[ctx.cmsId];
    const mateChange = QuestUtil.calcDropQuestMateFame(leaderMate, questCms);
    leaderMate.applyFameByMateChange(mateChange, { add: changes.syncAdd }, null);
  }

  for (const questCmsId of dropQuestCmsIds) {
    tryData.questManager.dropQuest(questCmsId);

    _.merge(changes.syncRemove, {
      questData: {
        contexts: {
          [questCmsId]: true,
        },
      },
    });
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetBattleEndResult(
  user: User,
  tryData: TryData,
  changes: Changes,
  battleEndResult: BattleEndResult
): CHANGE_TASK_RESULT {
  changes.syncAdd.battleEndResult = battleEndResult;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opDismantleShip(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipId: number,
  firedSailors: number
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }

  const userShip = tryData.fleets.getShip(shipId);
  if (!userShip) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  // 기함은 분해 못 함.
  if (userShip.getNub().formationIndex === cmsEx.FlagShipFormationIndex) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  // 잠긴 선박은 분해 못함.
  if (userShip.getNub().isLocked === 1) {
    return CHANGE_TASK_RESULT.LOCKED_SHIP;
  }

  if (!changes.shipIdsToDismantle) {
    changes.shipIdsToDismantle = [];
  }
  changes.shipIdsToDismantle.push(shipId);

  if (firedSailors > 0) {
    if (!changes.firedSailors) {
      changes.firedSailors = 0;
    }
    changes.firedSailors += firedSailors;
  }

  // 부품 해제
  if (!changes.unequippedShipSlotItemIds) {
    changes.unequippedShipSlotItemIds = [];
  }
  const bpCms = cms.Ship[userShip.getNub().cmsId].shipBlueprint;
  for (const slot of bpCms.shipSlot) {
    const defaultSlotCms = cms.ShipSlot[slot.Id];
    if (
      defaultSlotCms.slotType === SHIP_SLOT_TYPE.MATE ||
      defaultSlotCms.slotType === SHIP_SLOT_TYPE.NON_MATE
    ) {
      continue;
    }

    const shipSlot = userShip.getSlot(slot.Index);
    if (!shipSlot || !shipSlot.shipSlotItemId) {
      continue;
    }

    changes.unequippedShipSlotItemIds.push(shipSlot.shipSlotItemId);
  }
  // 인벤 공간이 부족하면 부품을 해제 할 수 없다
  if (!user.userInven.canAddShipSlotItem(changes.unequippedShipSlotItemIds.length)) {
    return CHANGE_TASK_RESULT.SHIP_SLOT_INVEN_FULL;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddUserShipBuildingExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  cultureType: SHIP_CULTURE_TYPE,
  addedExp: number
) {
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }

  const userShipBuildingChange: UserShipBuildingChange =
    tryData.shipBlueprints.buildUserShipBuildingChange(cultureType, addedExp);

  // 만렙인 경우 null.
  if (userShipBuildingChange === null) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.shipBlueprints.setUserShipBuildingExpLevel(userShipBuildingChange);

  _.merge<All, All>(changes.syncAdd, {
    user: {
      westShipBuildLevel: userShipBuildingChange.westShipBuildLevel,
      westShipBuildExp: userShipBuildingChange.westShipBuildExp,

      orientShipBuildLevel: userShipBuildingChange.orientShipBuildLevel,
      orientShipBuildExp: userShipBuildingChange.orientShipBuildExp,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetShipSailor(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipId: number,
  sailor: number
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  const ship = tryData.fleets.getShip(shipId);

  if (ship.getNub().sailor === sailor) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  ship.setSailor(sailor, null, false, null);
  _.merge<All, All>(changes.syncAdd, {
    ships: {
      [shipId]: {
        id: shipId,
        sailor,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetShipFormationIndex(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipId: number,
  formationIndex: number
): CHANGE_TASK_RESULT {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  const ship = tryData.fleets.getShip(shipId);

  if (ship.getNub().formationIndex === formationIndex) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  ship.setFormationIndex(formationIndex, null);
  _.merge<All, All>(changes.syncAdd, {
    ships: {
      [shipId]: {
        id: shipId,
        formationIndex,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// ! operator 에 비지니스 로직이 들어가면 안되지만,
// ! 리워드 시스템에서 적용이 어려움이 있어 예외적으로 로직이 들어가 있습니다.
// ! 우편 지급은 문제가 있음.
// - 예) 수령 전 우편 아이콘 '상급 편지', 수령 시 퀘스트 뽑기 실패 등으로 '빈 편지(대체 아이템)'
// @param bAllowAddToLimitIfExceeded 대체 아이템에 적용되는 옵션인 것 참고
export function opAddQuestItem(
  user: User,
  tryData: TryData,
  changes: Changes,
  itemCmsId: number,
  amount: number,
  bAllowOverInven: boolean,
  bAllowAddToLimitIfExceeded: boolean
): CHANGE_TASK_RESULT {
  const itemCms = cms.Item[itemCmsId];
  if (!itemCms || itemCms.type !== ITEM_TYPE.RANDOM_QUEST) {
    return CHANGE_TASK_RESULT.CMS_ERROR;
  }

  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  } else if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  // try data
  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }
  if (!tryData.questManager) {
    tryData.questManager = user.questManager.clone();
  }

  const addReplacementItem = (repItemCmsId: number, repCount: number): CHANGE_TASK_RESULT => {
    assert(repCount > 0);
    const replacementItemCms = cms.Item[repItemCmsId];
    if (!replacementItemCms) {
      mlog.error('[RewardTask] opAddQuestItem. Invalid itemCmsId(replacement item)', {
        repItemCmsId,
      });
      return CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (replacementItemCms.type === ITEM_TYPE.RANDOM_QUEST) {
      // 대체 아이템은 '퀘스트 시작 아이템'이 아니어야 함.
      mlog.error('[RewardTask] opAddQuestItem. Replacement item can not be quest starting item.', {
        repItemCmsId,
      });
      return CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }

    const accums: AccumulateParam[] = [];

    const bIsBound = true;
    const result = tryData.inven.itemInven.calcReceivable(
      repItemCmsId,
      repCount,
      bIsBound,
      bAllowOverInven
    );
    if (result.receivable !== repCount) {
      if (bAllowAddToLimitIfExceeded) {
        repCount = result.receivable;
        if (repCount === 0) {
          return CHANGE_TASK_RESULT.OK;
        }
      } else {
        mlog.warn('[RewardTask] opAddQuestItem. Exceeds limit(replacement item).', {
          userId: user.userId,
          repItemCmsId,
          toAdd: repCount,
          receivableResult: result,
        });
        return result.excessMaxHavable
          ? CHANGE_TASK_RESULT.ITEM_MAX_HAVABLE
          : result.excessSpace
          ? CHANGE_TASK_RESULT.INVEN_FULL
          : CHANGE_TASK_RESULT.INTERNAL_ERROR;
      }
    }

    const itemChange = tryData.inven.itemInven.buildItemChange(
      repItemCmsId,
      repCount,
      true,
      bIsBound
    );
    _.merge<All, All>(
      changes.syncAdd,
      tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add
    );

    if (accums && accums.length > 0) {
      if (!changes.itemGainAccumParams) {
        changes.itemGainAccumParams = [];
      }

      changes.itemGainAccumParams.push(
        ...accums.map((acc) => {
          return { targets: acc.targets, addedValue: acc.addedValue };
        })
      );
    }

    mlog.verbose('[RewardTask] opAddQuestItem. quest-starting-item replaced', {
      repItemCmsId,
      repCount,
    });

    return CHANGE_TASK_RESULT.OK;
  };
  //

  const result = tryData.inven.itemInven.calcQuestItemReceivable(
    itemCmsId,
    amount,
    bAllowOverInven
  );

  if (result.receivable !== amount) {
    mlog.verbose('[RewardTask] opAddQuestItem. Exceeds limit.', {
      userId: user.userId,
      itemCmsId,
      curCount: tryData.inven.itemInven.getQuestItemCount(itemCmsId),
      toAdd: amount,
      receivableResult: result,
    });

    // 소지 정책으로 인해 받을 수 없는 초과분은 대체 아이템으로 지급한다.
    const excessAmount = amount - result.receivable;
    if (excessAmount > 0) {
      const res = addReplacementItem(cms.Const.QuestStartItemReplaceItemId.value, excessAmount);
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    amount = result.receivable;
    if (amount === 0) {
      return CHANGE_TASK_RESULT.OK;
    }
  }

  // quest 를 선택하기 위해서는 user가 필요한데, 여기서는 예외적으로 changeTask 영향을 받기 전의 user를 사용한다.
  // (치명적이지 않기 때문에)
  // 다만, 퀘스트 완료 같은 operator 가 추가된다면 uniqueGroup 관련해서 문제 없을지 확인이 필요할 듯 함.
  const arrPickedQuest: QuestCandidate[] = [];
  QuestUtil.pickQuestItemQuestUsingUser(
    itemCms.id,
    user,
    new Set(_.map(tryData.questManager.contexts, (v) => v.cmsId)),
    true,
    amount,
    arrPickedQuest,
    false /* bEnsure */
  );

  for (let i = 0; i < amount; i++) {
    const pickedQuest = arrPickedQuest[i];
    if (!pickedQuest) {
      break;
    }

    const id = tryData.inven.itemInven.getNewQuestItemId();
    const questItem: QuestItem = {
      id,
      cmsId: itemCmsId,
      questCmsId: pickedQuest.questCmsId,
      questRnds: pickedQuest.rnds,
    };
    tryData.inven.itemInven.addQuestItem(questItem);

    _.merge<All, All>(changes.syncAdd, {
      questItems: {
        [id]: questItem,
      },
    });

    changes.lastQuestItemId = id;
  }

  const failedCntForPickingQuest = amount - arrPickedQuest.length;
  if (failedCntForPickingQuest > 0) {
    mlog.verbose('[RewardTask] opAddQuestItem. failed to pick quest.', {
      userId: user.userId,
      itemCmsId,
      failedCntForPickingQuest,
    });
    // 퀘스트 뽑기에 실패한 경우, 실패한 만큼 대체 아이템 지급
    const res = addReplacementItem(
      cms.Const.QuestStartItemReplaceItemId.value,
      failedCntForPickingQuest
    );
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetLineMailState(
  user: User,
  tryData: TryData,
  changes: Changes,
  mailId: number,
  state: MAIL_STATE
): CHANGE_TASK_RESULT {
  if (!tryData.mails) {
    tryData.mails = user.userMails.clone();
  }

  // validate
  const mail = tryData.mails.getUserLineMail(mailId);
  if (!mail) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (mail.state === state) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  // save
  tryData.mails.setLineMailState(mailId, state);
  _.merge<All, All>(changes.syncAdd, {
    lineMails: {
      [mailId]: {
        id: mailId,
        state,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opConsumeShield(
  user: User,
  tryData: TryData,
  changes: Changes,
  shieldCmsId: SHIELD_CMS_ID,
  curTimeUtc: number,
  userLevel: number
): CHANGE_TASK_RESULT {
  if (!tryData.shield) {
    tryData.shield = user.userShield.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  if (!tryData.shield.hasShield(shieldCmsId, curTimeUtc, userLevel, user.companyStat)) {
    return CHANGE_TASK_RESULT.NOT_ENOUGH_SHIELD;
  }

  const change = tryData.shield.buildShieldChangeWithConsume(
    shieldCmsId,
    curTimeUtc,
    userLevel,
    false,
    tryData.stats
  );
  tryData.shield.applyShieldChange(change, null);

  _.merge<All, All>(changes.syncAdd, {
    shields: {
      [change.cmsId]: change,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddShieldNonPurchaseCount(
  user: User,
  tryData: TryData,
  changes: Changes,
  shieldCmsId: number,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  } else if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.shield) {
    tryData.shield = user.userShield.clone();
  }
  if (!tryData.userLevel) {
    tryData.userLevel = user.level;
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const curTimeUtc = mutil.curTimeUtc();
  const change = tryData.shield.buildShieldChange(
    shieldCmsId,
    curTimeUtc,
    tryData.userLevel,
    tryData.stats
  );
  change.nonPurchaseCount += amount;
  tryData.shield.applyShieldChange(change, null);

  _.merge<All, All>(changes.syncAdd, {
    shields: {
      [change.cmsId]: change,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddShieldPurchaseCount(
  user: User,
  tryData: TryData,
  changes: Changes,
  shieldCmsId: number,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  } else if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.shield) {
    tryData.shield = user.userShield.clone();
  }
  if (!tryData.userLevel) {
    tryData.userLevel = user.level;
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const curTimeUtc = mutil.curTimeUtc();
  const change = tryData.shield.buildShieldChange(
    shieldCmsId,
    curTimeUtc,
    tryData.userLevel,
    tryData.stats
  );
  change.purchaseCount += amount;
  tryData.shield.applyShieldChange(change, null);

  _.merge<All, All>(changes.syncAdd, {
    shields: {
      [change.cmsId]: change,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddPalaceTaxFreePermit(
  user: User,
  tryData: TryData,
  changes: Changes,
  taxFreePermitCmsId: number,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  } else if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  const taxFreePermitCms = cms.TaxFreePermit[taxFreePermitCmsId];
  if (!taxFreePermitCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (cmsEx.getTaxFreePermitCashShopCmsId(taxFreePermitCmsId)) {
    mlog.error('[RewardTask] opAddTaxFreePermit. can not reward taxFreePermit of cashShop', {
      userId: user.userId,
      taxFreePermitCmsId,
    });
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.trade) {
    tryData.trade = user.userTrade.clone();
  }

  const timeSecToAdd = taxFreePermitCms.expiredTimeValue * amount;

  const curTimeUtc = mutil.curTimeUtc(); // 다른 operator 들도 따로 인자로 넣어주진 않긴 한데, 리팩토링시 참고
  const curExpirationTimeUtc = tryData.trade.getTaxFreePermitExpiration(taxFreePermitCmsId);
  const newExpirationTimeUtc =
    curExpirationTimeUtc && curExpirationTimeUtc > curTimeUtc // 면세증을 갖고 있고, 만료되지 않았는지
      ? curExpirationTimeUtc + timeSecToAdd
      : curTimeUtc + timeSecToAdd;

  if (newExpirationTimeUtc > cmsEx.TheEndTimeUtc) {
    // cmsEx.EndTimeUtc 가 DB 의 최대치를 의미하진 않는 듯한데..
    mlog.error('[RewardTask] opAddTaxFreePermit. Invalid expirationTimeUtc.', {
      userId: user.userId,
      newExpirationTimeUtc,
      amount,
    });
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  //* 보상으로 받을 때는 구매할 때와 다르게 최대 일수 제한을 적용하지 않음.
  // if (false) {
  //   if (
  //     newExpirationTimeUtc - curTimeUtc >=
  //     cms.Const.CashShopDurationDaysLimit.value * SECONDS_PER_DAY
  //   ) {
  //     return CHANGE_TASK_RESULT; // NOTHING? FULL?
  //   }
  // }

  tryData.trade.setTaxFreePermitExpiration(taxFreePermitCmsId, newExpirationTimeUtc);

  _.merge<All, All>(changes.syncAdd, {
    taxFreePermits: {
      [taxFreePermitCmsId]: {
        cmsId: taxFreePermitCmsId,
        expirationTimeUtc: newExpirationTimeUtc,
      },
    },
  });

  if (!changes.addedPalaceTaxFreePermitCounts) {
    changes.addedPalaceTaxFreePermitCounts = {};
  }

  if (!changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId]) {
    changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId] = amount;
  } else {
    changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId] += amount;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
export function opDeleteContributionShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (changes.syncAdd.contributionShopRestrictedProducts) {
    assert.fail('do-not-use-after-change-contributionShopRestrictedProducts');
    // contributionShopRestrictedProducts 를 변경하기 전에 사용해야 합니다.
    // 간단하게만 사용할 듯 해서 편의상, 각 상황에 대한 처리를 구현하지 않고 순서를 정했습니다.
  }
  if (!changes.syncRemove.contributionShopRestrictedProducts) {
    changes.syncRemove.contributionShopRestrictedProducts = [];
  }
  changes.syncRemove.contributionShopRestrictedProducts.push(cmsId.toString());

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetContributionShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  restrictedProduct: ContributionShopRestrictedProduct
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.contributionShopRestrictedProducts) {
    changes.syncAdd.contributionShopRestrictedProducts = {};
  }
  changes.syncAdd.contributionShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
export function opDeleteGuildShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (changes.syncAdd.guildShopRestrictedProducts) {
    assert.fail('do-not-use-after-change-guildShopRestrictedProducts');
    // guildShopRestrictedProducts 를 변경하기 전에 사용해야 합니다.
    // 간단하게만 사용할 듯 해서 편의상, 각 상황에 대한 처리를 구현하지 않고 순서를 정했습니다.
  }
  if (!changes.syncRemove.guildShopRestrictedProducts) {
    changes.syncRemove.guildShopRestrictedProducts = [];
  }
  changes.syncRemove.guildShopRestrictedProducts.push(cmsId.toString());

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetGuildShopRestrictedProduct(
  user: User,
  tryData: TryData,
  changes: Changes,
  restrictedProduct: GuildShopRestrictedProduct
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.guildShopRestrictedProducts) {
    changes.syncAdd.guildShopRestrictedProducts = {};
  }
  changes.syncAdd.guildShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opLandExploreAchievement(
  user: User,
  tryData: TryData,
  changes: Changes,
  landExploreCmsId: number
): CHANGE_TASK_RESULT {
  if (!landExploreCmsId) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  changes.landExploreCmsId = landExploreCmsId;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddDiscovery(
  user: User,
  tryData: TryData,
  changes: Changes,
  discoveryCmsId: number
): CHANGE_TASK_RESULT {
  if (!discoveryCmsId) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.userDiscovery) {
    tryData.userDiscovery = user.userDiscovery.clone();
  }

  // discovery update
  tryData.userDiscovery.discover(discoveryCmsId, user, true);
  if (!changes.discoveryCmsIds) {
    changes.discoveryCmsIds = [];
  }
  changes.discoveryCmsIds.push(discoveryCmsId);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddArenaTicket(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
): CHANGE_TASK_RESULT {
  if (amount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }
  if (amount < 0) {
    // 지원하지 않는다.
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (changes.addedArenaTicket === undefined) {
    changes.addedArenaTicket = amount;
  } else {
    changes.addedArenaTicket += amount;
  }
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetPassEventLastDailyResetTimeUtc(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventPageCmsId: number,
  lastDailyResetTimeUtc: number
): CHANGE_TASK_RESULT {
  const passEventPageCms = cms.EventPage[eventPageCmsId];
  if (!passEventPageCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.passEvent) {
    tryData.passEvent = user.userPassEvent.clone();
  }

  tryData.passEvent.setPassEventLastDailyResetTimeUtc(passEventPageCms.id, lastDailyResetTimeUtc);

  if (!changes.passEvents) {
    changes.passEvents = {};
  }
  if (!changes.passEvents[passEventPageCms.id]) {
    changes.passEvents[passEventPageCms.id] = {
      eventPageCmsId: passEventPageCms.id,
    };
  }
  changes.passEvents[passEventPageCms.id].lastDailyResetTimeUtc = lastDailyResetTimeUtc;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// 리워드 시스템으로 지급하려다 안하게 된거라 리워드 시스템을 위한 잔재(굳이 필요없을 검사 등)가 남아있는데,
// 추후에 아예 ReceivePassEventMissionRewardSpec 에서만 사용되는 것으로 한정하는 게 나을 듯함.
export function opAddPassEventExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventPageCmsId: number,
  expAmount: number,
  curTimeUtc: number
): CHANGE_TASK_RESULT {
  if (!Number.isInteger(expAmount)) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  } else if (expAmount < 0) {
    // 지원하지 않는다.
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  if (!Number.isInteger(curTimeUtc)) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const passEventPageCms = cms.EventPage[eventPageCmsId];
  if (!passEventPageCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }
  if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (expAmount === 0) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.passEvent) {
    tryData.passEvent = user.userPassEvent.clone();
  }

  if (UserPassEvent.isOutOfPeriodRewardTimeIncluded(curTimeUtc, passEventPageCms)) {
    mlog.info('opAddPassEventExp ignored. event page expired', {
      userId: user.userId,
      eventPageCmsId: passEventPageCms.id,
      curTimeUtc,
    });
    // 에러가 나을지?
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (user.userPassEvent.isPassEventCompleted(passEventPageCms.id)) {
    // 굳이 막는다면 operator 실행 순서에 따라서 기대결과랑 다르게 동작할 수 있을 법한 부분 고민해봐야할 듯?
    // 관련해서 일단 tryData 가 아닌 user 에서 이미 완료됐었는지를 가져오고 로그만 남김.
    mlog.info('opAddPassEventExp operated on completed pass event', {
      userId: user.userId,
      eventPageCmsId: passEventPageCms.id,
    });
  }

  const maxPassEventExp = cmsEx.getMaxPassEventExp(passEventPageCms);
  const { exp: oldAccumulatedExp } = tryData.passEvent.getPassEventExpLevel(passEventPageCms);
  if (oldAccumulatedExp >= maxPassEventExp) {
    // 다른 EXP 들이 최대치만큼만 지급하고 있고, 동일한 정책이라고 함.
    return CHANGE_TASK_RESULT.NOTHING;
  }
  const newAccumulatedExp = Math.min(oldAccumulatedExp + expAmount, maxPassEventExp);

  if (!changes.passEvents) {
    changes.passEvents = {};
  }
  if (!changes.passEvents[passEventPageCms.id]) {
    changes.passEvents[passEventPageCms.id] = {
      eventPageCmsId: passEventPageCms.id,
    };
  }
  if (!changes.passEvents[passEventPageCms.id].expChange) {
    changes.passEvents[passEventPageCms.id].expChange = {
      exp: undefined, // 밑에서 설정됨.
      level: undefined, // 밑에서 설정됨.
      addedExp: 0,
    };
  }
  const change = changes.passEvents[passEventPageCms.id].expChange;
  change.exp = newAccumulatedExp;
  change.level = cmsEx.calcPassEventLevel(passEventPageCms, newAccumulatedExp);
  change.addedExp += newAccumulatedExp - oldAccumulatedExp;

  tryData.passEvent.setPassEventExpLevel(passEventPageCms, change.exp, change.level, null);
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyExploreTicketChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  ticketChange: ExploreTicketChange
): CHANGE_TASK_RESULT {
  if (!ticketChange) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.explore) {
    tryData.explore = user.userExplore.clone();
  }
  _.merge<All, All>(changes.syncAdd, tryData.explore.applyTicketChange(ticketChange, null).add);
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyExploreQuickModeChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  quickModeChange: ExploreQuickModeChange
): CHANGE_TASK_RESULT {
  if (!quickModeChange) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.explore) {
    tryData.explore = user.userExplore.clone();
  }
  _.merge<All, All>(
    changes.syncAdd,
    tryData.explore.applyQuickModeChange(quickModeChange, null).add
  );
}

// -------------------------------------------------------------------------------------------------
export function opApplyTradeArea(
  user: User,
  tryData: TryData,
  changes: Changes,
  tradeAreaChange: TradeAreaChange
): CHANGE_TASK_RESULT {
  if (!tryData.tradeArea) {
    tryData.tradeArea = user.userTradeArea.clone();
  }

  tryData.tradeArea.applyChange(tradeAreaChange, undefined);
  if (!changes.syncAdd.tradeArea) {
    changes.syncAdd.tradeArea = {};
  }
  changes.syncAdd.tradeArea[tradeAreaChange.tradeAreaCmsId] = tradeAreaChange;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetEventGame(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventGame: EventGameChange
) {
  if (!changes.syncAdd.eventGames) {
    changes.syncAdd.eventGames = {};
  }

  if (!changes.syncAdd.eventGames[eventGame.eventPageCmsId]) {
    changes.syncAdd.eventGames[eventGame.eventPageCmsId] = {};
  }
  changes.syncAdd.eventGames[eventGame.eventPageCmsId][eventGame.eventGameCmsId] = eventGame;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddShipBlueprintSailMasteryExp(
  user: User,
  tryData: TryData,
  changes: Changes,
  addedExp: number
) {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }
  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }
  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  if (!changes.syncAdd.shipBlueprints) {
    changes.syncAdd.shipBlueprints = {};
  }
  if (!changes.bpSailMasteryforGlog) {
    changes.bpSailMasteryforGlog = {};
  }

  const bpSailMasteryExpChanges: { [bpCmsId: number]: ShipBpSailMasteryExpLevelChange } = {};
  const firstFleetShips: { [id: number]: Ship } = tryData.fleets.getFirstFleet().getShips();
  for (const ship of _.values(firstFleetShips)) {
    const shipBlueprintCmsId = cms.Ship[ship.getNub().cmsId].shipBlueprintId;
    const userBP = tryData.shipBlueprints.getUserShipBlueprint(shipBlueprintCmsId);
    // 설계도 없는 선박은 제외
    if (!userBP) {
      continue;
    }
    // 함대에 같은 설계도 선박이 여러대 있어도 경험치는 한번만 지급
    if (bpSailMasteryExpChanges[userBP.cmsId]) {
      continue;
    }

    const bluePrintCms = cms.ShipBlueprint[userBP.cmsId];
    if (!bluePrintCms.masteryAble) {
      continue;
    }

    const expChanges = userBP.calcSailMasteryExpLevel(addedExp, user);
    if (!expChanges) {
      continue;
    }

    bpSailMasteryExpChanges[userBP.cmsId] = expChanges;
    changes.bpSailMasteryforGlog[userBP.cmsId] = {
      cmsId: expChanges.cmsId,
      oldLevel: userBP.sailMasteryLevel,
      oldExp: userBP.sailMasteryExp,
      level: expChanges.sailMasteryLevel,
      exp: expChanges.sailMasteryExp,
      addedExp: expChanges.sailMasteryExp - userBP.sailMasteryExp,
    };
  }

  for (const change of _.values(bpSailMasteryExpChanges)) {
    tryData.shipBlueprints.setUserShipBlueprintSailMastery(
      change.cmsId,
      change.sailMasteryLevel,
      change.sailMasteryExp,
      tryData.stats,
      null
    );

    _.merge<All, All>(changes.syncAdd, {
      shipBlueprints: {
        [change.cmsId]: {
          cmsId: change.cmsId,
          sailMasteryLevel: change.sailMasteryLevel,
          sailMasteryExp: change.sailMasteryExp,
        },
      },
    });
  }
  return CHANGE_TASK_RESULT.OK;
}

export function opSetFleetDispatchLifeRemain(
  user: User,
  tryData: TryData,
  changes: Changes,
  fleetIndex: number,
  lifeRemain: number
) {
  if (!changes.fleetDispatchLifeRemains) {
    changes.fleetDispatchLifeRemains = {};
  }

  changes.fleetDispatchLifeRemains[fleetIndex] = lifeRemain;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetFleetDispatchEndView(
  user: User,
  tryData: TryData,
  changes: Changes,
  fleetIndex: number,
  state: number,
  actions: FleetDispatchActionSync[],
  endView: FleetDispatchEndViewSync
) {
  if (!changes.syncAdd.fleetDispatches) {
    changes.syncAdd.fleetDispatches = {};
  }

  if (!changes.syncAdd.fleetDispatches[fleetIndex]) {
    changes.syncAdd.fleetDispatches[fleetIndex] = { fleetIndex };
  }

  changes.syncAdd.fleetDispatches[fleetIndex].state = state;
  changes.syncAdd.fleetDispatches[fleetIndex].actions = actions;
  changes.syncAdd.fleetDispatches[fleetIndex].endView = endView;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetFleetDispatchReward(
  user: User,
  tryData: TryData,
  changes: Changes,
  fleetIndex: number,
  rewardResult: FleetDispatchRewardSyncResult,
  lostRewardResult: FleetDispatchRewardSyncResult,
  lostRewardIdCount: number
) {
  if (!changes.syncAdd.fleetDispatches) {
    changes.syncAdd.fleetDispatches = {};
  }

  if (!changes.syncAdd.fleetDispatches[fleetIndex]) {
    changes.syncAdd.fleetDispatches[fleetIndex] = { fleetIndex };
  }

  let rewards: FleetDispatchRewardSync[] = [];
  _.forOwn(rewardResult, (elem) => {
    _.forOwn(elem, (reward) => {
      rewards.push(reward);
    });
  });

  let lostRewards: FleetDispatchRewardSync[] = [];
  _.forOwn(lostRewardResult, (elem) => {
    _.forOwn(elem, (reward) => {
      lostRewards.push(reward);
    });
  });

  changes.syncAdd.fleetDispatches[fleetIndex].rewards = rewards;
  changes.syncAdd.fleetDispatches[fleetIndex].lostRewards = lostRewards;
  changes.syncAdd.fleetDispatches[fleetIndex].lostRewardIdCount = lostRewardIdCount;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opRemoveFleetDispatch(
  user: User,
  tryData: TryData,
  changes: Changes,
  fleetIndex: number
) {
  _.merge(changes.syncRemove, {
    fleetDispatches: {
      [fleetIndex]: true,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opBuyDailySubscription(
  user: User,
  tryData: TryData,
  changes: Changes,
  dsCmsId: number,
  curTimeUtc: number
) {
  if (!tryData.cashShop) {
    tryData.cashShop = user.userCashShop.clone();
  }

  if (!changes.syncAdd.dailySubscriptions) {
    changes.syncAdd.dailySubscriptions = {};
  }

  const dsCms: DailySubscriptionDesc = cms.DailySubscription[dsCmsId];
  tryData.cashShop.buyDailySubscription(dsCms, curTimeUtc);
  _.merge<All, All>(changes.syncAdd.dailySubscriptions, {
    [dsCmsId]: tryData.cashShop.getDailySubscription(dsCmsId, curTimeUtc),
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetDailySubscriptionLastReceiveTimeUtc(
  user: User,
  tryData: TryData,
  changes: Changes,
  dsCmsId: number,
  curTimeUtc: number
) {
  if (!tryData.cashShop) {
    tryData.cashShop = user.userCashShop.clone();
  }

  if (!changes.syncAdd.dailySubscriptions) {
    changes.syncAdd.dailySubscriptions = {};
  }

  const userDS = tryData.cashShop.getDailySubscription(dsCmsId, curTimeUtc);
  if (!userDS) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const dsResetHour: number = cms.ContentsResetHour.DailySubscriptionRewardReset.hour;
  if (!HasContentsResetTimePassed(curTimeUtc, userDS.lastReceiveTimeUtc, dsResetHour)) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_DAILY_SUBSCRIPTION_DAILY_REWARD;
  }

  userDS.lastReceiveTimeUtc = curTimeUtc;

  tryData.cashShop.setDailySubscription(userDS);
  _.merge<All, All>(changes.syncAdd.dailySubscriptions, {
    [dsCmsId]: userDS,
  });

  return CHANGE_TASK_RESULT.OK;
}
export function opDeleteGuildSynthesisSlot(
  user: User,
  tryData: TryData,
  changes: Changes,
  slotNo: number
) {
  if (!tryData.userGuild) {
    tryData.userGuild = user.userGuild.clone();
  }

  delete tryData.userGuild.guildSynthesisProgresses[slotNo];
  _.merge(changes.syncRemove, {
    userGuild: {
      synthesisProgresses: {
        [slotNo]: true,
      },
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetHotSpotCoolTimeUtc(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number,
  curTimeUtc: number
): CHANGE_TASK_RESULT {
  if (!tryData.cashShop) {
    tryData.cashShop = user.userCashShop.clone();
  }

  const cashShopCms = cms.CashShop[cmsId];
  if (!cashShopCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const hotSpotProduct = tryData.cashShop.getHotSpotProduct(cmsId);
  // 핫스팟 상품 등장 시간이 지났는지 검사
  if (
    !hotSpotProduct ||
    !hotSpotProduct.expireTimeUtc ||
    !hotSpotProduct.coolTimeUtc ||
    hotSpotProduct.expireTimeUtc < curTimeUtc
  ) {
    return CHANGE_TASK_RESULT.NOT_OPEN_HOT_SPOT_PRODUCT;
  }

  // expireTimeUtc랑 coolTimeUtc를 갱신
  hotSpotProduct.expireTimeUtc = curTimeUtc;
  hotSpotProduct.coolTimeUtc = curTimeUtc + cashShopCms.coolTimeDays * SECONDS_PER_DAY;

  if (!changes.syncAdd.hotSpotProducts) {
    changes.syncAdd.hotSpotProducts = {};
  }
  _.merge(changes.syncAdd.hotSpotProducts, { [cmsId]: hotSpotProduct });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetEventRankingMissionReward(
  user: User,
  tryData: TryData,
  changes: Changes,
  eventPageCmsId: number,
  rewardIdx: number
): CHANGE_TASK_RESULT {
  if (!tryData.userEventRanking) {
    tryData.userEventRanking = user.userEventRanking.clone();
  }
  if (tryData.userEventRanking.isUserRewarded(eventPageCmsId, rewardIdx)) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_EVENT_RANKING_MISSION_REWARD;
  }
  tryData.userEventRanking.addRewardIdx(eventPageCmsId, rewardIdx);

  if (!changes.addedEventRankingRewardIdx) {
    changes.addedEventRankingRewardIdx = {};
  }
  if (!changes.addedEventRankingRewardIdx[eventPageCmsId]) {
    changes.addedEventRankingRewardIdx[eventPageCmsId] = [];
  }
  changes.addedEventRankingRewardIdx[eventPageCmsId].push(rewardIdx);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetDiscoveryReward(
  user: User,
  tryData: TryData,
  changes: Changes,
  cmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.userDiscoveryReward) {
    tryData.userDiscoveryReward = user.userDiscoveryReward.clone();
  }
  if (tryData.userDiscoveryReward.isRewarded(cmsId)) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_DISCOVERY_REWARD;
  }
  tryData.userDiscoveryReward.addReward(cmsId);

  if (!changes.addedDiscoveryReward) {
    changes.addedDiscoveryReward = [];
  }
  changes.addedDiscoveryReward.push(cmsId);

  return CHANGE_TASK_RESULT.OK;
}

export function opApplyKarmaChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  karmaChange: KarmaChange
): CHANGE_TASK_RESULT {
  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }
  changes.syncAdd.user.karma = karmaChange.karma;
  changes.syncAdd.user.lastKarmaUpdateTimeUtc = karmaChange.lastUpdateTimeUtc;

  return CHANGE_TASK_RESULT.OK;
}

export function opAddShipCamouflage(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipCamouflageCmsId: number
) {
  const shipCamouflageCms = cms.ShipCamouflage[shipCamouflageCmsId];
  if (!shipCamouflageCms) {
    throw new MError('invaluid-ship-camouflage-cms', MErrorCode.INVALID_SHIP_CAMOUFLAGE_CMS_ID, {
      shipCamouflageCmsId,
    });
  }

  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  if (tryData.fleets.hasShipCamouflage(shipCamouflageCmsId)) {
    return CHANGE_TASK_RESULT.ALREADY_BOUGHT;
  }

  tryData.fleets.addShipCamouflage(user, shipCamouflageCmsId, null);

  if (!changes.addedShipCamouflages) {
    changes.addedShipCamouflages = [];
  }
  changes.addedShipCamouflages.push(shipCamouflageCmsId);

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetFishCatchReward(
  user: User,
  tryData: TryData,
  changes: Changes,
  fishCmsId: number
): CHANGE_TASK_RESULT {
  if (!tryData.userFishing) {
    tryData.userFishing = user.userFishing.clone();
  }

  if (tryData.userFishing.isRewarded(fishCmsId)) {
    return CHANGE_TASK_RESULT.ALREADY_RECEIVED_FISH_SIZE_REWARD;
  }

  if (!tryData.userFishing.canBigCatchReward(fishCmsId)) {
    return CHANGE_TASK_RESULT.ENOUGH_FISH_BIG_CATCH_SIZE_REWARD;
  }

  const fishCatch = tryData.userFishing.getFishCatch(fishCmsId);
  fishCatch.isRewarded = 1;

  tryData.userFishing.setFishCatch(fishCatch);

  if (!changes.addedFishCatchRewards) {
    changes.addedFishCatchRewards = {};
  }

  changes.addedFishCatchRewards[fishCmsId] = fishCatch;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
function AddMailExceededReward(
  user: User,
  tryData: TryData,
  changes: Changes,
  curTimeUtc: number,
  attachment: RewardCmsElem[]
) {
  if (!tryData.mails) {
    tryData.mails = user.userMails.clone();
  }

  const mailId = cms.Const.QuestExceededRewardMailId.value;
  const mailCms = cms.Mail[mailId];

  const expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
  const mail: MailCreatingParams = new BuilderMailCreateParams(
    tryData.mails.generateNewDirectMailId(),
    mailId,
    curTimeUtc,
    expireTimeUtc,
    0,
    null,
    null,
    null,
    null,
    JSON.stringify(attachment)
  ).getParam();

  tryData.mails.addDirectMail(mail, null);

  if (!changes.newMail) {
    changes.newMail = [];
  }
  changes.newMail.push(mail);
}

// -------------------------------------------------------------------------------------------------
export function opAddUserTitle(user: User, tryData: TryData, changes: Changes, cmsId: number) {
  if (!tryData.userTitles) {
    tryData.userTitles = user.userTitles.clone();
  }

  const userTitleCms: UserTitleDesc = cms.UserTitle[cmsId];
  if (!userTitleCms) {
    return CHANGE_TASK_RESULT.CMS_ERROR;
  }

  const curTimeUtc: number = mutil.curTimeUtc();
  const userTitle: UserTitle = tryData.userTitles.buildUserTitle(cmsId, curTimeUtc);
  if (!userTitle) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  tryData.userTitles.setUserTitle(userTitle);

  if (!changes.addedUserTitles) {
    changes.addedUserTitles = {};
  }
  changes.addedUserTitles[cmsId] = userTitle;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddFreeSweepTicket(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
) {
  if (!amount || amount <= 0) {
    return CHANGE_TASK_RESULT.CMS_ERROR;
  }

  if (!tryData.userSweepTicket) {
    tryData.userSweepTicket = user.userSweepTicket.clone();
  }

  const curCount: number = tryData.userSweepTicket.count;
  const newCount: number = tryData.userSweepTicket.buildAddFreeTicket(amount);

  if (curCount === newCount) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!changes.sweepTicket) {
    changes.sweepTicket = {
      count: tryData.userSweepTicket.count,
      buyCount: tryData.userSweepTicket.buyCount,
    };
  }

  tryData.userSweepTicket.count = newCount;
  changes.sweepTicket.count = newCount;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddBuySweepTicket(
  user: User,
  tryData: TryData,
  changes: Changes,
  amount: number
) {
  if (!amount || amount <= 0) {
    return CHANGE_TASK_RESULT.CMS_ERROR;
  }

  if (!tryData.userSweepTicket) {
    tryData.userSweepTicket = user.userSweepTicket.clone();
  }

  if (!changes.sweepTicket) {
    changes.sweepTicket = {
      count: tryData.userSweepTicket.count,
      buyCount: tryData.userSweepTicket.buyCount,
    };
  }

  tryData.userSweepTicket.buyCount += amount;
  changes.sweepTicket.buyCount += amount;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplySweepTicketChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  sweepTicketChange: { count: number; buyCount: number }
) {
  if (sweepTicketChange.count < 0 || sweepTicketChange.buyCount < 0) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.userSweepTicket) {
    tryData.userSweepTicket = user.userSweepTicket.clone();
  }

  if (!changes.sweepTicket) {
    changes.sweepTicket = {
      count: 0,
      buyCount: 0,
    };
  }

  tryData.userSweepTicket.applyTicketCount(
    sweepTicketChange.count,
    sweepTicketChange.buyCount,
    null,
    null
  );

  changes.sweepTicket.count = sweepTicketChange.count;
  changes.sweepTicket.buyCount = sweepTicketChange.buyCount;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opApplyShieldChange(
  user: User,
  tryData: TryData,
  changes: Changes,
  shieldChange: Shield
) {
  if (!shieldChange) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (!tryData.shield) {
    tryData.shield = user.userShield.clone();
  }

  tryData.shield.applyShieldChange(shieldChange, null);

  _.merge<All, All>(changes.syncAdd, {
    shields: {
      [shieldChange.cmsId]: shieldChange,
    },
  });

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opAddPet(user: User, tryData: TryData, changes: Changes, petCmsId: number) {
  if (!tryData.userPets) {
    tryData.userPets = user.userPets.clone();
  }

  if (tryData.userPets.hasPet(petCmsId)) {
    return CHANGE_TASK_RESULT.ALREADY_BOUGHT;
  }

  tryData.userPets.addNewPet(petCmsId);

  if (!changes.syncAdd.pets) {
    changes.syncAdd.pets = {};
  }
  changes.syncAdd.pets[petCmsId] = {
    cmsId: petCmsId,
  };

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opDeleteShip(user: User, tryData: TryData, changes: Changes, shipId: number) {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  if (!tryData.mates) {
    tryData.mates = user.userMates.clone();
  }

  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }

  if (!tryData.inven) {
    tryData.inven = user.userInven.clone();
  }

  const ship = tryData.fleets.getShip(shipId);
  if (!ship) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  const shipNub = ship.getNub();

  if (
    shipNub.assignment !== SHIP_ASSIGNMENT.CAPTURED &&
    shipNub.assignment !== SHIP_ASSIGNMENT.DOCK
  ) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (shipNub.formationIndex === cmsEx.FlagShipFormationIndex) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (shipNub.isLocked === 1) {
    return CHANGE_TASK_RESULT.LOCKED_SHIP;
  }

  if (!changes.syncRemove.ships) {
    changes.syncRemove.ships = [];
  }
  changes.syncRemove.ships.push(shipId.toString());
  tryData.fleets.deleteShip(
    shipId,
    null,
    tryData.mates,
    tryData.stats,
    tryData.fleets,
    tryData.shipBlueprints,
    tryData.inven,
    null,
    null,
    null,
    null,
    null,
    null,
    undefined
  );

  // 부품 해제
  if (!changes.unequippedShipSlotItemIds) {
    changes.unequippedShipSlotItemIds = [];
  }
  const bpCms = cms.Ship[shipNub.cmsId].shipBlueprint;
  for (const slot of bpCms.shipSlot) {
    const defaultSlotCms = cms.ShipSlot[slot.Id];
    if (
      defaultSlotCms.slotType === SHIP_SLOT_TYPE.MATE ||
      defaultSlotCms.slotType === SHIP_SLOT_TYPE.NON_MATE
    ) {
      continue;
    }

    const shipSlot = ship.getSlot(slot.Index);
    if (!shipSlot || !shipSlot.shipSlotItemId) {
      continue;
    }

    changes.unequippedShipSlotItemIds.push(shipSlot.shipSlotItemId);
  }

  if (!user.userInven.canAddShipSlotItem(changes.unequippedShipSlotItemIds.length)) {
    return CHANGE_TASK_RESULT.SHIP_SLOT_INVEN_FULL;
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
// opAddShip(_tryElem)을 먼저 수행 후 실행해야됨
export function opComposeShip(
  user: User,
  tryData: TryData,
  changes: Changes,
  shipIds: number[],
  shipComposeCmsId: number,
  pickedRandomRewards: PickedRandomReward[]
) {
  if (!tryData.fleets) {
    tryData.fleets = user.userFleets.clone();
  }

  if (!tryData.shipBlueprints) {
    tryData.shipBlueprints = user.userShipBlueprints.clone();
  }

  if (!tryData.stats) {
    tryData.stats = user.companyStat.clone();
  }

  const shipComposeCms = cms.ShipCompose[shipComposeCmsId];
  if (!shipComposeCms) {
    return CHANGE_TASK_RESULT.INTERNAL_ERROR;
  }

  if (shipIds.length !== shipComposeCms.shipComposeMaterialShipVal) {
    return CHANGE_TASK_RESULT.NOT_ENOUGH_SHIP_COUNT;
  }

  for (const shipId of shipIds) {
    const ship = user.userFleets.getShip(shipId);
    if (!ship) {
      return CHANGE_TASK_RESULT.INVALID_SHIP_ID;
    }

    if (shipComposeCms.shipComposeMaterialShipId !== ship.nub.cmsId) {
      return CHANGE_TASK_RESULT.NOT_MATCHED_MATERIAL_SHIP_CMS_ID;
    }

    if (
      ship.nub.assignment !== SHIP_ASSIGNMENT.DOCK &&
      ship.nub.assignment !== SHIP_ASSIGNMENT.CAPTURED
    ) {
      return CHANGE_TASK_RESULT.COMPOSE_SHIP_ONLY_IN_DOCK;
    }

    if (ship.nub.isLocked) {
      return CHANGE_TASK_RESULT.CAN_NOT_COMPOSE_LOCKED_SHIP;
    }
  }

  const shipCompose = tryData.fleets.getShipCompose(shipComposeCms.groupId);
  if (shipCompose.rewardedCount >= shipComposeCms.rewardMaxCnt) {
    return CHANGE_TASK_RESULT.ALREADY_COMPOSE_MAX_COUNT;
  }

  if (shipCompose.turn >= cms.Const.shipComposeMax.value) {
    return CHANGE_TASK_RESULT.CMS_ERROR;
  }

  const bIsGetComposeShip = pickedRandomRewards.some((pickedReward) =>
    pickedReward.rewards.some((reward) => {
      if (reward.rewardType !== REWARD_TYPE.SHIP) {
        return false;
      }
      const ship = tryData.fleets.getShip(reward.id);
      return ship?.nub.cmsId === shipComposeCms.shipComposeShipId;
    })
  );

  if (bIsGetComposeShip) {
    shipCompose.rewardedCount++;
    shipCompose.turn = 0;
  } else {
    shipCompose.turn++;
  }

  // 최초 획득인 경우 설계도 레벨 1
  if (bIsGetComposeShip) {
    const shipCms = cms.Ship[shipComposeCms.shipComposeShipId];
    const userBP = tryData.shipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
    if (!userBP) {
      if (!changes.syncAdd.shipBlueprints) {
        changes.syncAdd.shipBlueprints = {};
      }
      if (!changes.syncAdd.shipBlueprints[shipCms.shipBlueprintId]) {
        changes.syncAdd.shipBlueprints[shipCms.shipBlueprintId] = {
          cmsId: shipCms.shipBlueprintId,
          level: 1,
          exp: 0,
          slots: {},
          sailMasteryExp: 0,
          sailMasteryLevel: 1,
          isPurchased: 0,
        };

        tryData.shipBlueprints.createUserShipBlueprint(
          tryData.stats,
          shipCms.shipBlueprintId,
          1,
          0,
          1,
          0,
          null,
          null
        );
      }
    }
  }

  if (!changes.syncAdd.shipCompose) {
    changes.syncAdd.shipCompose = {};
  }
  changes.syncAdd.shipCompose[shipComposeCms.groupId] = shipCompose;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opResetLastPaidSmuggleEnterTownCmsId(
  user: User,
  tryData: TryData,
  changes: Changes
) {
  if (!tryData.userSmuggle) {
    tryData.userSmuggle = user.userSmuggle.clone();
  }

  if (!changes.syncAdd.user) {
    changes.syncAdd.user = {};
  }

  tryData.userSmuggle.lastPaidSmuggleEnterTownCmsId = 0;
  changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId = 0;

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetInfiniteLighthouseClearInfo(
  user: User,
  tryData: TryData,
  changes: Changes,
  bIsWin: boolean,
  sessionId: number,
  stageNum: number,
  usedTurn: number
): CHANGE_TASK_RESULT {
  if (!bIsWin) {
    return CHANGE_TASK_RESULT.NOTHING;
  }

  if (!tryData.userInfiniteLighthouse) {
    tryData.userInfiniteLighthouse = user.userInfiniteLighthouse.clone();
  }

  const clearedInfo = tryData.userInfiniteLighthouse.getClearedStage(sessionId, stageNum);
  if (!clearedInfo || clearedInfo.usedTurn > usedTurn) {
    if (!changes.syncAdd.clearedInfiniteLighthouseStages) {
      changes.syncAdd.clearedInfiniteLighthouseStages = {};
    }
    if (!changes.syncAdd.clearedInfiniteLighthouseStages[sessionId]) {
      changes.syncAdd.clearedInfiniteLighthouseStages[sessionId] = {};
    }

    changes.syncAdd.clearedInfiniteLighthouseStages[sessionId][stageNum] = {
      stage: stageNum,
      usedTurn,
    };
  }

  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetClash(user: User, tryData: TryData, changes: Changes, session: ClashSession) {
  if (!tryData.userClash) {
    tryData.userClash = user.userClash.clone();
  }

  tryData.userClash.applySession(session, null);

  changes.clash = session;
  return CHANGE_TASK_RESULT.OK;
}

// -------------------------------------------------------------------------------------------------
export function opSetReentry(user: User, tryData: TryData, changes: Changes, reentry: Reentry) {
  if (!tryData.userReentry) {
    tryData.userReentry = user.userReentry.clone();
  }

  tryData.userReentry.applyReentry(reentry, null);
  if (!changes.reentrys) {
    changes.reentrys = {};
  }

  changes.reentrys[reentry.type] = reentry;

  return CHANGE_TASK_RESULT.OK;
}
