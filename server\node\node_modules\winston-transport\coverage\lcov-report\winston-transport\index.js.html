<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for winston-transport/index.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">winston-transport/</a> index.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">68% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>17/25</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">55.56% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>10/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/4</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">68% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>17/25</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict';
&nbsp;
var stream = require('stream'),
    util = require('util');
&nbsp;
var TransportStream = module.exports = function TransportStream(opts) {
  stream.Writable.call(this, { objectMode: true });
  opts = opts || {};
  this.format = opts.format;
  this.level = opts.level;
  this.handleExceptions = opts.handleExceptions;
  this.log = this.log || opts.log;
&nbsp;
  var self = this;
&nbsp;
  //
  // Get the levels from the source we are piped from.
  //
  this.once('pipe', <span class="fstat-no" title="function not covered" >function (logger) {</span>
<span class="cstat-no" title="statement not covered" >    self.levels = logger.levels;</span>
<span class="cstat-no" title="statement not covered" >    self.level = self.level || logger.level;</span>
    //
    // TODO: Improve bookkeeping here to support pipe and unpipe
    // from multiple LogStream parents (e.g. Containers)
    //
<span class="cstat-no" title="statement not covered" >    self.parent = logger;</span>
  });
&nbsp;
  //
  // If and/or when the transport is removed from this instance
  //
  this.once('unpipe', <span class="fstat-no" title="function not covered" >function (src) {</span>
    //
    // TODO: Improve bookkeeping here to support pipe and unpipe
    // from multiple LogStream parents (e.g. Containers)
    //
<span class="cstat-no" title="statement not covered" >    if (src === self.parent) {</span>
      //
      // Remark: this may not be the desired implementation since
      // a single transport may be shared by multiple Logger
      // instances.
      //
      // Remark: depending on the use-case we may need to clean up
      // this.levels and this.level.
      //
<span class="cstat-no" title="statement not covered" >      this.parent = null;</span>
<span class="cstat-no" title="statement not covered" >      if (self.close) {</span>
<span class="cstat-no" title="statement not covered" >        self.close();</span>
      }
    }
  });
};
&nbsp;
util.inherits(TransportStream, stream.Writable);
&nbsp;
/*
 * @private function _write(info)
 * Writes the info object to our transport instance.
 */
TransportStream.prototype._write = function (info, enc, callback) {
  if (info.exception &amp;&amp; !this.handleExceptions) {
    return callback(null);
  }
&nbsp;
  //
  // Remark: This has to be handled in the base transport now because we cannot
  // conditionally write to our pipe targets as stream.
  //
  <span class="missing-if-branch" title="else path not taken" >E</span>if (!this.level || <span class="branch-1 cbranch-no" title="branch not covered" >this.levels[this.level] &gt;= this.levels[info.level])</span> {
    return this.log(info, callback);
  }
&nbsp;
<span class="cstat-no" title="statement not covered" >  return callback(null);</span>
};
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Mar 18 2017 18:17:55 GMT-0400 (EDT)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
