// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import mlog from '../mlog';
import { BaseApiClient } from './baseApiClient';
import type { LGServerListResponse } from './linegamesApiClient';
import ffi from 'ffi-napi';
import { addFinalizer } from '../finalizers';

export class SdoLoginResponse {
  code: number;
  msg: string;
  data: {
    adult_flag: number;
    channelcode: string,
    companyId: string;
    firstLoginDate: string;
    firstLoginType: string;
    inputUserId: string;
    invitation_code: string;
    isThirdBoundPhone: number;
    phone: string;
    ptid: string;
    railId: string;
    realInfoUrl: string;
    realInfo_force: number,
    realInfo_force_pay: number,
    realInfo_status: number,
    realInfo_status_pay: number,
    sndaCompanyId: string;
    sndaid: string;
    subAccName: string;
    thirdId: string;
    token: string;
    userAttribute: string;
    userIdType: number;
    userid: number;
  }
}

// TODO 외부로 빼주기
const APP_ID = '*********';
const APP_SECRET_KEY = 'b5d5a46567db40c8912c0d676109bd27'

export class SdoApiClient extends BaseApiClient {
  private loginNumber: number = 0;
  private maskword: any;
  private maskwordUpdateCallback: any;

  constructor() {
    super();
  }

  init(baseUrl: string, timeout?: number) {
    super.init(baseUrl, timeout);

    mlog.info(`SdoApiClient.init: url=${baseUrl}, timeout=${timeout}`);

    // TODO
    // if (mconf.http.sdo.disableMaskword === true) {
    //   mlog.warn('maskword is disabled by mconf.http.sdo.disableMaskword');
    // } else {
    //   this.initMaskword();
    // }
  }

  private initMaskword() {
    const update_cbtype = ffi.Function('void', ['int']);

    this.maskword = ffi.Library('./maskword_cn/libmaskword', {
      'MaskWordErrMsg': ['string', ['int']],
      'InitMaskWord': ['int', ['string', 'int', update_cbtype]],
      'SearchUtf8': ['bool', ['string']],
      'ReplaceUtf8': ['string', ['string']],
      'StopMaskWord': ['void', []],
    })

    this.maskwordUpdateCallback = ffi.Callback(
      'void',
      ['int'],
      (result) => {
        const resultMessage = this.maskword.MaskWordErrMsg(result);
        if (resultMessage !== 'CDN no update') {
          mlog.info(`init maskword callback, result = ${resultMessage}`);
        }
      }
    );

    // TODO 외부 설정으로 빼주기
    const result = this.maskword.InitMaskWord('http://gdc.jijiagames.com/maskword', 300, this.maskwordUpdateCallback);
    mlog.info(`init maskword, result=${this.maskword.MaskWordErrMsg(result)}`);

    addFinalizer('maskword', () => {
      this.maskword.StopMaskWord();
      this.maskwordUpdateCallback = undefined;
    })
  }

  async login(ticketId: string): Promise<SdoLoginResponse> {
    const timestamp = Math.floor(Date.now() / 1000)
    const sequence = this.loginNumber++;
    const signKey = this.sign({
      appid: APP_ID,
      timestamp: timestamp,
      sequence: sequence,
      ticket_id: ticketId,
    }, APP_SECRET_KEY)

    const queryParams = {
      appid: APP_ID,
      timestamp: timestamp,
      sequence: sequence,
      ticket_id: ticketId,
      sign: signKey,
    }

    const path = `/v1/open/ticket${this.makeQueryParams(queryParams)}`;
    const response = await this.get<SdoLoginResponse>(path);
    return response;
  }

  async isWorldInMaintenance(worldId: string, force: boolean = false) {
    return false;
  }

  async reportBulkMailResult(
    successUserList: string[],
    failUserList: { userTarget: string; reasonCd: string }[],
    configId: number
  ): Promise<boolean> {
    return true;
  }

  async reportAccountDeletionResult(
    successNidList: string[],
    failNidList: string[]
  ): Promise<boolean> {
    return true;
  }

  async getMails(userId: number, langCulture: string, level: number): Promise<any> {
    return [];
  }

  async hasBadWord(text: string): Promise<boolean> {
    if (!text || text.length === 0) {
      return false;
    }

    if (!this.maskword) {
      return false;
    }

    return this.maskword.SearchUtf8(text) === true;
  }

  async replaceBadWordText(text: string): Promise<string> {
    if (!text || text.length === 0) {
      return text;
    }

    if (!this.maskword) {
      return text;
    }

    return this.maskword.ReplaceUtf8(text);
  }

  async reportBadChat(
    reportUserId: number,
    targetUserId: number,
    reasonCd: string,
    chatMsg: string,
    addInfo: string
  ): Promise<void> {
  }

  async changeNidForServerMigration(nid: string, toGameServerId: string) {
  }

  async revoke(nid: string): Promise<boolean> {
    return true;
  }

  async cancelRevoke(gnidSessionToken: string): Promise<boolean> {
    return true;
  }

  async reqWhiteServerIpList(): Promise<string[]> {
    return [];
  }

  async getNidAndServerInfoByGnid(gnidSessionToken: string): Promise<LGServerListResponse> {
    // TODO
    return {
      gnid: gnidSessionToken,
      registeredGameServerList: [],
      createdNidGameServerIdList: [],
    }
  }

  async getServerList() {
    // TODO
    return [];
  }

  async sendPushNotification(message: string, nid: string): Promise<void>;
  async sendPushNotification(message: string, nids: string[]): Promise<void>;
  async sendPushNotification(message: string, arg: string | string[]): Promise<void> {
  }

  async reportBadChatReasonList(langCulture: string) {
    return {
    };
  }

  private makeQueryParams(params: Record<string, any>): string {
    const pairs: string[] = [];
    for (const key in params) {
      pairs.push(`${key}=${params[key]}`);
    }

    return `?${pairs.join('&')}`;
  }

  private sign(params: Record<string, any>, secretKey: string): string {
    const sortedKeys = Object.keys(params).sort();
    const pairs: string[] = [];

    for (const key of sortedKeys) {
      pairs.push(`${key}=${params[key]}`);
    }

    const str = pairs.join('&') + secretKey;
    return this.md5(str);
  }

  private md5(input: string): string {
    const crypto = require('node:crypto');
    return crypto.createHash('md5').update(input).digest('hex');
  }
}
