// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import mconf from '../mconf';
import mlog from '../mlog';
import { BaseApiClient } from './baseApiClient';
import { LGErrorCode } from './linegamesApiClient';
import { IPlatformPayApiClient } from './iPlatformPayApiClient';

export class LineGamesPayApiClient extends BaseApiClient implements IPlatformPayApiClient {
  private _authToken: string = null;
  private _bIsTokenRequested: boolean = false;
  private _authPwd: string;

  constructor() {
    super();
  }

  init(baseUrl: string, timeout?: number) {
    super.init(baseUrl, timeout);
  }
  setAuthPassword(passworld: string) {
    this._authPwd = passworld;
  }
  // https://developer.line.games/pages/viewpage.action?pageId=60326167
  cancelFloorReserve(
    appStoreCd: string,
    floorOrderId: string,
    floorStoreOrderId: string,
    billingOrderId: string,
    productId: string,
    userId: number
  ) {
    const body = {
      svcCd: mconf.LineGameCode,
      serverId: mconf.worldId,
      appStoreCd,
      floorOrderId,
      floorStoreOrderId,
      billingOrderId,
      productId,
      userId: userId.toString(),
      memo: 'FLOOR_CANCEL_FROM_GAME',
    };

    const url = 'api_pay/floorCancel';

    mlog.info(`requesting to line games. [${url}]`, body);
    return this.request(url, body);
  }

  protected async request(
    url: string,
    body?: any,
    timeoutMs?: number,
    contentType: 'x-www-form-urlencoded' | 'json' = 'json'
  ) {
    return Promise.resolve()
      .then(() => {
        if (!this._authToken) {
          return this.refreshAuthToken();
        }
        return null;
      })
      .then(() => {
        switch (contentType) {
          case 'json':
            return this.mrest.post(url, body, {
              headers: {
                authToken: this._authToken,
                svcCd: mconf.LineGameCode,
                'Content-type': 'application/json',
              },
              timeout: timeoutMs,
            });
          case 'x-www-form-urlencoded':
          default:
            return this.mrest.postForm(url, body, {
              headers: {
                authToken: this._authToken,
                svcCd: mconf.LineGameCode,
              },
              timeout: timeoutMs,
            });
        }
      })
      .then((axiosResp) => {
        const result = axiosResp.data;
        if (result.errorCd) {
          if (result.errorCd === LGErrorCode[LGErrorCode.EXPIRE_AUTH_TOKEN]) {
            this._authToken = null;
            return this.refreshAuthToken().then(() => {
              return this.request(url, body, timeoutMs, contentType);
            });
          }
        }
        return result;
      })
      .catch((err) => {
        const data = err.response?.data;
        mlog.error('LineGamesPayApiClient request is failed.', { url, body, data });
        this.rethrow(url, body, err);
      });
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=7275297
   */
  private refreshAuthToken(): Promise<any> {
    // 만료된 토큰을 사용해서 API를 호출하는 경우, 최초 만료가 갱신을 시도하고 나머지들은 여기서 일정시간 머물게 된다.
    // 토큰이 갱신 되면 빠져나가고 자연스럽게 postForm을 재 호출 하는 구조.
    return Promise.resolve()
      .then(() => {
        if (this._bIsTokenRequested) {
          return this.waitForToken(0);
        }
        return null;
      })
      .then(() => {
        this._bIsTokenRequested = true;

        const body = {
          svcCd: mconf.LineGameCode,
          authPwd: this._authPwd,
        };

        return this.mrest.postForm('/api/v2/auth/token/getNewToken', body);
      })
      .then((resp) => {
        const result = resp.data;
        if (result.success !== false && result.authToken) {
          this._authToken = result.authToken;
        } else {
          // TODO throw
        }
        this._bIsTokenRequested = false;
      });
  }

  private waitForToken(retries: number): Promise<any> {
    return this.delay(500).then(() => {
      if (this._authToken) {
        return Promise.resolve();
      }
      if (retries > 10) {
        return Promise.resolve();
      }
      return this.waitForToken(++retries);
    });
  }

  private delay(ms: number): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }
}
