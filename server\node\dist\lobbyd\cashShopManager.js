"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashShopManager = void 0;
const typedi_1 = require("typedi");
const cms_1 = __importDefault(require("../cms"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
let CashShopManager = class CashShopManager {
    async getSalesList(appStoreCd, curTimeUtc) {
        if (!this.billingSalesList ||
            !this.billingSalesList[appStoreCd] ||
            this.billingSalesList[appStoreCd].lastUpdateTimeUtc +
                cms_1.default.Define.BillingSalesListCachingExpireSec <
                curTimeUtc) {
            await mhttp_1.default.platformBillingApi.querySalesList(appStoreCd).then((billingApiResp) => {
                this.updateSalesList(billingApiResp, appStoreCd, curTimeUtc);
            });
        }
        return this.billingSalesList[appStoreCd].productList;
    }
    updateSalesList(resp, appStoreCd, curTimeUtc) {
        if (!resp.success) {
            mlog_1.default.error('[CashShopManager] update billing sales list failed', {
                msg: resp.msg,
                errorCd: resp.errorCd,
            });
            return;
        }
        if (!this.billingSalesList) {
            this.billingSalesList = {};
        }
        this.billingSalesList[appStoreCd] = {
            productList: {},
            lastUpdateTimeUtc: curTimeUtc,
        };
        const productList = this.billingSalesList[appStoreCd].productList;
        for (const product of resp.data.productList) {
            productList[product.productId] = product;
        }
    }
};
CashShopManager = __decorate([
    (0, typedi_1.Service)()
], CashShopManager);
exports.CashShopManager = CashShopManager;
//# sourceMappingURL=cashShopManager.js.map