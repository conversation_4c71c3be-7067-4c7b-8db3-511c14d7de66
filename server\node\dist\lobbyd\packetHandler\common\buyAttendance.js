"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BuyAttendance = void 0;
const lodash_1 = __importDefault(require("lodash"));
const assert_1 = __importDefault(require("assert"));
const cms_1 = __importDefault(require("../../../cms"));
const cmsEx = __importStar(require("../../../cms/ex"));
const merror_1 = require("../../../motiflib/merror");
const userConnection_1 = require("../../userConnection");
const mutil = __importStar(require("../../../motiflib/mutil"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const formula_1 = require("../../../formula");
const attendanceDesc_1 = require("../../../cms/attendanceDesc");
const rewardAndPaymentChangeSpec_1 = require("../../UserChangeTask/rewardAndPaymentChangeSpec");
const userChangeTask_1 = require("../../UserChangeTask/userChangeTask");
const userChangeOperator_1 = require("../../UserChangeTask/userChangeOperator");
const eventPageDesc_1 = require("../../../cms/eventPageDesc");
const enum_1 = require("../../../motiflib/model/auth/enum");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
// ----------------------------------------------------------------------------
// 출석체크 보충 기능
// ----------------------------------------------------------------------------
const rsn = 'buy_attendance';
const add_rsn = null;
const reasonForLineGamesCash = 'Buy_Reward_Attendance';
// ----------------------------------------------------------------------------
class Cph_Common_BuyAttendance {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    async exec(user, packet) {
        var _a;
        const reqBody = packet.bodyObj;
        const { eventPageCmsId, supplementCount } = reqBody;
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const eventPageCms = cms_1.default.EventPage[eventPageCmsId];
        if (!eventPageCms) {
            throw new merror_1.MError('invalid-cms-id', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                eventPageCmsId,
            });
        }
        if (!supplementCount || supplementCount <= 0) {
            throw new merror_1.MError('not-support-buy-attendance', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                reqBody,
            });
        }
        // 출석 보충기능 활성화 체크
        if (eventPageCms.type !== eventPageDesc_1.EventPageType.ATTENDANCE_MONTH || !eventPageCms.attendSupplement) {
            throw new merror_1.MError('not-support-buy-attendance', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                eventPageCmsId,
                eventPageCmsType: eventPageCms.type,
            });
        }
        if ((eventPageCms.localBitflag & (1 << mconf_1.default.countryCode)) === 0) {
            throw new merror_1.MError('invalid-local-bit-flag', merror_1.MErrorCode.NOT_ALLOWED_IN_COUNTRY, {
                eventPageCmsId,
            });
        }
        const curTimeUtc = mutil.curTimeUtc();
        const curDate = new Date(curTimeUtc * 1000);
        if (eventPageCms.startDate && curDate < mutil.newDateByCmsDateStr(eventPageCms.startDate)) {
            throw new merror_1.MError('not-attendance-open-time1', merror_1.MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
                eventPageCmsId,
                curDate,
                startDate: eventPageCms.startDate,
            });
        }
        if (eventPageCms.endDate && curDate > mutil.newDateByCmsDateStr(eventPageCms.endDate)) {
            throw new merror_1.MError('not-attendance-open-time2', merror_1.MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
                eventPageCmsId,
                curDate,
                endDate: eventPageCms.endDate,
            });
        }
        user.userContentsTerms.ensureContentsTerms(eventPageCms.contentsTerms, user);
        const userAttendance = lodash_1.default.cloneDeep(user.userAttendance.getAttendance(eventPageCmsId));
        // 금일 출석이 가능하다면 에러 처리
        if (!userAttendance.lastAttendanceTimeUtc ||
            (0, formula_1.HasContentsResetTimePassed)(curTimeUtc, userAttendance.lastAttendanceTimeUtc, cms_1.default.ContentsResetHour.EventPageReset.hour)) {
            throw new merror_1.MError('not-yet-attendance', merror_1.MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
                eventPageCmsId,
                userAttendance,
            });
        }
        const attendanceCms = cmsEx.getAttendance(eventPageCms.groupRef);
        // 일반 출석이 아니면 에러 처리
        if (!(attendanceCms === null || attendanceCms === void 0 ? void 0 : attendanceCms[attendanceDesc_1.ATTENDANCE_TYPE.ACCUMULATE1])) {
            throw new merror_1.MError('not-supported-attendance-cms-type', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                eventPageCmsId,
            });
        }
        // 현재 날짜 이후 보상 수령 불가
        if (userAttendance.accum + supplementCount > mutil.getLocalDate(curDate)) {
            throw new merror_1.MError('over-current-day-supplemnt', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                curDate,
                localDate: mutil.getLocalDate(curDate),
                userAttendance,
                supplementCount,
            });
        }
        // 마지막 day 보다 큰 값이 오면 에러 처리
        if (userAttendance.accum + supplementCount >
            cmsEx.getAttendanceLastDay(eventPageCms.groupRef)) {
            throw new merror_1.MError('invalid-over-last-day', merror_1.MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
                curDate,
                userAttendance,
                supplementCount,
                lastDay: cmsEx.getAttendanceLastDay(eventPageCms.groupRef),
            });
        }
        const attendanceForGlog = [];
        const rewardCmsIds = [];
        for (let addedDay = 1; addedDay <= supplementCount; addedDay++) {
            const attendanceCmsElem = (_a = attendanceCms === null || attendanceCms === void 0 ? void 0 : attendanceCms[attendanceDesc_1.ATTENDANCE_TYPE.ACCUMULATE1]) === null || _a === void 0 ? void 0 : _a[userAttendance.accum + addedDay];
            if (!attendanceCmsElem) {
                continue;
            }
            rewardCmsIds.push(attendanceCmsElem.reward);
            attendanceForGlog.push({
                pointType: cmsEx.RedGemPointCmsId,
                pointCost: cms_1.default.Const.attendanceCost.value,
                rewardDayCnt: userAttendance.accum + addedDay,
                rewardId: attendanceCmsElem.reward,
            });
        }
        const pointCost = {
            cmsId: cmsEx.RedGemPointCmsId,
            cost: cms_1.default.Const.attendanceCost.value * supplementCount,
        };
        userAttendance.accum += supplementCount;
        const changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.BUY_ATTENDANCE, new BuyAttendanceSpec(userAttendance, rewardCmsIds, eventPageCms.mail, curTimeUtc, pointCost));
        const res = changeTask.trySpec();
        if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
            throw new merror_1.MError('failed-to-buy-attendance', merror_1.MErrorCode.FAILED_TO_BUY_ATTENDANCE_USER_CHANGE_TASK, {
                res,
                reqBody,
            });
        }
        const sync = await changeTask.apply();
        // glog
        const reward_data = [];
        for (const rewardFixedCmsId of rewardCmsIds) {
            reward_data.push(...cmsEx.convertRewardFixedToGLogRewardData(rewardFixedCmsId));
        }
        for (const glogData of attendanceForGlog) {
            user.glog('event', {
                rsn,
                add_rsn,
                event_id: eventPageCms.id,
                event_name: eventPageCms.name,
                event_type: eventPageCms.type,
                event_group: eventPageCms.groupRef,
                reward_day_cnt: glogData.rewardDayCnt,
                reward_data: cmsEx.convertRewardFixedToGLogRewardData(glogData.rewardId),
                event_cnt: userAttendance.consecutive,
                pr_data: [
                    {
                        type: glogData.pointType,
                        amt: glogData.pointCost,
                    },
                ],
                exchange_hash: changeTask.getExchangeHash(),
            });
        }
        // [빌링] 중국의 경우 캐시 반영을 수동으로 해줘야함.
        if (mconf_1.default.platform === enum_1.PLATFORM.SDO) {
            const cashPair = await mhttp_1.default.platformBillingApi.queryCashPair(user.userId, user.storeCode, user.countryCreated);
            user.userPoints.onChargeByPurchaseProduct([
                {
                    coinCd: 'red_gem',
                    paymentType: 'PAID',
                    balance: cashPair.paidRedGemBalance,
                },
                {
                    coinCd: 'red_gem',
                    paymentType: 'FREE',
                    balance: cashPair.freeRedGemBalance,
                },
            ], null);
        }
        return await user.sendJsonPacket(packet.seqNum, packet.type, { sync });
    }
}
exports.Cph_Common_BuyAttendance = Cph_Common_BuyAttendance;
class BuyAttendanceSpec extends rewardAndPaymentChangeSpec_1.RewardAndPaymentSpec {
    constructor(attendance, rewardCmsIds, mailCmsId, curTimeUtc, pointCost) {
        const resultFunction = (result, rnpElem, user, tryData, changes) => {
            let bBreakRnpElemsLoop = false;
            let changeTaskResultOfInternalOp;
            if (result !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
                const mailCms = cms_1.default.Mail[mailCmsId];
                let expireTimeUtc = null;
                let bShouldSetExpirationWhenReceiveAttachment = 0;
                if (mailCms.mailKeepTime > 0) {
                    expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
                }
                else if (mailCms.mailKeepTime === -1) {
                    bShouldSetExpirationWhenReceiveAttachment = 1;
                }
                changeTaskResultOfInternalOp = (0, userChangeOperator_1.opAddDirectmail)(user, tryData, changes, mailCmsId, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, null, null, null, cmsEx.convertRewardFixedToCustomAttachmentStr(rnpElem.cmsId, true, curTimeUtc));
                if (changeTaskResultOfInternalOp > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
                    bBreakRnpElemsLoop = true;
                }
            }
            return { bBreakRnpElemsLoop, changeTaskResultOfInternalOp };
        };
        const rnpElems = [];
        for (const cmsId of rewardCmsIds) {
            rnpElems.push({
                type: rewardAndPaymentChangeSpec_1.RNP_TYPE.REWARD_FIXED,
                cmsId,
                bIsNotPermitAddToHardCapLimitLine: true,
                resultFunction,
                bIsAccum: true,
                bIsBound: true,
            });
        }
        super(rnpElems, { gainReason: reasonForLineGamesCash });
        this._attendance = attendance;
        this._pointCost = pointCost;
    }
    accumulate(user, tryData, changes) {
        let res;
        // 재화 소모
        (0, assert_1.default)(-this._pointCost.cost < 0);
        res = (0, userChangeOperator_1.opAddPoint)(user, tryData, changes, this._pointCost.cmsId, -this._pointCost.cost, false, undefined, { itemId: rsn }, true);
        if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
            return res;
        }
        res = super.accumulate(user, tryData, changes);
        if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
            return res;
        }
        return (0, userChangeOperator_1.opSetAttendance)(user, tryData, changes, this._attendance);
    }
}
//# sourceMappingURL=buyAttendance.js.map