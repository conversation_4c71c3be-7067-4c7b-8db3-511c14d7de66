// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_consume_cash_cn';

export interface Result {
  userId: number;
  paidRedGemBalance: number;
  freeRedGemBalance: number;
}

const spFunction = query.generateSPFunction(spName);

export default async function (
  connection: query.Connection,
  userId: number,
  cmsId: number,
  amount: number,
): Promise<Result> {
  const qr = await spFunction(connection, userId, cmsId, amount);
  const rows = qr.rows;
  return {
    userId: userId,
    paidRedGemBalance: Number.parseInt(rows[0][0].paidRedGemBalance),
    freeRedGemBalance: Number.parseInt(rows[0][0].freeRedGemBalance),
  };
}
