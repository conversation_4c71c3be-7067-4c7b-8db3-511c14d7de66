"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingQueryInvenPurchaseList = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const userConnection_1 = require("../../userConnection");
// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------
const rsn = null;
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_BillingQueryInvenPurchaseList {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        return Promise.resolve()
            .then(() => {
            return mhttp_1.default.platformBillingApi.queryInventoryPurchaseList(user.userId.toString());
        })
            .then((billingApiResp) => {
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                billingApiResp,
            });
        });
    }
}
exports.Cph_Common_BillingQueryInvenPurchaseList = Cph_Common_BillingQueryInvenPurchaseList;
//# sourceMappingURL=billingQueryInvenPurchaseList.js.map