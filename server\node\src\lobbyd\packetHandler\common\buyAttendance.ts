// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { Resp } from '../../type/sync';
import { User } from '../../user';
import { <PERSON><PERSON><PERSON>, CONNECTION_STATE } from '../../userConnection';
import * as mutil from '../../../motiflib/mutil';
import mconf from '../../../motiflib/mconf';
import { HasContentsResetTimePassed } from '../../../formula';
import { ATTENDANCE_TYPE } from '../../../cms/attendanceDesc';
import {
  RewardAndPaymentSpec,
  RewardAndPaymentElem,
  RNP_TYPE,
} from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  CHANGE_TASK_RESULT,
  TryData,
  Changes,
  CHANGE_TASK_REASON,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import {
  opSetAttendance,
  opAddDirectmail,
  opAddPoint,
} from '../../UserChangeTask/userChangeOperator';
import { Attendance } from '../../userAttendance';
import { RewardData } from '../../../motiflib/gameLog';
import { ClientPacketHandler } from '../index';
import { EventPageType } from '../../../cms/eventPageDesc';
import { PLATFORM } from '../../../motiflib/model/auth/enum';

// ----------------------------------------------------------------------------
// 출석체크 보충 기능
// ----------------------------------------------------------------------------

const rsn = 'buy_attendance';
const add_rsn = null;
const reasonForLineGamesCash = 'Buy_Reward_Attendance';

interface RequestBody {
  eventPageCmsId: number;
  supplementCount: number;
}

interface AttendanceForGlog {
  pointType: number;
  pointCost: number;
  rewardDayCnt: number;
  rewardId: number;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyAttendance implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { eventPageCmsId, supplementCount } = reqBody;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    const eventPageCms = cms.EventPage[eventPageCmsId];
    if (!eventPageCms) {
      throw new MError('invalid-cms-id', MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
        eventPageCmsId,
      });
    }
    if (!supplementCount || supplementCount <= 0) {
      throw new MError('not-support-buy-attendance', MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
        reqBody,
      });
    }
    // 출석 보충기능 활성화 체크
    if (eventPageCms.type !== EventPageType.ATTENDANCE_MONTH || !eventPageCms.attendSupplement) {
      throw new MError('not-support-buy-attendance', MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
        eventPageCmsId,
        eventPageCmsType: eventPageCms.type,
      });
    }
    if ((eventPageCms.localBitflag & (1 << mconf.countryCode)) === 0) {
      throw new MError('invalid-local-bit-flag', MErrorCode.NOT_ALLOWED_IN_COUNTRY, {
        eventPageCmsId,
      });
    }

    const curTimeUtc = mutil.curTimeUtc();
    const curDate = new Date(curTimeUtc * 1000);

    if (eventPageCms.startDate && curDate < mutil.newDateByCmsDateStr(eventPageCms.startDate)) {
      throw new MError('not-attendance-open-time1', MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
        eventPageCmsId,
        curDate,
        startDate: eventPageCms.startDate,
      });
    }
    if (eventPageCms.endDate && curDate > mutil.newDateByCmsDateStr(eventPageCms.endDate)) {
      throw new MError('not-attendance-open-time2', MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
        eventPageCmsId,
        curDate,
        endDate: eventPageCms.endDate,
      });
    }

    user.userContentsTerms.ensureContentsTerms(eventPageCms.contentsTerms, user);

    const userAttendance = _.cloneDeep(user.userAttendance.getAttendance(eventPageCmsId));
    // 금일 출석이 가능하다면 에러 처리
    if (
      !userAttendance.lastAttendanceTimeUtc ||
      HasContentsResetTimePassed(
        curTimeUtc,
        userAttendance.lastAttendanceTimeUtc,
        cms.ContentsResetHour.EventPageReset.hour
      )
    ) {
      throw new MError('not-yet-attendance', MErrorCode.NOT_BUY_ATTENDANCE_OPEN_TIME, {
        eventPageCmsId,
        userAttendance,
      });
    }

    const attendanceCms = cmsEx.getAttendance(eventPageCms.groupRef);
    // 일반 출석이 아니면 에러 처리
    if (!attendanceCms?.[ATTENDANCE_TYPE.ACCUMULATE1]) {
      throw new MError(
        'not-supported-attendance-cms-type',
        MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE,
        {
          eventPageCmsId,
        }
      );
    }

    // 현재 날짜 이후 보상 수령 불가
    if (userAttendance.accum + supplementCount > mutil.getLocalDate(curDate)) {
      throw new MError('over-current-day-supplemnt', MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
        curDate,
        localDate: mutil.getLocalDate(curDate),
        userAttendance,
        supplementCount,
      });
    }

    // 마지막 day 보다 큰 값이 오면 에러 처리
    if (
      userAttendance.accum + supplementCount >
      cmsEx.getAttendanceLastDay(eventPageCms.groupRef)
    ) {
      throw new MError('invalid-over-last-day', MErrorCode.INVALID_REQ_BODY_BUY_ATTENDANCE, {
        curDate,
        userAttendance,
        supplementCount,
        lastDay: cmsEx.getAttendanceLastDay(eventPageCms.groupRef),
      });
    }

    const attendanceForGlog: AttendanceForGlog[] = [];
    const rewardCmsIds = [];
    for (let addedDay = 1; addedDay <= supplementCount; addedDay++) {
      const attendanceCmsElem =
        attendanceCms?.[ATTENDANCE_TYPE.ACCUMULATE1]?.[userAttendance.accum + addedDay];
      if (!attendanceCmsElem) {
        continue;
      }

      rewardCmsIds.push(attendanceCmsElem.reward);
      attendanceForGlog.push({
        pointType: cmsEx.RedGemPointCmsId,
        pointCost: cms.Const.attendanceCost.value,
        rewardDayCnt: userAttendance.accum + addedDay,
        rewardId: attendanceCmsElem.reward,
      });
    }

    const pointCost = {
      cmsId: cmsEx.RedGemPointCmsId,
      cost: cms.Const.attendanceCost.value * supplementCount,
    };

    userAttendance.accum += supplementCount;
    const changeTask = new UserChangeTask(
      user,
      CHANGE_TASK_REASON.BUY_ATTENDANCE,
      new BuyAttendanceSpec(userAttendance, rewardCmsIds, eventPageCms.mail, curTimeUtc, pointCost)
    );

    const res = changeTask.trySpec();
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      throw new MError(
        'failed-to-buy-attendance',
        MErrorCode.FAILED_TO_BUY_ATTENDANCE_USER_CHANGE_TASK,
        {
          res,
          reqBody,
        }
      );
    }

    return changeTask.apply().then((sync) => {
      // glog
      const reward_data: RewardData[] = [];
      for (const rewardFixedCmsId of rewardCmsIds) {
        reward_data.push(...cmsEx.convertRewardFixedToGLogRewardData(rewardFixedCmsId));
      }

      for (const glogData of attendanceForGlog) {
        user.glog('event', {
          rsn,
          add_rsn,
          event_id: eventPageCms.id,
          event_name: eventPageCms.name,
          event_type: eventPageCms.type,
          event_group: eventPageCms.groupRef,
          reward_day_cnt: glogData.rewardDayCnt,
          reward_data: cmsEx.convertRewardFixedToGLogRewardData(glogData.rewardId),
          event_cnt: userAttendance.consecutive,
          pr_data: [
            {
              type: glogData.pointType,
              amt: glogData.pointCost,
            },
          ],
          exchange_hash: changeTask.getExchangeHash(),
        });
      }

      // [빌링] 중국의 경우 캐시 반영을 수동으로 해줘야함.
      if (mconf.platform === PLATFORM.SDO) {
        const ret = await mhttp.platformBillingApi.queryCashPair(user.userId, user.storeCode, user.countryCreated);

        user.userPoints.onChargeByPurchaseProduct(
          [
            {
              coinCd: 'red_gem',
              paymentType: 'PAID',
              balance: user.userPoints.paidRedGem,
            },
            {
              coinCd: 'red_gem',
              paymentType: 'FREE',
              balance: user.userPoints.freeRedGem,
            },
          ],
          null
        );
      }

      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
    });
  }
}

class BuyAttendanceSpec extends RewardAndPaymentSpec {
  private _attendance: Attendance;
  private _pointCost: { cost: number; cmsId: number };

  constructor(
    attendance: Attendance,
    rewardCmsIds: number[],
    mailCmsId: number,
    curTimeUtc: number,
    pointCost: { cost: number; cmsId: number }
  ) {
    const resultFunction = (
      result: CHANGE_TASK_RESULT,
      rnpElem: RewardAndPaymentElem,
      user: User,
      tryData: TryData,
      changes: Changes
    ) => {
      let bBreakRnpElemsLoop = false;
      let changeTaskResultOfInternalOp: CHANGE_TASK_RESULT;
      if (result !== CHANGE_TASK_RESULT.OK) {
        const mailCms = cms.Mail[mailCmsId];
        let expireTimeUtc = null;
        let bShouldSetExpirationWhenReceiveAttachment = 0;
        if (mailCms.mailKeepTime > 0) {
          expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
        } else if (mailCms.mailKeepTime === -1) {
          bShouldSetExpirationWhenReceiveAttachment = 1;
        }

        changeTaskResultOfInternalOp = opAddDirectmail(
          user,
          tryData,
          changes,
          mailCmsId,
          curTimeUtc,
          expireTimeUtc,
          bShouldSetExpirationWhenReceiveAttachment,
          null,
          null,
          null,
          null,
          cmsEx.convertRewardFixedToCustomAttachmentStr(rnpElem.cmsId, true, curTimeUtc)
        );
        if (changeTaskResultOfInternalOp > CHANGE_TASK_RESULT.OK_MAX) {
          bBreakRnpElemsLoop = true;
        }
      }

      return { bBreakRnpElemsLoop, changeTaskResultOfInternalOp };
    };

    const rnpElems: RewardAndPaymentElem[] = [];
    for (const cmsId of rewardCmsIds) {
      rnpElems.push({
        type: RNP_TYPE.REWARD_FIXED,
        cmsId,
        bIsNotPermitAddToHardCapLimitLine: true,
        resultFunction,
        bIsAccum: true,
        bIsBound: true,
      });
    }
    super(rnpElems, { gainReason: reasonForLineGamesCash });

    this._attendance = attendance;
    this._pointCost = pointCost;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    let res: CHANGE_TASK_RESULT;

    // 재화 소모
    assert(-this._pointCost.cost < 0);
    res = opAddPoint(
      user,
      tryData,
      changes,
      this._pointCost.cmsId,
      -this._pointCost.cost,
      false,
      undefined,
      { itemId: rsn },
      true
    );
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }

    res = super.accumulate(user, tryData, changes);
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }
    return opSetAttendance(user, tryData, changes, this._attendance);
  }
}
