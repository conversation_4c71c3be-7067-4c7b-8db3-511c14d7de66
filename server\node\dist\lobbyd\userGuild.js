"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserGuild = exports.GuildPointMission = void 0;
const lodash_1 = __importDefault(require("lodash"));
const cms_1 = __importDefault(require("../cms"));
const ex_1 = require("../cms/ex");
const lobby_1 = require("../motiflib/model/lobby");
const mutil_1 = require("../motiflib/mutil");
const Container_1 = require("typedi/Container");
const server_1 = require("./server");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const oceanNpcDesc_1 = require("../cms/oceanNpcDesc");
const guildUtil_1 = require("./guildUtil");
const rankingDesc_1 = require("../cms/rankingDesc");
const userEventRanking_1 = require("./userEventRanking");
var GuildPointMission;
(function (GuildPointMission) {
    function _add(origin, category, v) {
        if (!origin) {
            origin = {};
        }
        if (!origin[category]) {
            origin[category] = 0;
        }
        origin[category] += v;
        return origin;
    }
    function save(user, guildData, userLightInfos, category, addGP) {
        const { guildRedis, rankingManager } = Container_1.Container.get(server_1.LobbyService);
        const guildId = user.userGuild.guildId;
        const dgp = _add(guildData.dailyGuildPoints[user.userId], category, addGP);
        const wgp = _add(guildData.weeklyGuildPoints[user.userId], category, addGP);
        const agp = _add(guildData.accumGuildPoints[user.userId], category, addGP);
        guildData.dailyGuildPoints[user.userId] = dgp;
        guildData.weeklyGuildPoints[user.userId] = wgp;
        guildData.accumGuildPoints[user.userId] = agp;
        const guildMember = guildData.members[user.userId];
        guildMember.guildPointUpdateTimeUtc = (0, mutil_1.curTimeUtc)();
        const sync = {};
        // 라인게임즈 로그용
        let nation = null;
        let hardCap = 0;
        let rsn;
        const nationCms = cms_1.default.Nation[user.nationCmsId];
        nation = nationCms ? nationCms.name : null;
        const grade = guildData.members[user.userId].grade;
        if (category === lobby_1.GUILD_POINT_CATEGORY.ATTENDANCE) {
            hardCap = cms_1.default.Const.GuildAttendancePerPoint.value;
            rsn = 'exp_by_attendance';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.BATTLE) {
            hardCap = cms_1.default.Const.GuildPointBattleMaxLimit.value;
            rsn = 'exp_by_battle';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.TRADE) {
            hardCap = cms_1.default.Const.GuildTradeMaxLimit.value;
            rsn = 'exp_by_trade';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.EXPLORE) {
            hardCap = cms_1.default.Const.GuildPointExploreMaxLimit.value;
            rsn = 'exp_by_explore';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.PLUNDER) {
            hardCap = cms_1.default.Const.GuildPointPVPMaxLimit.value;
            rsn = 'exp_by_plunder';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.CRAFT) {
            hardCap = cms_1.default.Const.GuildPointCraftMaxLimit.value;
            rsn = 'exp_by_craft';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.INVEST) {
            hardCap = cms_1.default.Const.GuildInvestMaxLimit.value;
            rsn = 'exp_by_invest';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE) {
            hardCap = cms_1.default.Const.GuildPointUnionRequestMaxLimit.value;
            rsn = 'exp_by_union_quest_complete';
        }
        else if (category === lobby_1.GUILD_POINT_CATEGORY.SYNTHESIS) {
            hardCap = cms_1.default.Const.GuildPointUnionRequestMaxLimit.value;
            rsn = 'exp_by_synthesis';
        }
        // 레디스에 길드포인트 업데이트
        return (guildRedis['updateGuildPoint'](user.userId, guildId, JSON.stringify(dgp), JSON.stringify(wgp), JSON.stringify(agp))
            .then(() => {
            return guildRedis['updateGuildMember'](guildId, user.userId, JSON.stringify(guildMember));
        })
            .then(() => {
            lodash_1.default.merge(sync, {
                add: {
                    userGuild: {
                        guild: {
                            members: {
                                [user.userId]: {
                                    dailyGuildPoints: {
                                        [category]: dgp[category],
                                    },
                                    weeklyGuildPoints: {
                                        [category]: wgp[category],
                                    },
                                    accumGuildPoints: {
                                        [category]: agp[category],
                                    },
                                },
                            },
                        },
                    },
                },
            });
            const guild_data = guildUtil_1.GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);
            user.glog('guild_active_point', {
                rsn: 'guild_active_point',
                add_rsn: null,
                nation,
                grade,
                category,
                category_cv: addGP,
                category_progress: `${dgp[category]}/${hardCap}`,
                daily_point: convertGuildPointToLog(dgp),
                weekly_point: convertGuildPointToLog(wgp),
                total_point: convertGuildPointToLog(agp),
                guild_data,
            });
        })
            // 길드경험치
            .then(() => {
            return guildUtil_1.GuildUtil.updateExp(user, guildId, guildData, userLightInfos, Math.floor(addGP), rsn, null);
        })
            .then((syncResult) => {
            lodash_1.default.merge(sync, syncResult);
            // 기여도 월드 랭킹 업데이트
            let score = 0;
            lodash_1.default.forOwn(guildData.accumGuildPoints, (agp) => {
                lodash_1.default.forOwn(agp, (value) => (score += value));
            });
            rankingManager.updateRanking(rankingDesc_1.RANKING_CMS_ID.GUILD_CONTRIBUTION, guildId, score, user.userId);
            return sync;
        }));
    }
    GuildPointMission.save = save;
    // 출석 길드포인트 등록 후 길드정보를 반환.
    function getGuildInfoAndAttendance(user, bAttendance) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        let guildData;
        let userLightInfos;
        const sync = {};
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId)
            .then((result) => {
            guildData = result.guildData;
            userLightInfos = result.userLightInfos;
            if (bAttendance === true) {
                let curDailyPoint = 0;
                if (guildData.dailyGuildPoints[user.userId]) {
                    const dailyPoints = guildData.dailyGuildPoints[user.userId];
                    if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.ATTENDANCE]) {
                        curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.ATTENDANCE];
                    }
                }
                let addedValue = 0;
                if (curDailyPoint < cms_1.default.Const.GuildPointAttendanceMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildAttendancePerPoint.value;
                    if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointAttendanceMaxLimit.value) {
                        addedValue = cms_1.default.Const.GuildPointAttendanceMaxLimit.value - curDailyPoint;
                    }
                }
                if (!(0, mutil_1.isNotANumber)(addedValue) && addedValue > 0) {
                    return save(user, guildData, userLightInfos, lobby_1.GUILD_POINT_CATEGORY.ATTENDANCE, addedValue);
                }
            }
        })
            .then(() => {
            lodash_1.default.merge(sync, guildUtil_1.GuildUtil.buildGuildSyncDataAll(user, guildData, userLightInfos));
            lodash_1.default.merge(sync, {
                add: {
                    userGuild: {
                        guild: {
                            lastResetTimeUtc: (0, mutil_1.curTimeUtc)(),
                        },
                    },
                },
            });
            return sync;
        });
    }
    GuildPointMission.getGuildInfoAndAttendance = getGuildInfoAndAttendance;
    // PVE 전투 승리 길드포인트 추가.
    function processBattleWin(user, npcCmsId) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        const npcCms = (0, ex_1.getOceanNpcCms)()[npcCmsId];
        if (!npcCms) {
            mlog_1.default.error('invalid-ocean-npc-cms-id-at-processBattleWin', {
                userId: user.userId,
                npcCmsId,
            });
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.BATTLE]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.BATTLE];
                }
            }
            let addedValue = 0;
            if (npcCms.OceanNpcType === oceanNpcDesc_1.OCEAN_NPC_TYPE.LOCAL ||
                npcCms.OceanNpcType === oceanNpcDesc_1.OCEAN_NPC_TYPE.EVENT) {
                addedValue = cms_1.default.Const.GuildBattlePerPoint1.value;
            }
            else if (npcCms.OceanNpcType === oceanNpcDesc_1.OCEAN_NPC_TYPE.ELITE) {
                addedValue = cms_1.default.Const.GuildBattlePerPoint2.value;
            }
            if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointBattleMaxLimit.value) {
                addedValue = cms_1.default.Const.GuildPointBattleMaxLimit.value - curDailyPoint;
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, result.guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.BATTLE, addedValue);
        });
    }
    GuildPointMission.processBattleWin = processBattleWin;
    // PVP 전투 승리 길드포인트 추가.
    function processPvpPlunder(user) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.PLUNDER]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.PLUNDER];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildPointPVPMaxLimit.value) {
                addedValue = cms_1.default.Const.GuildPVPPerPoint.value;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointPVPMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildPointPVPMaxLimit.value - curDailyPoint;
                }
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, result.guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.PLUNDER, addedValue);
        });
    }
    GuildPointMission.processPvpPlunder = processPvpPlunder;
    // 탐험 길드포인트 추가.
    function processExplore(user) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.EXPLORE]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.EXPLORE];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildPointExploreMaxLimit.value) {
                addedValue = cms_1.default.Const.GuildExplorePerPoint.value;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointExploreMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildPointExploreMaxLimit.value - curDailyPoint;
                }
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, result.guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.EXPLORE, addedValue);
        });
    }
    GuildPointMission.processExplore = processExplore;
    // 교역 길드포인트 추가.
    function processTrade(user, profit) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        if (profit <= 0) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.TRADE]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.TRADE];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildTradeMaxLimit.value) {
                const m = profit / cms_1.default.Const.GuildTradeBaseDucat.value;
                addedValue = cms_1.default.Const.GuildTradePerPoint.value * m;
                addedValue = Math.floor(addedValue * 100) / 100;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildTradeMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildTradeMaxLimit.value - curDailyPoint;
                }
            }
            if (!addedValue) {
                return {};
            }
            return save(user, guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.TRADE, addedValue);
        });
    }
    GuildPointMission.processTrade = processTrade;
    // 제작 길드포인트 추가.
    function processCraft(user, pts, guild) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return Promise.resolve()
            .then(() => {
            if (!guild) {
                return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId);
            }
            return guild;
        })
            .then((guild) => {
            const guildData = guild.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.CRAFT]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.CRAFT];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildPointCraftMaxLimit.value) {
                addedValue = pts;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointCraftMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildPointCraftMaxLimit.value - curDailyPoint;
                }
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, guildData, guild.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.CRAFT, addedValue);
        });
    }
    GuildPointMission.processCraft = processCraft;
    // 합성 길드포인트 추가.
    function processSynthesis(user, pts, guild) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return Promise.resolve()
            .then(() => {
            if (!guild) {
                return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId);
            }
            return guild;
        })
            .then((guild) => {
            const guildData = guild.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.SYNTHESIS]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.SYNTHESIS];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildPointSynthesisMaxLimit.value) {
                addedValue = pts;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointSynthesisMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildPointSynthesisMaxLimit.value - curDailyPoint;
                }
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, guildData, guild.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.SYNTHESIS, addedValue);
        });
    }
    GuildPointMission.processSynthesis = processSynthesis;
    // 투자 길드포인트 추가.
    function processGoverInvest(user, investPoints) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        if (investPoints <= 0) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.INVEST]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.INVEST];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildInvestMaxLimit.value) {
                const m = investPoints / cms_1.default.Const.GuildInvestBaseDucat.value;
                addedValue = Math.floor(cms_1.default.Const.GuildInvestPerPoint.value * m);
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildInvestMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildInvestMaxLimit.value - curDailyPoint;
                }
            }
            if (!addedValue) {
                return {};
            }
            return save(user, guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.INVEST, addedValue);
        });
    }
    GuildPointMission.processGoverInvest = processGoverInvest;
    // 조합의뢰 완료 길드포인트 추가.
    function processUnionQuestComplete(user) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            const guildData = result.guildData;
            let curDailyPoint = 0;
            if (guildData.dailyGuildPoints[user.userId]) {
                const dailyPoints = guildData.dailyGuildPoints[user.userId];
                if (dailyPoints[lobby_1.GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE]) {
                    curDailyPoint = dailyPoints[lobby_1.GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE];
                }
            }
            let addedValue = 0;
            if (curDailyPoint < cms_1.default.Const.GuildPointUnionRequestMaxLimit.value) {
                addedValue = cms_1.default.Const.GuildUnionRequestPerPoint.value;
                if (curDailyPoint + addedValue > cms_1.default.Const.GuildPointUnionRequestMaxLimit.value) {
                    addedValue = cms_1.default.Const.GuildPointUnionRequestMaxLimit.value - curDailyPoint;
                }
            }
            if ((0, mutil_1.isNotANumber)(addedValue) || addedValue === 0) {
                return {};
            }
            return save(user, result.guildData, result.userLightInfos, lobby_1.GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE, addedValue);
        });
    }
    GuildPointMission.processUnionQuestComplete = processUnionQuestComplete;
    function processDev(user, category, value) {
        if (!user.userGuild.guildId) {
            return Promise.resolve({});
        }
        return guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then((result) => {
            // 진행도는 제외
            return save(user, result.guildData, result.userLightInfos, category, value);
        });
    }
    GuildPointMission.processDev = processDev;
})(GuildPointMission = exports.GuildPointMission || (exports.GuildPointMission = {}));
class UserGuild {
    constructor() {
        this.guildId = 0;
        this.lastGuildLeftTimeUtc = 0;
        // 가입 대기중인 길드 목록
        this.waitingJoinGuildIds = {};
        // 가입된 길드 캐시정보 (마을 및 오션에서 캐릭터 상단에 표시될 길드정보)
        this.guildApperance = {
            guildName: '',
            grade: 0,
            emblemImageCmsId: 0,
            emblemColorCmsId: 0,
            emblemBorderCmsId: 0,
        };
        // 진행 중인 상회제작 아이템
        this.guildCraftProgresses = {};
        // 진행 중인 합성 아이템
        this.guildSynthesisProgresses = {};
    }
    clone() {
        const c = new UserGuild();
        c.cloneSet(this.guildId, this.lastGuildLeftTimeUtc, lodash_1.default.cloneDeep(this.waitingJoinGuildIds), lodash_1.default.cloneDeep(this.guildApperance), lodash_1.default.cloneDeep(this.guildCraftProgresses), lodash_1.default.cloneDeep(this.guildSynthesisProgresses));
        return c;
    }
    cloneSet(guildId, lastGuildLeftTimeUtc, waitingJoinGuildIds, guildApperance, guildCraftProgresses, guildSynthesisProgresses) {
        this.guildId = guildId;
        this.lastGuildLeftTimeUtc = lastGuildLeftTimeUtc;
        this.waitingJoinGuildIds = waitingJoinGuildIds;
        this.guildApperance = guildApperance;
        this.guildCraftProgresses = guildCraftProgresses;
        this.guildSynthesisProgresses = guildSynthesisProgresses;
    }
    initWithLoginInfo(loginInfo) {
        this.guildId = loginInfo.guildId;
        this.lastGuildLeftTimeUtc = parseInt(loginInfo.lastGuildLeftTimeUtc, 10);
        loginInfo.waitingJoinGuilds.forEach((guild) => {
            this.waitingJoinGuildIds[guild.guildId] = {
                regTimeUtc: parseInt(guild.regTimeUtc, 10),
            };
        });
        loginInfo.guildCraftProgresses.forEach((craft) => {
            this.guildCraftProgresses[craft.slot] = {
                cmsId: craft.guildCraftCmsId,
                slot: craft.slot,
                startTimeUtc: parseInt(craft.startTimeUtc, 10),
                completionTimeUtc: parseInt(craft.completionTimeUtc),
            };
        });
        loginInfo.guildSynthesisProgresses.forEach((synthesis) => {
            this.guildSynthesisProgresses[synthesis.slot] = {
                cmsId: synthesis.guildSynthesisCmsId,
                slot: synthesis.slot,
                startTimeUtc: parseInt(synthesis.startTimeUtc, 10),
                completionTimeUtc: parseInt(synthesis.completionTimeUtc),
            };
        });
    }
    getSyncData() {
        const craftProgresses = {};
        lodash_1.default.forOwn(this.guildCraftProgresses, (elem) => {
            craftProgresses[elem.slot] = {
                cmsId: elem.cmsId,
                slot: elem.slot,
                startTimeUtc: elem.startTimeUtc,
                completionTimeUtc: elem.completionTimeUtc,
            };
        });
        const synthesisProgresses = {};
        lodash_1.default.forOwn(this.guildSynthesisProgresses, (elem) => {
            synthesisProgresses[elem.slot] = {
                cmsId: elem.cmsId,
                slot: elem.slot,
                startTimeUtc: elem.startTimeUtc,
                completionTimeUtc: elem.completionTimeUtc,
            };
        });
        const sync = {
            userGuild: {
                waitingJoinGuildIds: this.waitingJoinGuildIds,
                leftGuildTimeUtc: this.lastGuildLeftTimeUtc,
                craftProgresses,
                synthesisProgresses,
            },
        };
        return sync;
    }
    getGuildName() {
        return this.guildApperance.guildName;
    }
    setGuildAppearance(ga) {
        lodash_1.default.merge(this.guildApperance, ga);
    }
    joinGuild(user, id, guildData) {
        this.guildId = id;
        this.waitingJoinGuildIds = {};
        this.guildApperance = {
            guildName: guildData.guild.guildName,
            grade: guildData.members[user.userId].grade,
            emblemImageCmsId: guildData.guild.emblemImageCmsId,
            emblemColorCmsId: guildData.guild.emblemColorCmsId,
            emblemBorderCmsId: guildData.guild.emblemBorderCmsId,
        };
        // 채팅 채널 생성
        const userIdStr = user.userId.toString();
        const channelName = `GUILD_${id}`;
        mhttp_1.default.platformChatApi
            .existChannel(channelName)
            .then((ret) => {
            if (!ret) {
                return mhttp_1.default.platformChatApi.createGuildChannel(channelName, userIdStr, false);
            }
            return null;
        })
            .then(() => {
            return mhttp_1.default.platformChatApi.allowGuildChannel(channelName, userIdStr);
        })
            .then(() => {
            return mhttp_1.default.platformChatApi.channelJoin(channelName, userIdStr);
        })
            .catch((err) => {
            mlog_1.default.error('mhttp.chatd joinGuild is failed.', {
                err: err.message,
                userId: user.userId,
                guildId: id,
            });
        });
        user.onGuildJoinOrChange();
        return {
            remove: {
                userGuild: {
                    waitingJoinGuildIds: true,
                },
            },
        };
    }
    updateGuildAppearance(user, guildName, grade, emblemImageCmsId, emblemColorCmsId, emblemBorderCmsId) {
        let isChanged = false;
        if (guildName && this.guildApperance.guildName !== guildName) {
            isChanged = true;
            lodash_1.default.merge(this.guildApperance, { guildName });
        }
        if (!(0, mutil_1.isNotANumber)(grade) && this.guildApperance.grade !== grade) {
            isChanged = true;
            lodash_1.default.merge(this.guildApperance, { grade });
        }
        if (!(0, mutil_1.isNotANumber)(emblemImageCmsId) &&
            this.guildApperance.emblemImageCmsId !== emblemImageCmsId) {
            isChanged = true;
            lodash_1.default.merge(this.guildApperance, { emblemImageCmsId });
        }
        if (!(0, mutil_1.isNotANumber)(emblemColorCmsId) &&
            this.guildApperance.emblemColorCmsId !== emblemColorCmsId) {
            isChanged = true;
            lodash_1.default.merge(this.guildApperance, { emblemColorCmsId });
        }
        if (!(0, mutil_1.isNotANumber)(emblemBorderCmsId) &&
            this.guildApperance.emblemBorderCmsId !== emblemBorderCmsId) {
            isChanged = true;
            lodash_1.default.merge(this.guildApperance, { emblemBorderCmsId });
        }
        if (isChanged) {
            user.onGuildJoinOrChange();
        }
    }
    leaveGuild(user, bShouldDeleteChattingChannel, bShouldDisallowChattingChannel) {
        const guildId = this.guildId;
        this.guildId = 0;
        this.lastGuildLeftTimeUtc = (0, mutil_1.curTimeUtc)();
        this.guildApperance = {
            guildName: '',
            grade: 0,
            emblemImageCmsId: 0,
            emblemColorCmsId: 0,
            emblemBorderCmsId: 0,
        };
        const removeEventPageCmsIds = userEventRanking_1.EventRankingUtil.removeGuildUser(user.userId, guildId, (0, mutil_1.curTimeUtc)());
        const userIdStr = user.userId.toString();
        const channelName = `GUILD_${guildId}`;
        mhttp_1.default.platformChatApi
            .channelLeave(channelName, userIdStr)
            .then(() => {
            if (bShouldDisallowChattingChannel) {
                return mhttp_1.default.platformChatApi.disallowGuildChannel(channelName, userIdStr);
            }
            return null;
        })
            .then(() => {
            if (bShouldDeleteChattingChannel) {
                return mhttp_1.default.platformChatApi.deleteChannel(channelName);
            }
            return null;
        })
            .catch((err) => {
            mlog_1.default.error('mhttp.chatd leaveGuild is failed.', {
                err: err.message,
                userId: user.userId,
                guildId,
            });
        });
        user.onGuildLeave();
        const sync = {
            remove: {
                userGuild: {
                    guild: true,
                },
            },
            add: {
                userGuild: {
                    leftGuildTimeUtc: this.lastGuildLeftTimeUtc,
                },
            },
        };
        for (const eventPageCmsId of removeEventPageCmsIds) {
            lodash_1.default.merge(sync, {
                add: {
                    eventRanking: {
                        [eventPageCmsId]: {
                            guildUserScore: 0,
                        },
                    },
                },
            });
        }
        return sync;
    }
    addWaitingJoinGuild(guildId, regTimeUtc) {
        this.waitingJoinGuildIds[guildId] = {
            regTimeUtc,
        };
        return {
            add: {
                userGuild: {
                    waitingJoinGuildIds: {
                        [guildId]: {
                            regTimeUtc,
                        },
                    },
                },
            },
        };
    }
    removeWaitingJoinGuild(guildId) {
        delete this.waitingJoinGuildIds[guildId];
        return {
            remove: {
                userGuild: {
                    waitingJoinGuildIds: {
                        [guildId]: true,
                    },
                },
            },
        };
    }
    getGuildAppearance() {
        if (!this.guildId) {
            return null;
        }
        return this.guildApperance;
    }
    applyGulidSynthesisProgress(gsp) {
        this.guildSynthesisProgresses[gsp.slot] = gsp;
    }
    removeGulidSynthesisProgress(slotNo) {
        delete this.guildSynthesisProgresses[slotNo];
    }
}
exports.UserGuild = UserGuild;
const convertGuildPointToLog = (gp) => {
    const result = {};
    for (let i = 1; i < lobby_1.GUILD_POINT_CATEGORY.MAX; i++) {
        result[`k_${i}`] = gp[i] || 0;
    }
    return result;
};
//# sourceMappingURL=userGuild.js.map