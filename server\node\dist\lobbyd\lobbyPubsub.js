"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pubUserKicked = exports.init = void 0;
const typedi_1 = require("typedi");
const lodash_1 = __importDefault(require("lodash"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const pubsub_1 = __importDefault(require("../redislib/pubsub"));
const proto = __importStar(require("../proto/lobby/proto"));
const townManager_1 = require("./townManager");
const userManager_1 = require("./userManager");
const cmsEx = __importStar(require("../cms/ex"));
const arenaManager_1 = require("./arenaManager");
const server_1 = require("./server");
const formula_1 = require("../formula");
const cms_1 = __importDefault(require("../cms"));
const town_1 = require("../motiflib/model/town");
const userToast_1 = require("./userToast");
const villageManager_1 = require("./villageManager");
const clashPrizeManager_1 = require("./clashPrizeManager");
const enum_1 = require("../motiflib/model/auth/enum");
const const_1 = require("../motiflib/const");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const cashShopDesc_1 = require("../cms/cashShopDesc");
const sdoBillingUtil_1 = require("./sdoBillingUtil");
const cashShopBuyWithoutPurchase_1 = require("./packetHandler/common/cashShopBuyWithoutPurchase");
// ----------------------------------------------------------------------------
// Pubsub handler functions.
// ----------------------------------------------------------------------------
// ----------------------------------------------------------------------------
function handleKick(msgStr) {
    mlog_1.default.debug('handleKick with msgStr:', { msgStr });
    const msg = JSON.parse(msgStr);
    const userId = msg.userId;
    const reason = msg.reason;
    const authdId = msg.authdId;
    mlog_1.default.warn('kicking user ...', {
        userId,
        reason,
        authdId,
    });
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    return userManager.kickUser(userId, reason, authdId).catch((err) => {
        mlog_1.default.error('lobbyPubsub.handleKick error', {
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleDevelopmentNationSharePointChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    if (msg.towns.length === 0) {
        return;
    }
    mlog_1.default.info('[SEASON] handleDevelopmentNationSharePointChanged publish called');
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    return townManager
        .onDevelopmentNationSharePointChanged(msg.towns.map((elem) => elem.townCmsId), msg.towns[0].updateTimeUtc)
        .then(() => {
        mlog_1.default.info('[SEASON] handleDevelopmentNationSharePointChanged publish finished');
    })
        .catch((err) => {
        mlog_1.default.alert('lobbyPubsub.handleDevelopmentNationSharePointChanged error', {
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleInvestmentSessionClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    if (msg.towns.length === 0) {
        return;
    }
    mlog_1.default.info(`[SEASON] handleInvestmentSessionClosed publish received for sessionId[${msg.sessionId}], season[${msg.seasonId}]`);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    return townManager
        .onInvestmentSessionClosed(msg.towns, msg.sessionId, msg.seasonId)
        .then(() => {
        mlog_1.default.info(`[SEASON] handleInvestmentSessionClosed publish finished for sessionId[${msg.sessionId}], season[${msg.seasonId}]`);
    })
        .catch((err) => {
        mlog_1.default.error('lobbyPubsub.handleInvestmentSessionClosed error', {
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleTownInvested(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    return townManager.onInvested(msg.townCmsId, msg.sessionId, msg.updateTimeUtc).catch((err) => {
        mlog_1.default.alert('lobbyPubsub.handleTownInvested error', {
            townCmsId: msg.townCmsId,
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleTownMayorTaxChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    lodash_1.default.forOwn(msg.changes, (elem, townCmsIdStr) => {
        const townCmsId = parseInt(townCmsIdStr, 10);
        for (const subChange of elem) {
            townManager.setTownMayorTax(townCmsId, subChange.nationCmsId, subChange.tax);
        }
    });
    mlog_1.default.info('[SEASON] handleTownMayorTaxChanged finished', {
        curTimeUtc: msg.curTimeUtc,
    });
}
// ----------------------------------------------------------------------------
function handleTownMayorShipyardTaxChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    lodash_1.default.forOwn(msg.changes, (elem, townCmsIdStr) => {
        const townCmsId = parseInt(townCmsIdStr, 10);
        for (const subChange of elem) {
            townManager.setTownMayorShipyardTax(townCmsId, subChange.nationCmsId, subChange.tax);
        }
    });
}
// ----------------------------------------------------------------------------
function handleDevTownNationSharePointChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    return townManager.devUpdateTownNationSharePoint(msg.townCmsId, msg.nationCmsId, msg.nationSharePoint, msg.updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleTownMayorChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.setTownMayor(msg.townCmsId, msg.mayorUserId, msg.mayorUserName, msg.mayorNationCmsId, msg.updateTimeUtc);
    // 투자 점수 로드
    const weeklySessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(msg.updateTimeUtc, cms_1.default.Define.InvestmentWeeklySessionPivotDay);
    townManager.onInvested(msg.townCmsId, weeklySessionId, msg.updateTimeUtc).catch((err) => {
        mlog_1.default.alert('lobbyPubsub.handleTownMayorChanged townManager.onInvested is failed.', {
            townCmsId: msg.townCmsId,
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleTradeAllUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onAllTradePercentChanged(msg.townCmsId, msg.timeMs);
}
// ----------------------------------------------------------------------------
function handleSomeTradeGoodsUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onSomeTradePercentChanged(msg.townCmsId, msg.changedCmsIds, msg.timeMs);
}
// ----------------------------------------------------------------------------
function handleTradeEventOccurred(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onTradeEventOccurred(msg.townCmsId, msg.occurredEvents);
    // UWO-11420 [서버] Common.UPDATE_TRADE_EVENT_SC 패킷 최적화
    const events = [];
    lodash_1.default.forOwn(msg.occurredEvents, (event, idStr) => {
        const id = parseInt(idStr, 10);
        const eventType = event.t;
        const expiration = event.e;
        let elem;
        if ((0, town_1.isCategoryEvent)(eventType)) {
            // id is trade goods category.
            elem = `c${id}:${eventType}:${expiration}`;
        }
        else {
            // id is trade goods cms id.
            elem = `${id - cmsEx.TradeGooodsStartingCmsId}:${eventType}:${expiration}`;
        }
        events.push(elem);
    });
    const resp = {
        sync: {
            add: {
                tradeEvents: {
                    [msg.townCmsId]: events,
                },
            },
        },
    };
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}
// ----------------------------------------------------------------------------
function handleTradeEventExpired(msgStr, bActiveMayorTradeEvent = false) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    const townCmsId = msg.townCmsId;
    townManager.onTradeEventExpired(msg.townCmsId, msg.goodsCmsIdOrCategories);
    // 기존 클라이언트에서 expire 확인하여 이벤트 삭제처리 하였으나
    // 시장 마켓 이벤트 활성화시  기존 이벤트 삭제 처리로 인해 아래와 같이 전부 삭제 처리
    if (bActiveMayorTradeEvent) {
        const resp = {
            sync: {
                remove: {
                    tradeEvents: [townCmsId.toString()],
                    towns: {
                        [townCmsId.toString()]: ['crazeBudget'],
                    },
                },
            },
        };
        const userManager = typedi_1.Container.get(userManager_1.UserManager);
        userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
    }
}
// ----------------------------------------------------------------------------
function handleTradeCrazeEventIsClosedByDucat(msgStr) {
    const msg = JSON.parse(msgStr);
    const townCmsId = msg.townCmsId;
    const pricePercents = msg.crazeEvnets;
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onTradeCrazeEventIsClosedByDucat(townCmsId);
    const resp = {
        sync: {
            add: {
                towns: {
                    [townCmsId]: {
                        tradePricePercents: pricePercents,
                    },
                },
            },
            remove: {
                tradeEvents: [townCmsId.toString()],
                towns: {
                    [townCmsId]: ['crazeBudget'],
                },
            },
        },
    };
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}
// ----------------------------------------------------------------------------
function handleUnpopularEventClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    const { townCmsId, tradeGoodsCmsId, percent } = msg;
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onTradeUnpopularIsClosedByDucat(townCmsId, tradeGoodsCmsId, percent);
    const resp = {
        sync: {
            add: {
                towns: {
                    [townCmsId]: {
                        tradePricePercents: {
                            [tradeGoodsCmsId]: percent,
                        },
                    },
                },
            },
            remove: {
                tradeEvents: [townCmsId.toString()],
                towns: {
                    [townCmsId]: ['tradeUnpopular'],
                },
            },
        },
    };
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}
// ----------------------------------------------------------------------------
function handleSmuggleAllUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    townManager.onAllSmugglePercentChanged(msg.townCmsId, msg.timeMs);
}
// ----------------------------------------------------------------------------
function handleArenaSessionClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    const arenaManager = typedi_1.Container.get(arenaManager_1.ArenaManager);
    return arenaManager.onArenaSessionClosed(msg.sessionId);
}
// function handleChinaUnder18ClosingTime() {
//   mlog.info('china under 18 closing time.');
//   const userManager = Container.get(UserManager);
//   return userManager.kickChinaAllUnder18(KICK_REASON.CHINA_UNDER_18_CLOSING_TIME);
// }
// ----------------------------------------------------------------------------
function handleUpdateRaid(msgStr) {
    const { notice } = JSON.parse(msgStr);
    const { raidManager } = typedi_1.Container.get(server_1.LobbyService);
    raidManager.subcribe(notice);
}
// ----------------------------------------------------------------------------
function handleUpdateGuildRaid(msgStr) {
    const { guildId, cmsId, notice } = JSON.parse(msgStr);
    const { guildRaidManager } = typedi_1.Container.get(server_1.LobbyService);
    guildRaidManager.subcribe(guildId, cmsId, notice);
}
// ----------------------------------------------------------------------------
function handleVillageEventUpdate(msgStr) {
    const villageManager = typedi_1.Container.get(villageManager_1.VillageManager);
    villageManager.loadVillageEvents();
}
// ----------------------------------------------------------------------------
function handleVillageStorageUpdate(msgStr) {
    const msg = JSON.parse(msgStr);
    const villageManager = typedi_1.Container.get(villageManager_1.VillageManager);
    villageManager.loadVillageStorages(msg.curSessionId, msg.nextSessionId);
}
// ----------------------------------------------------------------------------
function handleVillageStorageDeleteExpiredSession(msgStr) {
    const msg = JSON.parse(msgStr);
    const villageManager = typedi_1.Container.get(villageManager_1.VillageManager);
    villageManager.deleteExpiredStorageSessions(msg);
}
function handleNationElectionCandidateRegistered(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onElectionCandidateRegistered(msg);
}
// ----------------------------------------------------------------------------
function handleNationElectionCandidatesModified(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onElectionCandidatesModified(msg);
}
// ----------------------------------------------------------------------------
// 투표 발생시는 브로드캐스팅 없이 주기적인 유저의 요청으로 동기화한다
function handleNationElectionVotedToCandidate(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onVotedToElectionCandidate(msg);
}
// ----------------------------------------------------------------------------
// 투표 갱신시는 브로드캐스팅 없이 주기적인 유저의 요청으로 동기화한다
function handleElectionVotesUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onElectionCandidateVotesUpdated(msg);
}
// ----------------------------------------------------------------------------
function handleNationElectionSessionClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    const { nations, sessionId } = msg;
    return nationManager.onElectionSessionClosed(nations, sessionId).then(() => {
        const userManager = typedi_1.Container.get(userManager_1.UserManager);
        // 당선자 본인의 맵에서 표시되는 관직 관련 외형 정보 변경 및 sync 동기화
        nations.forEach((nation) => {
            if (nation.primeMinisterUserId) {
                const user = userManager.getUserByUserId(nation.primeMinisterUserId);
                if (user) {
                    user.onNationCabinetJoinOrChange();
                    // sync 동기화
                    user.userNation.applyCabinetMemberSync(user);
                }
            }
        });
    });
}
// ----------------------------------------------------------------------------
function handleNationElectionCandidatesRemoved(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onElectionCandidatesRemoved(msg);
}
// ----------------------------------------------------------------------------
function handleNationCabinetMemberAppointed(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    // 피임명자 본인의 맵에서 표시되는 관직 관련 외형 정보 변경 및 sync 동기화
    nationManager.onNationCabinetMemberAppointed(msg).then(() => {
        const userManager = typedi_1.Container.get(userManager_1.UserManager);
        const user = userManager.getUserByUserId(msg.memberUserId);
        if (user) {
            user.onNationCabinetJoinOrChange();
            // sync 동기화
            user.userNation.applyCabinetMemberSync(user);
        }
    });
}
// ----------------------------------------------------------------------------
function handleNationCabinetMemberWageRateChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationCabinetMemberWageRateChanged(msg);
}
// ----------------------------------------------------------------------------
function handleNationCabinetMembersRemoved(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    const nation = nationManager.get(msg.nationCmsId);
    if (!nation) {
        return;
    }
    const primeministerUserId = nation.getPrimeMinisterUserId(msg.sessionId);
    nationManager.onNationCabinetMembersRemoved(msg);
    // 피해임자 본인의 외형 정보 변경 및 sync 동기화
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    msg.memberUserIds.forEach((userId) => {
        const user = userManager.getUserByUserId(userId);
        if (user) {
            user.onNationCabinetLeave();
            // sync 동기화
            user.userNation.applyCabinetMemberSync(user);
        }
    });
    // 사퇴한 유저중에 총리가 있는경우 전국민에게 브로드캐스트한다
    // !! 주의 !! 원칙적으로는 브로드캐스팅 받는 모든 유저의 updateSyncTracker 를 호출해줘야 하지만
    //  총리가 사퇴한 경우는 1회성이고 다시 임명되지 않기 때문에 호출하지 않는다
    if (primeministerUserId && msg.memberUserIds.indexOf(primeministerUserId) >= 0) {
        mlog_1.default.info('[ELECTION] handleNationCabinetMembersRemoved - primeMinister gone.. broadcasting to nation users', {
            nationCmsId: msg.nationCmsId,
            sessionId: msg.sessionId,
            primeministerUserId,
            memberUserIds: msg.memberUserIds,
        });
        const nationUserIds = userManager.gatherNationUserIds(msg.nationCmsId, [primeministerUserId]);
        const resp = {
            sync: {
                remove: {
                    nations: {
                        [msg.nationCmsId]: {
                            cabinetMembers: {
                                [msg.sessionId]: { [primeministerUserId]: true },
                            },
                        },
                    },
                },
            },
        };
        mlog_1.default.verbose('[ELECTION] handleNationCabinetMembersRemoved - broadcasting nation users', {
            nationUserIds,
        });
        userManager.broadcastJsonPacket(nationUserIds, proto.Common.NATION_ELECTION_UPDATE_SC, resp);
    }
}
// ----------------------------------------------------------------------------
function handleNationWageWeeklySessionClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleNationBudgetUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleNationBudgetDonated(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
    nationManager.onNationBudgetDonated(msg.nations, msg.updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleNationPoliciesUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationPoliciesUpdated(msg.nations, msg.updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleNationGoalPromiseChanged(msgStr) {
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    const msg = JSON.parse(msgStr);
    const { nationCmsId, cabinetSessionId, updateTimeUtc } = msg;
    nationManager.onGoalPromiseChanged(nationCmsId, cabinetSessionId, updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleSupportShopPurchasesd(msgStr) {
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    const msg = JSON.parse(msgStr);
    const { nationCmsId, cabinetSessionId, updateTimeUtc } = msg;
    nationManager.onSupportShopPurchased(nationCmsId, cabinetSessionId, updateTimeUtc);
}
// ----------------------------------------------------------------------------
function handleNationNoticeUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationNoticeUpdated(msg);
}
// ----------------------------------------------------------------------------
function handleDevNationCabinetMembersAllReload(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onDevNationCabinetMembersAllReload(msg);
}
// ----------------------------------------------------------------------------
function handleNationCabinetMemberThoughtChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    nationManager.onNationCabinetMemberThoughtChanged(msg);
}
// ----------------------------------------------------------------------------
function handleBoughtWebShopProduct(msgStr) {
    const msg = JSON.parse(msgStr);
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    userManager.onBoughtWebShopProduct(msg);
}
// ----------------------------------------------------------------------------
function handleClashPrize(msgStr) {
    const clashPrizeManager = typedi_1.Container.get(clashPrizeManager_1.ClashPrizeManager);
    clashPrizeManager.updateRankers();
}
// ----------------------------------------------------------------------------
function handleInvestmentSeasonRankSessionChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    mlog_1.default.info('[SEASON] handleInvestmentSeasonRankSessionChanged publish called');
    const { investmentSeasonRankingManager } = typedi_1.Container.get(server_1.LobbyService);
    investmentSeasonRankingManager
        .onInvestmentSeasonRankSessionChanged(msg.seasonId, msg.updateTimeUtc)
        .catch((err) => {
        mlog_1.default.alert('lobbyPubsub.handleInvestmentSeasonRankChanged error', {
            seasonId: msg.seasonId,
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
function handleInvestmentSeasonRankClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    mlog_1.default.info('[SEASON] handleInvestmentSeasonRankClosed publish called');
    const { investmentSeasonRankingManager } = typedi_1.Container.get(server_1.LobbyService);
    investmentSeasonRankingManager.onInvestmentSeasonRankClosed(msg.seasonId).catch((err) => {
        mlog_1.default.alert('lobbyPubsub.handleInvestmentSeasonRankClosed error', {
            seasonId: msg.seasonId,
            err: err.message,
            stack: err.stack,
        });
    });
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
const init = () => {
    const worldPubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
    const authPubsub = typedi_1.Container.of('pubsub-auth').get(pubsub_1.default);
    // Subscribe.
    let ch = `kick:${mconf_1.default.appId}`;
    authPubsub.subscribe(ch, handleKick);
    authPubsub.subscribe(`web_shop:${mconf_1.default.appId}`, handleBoughtWebShopProduct);
    worldPubsub.subscribe('development_nation_share_point_changed', handleDevelopmentNationSharePointChanged);
    worldPubsub.subscribe('investment_session_closed', handleInvestmentSessionClosed);
    worldPubsub.subscribe('town_invested', handleTownInvested);
    worldPubsub.subscribe('town_mayor_tax_changed', handleTownMayorTaxChanged);
    worldPubsub.subscribe('town_mayor_shipyard_tax_changed', handleTownMayorShipyardTaxChanged);
    worldPubsub.subscribe('dev_town_nation_share_point_changed', handleDevTownNationSharePointChanged);
    worldPubsub.subscribe('town_mayor_changed', handleTownMayorChanged);
    worldPubsub.subscribe('trade_all_updated', handleTradeAllUpdated);
    worldPubsub.subscribe('some_trade_goods_updated', handleSomeTradeGoodsUpdated);
    worldPubsub.subscribe('trade_event_occurred', handleTradeEventOccurred);
    worldPubsub.subscribe('trade_event_expired', handleTradeEventExpired);
    worldPubsub.subscribe('trade_craze_event_is_closed_by_budget', handleTradeCrazeEventIsClosedByDucat);
    worldPubsub.subscribe('unpopular_event_closed', handleUnpopularEventClosed);
    worldPubsub.subscribe('smuggle_all_updated', handleSmuggleAllUpdated);
    worldPubsub.subscribe('arena_session_closed', handleArenaSessionClosed);
    worldPubsub.subscribe('update_raid', handleUpdateRaid);
    worldPubsub.subscribe('update_guild_raid', handleUpdateGuildRaid);
    worldPubsub.subscribe('world_broadcast', userToast_1.handleWorldBroadCastToastMsg);
    worldPubsub.subscribe('nation_election_candidate_registered', handleNationElectionCandidateRegistered);
    worldPubsub.subscribe('nation_election_candidates_modified', handleNationElectionCandidatesModified);
    worldPubsub.subscribe('nation_election_voted_to_candidate', handleNationElectionVotedToCandidate);
    worldPubsub.subscribe('nation_election_votes_updated', handleElectionVotesUpdated);
    worldPubsub.subscribe('nation_election_session_closed', handleNationElectionSessionClosed);
    worldPubsub.subscribe('nation_election_candidates_removed', handleNationElectionCandidatesRemoved);
    worldPubsub.subscribe('nation_cabinet_member_appointed', handleNationCabinetMemberAppointed);
    worldPubsub.subscribe('nation_cabinet_member_wage_rate_changed', handleNationCabinetMemberWageRateChanged);
    worldPubsub.subscribe('nation_cabinet_member_removed', handleNationCabinetMembersRemoved);
    worldPubsub.subscribe('nation_goal_promise_changed', handleNationGoalPromiseChanged);
    worldPubsub.subscribe('nation_wage_weekly_session_closed', handleNationWageWeeklySessionClosed);
    worldPubsub.subscribe('nation_budget_updated', handleNationBudgetUpdated);
    worldPubsub.subscribe('nation_policies_updated', handleNationPoliciesUpdated);
    worldPubsub.subscribe('nation_support_shop_purchased', handleSupportShopPurchasesd);
    worldPubsub.subscribe('nation_budget_donated', handleNationBudgetDonated);
    worldPubsub.subscribe('nation_notice_updated', handleNationNoticeUpdated);
    worldPubsub.subscribe('dev_nation_cabinet_members_all_reload', handleDevNationCabinetMembersAllReload);
    worldPubsub.subscribe('nation_cabinet_member_thought_changed', handleNationCabinetMemberThoughtChanged);
    // 중국 미성년자 몰입 방지 접속 시간 체크
    // if (mconf.countryCode === COUNTRY_CODE.CHINA) {
    //   pubsub.subscribe('china_under_18_closing_time', handleChinaUnder18ClosingTime);
    // }
    worldPubsub.subscribe('village_event_updated', handleVillageEventUpdate);
    worldPubsub.subscribe('village_storage_updated', handleVillageStorageUpdate);
    worldPubsub.subscribe('village_storage_delete_expired_session', handleVillageStorageDeleteExpiredSession);
    worldPubsub.subscribe('clash_season_rankers_updated', handleClashPrize);
    worldPubsub.subscribe('investment_season_rank_session_changed', handleInvestmentSeasonRankSessionChanged);
    worldPubsub.subscribe('investment_season_rank_closed', handleInvestmentSeasonRankClosed);
    // 중국 전용 이벤트들 처리
    if (mconf_1.default.platform === enum_1.PLATFORM.SDO) {
        worldPubsub.subscribe('sdo-anti-addiction-kick', handleSdoAntiAddictionKick);
        worldPubsub.subscribe('sdo-payment-completed', handleSdoPaymentCompleted);
        setInterval(() => {
            tickAntiAddictionHeartbeat().catch(error => {
                console.log(error);
            });
        }, 601000); // per minutes
        // TODO
        // worldPubsub.subscribe('sdo-user-mute-list-changed', handleSdoUserMuteListChanged);
        // TODO
        // worldPubsub.subscribe('sdo-chat-relay', handleChatRelay);
    }
};
exports.init = init;
// ----------------------------------------------------------------------------
const pubUserKicked = (userId, authdId, message) => {
    const authPubsub = typedi_1.Container.of('pubsub-auth').get(pubsub_1.default);
    const ch = `kick:${authdId}`;
    const msg = {
        userId,
        message,
    };
    mlog_1.default.debug('publish user kicked', {
        userId,
        authdId,
    });
    return authPubsub.publish(ch, JSON.stringify(msg));
};
exports.pubUserKicked = pubUserKicked;
// ----------------------------------------------------------------------------
// 중국 전용 이벤트 핸들러들.
// ----------------------------------------------------------------------------
async function tickAntiAddictionHeartbeat() {
    var _a, _b, _c, _d;
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    const users = userManager.getUsers();
    if (users.length === 0) {
        return;
    }
    const heartbeatUsersInfo = [];
    for (const user of users) {
        heartbeatUsersInfo.push({
            areaid: ((_a = mconf_1.default.sdo) === null || _a === void 0 ? void 0 : _a.areaId) || -1,
            groupid: ((_b = mconf_1.default.sdo) === null || _b === void 0 ? void 0 : _b.groupId) || -1,
            userid: user.accountId,
            accounttype: 25,
            characterid: `${user.accountId}`,
        });
    }
    await mhttp_1.default.sdoaa.heartbeat(heartbeatUsersInfo);
    const promises = [];
    for (const user of users) {
        promises.push(mhttp_1.default.sdoaa.queryTime({
            userId: user.accountId,
            accountType: 25,
            areaId: ((_c = mconf_1.default.sdo) === null || _c === void 0 ? void 0 : _c.areaId) || -1,
            groupId: ((_d = mconf_1.default.sdo) === null || _d === void 0 ? void 0 : _d.groupId) || -1,
            endpointIp: '127.0.0.1',
            endpointPort: 0,
            characterId: `${user.accountId}`,
            deviceId: '',
        }));
    }
    const times = await Promise.all(promises);
    // 이미 시간이 초과된 경우에는 kick
    // 초과되지는 않았지만, 15분 미만일 경우에는 경고 처리(ANTIADDICTION_DEADLINE_IS_IMMINENT_CN)
    for (const time of times) {
        const user = users.find(x => x.accountId === time.userId);
        if (!user) {
            continue;
        }
        if (time.remainingTime <= 0) {
            userManager.kickUser(user.userId, const_1.KICK_REASON.CHINA_UNDER_18_CLOSING_TIME, time.userId, time.message);
        }
        else {
            if (time.remainingTime > 60 &&
                time.remainingTime < mhttp_1.default.sdoaa.remainingTimeWarnThresholdInSec) {
                if (!user.antiAddictionWarningSent) {
                    user.sendJsonPacket(0, proto.Common.ANTIADDICTION_DEADLINE_IS_IMMINENT_CN, {
                        remainingTime: time.remainingTime,
                        message: time.message,
                    });
                    user.antiAddictionWarningSent = true;
                }
            }
        }
    }
    mlog_1.default.info('anti-addiction-times:', times);
}
async function handleSdoAntiAddictionKick(msgStr) {
    mlog_1.default.info(`handleAntiAddictionKick: ${msgStr}`);
    const { msg, userid, characterid, areaid, appid, groupid, guid } = JSON.parse(msgStr);
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    const users = userManager.getUsers();
    const user = users.find(x => x.accountId === userid);
    if (user) {
        mlog_1.default.info(`kickedByAntiAddictionKick: ${userid}`);
        userManager.kickUser(user.userId, const_1.KICK_REASON.CHINA_UNDER_18_CLOSING_TIME, userid, msg);
    }
}
async function handleSdoPaymentCompleted(msgStr) {
    const { accountId, shopItemId, productId, gameOrderId, paymentOrderId, } = JSON.parse(msgStr);
    mlog_1.default.info(`handleSdoPaymentCompleted:`, {
        accountId,
        shopItemId,
        productId,
        gameOrderId,
        paymentOrderId,
    });
    const { userCacheRedis } = typedi_1.Container.get(server_1.LobbyService);
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    const users = userManager.getUsers();
    const user = users.find(x => x.accountId === accountId);
    if (user) {
        try {
            let response;
            const productType = cms_1.default.CashShop[shopItemId].productType;
            if (productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.EVENT_PAGE) {
                response = await sdoBillingUtil_1.SdoBillingUtil.receiveProducts(user, shopItemId);
            }
            else {
                response = await cashShopBuyWithoutPurchase_1.Cph_Common_CashShopBuyWithoutPurchase.staticExec(user, {
                    cmsId: shopItemId,
                    amount: 1,
                    bPermitExchange: false,
                    bUseGachaTicket: false,
                });
            }
            await userCacheRedis['cashShopConsumeTransaction_CN'](accountId, gameOrderId);
            await user.sendJsonPacket(0, proto.Common.CASH_SHOP_CONSUME_TRANSACTION_CN, response);
        }
        catch (e) {
            mlog_1.default.error('handleSdoPaymentCompleted error:', {
                accountId,
                shopItemId,
                productId,
                gameOrderId,
                paymentOrderId,
                error: e.message,
                stack: e.stack,
            });
        }
    }
}
//# sourceMappingURL=lobbyPubsub.js.map