// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import cms from '../../cms';
import { User } from '../../lobbyd/user';

export enum CHANNEL_TYPE {
  ALL = 0,
  SYSTEM = 1,
  WORLD = 2,
  NATION = 3,
  GUILD = 4,
  REGION = 5,
  MAX = 5,
}

export function getAliasName(channelType: CHANNEL_TYPE, channelName?: string): string | null {
  if (channelType === CHANNEL_TYPE.SYSTEM) {
    return '안내';
  } else if (channelType === CHANNEL_TYPE.WORLD) {
    return '세계';
  } else if (channelType === CHANNEL_TYPE.NATION) {
    return '국가_' + cms.Nation[channelName].name;
  } else if (channelType === CHANNEL_TYPE.REGION) {
    if (cms.Region[channelName]) {
      return '지역_' + cms.Region[channelName].name;
    } else if (cms.Town[channelName]) {
      return '지역_' + cms.Town[channelName].name;
    }
  } else if (channelType === CHANNEL_TYPE.GUILD) {
    return '상회_' + channelName.replace('GUILD_', '');
  }
  return null;
}

export enum PublicChannle {}

export interface ChannelInfo {
  name: string; // 채널이름 (deprecated)
  channel_name: string; // 채널이름
  is_group: boolean; // 채널그룹여부
  is_public: boolean; // 채널공개여부
  user_limit: number; // 채널유저제한 (int)
}

export interface CreateChannelResult {
  channel_name: string; // 채널이름
  is_group: boolean; // 채널그룹여부
  is_public: boolean; // 채널공개여부
  user_limit: number; // 채널유저제한 (int)
}

export interface VolanteUser {
  id: string; // 채팅 서버 유저 아이디 = 게임 서버 아이디의 문자열 타입
  nickname: string;
  channels: ChannelInfo[];
}

export interface VolanteUserSession {
  session: VolanteSession;
}

export interface VolanteSession {
  id: string;
  nickname: string;
  channels: string[];
  server_id: string;
}

/**
 * 플랫폼 채팅 API 공통 인터페이스
 */
export interface IPlatformChatApiClient {
  /**
   * 초기화
   */
  init(baseUrl: string, timeout?: number): void;

  /**
   * Salt 설정
   */
  setSalt(salt: string): void;

  /**
   * Slack 알림 전송
   */
  sendToSlack(message: string): Promise<void>;

  /**
   * ID 토큰 생성
   */
  getIdToken(id: string): string;

  /**
   * 모든 채널 목록 조회
   */
  getAllChannels(): Promise<string[]>;

  /**
   * 특정 채널 정보 조회
   */
  getChannel(channelName: string): Promise<ChannelInfo>;

  /**
   * 채널 존재 여부 확인
   */
  existChannel(channelName: string): Promise<boolean>;

  /**
   * 길드 채널 허용 사용자 목록 조회
   */
  getAllowUserList(channelName: string): Promise<string[]>;

  /**
   * 공용 채널 생성 (월드 / 지역 / 국가)
   * public & persistent & !group channel
   */
  createPublicChannel(name: string, alias: string): Promise<CreateChannelResult>;

  /**
   * 길드 채널 생성
   * private & persistent & !group channel
   */
  createGuildChannel(
    channelName: string,
    userId: string,
    bShouldAllowChannel: boolean
  ): Promise<CreateChannelResult>;

  /**
   * 길드 채널 입장 허용
   */
  allowGuildChannel(channelName: string, userId: string): Promise<any>;

  /**
   * 길드 채널 허용 사용자 삭제
   */
  disallowGuildChannel(channelName: string, userId: string): Promise<any>;

  /**
   * 채널 삭제
   */
  deleteChannel(channelName: string): Promise<any>;

  /**
   * 사용자 생성 (존재하지 않는 경우)
   */
  createUserIfNotExists(userId: string, nickName: string): Promise<void>;

  /**
   * 사용자 정보 조회
   */
  getUser(userId: string): Promise<VolanteUser>;

  /**
   * 사용자 세션 정보 조회
   */
  getSessions(userId: string): Promise<VolanteUserSession>;

  /**
   * 사용자 정보 수정
   */
  updateUser(userId: string, nickName: string, extraData: JsonLike): Promise<void>;

  /**
   * 채널 입장
   */
  channelJoin(channelName: string, userId: string): Promise<void>;

  /**
   * 채널 퇴장
   */
  channelLeave(channelName: string, userId: string): Promise<void>;

  /**
   * 사용자가 차단한 사용자 수 조회
   */
  getUserMuteUserCount(userId: string): Promise<number>;

  /**
   * 차단한 사용자 목록 조회
   */
  getMuteUserIds(userId: string): Promise<number[]>;

  /**
   * 사용자 차단
   */
  muteUser(userId: string, targetUserId: string): Promise<unknown>;

  /**
   * 사용자 차단 해제
   */
  unmuteUser(userId: string, targetUserId: string): Promise<unknown>;

  /**
   * Volante 사용자 정보 업데이트
   */
  updateVolanteUser(user: User): Promise<void>;
}
