"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SdoBillingUtil = void 0;
const cms_1 = __importDefault(require("../cms"));
const merror_1 = require("../motiflib/merror");
const merrorCode_1 = require("../motiflib/merrorCode");
const userCashShop_1 = require("./userCashShop");
const billingReceiveInvenPurchases_1 = require("./packetHandler/common/billingReceiveInvenPurchases");
const mutil_1 = require("../motiflib/mutil");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const rewardDesc_1 = require("../cms/rewardDesc");
class SdoBillingUtil {
    static async receiveProducts(user, cashShopCmsId) {
        const cashShopCms = cms_1.default.CashShop[cashShopCmsId];
        if (!cashShopCms) {
            throw new merror_1.MError('invalid-cash-shop-cms', merrorCode_1.MErrorCode.ADMIN_INVALID_CASH_SHOP_CMS_ID, {
                cashShopCmsId,
            });
        }
        const result = { sync: {} };
        const mailIds = [];
        const ensuredGiveItemsList = SdoBillingUtil.buildEnsuredGiveItemsListByCashShopCms(cashShopCms);
        await (0, billingReceiveInvenPurchases_1.ReceiveProducts)(ensuredGiveItemsList, result, mailIds, user, (0, mutil_1.curTimeUtc)());
        if (mailIds.length > 0) {
            result.mailIds = mailIds;
        }
        const cashPair = await mhttp_1.default.platformBillingApi
            .queryCashPair(user.userId, user.storeCode, user.countryCreated);
        user.userPoints.onChargeByPurchaseProduct([
            {
                coinCd: 'red_gem',
                paymentType: 'PAID',
                balance: cashPair.paidRedGemBalance,
            },
            {
                coinCd: 'red_gem',
                paymentType: 'FREE',
                balance: cashPair.freeRedGemBalance,
            }
        ], null);
        return result;
    }
    static buildEnsuredGiveItemsListByCashShopCms(cashShopCms) {
        const ensuredGiveItemsList = [];
        if (cashShopCms.productRewardFixedId) {
            ensuredGiveItemsList.push(SdoBillingUtil.buildEnsuredGiveItemsByRewardFixedId(cashShopCms.productRewardFixedId));
        }
        if (cashShopCms.eventPageId) {
            const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('EventPage', cashShopCms.eventPageId, 1);
            if (ensuredGiveItems) {
                ensuredGiveItemsList.push(ensuredGiveItems);
            }
        }
        if (cashShopCms.dailySubscriptionId) {
            const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('DailySubscription', cashShopCms.dailySubscriptionId, 1);
            if (ensuredGiveItems) {
                ensuredGiveItemsList.push(ensuredGiveItems);
            }
        }
        if (cashShopCms.illustSkinId) {
            const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('IllustSkin', cashShopCms.illustSkinId, 1);
            if (ensuredGiveItems) {
                ensuredGiveItemsList.push(ensuredGiveItems);
            }
        }
        if (cashShopCms.userTitleId) {
            const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('UserTitle', cashShopCms.userTitleId, 1);
            if (ensuredGiveItems) {
                ensuredGiveItemsList.push(ensuredGiveItems);
            }
        }
        if (cashShopCms.productWorldBuffId && cashShopCms.productWorldBuffId.length > 0) {
            const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('WorldBuff', cashShopCms.id, 1);
            if (ensuredGiveItems) {
                ensuredGiveItemsList.push(ensuredGiveItems);
            }
        }
        return ensuredGiveItemsList;
    }
    static buildEnsuredGiveItems(productItemType, itemCd, amount) {
        var _a;
        const ensureGiveItems = [];
        const giveItem = {
            productItemType,
            itemCd: itemCd.toString(),
            coinChargeTypeCd: undefined,
            coinManageBalanceYn: 'N',
            amount,
        };
        const ret = userCashShop_1.BillingUtil.buildEnsuredGiveItem(giveItem);
        if (ret.bOk !== true) {
            mlog_1.default.warn(`failed to ensure give item (${userCashShop_1.BillingUtil.giveItemToString(giveItem)}). reason: ${(_a = ret.err) === null || _a === void 0 ? void 0 : _a.reason}.`);
            return undefined;
        }
        ensureGiveItems.push(ret.value);
        return ensureGiveItems;
    }
    static buildEnsuredGiveItemsByRewardFixedId(rewardFixedId) {
        const rewardFixedCms = cms_1.default.RewardFixed[rewardFixedId];
        if (!rewardFixedCms) {
            return undefined;
        }
        const ensuredGiveItems = [];
        for (const elem of rewardFixedCms.rewardFixed) {
            const productItemType = SdoBillingUtil.rewardTypeToProductItemType(elem.Type);
            const fixedRewardEnsuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems(productItemType, elem.Id, elem.Quantity);
            if (fixedRewardEnsuredGiveItems) {
                ensuredGiveItems.push(...fixedRewardEnsuredGiveItems);
            }
        }
        return ensuredGiveItems;
    }
    // REWARD_TYPE을 빌링 어드민 툴을 통해 등록되어 있는 타입으로 변환
    // userCashShop.ts의 buildEnsuredGiveItem의 함수 안에 타입 있음
    static rewardTypeToProductItemType(rewardType) {
        switch (rewardType) {
            case rewardDesc_1.REWARD_TYPE.ITEM:
                return 'Item';
            case rewardDesc_1.REWARD_TYPE.SHIP:
                return 'Ship';
            case rewardDesc_1.REWARD_TYPE.MATE_EQUIP:
                return 'CEquip';
            case rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM:
                return 'ShipSlot';
            case rewardDesc_1.REWARD_TYPE.MATE:
                return 'Mate';
            case rewardDesc_1.REWARD_TYPE.USER_TITLE:
                return 'UserTitle';
            case rewardDesc_1.REWARD_TYPE.POINT:
                return 'Point';
            case rewardDesc_1.REWARD_TYPE.PET:
                return 'Pet';
            default:
                return 'DEFAULT_TYPE';
        }
    }
}
exports.SdoBillingUtil = SdoBillingUtil;
//# sourceMappingURL=sdoBillingUtil.js.map