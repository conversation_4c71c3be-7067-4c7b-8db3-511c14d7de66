{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/motiflib/mhttp/index.ts"], "names": [], "mappings": ";;;;;AAAA,mDAAgD;AAChD,uDAAoD;AACpD,mDAAgD;AAChD,qDAAuD;AACvD,qDAAuD;AACvD,mDAAqD;AACrD,uDAAoD;AACpD,qDAAkD;AAClD,iDAA8C;AAC9C,mDAAgD;AAChD,6DAA0D;AAC1D,qDAAwC;AACxC,mEAAgE;AAChE,2EAAwE;AACxE,mEAAgE;AAGhE,iDAA8C;AAC9C,+DAA4D;AAC5D,6CAA8C;AAC9C,mDAA2B;AAC3B,2EAAwE;AAExE,qEAAkE;AAClE,yDAAsD;AAItD,6BAA6B;AAC7B,MAAM,WAAW;IAmCf;QAfA,iCAAiC;QAC1B,cAAS,GAYZ,EAAE,CAAC;QAGL,IAAI,CAAC,OAAO,GAAG,IAAI,iCAAe,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACvF,IAAI,CAAC,KAAK,GAAG,IAAI,6BAAa,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,oCAAmB,EAAE,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,IAAI,kCAAkB,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,oCAAmB,EAAE,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,IAAI,6BAAa,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,iCAAe,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,+BAAc,EAAE,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,6CAAqB,EAAE,CAAC;QAC1C,qDAAqD;QACrD,mCAAmC;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,2BAAY,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,6CAAqB,EAAE,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,IAAI,qDAAyB,EAAE,CAAC;IAC/C,CAAC;IAEG,kFAAkF;IAClF,kEAAkE;IAEtE,IAAI;QACF,0DAA0D;QAC1D,IAAI,eAAK,CAAC,QAAQ,KAAK,eAAQ,CAAC,IAAI,EAAE;YACpC,MAAM,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;YACpD,kBAAkB,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC;YAEtC,MAAM,yBAAyB,GAAG,IAAI,qDAAyB,EAAE,CAAC;YAClE,yBAAyB,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzF,yBAAyB,CAAC,eAAe,CAAC,eAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzE,IAAI,CAAC,kBAAkB,GAAG,yBAAyB,CAAC;YAEpD,MAAM,sBAAsB,GAAG,IAAI,+CAAsB,EAAE,CAAC;YAC5D,sBAAsB,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC7E;aAAM,IAAI,eAAK,CAAC,QAAQ,KAAK,eAAQ,CAAC,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC9D,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;YAEhC,kCAAkC;YAClC,MAAM,mBAAmB,GAAG,IAAI,yCAAmB,EAAE,CAAC;YACtD,IAAI,eAAK,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClB,oEAAoE;aACrE;YACD,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;YAE9C,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,eAAe,GAAG,gBAAgB,CAAC;YAExC,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EACpB,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EACxB,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACrD;aAAM;YACL,cAAI,CAAC,KAAK,CAAC,eAAe,eAAK,CAAC,QAAQ,mBAAmB,CAAC,CAAC;SAC9D;QAED,IAAI,eAAK,CAAC,IAAI,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACjE;QACD,IAAI,eAAK,CAAC,IAAI,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACjE;QACD,IAAI,eAAK,CAAC,IAAI,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACvE;QACD,IAAI,eAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACpE;QAED,wBAAwB;QACxB,+DAA+D;QAC/D,IAAI;QACJ,IAAI,eAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACpE;QACD,+BAA+B;QAC/B,oFAAoF;QACpF,oEAAoE;QACpE,IAAI;QACJ,0BAA0B;QAC1B,qEAAqE;QACrE,+CAA+C;QAC/C,IAAI;QACJ,IAAI,eAAK,CAAC,IAAI,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACjE;QACD,IAAI,eAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACxD;QAED,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,EAAE;YACpC,IAAI,WAAW,GAA8B,IAAI,CAAC;YAClD,IAAI,kBAAkB,GAAqC,IAAI,CAAC;YAChE,IAAI,eAAe,GAAkC,IAAI,CAAC;YAE1D,IAAI,KAAoB,CAAC;YACzB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpB,KAAK,GAAG,IAAI,6BAAa,EAAE,CAAC;gBAC5B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC3D,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACtC;YACD,IAAI,UAAqC,CAAC;YAC1C,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE;gBACzB,UAAU,GAAG,IAAI,qDAAyB,EAAE,CAAC;gBAC7C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAC1E,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aAC3D;YACD,IAAI,MAA6B,CAAC;YAClC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACrB,MAAM,GAAG,IAAI,6CAAqB,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC9D,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aACnD;YAED,IAAI,KAAoB,CAAC;YACzB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpB,KAAK,GAAG,IAAI,6BAAa,EAAE,CAAC;gBAC5B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aAC5D;YAED,IAAI,OAAwB,CAAC;YAC7B,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtB,OAAO,GAAG,IAAI,iCAAe,EAAE,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAClE;YAED,IAAI,MAAsB,CAAC;YAC3B,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACrB,MAAM,GAAG,IAAI,+BAAc,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC/D;YAED,MAAM;YACN,+BAA+B;YAC/B,WAAW;YACX,gBAAgB;YAChB,WAAW;YACX,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,KAAK;QACP,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;AAEtC,kBAAe,WAAW,CAAC"}