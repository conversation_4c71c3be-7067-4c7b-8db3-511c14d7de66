// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mlog from '../../../motiflib/mlog';
import mhttp from '../../../motiflib/mhttp';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { <PERSON>lient<PERSON><PERSON>etHandler } from '../index';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------

interface RequestBody {
  productId: string;
}

interface ResponseBody {
  billingApiResp: unknown;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingQueryProductGiveItemDetail implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;
    const { productId } = reqBody;

    return Promise.resolve()
      .then(() => {
        return mhttp.platformBillingApi.queryProductGiveItemDetail(user.storeCode, productId);
      })
      .then((billingApiResp) => {
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          billingApiResp,
        });
      });
  }
}
