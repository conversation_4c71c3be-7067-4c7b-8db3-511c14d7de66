"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.opSetLastTownCmsId = exports.opApplyGameStateChange = exports.opSetLastGameState = exports.opSetGameState = exports.opSetbGameOver = exports.opResetGameOverLossesPvpLoss = exports.opResetGameOverLosses = exports.opResetTownState = exports.opAddTotalSailedDays = exports.opSetSailing = exports.opSetCashShopFixedTermProduct = exports.opDeleteCashShopFixedTermProduct = exports.opSetCashShopRestrictedProduct = exports.opDeleteCashShopRestrictedProduct = exports.opDecreaseMateLoyalty = exports.opRecordNationEventOccur = exports.opModifyReputation = exports.opSetReputation = exports.opResetPalaceRoyalOrder = exports.opSetNation = exports.opSetInsuranceCmsId = exports.opSetBattleRewards = exports.opSetPassEventMissions = exports.opSetEventMissionRewarded = exports.opSetAchievementPointRewarded = exports.opSetAchievementRewarded = exports.opSetTaskCategoryRewarded = exports.opSetTaskRewarded = exports.opSetCompleteTask = exports.opAddDirectmail = exports.opSetDirectmailExpireTimeUtc = exports.opSetDirectMailState = exports.opUpgradeShipBlueprint = exports.opAddSailor = exports.opAddMate = exports.opAddShip = exports.opAddFame = exports.opAddMateEquip = exports.opUnloadSmuggleGoods = exports.opLoadSmuggleGoods = exports.opUnloadTradeGoods = exports.opLoadTradeGoods = exports.opUnloadDepartSupply = exports.opLoadDepartSupply = exports.opAddMileage = exports.opAddPoint = exports.opAddItem = exports.opAddDiscoveryExp = exports.opAddAdventureExp = exports.opAddRewardExp = void 0;
exports.opApplyExploreTicketChange = exports.opAddPassEventExp = exports.opSetPassEventLastDailyResetTimeUtc = exports.opAddArenaTicket = exports.opAddDiscovery = exports.opLandExploreAchievement = exports.opSetGuildShopRestrictedProduct = exports.opDeleteGuildShopRestrictedProduct = exports.opSetContributionShopRestrictedProduct = exports.opDeleteContributionShopRestrictedProduct = exports.opAddPalaceTaxFreePermit = exports.opAddShieldPurchaseCount = exports.opAddShieldNonPurchaseCount = exports.opConsumeShield = exports.opSetLineMailState = exports.opAddQuestItem = exports.opSetShipFormationIndex = exports.opSetShipSailor = exports.opAddUserShipBuildingExp = exports.opDismantleShip = exports.opSetBattleEndResult = exports.opQuestDropByReturn = exports.opDropQuest = exports.opSetChallenge = exports.opBattleLogEnd = exports.opApplyItemChange = exports.opSetQuestFlags = exports.opApplyInsuranceUnpaidChange = exports.opApplyShipCargoChange = exports.opSetVillageFriendshipRewarded = exports.opSetVillageLastDepartureTimeUtc = exports.opSetShipLife = exports.opSetShipDurabilityAndSailor = exports.opSetMultiPvpLoss = exports.opSetGameOverLosses = exports.opSetFame = exports.opApplyQuickModeChange = exports.opApplyFreeTakebackChange = exports.opSetUserExpLevel = exports.opApplyPointChange = exports.opApplyEnergyChange = exports.opApplyMateExpChange = exports.opAddShipSlotItem = exports.opAddEnergy = exports.opSetAttendance = exports.opAddMateInjuryState = exports.opAddMateIntimacyOrLoyalty = exports.opAddSoundPack = exports.opSetCashShopGachaBoxGuaranteeAccum = exports.opSetArrivalTownCmsId = void 0;
exports.opSetReentry = exports.opSetClash = exports.opSetInfiniteLighthouseClearInfo = exports.opResetLastPaidSmuggleEnterTownCmsId = exports.opComposeShip = exports.opDeleteShip = exports.opAddPet = exports.opApplyShieldChange = exports.opApplySweepTicketChange = exports.opAddBuySweepTicket = exports.opAddFreeSweepTicket = exports.opAddUserTitle = exports.opSetFishCatchReward = exports.opAddShipCamouflage = exports.opApplyKarmaChange = exports.opSetDiscoveryReward = exports.opSetEventRankingMissionReward = exports.opSetHotSpotCoolTimeUtc = exports.opDeleteGuildSynthesisSlot = exports.opSetDailySubscriptionLastReceiveTimeUtc = exports.opBuyDailySubscription = exports.opRemoveFleetDispatch = exports.opSetFleetDispatchReward = exports.opSetFleetDispatchEndView = exports.opSetFleetDispatchLifeRemain = exports.opAddShipBlueprintSailMasteryExp = exports.opSetEventGame = exports.opApplyTradeArea = exports.opApplyExploreQuickModeChange = void 0;
const lodash_1 = __importDefault(require("lodash"));
const typedi_1 = require("typedi");
const assert_1 = __importDefault(require("assert"));
const cms_1 = __importDefault(require("../../cms"));
const cmsEx = __importStar(require("../../cms/ex"));
const mutil = __importStar(require("../../motiflib/mutil"));
const user_1 = require("../user");
const userChangeTask_1 = require("./userChangeTask");
const mate_1 = __importStar(require("../mate"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const merror_1 = require("../../motiflib/merror");
const userMates_1 = __importDefault(require("../userMates"));
const ship_1 = require("../ship");
const fleet_1 = __importDefault(require("../fleet"));
const lobby_1 = require("../../motiflib/model/lobby");
const server_1 = require("../server");
const itemDesc_1 = require("../../cms/itemDesc");
const questUtil_1 = require("../questUtil");
const shipSlotDesc_1 = require("../../cms/shipSlotDesc");
const pointDesc_1 = require("../../cms/pointDesc");
const statHelper_1 = require("../statHelper");
const eventPageDesc_1 = require("../../cms/eventPageDesc");
const userPassEvent_1 = __importDefault(require("../userPassEvent"));
const formula_1 = require("../../formula");
const rewardDesc_1 = require("../../cms/rewardDesc");
const mailBuilder_1 = require("../../motiflib/mailBuilder");
const shipBuildUtil_1 = require("../../motiflib/model/lobby/shipBuildUtil");
// -------------------------------------------------------------------------------------------------
// * operator 작성 시 CHANGE_TASK_RESULT 확인 필수! ( FULL_MAX 등 주의 사항 )
// -------------------------------------------------------------------------------------------------
// -------------------------------------------------------------------------------------------------
function opAddRewardExp(user, tryData, changes, jobType, amount, fleetIndex, bOnlyMateExp = false) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.energy) {
        tryData.energy = user.userEnergy.clone();
    }
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    if (!changes.syncAdd.points) {
        changes.syncAdd.points = {};
    }
    if (!changes.mateExp) {
        changes.mateExp = {};
    }
    if (!changes.mateExp[jobType]) {
        changes.mateExp[jobType] = [];
    }
    if (!changes.actualGainExp) {
        changes.actualGainExp = {};
    }
    let bChanged = false;
    // 국가 순위 보너스
    const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
    const nationRank = nationManager.getPowerRank(user.nationCmsId);
    if (nationRank) {
        const nationRankingEffectCms = cms_1.default.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.EXP];
        const nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];
        amount = Math.floor((amount * nationRankBonus) / 1000);
    }
    // reward 를 통해 경험치를 얻는 경우 getShipSlotExpJobPanelty 적용하지 않는다.
    // mate
    const fleet = user.userFleets.getFleet(fleetIndex);
    const mateCmsIds = fleet.getAllMateCmsIds();
    for (const mateCmsId of mateCmsIds) {
        const mate = tryData.mates.getMate(mateCmsId);
        const oldLevel = mate.getLevel(jobType);
        const newLevelAndExp = mate.calcExpLevel(jobType, amount, tryData.stats, tryData.userLevel, fleetIndex);
        if (newLevelAndExp) {
            mate.setExpLevel(jobType, newLevelAndExp, tryData.stats, null);
            const change = changes.mateExp[jobType].find((elem) => {
                return elem.mateCmsId === mateCmsId;
            });
            if (change) {
                change.exp = newLevelAndExp.exp;
                change.level = newLevelAndExp.level;
            }
            else {
                changes.mateExp[jobType].push({
                    mateCmsId,
                    exp: newLevelAndExp.exp,
                    level: newLevelAndExp.level,
                    oldLevel,
                });
            }
            bChanged = true;
        }
    }
    // user exp, level
    if (!bOnlyMateExp) {
        const expLevelChange = user_1.User.calcExpLevel(amount, mutil.curTimeUtc(), tryData.userExp, tryData.userLevel, tryData.stats, tryData.energy);
        if (expLevelChange) {
            tryData.userExp = expLevelChange.exp;
            tryData.userLevel = expLevelChange.level;
            changes.syncAdd.user.exp = tryData.userExp;
            changes.syncAdd.user.level = tryData.userLevel;
            bChanged = true;
        }
        if (expLevelChange && expLevelChange.energyChange) {
            lodash_1.default.merge(changes.syncAdd, tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add);
            bChanged = true;
        }
    }
    if (!bChanged) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!changes.actualGainExp[jobType]) {
        changes.actualGainExp[jobType] = 0;
    }
    changes.actualGainExp[jobType] += amount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddRewardExp = opAddRewardExp;
// -------------------------------------------------------------------------------------------------
function opAddAdventureExp(user, tryData, changes, amount) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.energy) {
        tryData.energy = user.userEnergy.clone();
    }
    if (!changes.mateExp) {
        changes.mateExp = {};
    }
    if (!changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE]) {
        changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE] = [];
    }
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    if (!changes.syncAdd.points) {
        changes.syncAdd.points = {};
    }
    if (!changes.actualGainExp) {
        changes.actualGainExp = {};
    }
    const clearAdventurerExp = 0; // TODO: 재해 해소에 따른 보너스를 구현합니다.
    const mateExpChanges = [];
    const admiralGainedExp = tryData.mates.buildMateAdventureExpChangesBySailing(amount, clearAdventurerExp, mateExpChanges, tryData.fleets, tryData.stats, tryData.shipBlueprints, user.nationCmsId, cmsEx.FirstFleetIndex, tryData.userLevel);
    for (const elem of mateExpChanges) {
        const tryDataMate = tryData.mates.getMate(elem.mateCmsId);
        const oldLevel = tryDataMate.getLevel(cmsEx.JOB_TYPE.ADVENTURE);
        tryDataMate.setExpLevel(cmsEx.JOB_TYPE.ADVENTURE, elem, tryData.stats, null);
        const change = changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].find((changesElem) => {
            return changesElem.mateCmsId === elem.mateCmsId;
        });
        if (change) {
            change.exp = elem.exp;
            change.level = elem.level;
        }
        else {
            changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].push({
                mateCmsId: elem.mateCmsId,
                exp: elem.exp,
                level: elem.level,
                oldLevel,
            });
        }
    }
    const expLevelChange = user_1.User.calcExpLevel(admiralGainedExp, mutil.curTimeUtc(), tryData.userExp, tryData.userLevel, tryData.stats, tryData.energy);
    if (expLevelChange) {
        tryData.userExp = expLevelChange.exp;
        tryData.userLevel = expLevelChange.level;
        changes.syncAdd.user.exp = tryData.userExp;
        changes.syncAdd.user.level = tryData.userLevel;
    }
    if (expLevelChange && expLevelChange.energyChange) {
        lodash_1.default.merge(changes.syncAdd, tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add);
    }
    if (!changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE]) {
        changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] = 0;
    }
    changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] += amount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddAdventureExp = opAddAdventureExp;
// -------------------------------------------------------------------------------------------------
function opAddDiscoveryExp(user, tryData, changes, amount) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.energy) {
        tryData.energy = user.userEnergy.clone();
    }
    if (!changes.mateExp) {
        changes.mateExp = {};
    }
    if (!changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE]) {
        changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE] = [];
    }
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    if (!changes.syncAdd.points) {
        changes.syncAdd.points = {};
    }
    if (!changes.actualGainExp) {
        changes.actualGainExp = {};
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    const mateExpChanges = [];
    const admiralGainedExp = tryData.mates.buildMateAdventureExpChangesByDiscovery(amount, mateExpChanges, tryData.fleets, tryData.stats, tryData.shipBlueprints, user.nationCmsId, cmsEx.FirstFleetIndex, tryData.userLevel);
    for (const elem of mateExpChanges) {
        const tryDataMate = tryData.mates.getMate(elem.mateCmsId);
        const oldLevel = tryDataMate.getLevel(cmsEx.JOB_TYPE.ADVENTURE);
        tryDataMate.setExpLevel(cmsEx.JOB_TYPE.ADVENTURE, elem, tryData.stats, null);
        const change = changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].find((changesElem) => {
            return changesElem.mateCmsId === elem.mateCmsId;
        });
        if (change) {
            change.exp = elem.exp;
            change.level = elem.level;
        }
        else {
            changes.mateExp[cmsEx.JOB_TYPE.ADVENTURE].push({
                mateCmsId: elem.mateCmsId,
                exp: elem.exp,
                level: elem.level,
                oldLevel,
            });
        }
    }
    const expLevelChange = user_1.User.calcExpLevel(admiralGainedExp, mutil.curTimeUtc(), tryData.userExp, tryData.userLevel, tryData.stats, tryData.energy);
    if (expLevelChange) {
        tryData.userExp = expLevelChange.exp;
        tryData.userLevel = expLevelChange.level;
        changes.syncAdd.user.exp = tryData.userExp;
        changes.syncAdd.user.level = tryData.userLevel;
    }
    if (expLevelChange && expLevelChange.energyChange) {
        lodash_1.default.merge(changes.syncAdd, tryData.energy.applyEnergyChange(expLevelChange.energyChange, null).add);
    }
    if (!changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE]) {
        changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] = 0;
    }
    changes.actualGainExp[cmsEx.JOB_TYPE.ADVENTURE] += amount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddDiscoveryExp = opAddDiscoveryExp;
// -------------------------------------------------------------------------------------------------
// @param bIsBound 획득일 때만 사용 됨.
// @param bIfExceededSendMail 넘칠경우 메일로 보냄 (bAllowAddToLimitIfExceeded가 true면 메일로 안보냄)
// @param isForcedBound 강제로 귀속여부 결정
function opAddItem(user, tryData, changes, itemCmsId, amount, bAllowOverInven, bAllowAddToLimitIfExceeded, extraStr, inBIsAccum, inBIsBound = true, bIfExceededAddMail = false, bIfExceededLimitAddMail = false, isForcedBound) {
    const itemCms = cms_1.default.Item[itemCmsId];
    if (!itemCms || itemCms.type === itemDesc_1.ITEM_TYPE.RANDOM_QUEST) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // try data
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const accums = [];
    let itemChange;
    if (amount > 0) {
        // 지급하는 경우
        const extra = extraStr ? JSON.parse(extraStr) : undefined;
        // 귀속 여부 결정.
        let bIsBound = true;
        if (itemCms.isCashMarket && ((extra && extra.isBound === 0) || !inBIsBound)) {
            bIsBound = false;
        }
        // 귀속 여부를 정하는 규칙이 위에 있지만.. 예외적인 상황(로그인 아이템 변경 changeItems)
        if (isForcedBound !== undefined) {
            bIsBound = isForcedBound;
        }
        let bIsAccum = false;
        if (extra) {
            if (extra.isAccum === 1 && inBIsAccum) {
                bIsAccum = true;
            }
        }
        else if (inBIsAccum) {
            bIsAccum = true;
        }
        const result = tryData.inven.itemInven.calcReceivable(itemCmsId, amount, bIsBound, bAllowOverInven);
        if (result.receivable !== amount) {
            if (bAllowAddToLimitIfExceeded) {
                if (bIfExceededLimitAddMail) {
                    const exceededAmount = amount - result.receivable;
                    const attachment = [
                        {
                            Id: itemCmsId,
                            Type: rewardDesc_1.REWARD_TYPE.ITEM,
                            Quantity: exceededAmount,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                }
                amount = result.receivable;
                if (amount === 0) {
                    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
                }
            }
            else {
                if (bIfExceededAddMail) {
                    const exceededRewards = [
                        {
                            Id: itemCmsId,
                            Type: rewardDesc_1.REWARD_TYPE.ITEM,
                            Quantity: result.excessSpace,
                            Extra: extraStr,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), exceededRewards);
                }
                else {
                    mlog_1.default.warn('[RewardTask] opAddItem. Exceeds limit.', {
                        userId: user.userId,
                        cmsId: itemCmsId,
                        unboundOption: !bIsBound ? { inBIsBound, extraStr } : undefined,
                        toAdd: amount,
                        receivableResult: result,
                    });
                    return result.excessMaxHavable
                        ? userChangeTask_1.CHANGE_TASK_RESULT.ITEM_MAX_HAVABLE
                        : result.excessSpace
                            ? userChangeTask_1.CHANGE_TASK_RESULT.INVEN_FULL
                            : userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
                }
            }
        }
        itemChange = tryData.inven.itemInven.buildItemChange(itemCmsId, amount, bIsAccum, bIsBound);
    }
    else {
        // 차감하는 경우
        if (inBIsBound !== true) {
            mlog_1.default.debug('[RewardTask] opAddItem. Not supported option.', {
                userId: user.userId,
                cmsId: itemCmsId,
                inBIsBound,
            });
        }
        const curCount = tryData.inven.itemInven.getCount(itemCmsId);
        if (curCount + amount < 0) {
            mlog_1.default.warn('[RewardTask] opAddItem. Not enough item.', {
                userId: user.userId,
                cmsId: itemCmsId,
                curCount,
                toRemove: amount,
            });
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_ITEM;
        }
        itemChange = tryData.inven.itemInven.buildItemChange(itemCmsId, amount, inBIsAccum);
    }
    (0, assert_1.default)(itemChange);
    lodash_1.default.merge(changes.syncAdd, tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add);
    if (accums && accums.length > 0) {
        if (!changes.itemGainAccumParams) {
            changes.itemGainAccumParams = [];
        }
        changes.itemGainAccumParams.push(...accums.map((acc) => {
            return { targets: acc.targets, addedValue: acc.addedValue };
        }));
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddItem = opAddItem;
/**
 *
 * @param user
 * @param tryData
 * @param changes
 * @param cmsId
 * @param addedAmount
 * @param bCutOffLast3Digits 끝 3자리 절삭이 필요한지.
 * @param bPermitExchange 포인트가 부족할 경우 환전을 허용할 것인지.
 * @param lgCashParam lg cash 사용에 필요한 인자
 * @param bIsNotPermitAddToHardCapLimitLine 지급 전 포인트가 hard cap 을 넘지 않았으나 지급 후 포인트가 hard cap 을
 * 넘을 경우 지급 후 금액을 hard cap 금액으로 맞추지 않을지 여부
 * @param bIfExceededLimitAddMail 지급 후 포인트가 hard cap을 넘는다면 나머지 포인트는 메일로
 * @returns
 */
function opAddPoint(user, tryData, changes, cmsId, addedAmount, bCutOffLast3Digits, bPermitExchange, lgCashParam, bIsNotPermitAddToHardCapLimitLine, bIfExceededLimitAddMail = false) {
    if (addedAmount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // 환전 허용할 경우 lgCashParam 값이 있어야 된다.
    (0, assert_1.default)(!bPermitExchange || lgCashParam);
    const pointCms = cms_1.default.Point[cmsId];
    if (!pointCms || cmsId === cmsEx.EnergyPointCmsId || cmsId === cmsEx.CashShopMileage) {
        mlog_1.default.error('[RewardTask] opAddPoint. Invalid point.', {
            userId: user.userId,
            cmsId: cmsId,
        });
        throw new merror_1.MError('cannot-reward-point', merror_1.MErrorCode.INVALID_POINT);
    }
    // try data
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    const oldPoint = tryData.points.getPoint(cmsId);
    if (cmsId === cmsEx.DucatPointCmsId && addedAmount > 0 && bCutOffLast3Digits) {
        let digit = 0;
        let temp = addedAmount;
        while (temp >= 1) {
            temp /= 10;
            digit++;
        }
        if (digit > 3) {
            const divide = Math.pow(10, digit - 3);
            addedAmount = Math.floor(addedAmount / divide) * divide;
        }
    }
    if (addedAmount > 0) {
        let newPoint = oldPoint + addedAmount;
        if (oldPoint >= pointCms.hardCap) {
            return userChangeTask_1.CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP;
        }
        else if (newPoint > pointCms.hardCap) {
            if (bIsNotPermitAddToHardCapLimitLine) {
                return userChangeTask_1.CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP;
            }
            else {
                if (bIfExceededLimitAddMail) {
                    const exceededPoint = newPoint - pointCms.hardCap;
                    const attachment = [
                        {
                            Id: cmsId,
                            Type: rewardDesc_1.REWARD_TYPE.POINT,
                            Quantity: exceededPoint,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                    addedAmount -= exceededPoint;
                }
                newPoint = pointCms.hardCap;
            }
        }
        tryData.points.setPointForChangeTask(cmsId, newPoint);
        if ((0, pointDesc_1.isCash)(cmsId)) {
            if (!changes.cashGains) {
                changes.cashGains = [];
            }
            // TODO 중국 처리 코드에서 호출될때 사유가 넘겨져야함.
            (0, assert_1.default)(lgCashParam && lgCashParam.gainReason);
            changes.cashGains.push({
                cmsId: cmsId,
                amount: addedAmount,
                reason: lgCashParam.gainReason,
            });
        }
        else {
            if (!changes.syncAdd.points) {
                changes.syncAdd.points = {};
            }
            changes.syncAdd.points[cmsId] = {
                cmsId: cmsId,
                value: tryData.points.getPoint(cmsId),
            };
        }
        if (!changes.pointsGainAmount) {
            changes.pointsGainAmount = {};
        }
        if (!changes.pointsGainAmount[cmsId]) {
            changes.pointsGainAmount[cmsId] = addedAmount;
        }
        else {
            changes.pointsGainAmount[cmsId] += addedAmount;
        }
    }
    else {
        const pcChanges = tryData.points.buildPointAndCashChangesByPayment([
            {
                cmsId,
                cost: -addedAmount,
            },
        ], bPermitExchange, lgCashParam, false);
        if (!pcChanges) {
            mlog_1.default.warn('[RewardTask] opAddPoint. Not enough point.', {
                userId: user.userId,
                cmsId,
                addedAmount,
                userValue: user.userPoints.getPoint(cmsId),
                trryDataValue: tryData.points.getPoint(cmsId),
            });
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_POINT;
        }
        for (const change of pcChanges.pointChanges) {
            tryData.points.setPointForChangeTask(change.cmsId, change.value);
            lodash_1.default.merge(changes.syncAdd, {
                points: {
                    [change.cmsId]: change,
                },
            });
        }
        if (pcChanges.cashPayments && pcChanges.cashPayments.length > 0) {
            (0, assert_1.default)(lgCashParam && (lgCashParam.itemId || lgCashParam.productId));
            if (!changes.cashPayments) {
                changes.cashPayments = [];
            }
            for (const cashChange of pcChanges.cashPayments) {
                tryData.points.setPointForChangeTask(cashChange.cmsId, tryData.points.getPoint(cashChange.cmsId) - cashChange.amount);
                changes.cashPayments.push({
                    cmsId: cashChange.cmsId,
                    amount: cashChange.amount,
                    lgCashParam: cashChange.lgCashParam,
                    glogPointExchangeData: cashChange.glogPointExchangeData,
                });
            }
        }
        if (!changes.pointsConsumeAmountForGlog) {
            changes.pointsConsumeAmountForGlog = {};
        }
        if (!changes.pointsConsumeAmountForGlog[cmsId]) {
            changes.pointsConsumeAmountForGlog[cmsId] = addedAmount;
        }
        else {
            changes.pointsConsumeAmountForGlog[cmsId] += addedAmount;
        }
    } // end of if (addedAmount < 0)
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddPoint = opAddPoint;
function opAddMileage(user, tryData, changes, addedAmount, curTimeUtc) {
    if (addedAmount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // try data
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    // 차감되는 경우 충분한지 비교
    if (addedAmount < 0) {
        const mileage = tryData.points.getMileage(curTimeUtc);
        if (mileage < -addedAmount) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_POINT;
        }
    }
    const mileageChanges = tryData.points.buildMileageChanges(addedAmount, curTimeUtc);
    if (!changes.mileages) {
        changes.mileages = [];
    }
    for (const mileageChange of mileageChanges) {
        const idx = changes.mileages.findIndex((elem) => elem.month === mileageChange.month);
        if (idx === -1) {
            changes.mileages.push(mileageChange);
        }
        else {
            changes.mileages[idx].value = mileageChange.value;
        }
    }
    tryData.points.applyMileageChanges(mileageChanges, null);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddMileage = opAddMileage;
// -------------------------------------------------------------------------------------------------
function opLoadDepartSupply(user, tryData, changes, cmsId, quantity, bSelectLoadsIfExceededRatio, bIfExceededAddMail = false) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (quantity < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    let remainQuantity = 0;
    if (bSelectLoadsIfExceededRatio === undefined) {
        // 현재 함대에 적재 가능한 양을 받아옴.
        remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
    }
    else {
        // 보급품의 적재 비율을 고려하여 현재 적재 가능한 양을 받아옴.
        remainQuantity = tryData.fleets
            .getFirstFleet()
            .calcRemainQuantityForLoadType(cmsEx.convertCargoCmsIdToLoadPresetType(cmsId), tryData.stats, tryData.fleets);
        if (bSelectLoadsIfExceededRatio === true && quantity > remainQuantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
        }
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    quantity = Math.min(quantity, remainQuantity);
    // load
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    for (const ship of lodash_1.default.values(firstFleetShips)) {
        const curLoad = ship.getCurLoad();
        const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
        const avail = totalCapacity - curLoad;
        if (avail <= 0) {
            continue;
        }
        const loadAmount = Math.min(avail, quantity);
        const change = {
            shipId: ship.getNub().id,
            cmsId: cmsId,
            quantity: ship.getCargoQuantity(cmsId) + loadAmount,
            pointInvested: 0,
        };
        ship.applyCargoChange(change, null, null);
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
            };
        }
        if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
            changes.syncAdd.ships[ship.getNub().id].cargos = {};
        }
        changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = change;
        quantity -= loadAmount;
        if (quantity === 0) {
            break;
        }
    }
    if (quantity > 0 && bIfExceededAddMail) {
        const attachment = [
            {
                Id: cmsId,
                Type: rewardDesc_1.REWARD_TYPE.DEPART_SUPPLY,
                Quantity: quantity,
            },
        ];
        AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opLoadDepartSupply = opLoadDepartSupply;
// -------------------------------------------------------------------------------------------------
function opUnloadDepartSupply(user, tryData, changes, cmsId, quantity) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (quantity < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    let remaining = quantity;
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    for (const ship of lodash_1.default.values(firstFleetShips)) {
        const cargo = ship.getCargo(cmsId);
        if (cargo.quantity >= remaining) {
            cargo.quantity -= remaining;
            remaining = 0;
        }
        else {
            remaining -= cargo.quantity;
            cargo.quantity = 0;
        }
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
                cargos: {},
            };
        }
        if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
            changes.syncAdd.ships[ship.getNub().id].cargos = {};
        }
        changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;
        if (remaining === 0) {
            break;
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opUnloadDepartSupply = opUnloadDepartSupply;
// -------------------------------------------------------------------------------------------------
function opLoadTradeGoods(user, tryData, changes, cmsId, quantity, bSelectLoadsIfExceededRatio, shipId, pointInvested, bIfExceededAddMail = false) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (quantity < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const tradeGoodsCms = cms_1.default.TradeGoods[cmsId];
    let tradeGoodsPrice = tradeGoodsCms.basisDucat;
    if (pointInvested) {
        tradeGoodsPrice = Math.floor(pointInvested / quantity);
    }
    let remainQuantity = 0;
    if (bSelectLoadsIfExceededRatio === undefined) {
        // 현재 함대에 적재 가능한 양을 받아옴.
        remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
    }
    else {
        // 교역품의 적재 비율을 고려하여 현재 적재 가능한 양을 받아옴.
        remainQuantity = tryData.fleets
            .getFirstFleet()
            .calcRemainQuantityForLoadType(cmsEx.convertCargoCmsIdToLoadPresetType(cmsId), tryData.stats, tryData.fleets);
        if (bSelectLoadsIfExceededRatio === true && quantity > remainQuantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
        }
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    quantity = Math.min(quantity, remainQuantity);
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    let shipIds = [];
    if (shipId) {
        shipIds.push(shipId);
    }
    else {
        shipIds = Object.keys(firstFleetShips);
    }
    for (const shipIdStr of shipIds) {
        const shipId = parseInt(shipIdStr, 10);
        const ship = firstFleetShips[shipId];
        const curLoad = ship.getCurLoad();
        const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
        const avail = totalCapacity - curLoad;
        if (avail <= 0) {
            continue;
        }
        const loadAmount = Math.min(avail, quantity);
        const cargo = ship.getCargo(cmsId);
        cargo.quantity += loadAmount;
        cargo.pointInvested += loadAmount * tradeGoodsPrice;
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
                cargos: {},
            };
        }
        if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
            changes.syncAdd.ships[ship.getNub().id].cargos = {};
        }
        changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;
        quantity -= loadAmount;
        if (quantity === 0) {
            break;
        }
    }
    if (quantity > 0 && bIfExceededAddMail) {
        const attachment = [
            {
                Id: cmsId,
                Type: rewardDesc_1.REWARD_TYPE.TRADE_GOODS,
                Quantity: quantity,
                //shipId, //! 일단 주석
                pointInvested,
            },
        ];
        AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opLoadTradeGoods = opLoadTradeGoods;
// -------------------------------------------------------------------------------------------------
function opUnloadTradeGoods(user, tryData, changes, cmsId, quantity, shipId) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (quantity < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    let remaining = quantity;
    let shipIds = [];
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    if (shipId) {
        shipIds.push(shipId);
        if (tryData.fleets.getShip(shipId).getCargoQuantity(cmsId) < quantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
        }
    }
    else {
        shipIds = Object.keys(firstFleetShips);
        if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
        }
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    for (const shipIdStr of shipIds) {
        const shipId = parseInt(shipIdStr, 10);
        const ship = firstFleetShips[shipId];
        const cargo = ship.getCargo(shipId);
        if (cargo.quantity >= remaining) {
            cargo.pointInvested -= Math.floor((cargo.pointInvested / cargo.quantity) * remaining);
            cargo.quantity -= remaining;
            remaining = 0;
        }
        else {
            remaining -= cargo.quantity;
            cargo.quantity = 0;
            cargo.pointInvested = 0;
        }
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
                cargos: {},
            };
        }
        if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
            changes.syncAdd.ships[ship.getNub().id].cargos = {};
        }
        changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;
        if (remaining === 0) {
            break;
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opUnloadTradeGoods = opUnloadTradeGoods;
// -------------------------------------------------------------------------------------------------
function opLoadSmuggleGoods(user, tryData, changes, cmsId, quantity, bSelectLoadsIfExceededRatio, shipId, pointInvested, bIfExceededAddMail = false) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const smuggleGoodsCms = cms_1.default.SmuggleGoods[cmsId];
    let smuggleGoodsPrice = smuggleGoodsCms.basisDucat;
    if (pointInvested) {
        smuggleGoodsPrice = Math.floor(pointInvested / quantity);
    }
    let remainQuantity = 0;
    if (bSelectLoadsIfExceededRatio === undefined) {
        remainQuantity = tryData.fleets.getFirstFleet().calcRemainQuantity(tryData.stats);
    }
    else {
        remainQuantity = tryData.fleets
            .getFirstFleet()
            .calcRemainQuantityForLoadType(cmsEx.convertCargoCmsIdToLoadPresetType(cmsId), tryData.stats, tryData.fleets);
        if (bSelectLoadsIfExceededRatio && quantity > remainQuantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.EXCEEDS_LOAD_RATIO;
        }
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    quantity = Math.min(quantity, remainQuantity);
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    let shipIds = [];
    if (shipId) {
        shipIds.push(shipId);
    }
    else {
        shipIds = Object.keys(firstFleetShips);
    }
    for (const shipIdStr of shipIds) {
        const shipId = parseInt(shipIdStr, 10);
        const ship = firstFleetShips[shipId];
        const curLoad = ship.getCurLoad();
        const totalCapacity = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_HOLD);
        const avail = totalCapacity - curLoad;
        if (avail <= 0) {
            continue;
        }
        const loadAmount = Math.min(avail, quantity);
        const cargo = ship.getCargo(cmsId);
        cargo.quantity += loadAmount;
        cargo.pointInvested += loadAmount * smuggleGoodsPrice;
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
                cargos: {},
            };
        }
        if (!changes.syncAdd.ships[ship.getNub().id].cargos) {
            changes.syncAdd.ships[ship.getNub().id].cargos = {};
        }
        changes.syncAdd.ships[ship.getNub().id].cargos[cmsId] = cargo;
        quantity -= loadAmount;
        if (quantity === 0) {
            break;
        }
    }
    if (quantity > 0 && bIfExceededAddMail) {
        const attachment = [
            {
                Id: cmsId,
                Type: rewardDesc_1.REWARD_TYPE.SMUGGLE_GOODS,
                Quantity: quantity,
                pointInvested,
            },
        ];
        AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opLoadSmuggleGoods = opLoadSmuggleGoods;
// -------------------------------------------------------------------------------------------------
function opUnloadSmuggleGoods(user, tryData, changes, cmsId, quantity, shipId) {
    if (quantity === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (quantity < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    let remaining = quantity;
    let shipIds = [];
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    if (shipId) {
        shipIds.push(shipId);
        if (tryData.fleets.getShip(shipId).getCargoQuantity(cmsId) < quantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
        }
    }
    else {
        shipIds = Object.keys(firstFleetShips);
        if (tryData.fleets.getFirstFleet().getCargoQuantity(cmsId) < quantity) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_CARGO;
        }
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    for (const shipIdStr of shipIds) {
        const shipId = parseInt(shipIdStr, 10);
        const ship = firstFleetShips[shipId];
        const cargo = ship.getCargo(shipId);
        const nub = ship.getNub();
        if (cargo.quantity >= remaining) {
            cargo.pointInvested -= Math.floor((cargo.pointInvested / cargo.quantity) * remaining);
            cargo.quantity -= remaining;
            remaining = 0;
        }
        else {
            remaining -= cargo.quantity;
            cargo.quantity = 0;
            cargo.pointInvested = 0;
        }
        if (!changes.syncAdd.ships[nub.id]) {
            changes.syncAdd.ships[nub.id] = {
                id: nub.id,
                cargos: {},
            };
        }
        if (!changes.syncAdd.ships[nub.id].cargos) {
            changes.syncAdd.ships[nub.id].cargos = {};
        }
        changes.syncAdd.ships[nub.id].cargos[cmsId] = cargo;
        if (remaining === 0) {
            break;
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opUnloadSmuggleGoods = opUnloadSmuggleGoods;
// -------------------------------------------------------------------------------------------------
function opAddMateEquip(user, tryData, changes, cmsId, count, bAllowOverInven, bAllowAddToLimitIfExceeded, extraStr, bIsBound = true, bIfExceededAddMail = false, bIfExceededLimitAddMail = false) {
    if (count === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (count < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const equipCms = cms_1.default.CEquip[cmsId];
    if (!equipCms) {
        throw new merror_1.MError('cannot-reward-mate-equip', merror_1.MErrorCode.INVALID_MATE_EQUIP);
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const extra = extraStr ? JSON.parse(extraStr) : undefined;
    // Check 'havable'.
    const maxHavable = equipCms.havableCount;
    if (maxHavable !== undefined) {
        const curCount = tryData.mates.getMateEquipmentCountOfCmsId(cmsId);
        const addable = Math.max(maxHavable - curCount, 0);
        if (count > addable) {
            if (bAllowAddToLimitIfExceeded) {
                if (bIfExceededLimitAddMail) {
                    const exceededCount = count - addable;
                    const attachment = [
                        {
                            Id: cmsId,
                            Type: rewardDesc_1.REWARD_TYPE.MATE_EQUIP,
                            Quantity: exceededCount,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                }
                count = addable;
                if (count === 0) {
                    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
                }
            }
            else {
                mlog_1.default.warn('[REWARD] Max mate equip havable.', {
                    userId: user.userId,
                    cEquipCmsId: cmsId,
                    maxHavable,
                    curCount,
                    toAdd: count,
                });
                return userChangeTask_1.CHANGE_TASK_RESULT.MATE_EQUIP_MAX_HAVABLE;
            }
        }
    }
    // Check equip inven space.
    if (!bAllowOverInven) {
        // addable은 count보다 작거나 같을 수 밖에 없음
        const addable = tryData.mates.calcMateEquipmentAddableForSpace(equipCms, count, tryData.inven, 0);
        if (addable !== count) {
            if (bAllowAddToLimitIfExceeded) {
                count = addable;
                if (count === 0) {
                    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
                }
            }
            else {
                if (bIfExceededAddMail) {
                    const exceededCount = count - addable;
                    const attachment = [
                        {
                            Id: cmsId,
                            Type: rewardDesc_1.REWARD_TYPE.MATE_EQUIP,
                            Quantity: exceededCount,
                            Extra: extraStr,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                    count = addable;
                }
                else {
                    mlog_1.default.warn('[REWARD] Not enough mate equip space.', {
                        userId: user.userId,
                        count,
                        addable,
                    });
                    return userChangeTask_1.CHANGE_TASK_RESULT.MATE_EQUIP_FULL;
                }
            }
        }
    }
    if (!changes.syncAdd.mateEquipments) {
        changes.syncAdd.mateEquipments = {};
    }
    const curTimeUtc = mutil.curTimeUtc();
    // 귀속 여부 결정.
    let isBound = 1;
    if (equipCms.isCashMarket &&
        ((extra && extra.isBound === 0) || !bIsBound) &&
        !equipCms.expireType &&
        !equipCms.exclusiveMateId) {
        isBound = 0;
    }
    // save to try data and changes
    for (let i = 0; i < count; i++) {
        const newItem = tryData.mates.buildMateEquipmentNub(cmsId, 0, isBound, 0, curTimeUtc, extra === null || extra === void 0 ? void 0 : extra.expireTimeUtc, extra === null || extra === void 0 ? void 0 : extra.enchantLv);
        tryData.mates.addMateEquipment(newItem, null);
        changes.syncAdd.mateEquipments[newItem.id] = newItem;
        changes.lastMateEquipmentId = newItem.id;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddMateEquip = opAddMateEquip;
// -------------------------------------------------------------------------------------------------
function opAddFame(user, tryData, changes, jobType, amount) {
    if (!Number.isInteger(amount)) {
        mlog_1.default.error('[RewardTask] opAddFame. amount integer expected.', {
            userId: user.userId,
            amount,
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const leader = userMates_1.default.getLeaderMate(tryData.fleets, tryData.mates);
    if (!leader) {
        mlog_1.default.error('[RewardTask] Cannot add fame without an admiral.', {
            userId: user.userId,
        });
        throw new merror_1.MError('no-adirmal', merror_1.MErrorCode.NEED_ADMIRAL);
    }
    // Check job type.
    if (jobType < cmsEx.JOB_TYPE.ADVENTURE || jobType > cmsEx.JOB_TYPE.BATTLE) {
        mlog_1.default.error('[RewardTask] Invalid fame job type.', {
            userId: user.userId,
            jobType: jobType,
        });
        throw new merror_1.MError('invalid-job-type', merror_1.MErrorCode.INVALID_JOB_TYPE);
    }
    const gainFame = mate_1.default.calcGainFame(leader.getStat(tryData.stats), jobType, amount, user.nationCmsId, tryData.stats.getFleetStat(cmsEx.FirstFleetIndex));
    if (gainFame > 0) {
        if (!changes.totalGainFames) {
            changes.totalGainFames = {};
        }
        if (changes.totalGainFames[jobType] === undefined) {
            changes.totalGainFames[jobType] = 0;
        }
        changes.totalGainFames[jobType] += gainFame;
    }
    const newFame = leader.calcFame(jobType, gainFame);
    if (newFame === undefined || newFame === leader.getFame(jobType)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // Update the try data.
    leader.setFame(jobType, newFame, null);
    // Save the change.
    if (!changes.syncAdd.mates) {
        changes.syncAdd.mates = {};
    }
    if (!changes.syncAdd.mates[leader.getNub().cmsId]) {
        changes.syncAdd.mates[leader.getNub().cmsId] = {
            cmsId: leader.getNub().cmsId,
        };
    }
    switch (jobType) {
        case cmsEx.JOB_TYPE.ADVENTURE:
            changes.syncAdd.mates[leader.getNub().cmsId].adventureFame = leader.getFame(jobType);
            break;
        case cmsEx.JOB_TYPE.TRADE:
            changes.syncAdd.mates[leader.getNub().cmsId].tradeFame = leader.getFame(jobType);
            break;
        case cmsEx.JOB_TYPE.BATTLE:
            changes.syncAdd.mates[leader.getNub().cmsId].battleFame = leader.getFame(jobType);
            break;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddFame = opAddFame;
// -------------------------------------------------------------------------------------------------
/**
 * @param user
 * @param tryData
 * @param changes
 * @param cmsId
 * @param bCaptured
 * @param name
 * @param bAllowExceedDock
 * @param fixedRandomStat 값이 존재할 경우 랜덤스탯이 해당 값으로 고정
 * @param extraStr
 * @param bIsBound
 * @param pickedRandomRewardElem null 이 아닐 경우 ship id 를 pickedRandomRewardElem.id 에 넣어주면 된다.
 * @returns
 */
function opAddShip(user, tryData, changes, cmsId, bCaptured, name, bAllowExceedDock, fixedRandomStat, extraStr, bIsBound = true, pickedRandomRewardElem, bIfExceededAddMail = false) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.userLevel) {
        tryData.userLevel = user.level;
    }
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    const shipCms = cms_1.default.Ship[cmsId];
    if (!shipCms) {
        throw new merror_1.MError('cannot-reward-ship', merror_1.MErrorCode.INVALID_SHIP);
    }
    const extra = extraStr ? JSON.parse(extraStr) : undefined;
    // Make a new ship nub and save it as a change.
    let rndStats;
    if (extra && extra.rndStats) {
        rndStats = extra.rndStats;
    }
    else {
        rndStats = (0, ship_1.makeShipRndStats)(shipCms.shipBlueprintId, fixedRandomStat);
    }
    let defaultDurability = cmsEx.shipDefaultDurability(shipCms.shipBlueprintId, rndStats);
    let defaultLife = cmsEx.shipDefaultLife(shipCms.shipBlueprintId);
    if (bCaptured) {
        // 나포 선박은 내구도 비율을 조정한다
        defaultDurability = Math.floor((defaultDurability * cms_1.default.Const.CapturedShipDurabilityRate.value) / 1000);
        defaultLife = Math.floor((defaultLife * cms_1.default.Const.CapturedShipLifeRate.value) / 1000);
    }
    // 귀속 여부 결정.
    let isBound = 1;
    // 나포 선박은 항상 귀속.
    if (!bCaptured && shipCms.isCashMarket && ((extra && extra.isBound === 0) || !bIsBound)) {
        isBound = 0;
    }
    const id = tryData.fleets.getNewShipId();
    const { shipNub: newShipNub, bpExpLevelChange } = shipBuildUtil_1.ShipBuildUtil.buildShipNubAndShipBlueprint(tryData.stats, tryData.shipBlueprints, false, id, cmsId, bCaptured ? lobby_1.SHIP_ASSIGNMENT.CAPTURED : lobby_1.SHIP_ASSIGNMENT.DOCK, cmsEx.NoFleetIndex, 0, (extra === null || extra === void 0 ? void 0 : extra.life) !== undefined ? extra.life : defaultLife, defaultDurability, rndStats, (extra === null || extra === void 0 ? void 0 : extra.enchantedStats) !== undefined ? extra.enchantedStats : [], (extra === null || extra === void 0 ? void 0 : extra.enchantResult) !== undefined ? extra.enchantResult : null, (extra === null || extra === void 0 ? void 0 : extra.enchantCount) !== undefined ? extra.enchantCount : 0, isBound, (extra === null || extra === void 0 ? void 0 : extra.guid) !== undefined ? extra.guid : `${user.userId}:${id}`, name);
    if (bpExpLevelChange) {
        if (!tryData.shipBlueprints.getUserShipBlueprint(bpExpLevelChange.cmsId)) {
            tryData.shipBlueprints.createUserShipBlueprint(tryData.stats, bpExpLevelChange.cmsId, bpExpLevelChange.level, bpExpLevelChange.exp, 1, 0, null, null);
        }
        else {
            tryData.shipBlueprints.setUserShipBlueprintLevel(bpExpLevelChange.cmsId, bpExpLevelChange.level, bpExpLevelChange.exp, tryData.stats, null);
        }
        if (!changes.syncAdd.shipBlueprints) {
            changes.syncAdd.shipBlueprints = {};
        }
        if (!changes.syncAdd.shipBlueprints[bpExpLevelChange.cmsId]) {
            changes.syncAdd.shipBlueprints[bpExpLevelChange.cmsId] = {
                cmsId: bpExpLevelChange.cmsId,
                level: bpExpLevelChange.level,
                exp: bpExpLevelChange.exp,
                slots: {},
            };
        }
    }
    let newCaptainCmsId; // 1함대에 배치될 경우 해당 배의 선장.
    const firstFleetShipsCount = tryData.fleets.getFirstFleet().getShipsCount();
    // 가능할 경우 지급할 배를 자동으로 1함대 배치.
    if (user.userState.isInTown()) {
        if (!bCaptured) {
            if (fleet_1.default.canAddMoreToFleet(lobby_1.SHIP_ASSIGNMENT.FLEET, cmsEx.FirstFleetIndex, firstFleetShipsCount, 1, tryData.userLevel, tryData.inven)) {
                const firstCanditateMateCmsIds = []; // 첫 번째 후보군.
                const secondCanditateMateCmsIds = []; // 두 번째 후보군.
                for (const mateCmsIdStr of Object.keys(tryData.mates.getMates())) {
                    const mateCmsId = parseInt(mateCmsIdStr, 10);
                    const curShipSlot = tryData.fleets.getMateShipSlot(mateCmsId);
                    if (curShipSlot.shipId) {
                        // 선장이 아니고 잠기지 않은 선실에 배치된 항해사의 경우 두 번째 후보군에 넣는다.
                        if (curShipSlot.slotIndex !== cmsEx.ShipSlotIndexCaptainRoom && !curShipSlot.isLocked) {
                            secondCanditateMateCmsIds.push(mateCmsId);
                        }
                    }
                    else {
                        // 배치 되지 않은 항해사를 첫 번째 후보군에 넣는다.
                        firstCanditateMateCmsIds.push(mateCmsId);
                    }
                }
                const sortFunc = (a, b) => {
                    const aMate = tryData.mates.getMate(a);
                    const bMate = tryData.mates.getMate(b);
                    let aMateSumLv = 0;
                    let bMateSumLv = 0;
                    for (let i = cmsEx.JOB_TYPE.NONE + 1; i <= cmsEx.JOB_TYPE.BATTLE; i++) {
                        // 이번 트랜젝션에서 얻을 항해사는 undefined.
                        if (aMate) {
                            aMateSumLv += aMate.getLevel(i);
                        }
                        if (bMate) {
                            bMateSumLv += bMate.getLevel(i);
                        }
                    }
                    if (aMateSumLv !== bMateSumLv) {
                        return bMateSumLv - aMateSumLv;
                    }
                    return a - b;
                };
                // 첫 번째 후보군에 아무도 없을 경우 두 번째 후보군에서 선장을 선택한다.
                if (firstCanditateMateCmsIds.length > 0) {
                    firstCanditateMateCmsIds.sort(sortFunc);
                    newCaptainCmsId = firstCanditateMateCmsIds[0];
                }
                else if (secondCanditateMateCmsIds.length > 0) {
                    secondCanditateMateCmsIds.sort(sortFunc);
                    newCaptainCmsId = secondCanditateMateCmsIds[0];
                }
                // TODO: 차후 2~4함대가 생길 경우 해당 함대에 배치된 항해사 고려 필요함.
            }
        }
    }
    // 선장으로 넣을 수 있는 항해사가 있을 경우 배를 도크가 아닌 1함대에 넣는다.
    if (newCaptainCmsId) {
        newShipNub.fleetIndex = cmsEx.FirstFleetIndex;
        newShipNub.assignment = lobby_1.SHIP_ASSIGNMENT.FLEET;
        newShipNub.formationIndex = firstFleetShipsCount + 1;
        newShipNub.slots[cmsEx.ShipSlotIndexCaptainRoom] = {
            slotIndex: cmsEx.ShipSlotIndexCaptainRoom,
            mateCmsId: newCaptainCmsId,
            isLocked: 0,
            shipSlotItemId: null,
        };
    }
    else if (bCaptured) {
        if (!fleet_1.default.canAddMoreToFleet(lobby_1.SHIP_ASSIGNMENT.CAPTURED, cmsEx.NoFleetIndex, tryData.fleets.getShipsCountByAssignment(lobby_1.SHIP_ASSIGNMENT.CAPTURED), 1, tryData.userLevel, tryData.inven)) {
            if (bIfExceededAddMail) {
                const attachment = [
                    {
                        Id: cmsId,
                        Type: rewardDesc_1.REWARD_TYPE.SHIP,
                        Quantity: 1,
                        Extra: extraStr,
                    },
                ];
                AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
            }
            else {
                return userChangeTask_1.CHANGE_TASK_RESULT.CAPTURED_FULL;
            }
        }
    }
    else {
        if (!fleet_1.default.canAddMoreToFleet(lobby_1.SHIP_ASSIGNMENT.DOCK, cmsEx.NoFleetIndex, tryData.fleets.getShipsCountByAssignment(lobby_1.SHIP_ASSIGNMENT.DOCK), 1, tryData.userLevel, tryData.inven) &&
            !bAllowExceedDock) {
            if (bIfExceededAddMail) {
                const attachment = [
                    {
                        Id: cmsId,
                        Type: rewardDesc_1.REWARD_TYPE.SHIP,
                        Quantity: 1,
                        Extra: extraStr,
                    },
                ];
                AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
            }
            else {
                return userChangeTask_1.CHANGE_TASK_RESULT.DOCK_FULL;
            }
        }
    }
    if (newCaptainCmsId) {
        const oldShipSlot = tryData.fleets.getMateShipSlot(newCaptainCmsId);
        const oldShip = tryData.fleets.getShip(oldShipSlot.shipId);
        if (oldShip) {
            oldShip.setSlotMate({
                slotIndex: oldShipSlot.slotIndex,
                mateCmsId: 0,
                isLocked: 0,
                shipSlotItemId: null,
            }, tryData.shipBlueprints, tryData.mates, tryData.fleets, undefined, // companyStat
            undefined, // userPassives
            undefined, // questMgr
            undefined, // userSailing
            undefined, // userTriggers
            undefined, // userBuffs
            false, // bOnLogin
            undefined);
            if (!changes.syncAdd.ships[oldShipSlot.shipId]) {
                changes.syncAdd.ships[oldShipSlot.shipId] = {
                    id: oldShipSlot.shipId,
                    slots: {},
                };
            }
            if (!changes.syncAdd.ships[oldShipSlot.shipId].slots) {
                changes.syncAdd.ships[oldShipSlot.shipId].slots = {};
            }
            changes.syncAdd.ships[oldShipSlot.shipId].slots[oldShipSlot.slotIndex] = {
                slotIndex: oldShipSlot.slotIndex,
                mateCmsId: 0,
                isLocked: 0,
                shipSlotItemId: null,
            };
        }
    }
    tryData.fleets.addNewShip(newShipNub, tryData.stats, tryData.shipBlueprints, tryData.mates, tryData.fleets, tryData.inven, null, // userBuffs
    null, // userPassives
    null, // userCollection
    null, // questMgr
    null, // userSailing
    null, // userTriggers
    null, // userNation
    null, // userResearch
    null, // glogParam
    undefined);
    changes.syncAdd.ships[newShipNub.id] = newShipNub;
    changes.lastShipId = newShipNub.id;
    if (pickedRandomRewardElem) {
        pickedRandomRewardElem.id = newShipNub.id;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShip = opAddShip;
// -------------------------------------------------------------------------------------------------
function opAddMate(user, tryData, changes, cmsId, 
// 이 밑으론 항해사를 이미 가지고 있을때 계약서 지급 용도
bIsProceedDuplicated = true, bAllowOverInven = false, bAllowAddToLimitIfExceeded = false, extraStr = undefined, inBIsBound = true, bIfExceededAddMail = false, bIfExceededLimitAddMail = false) {
    const mateCms = cms_1.default.Mate[cmsId];
    if (!mateCms) {
        throw new merror_1.MError('cannot-reward-mate', merror_1.MErrorCode.INVALID_MATE);
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.questManager) {
        tryData.questManager = user.questManager.clone();
    }
    if (!tryData.cashShop) {
        tryData.cashShop = user.userCashShop.clone();
    }
    // Check duplicate mates.
    if (tryData.mates.hasMate(cmsId)) {
        if (bIsProceedDuplicated === false) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
        }
        return opAddItem(user, tryData, changes, mateCms.reRecruitRewardItemId, cms_1.default.Const.CashShopDuplicatedMateToPiece.value, bAllowOverInven, bAllowAddToLimitIfExceeded, extraStr, true, inBIsBound, bIfExceededAddMail, bIfExceededLimitAddMail);
    }
    if (!changes.syncAdd.mates) {
        changes.syncAdd.mates = {};
    }
    // Save change.
    const mateNub = mate_1.default.defaultNub(cmsId);
    tryData.mates.addNewMate(mateNub, tryData.stats, null, null, undefined);
    changes.syncAdd.mates[mateNub.cmsId] = mateNub;
    if (tryData.mates.getUnemployedMate(mateCms.id)) {
        tryData.mates.deleteUnemployedMate(mateCms.id);
        lodash_1.default.merge(changes.syncRemove, {
            unemployedMates: {
                [mateCms.id]: true,
            },
        });
    }
    const curTimeUtc = mutil.curTimeUtc();
    if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
        for (const cequipCmsId of mateCms.CEquipId) {
            const newItem = tryData.mates.buildMateEquipmentNub(cequipCmsId, mateCms.id, 1, 0, curTimeUtc);
            tryData.mates.addMateEquipment(newItem, null);
            lodash_1.default.merge(changes.syncAdd, {
                mateEquipments: {
                    [newItem.id]: newItem,
                },
            });
            if (newItem.equippedMateCmsId) {
                tryData.mates.equipMateEquipment(newItem.equippedMateCmsId, newItem.id, 
                // 스탯/패시브를 위한 인자로만 쓰이는 걸로 보장되는 지 확인 필요.
                undefined, // companyStat
                undefined, // userPassives
                undefined, // userFleets
                undefined, // userSailing
                undefined, // userTriggers
                undefined, // userBuffs
                undefined, // sync
                undefined // glog
                );
            }
            changes.lastMateEquipmentId = newItem.id;
        }
    }
    const addedPassives = mate_1.MateUtil.getRelationChronicleLearnablePassives(tryData.mates, tryData.questManager, mateCms.id);
    if (addedPassives && addedPassives.length > 0) {
        if (!changes.addedMatePassives) {
            changes.addedMatePassives = [];
        }
        for (const elem of addedPassives) {
            tryData.mates.getMate(cmsId).addPassive(elem.passiveCmsId, 0, null);
            changes.addedMatePassives.push(elem);
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddMate = opAddMate;
// -------------------------------------------------------------------------------------------------
// 넘치는 항해사는 버려짐.
function opAddSailor(user, tryData, changes, amount, bFillWreckedShip) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.state) {
        tryData.state = user.userState.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!changes.syncAdd.ships) {
        changes.syncAdd.ships = {};
    }
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    let remaining = amount;
    for (const ship of lodash_1.default.values(firstFleetShips)) {
        // 해상에 있을 경우 난파 중인 배에는 선원을 지급하지 않는다. (bFillWreckedShip 가 false 인 경우)
        if (!tryData.state.isInTown() && !bFillWreckedShip && ship.isWrecked()) {
            continue;
        }
        const max = ship.getStat(tryData.stats).get(cmsEx.STAT_TYPE.SHIP_MAX_SAILOR);
        const avail = max - ship.getSailor();
        if (avail <= 0) {
            continue;
        }
        const sailorToAdd = Math.min(avail, remaining);
        if (!changes.syncAdd.ships[ship.getNub().id]) {
            changes.syncAdd.ships[ship.getNub().id] = {
                id: ship.getNub().id,
                sailor: ship.getSailor(),
            };
        }
        if (changes.syncAdd.ships[ship.getNub().id].sailor === undefined) {
            changes.syncAdd.ships[ship.getNub().id].sailor = ship.getSailor();
        }
        changes.syncAdd.ships[ship.getNub().id].sailor += sailorToAdd;
        remaining -= sailorToAdd;
        if (remaining === 0) {
            break;
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddSailor = opAddSailor;
// -------------------------------------------------------------------------------------------------
function opUpgradeShipBlueprint(user, tryData, changes, cmsId, bAllowOnlyOpen) {
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const blueprintCms = cms_1.default.ShipBlueprint[cmsId];
    const shipCms = cms_1.default.Ship[blueprintCms.shipId];
    const bpMaxLevel = cms_1.default.Const['ShipMaxEnchant' + shipCms.shipSize].value;
    const userBP = tryData.shipBlueprints.getUserShipBlueprint(cmsId);
    if (userBP && userBP.level >= bpMaxLevel) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    let newLevel;
    let newExp;
    if (userBP) {
        if (bAllowOnlyOpen) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
        }
        newLevel = userBP.level + 1;
        newExp = cmsEx.getShipBuildMasteryExpCmsAccumulateExp(newLevel - 1, shipCms.shipSize);
    }
    else {
        newLevel = 1;
        newExp = 0;
    }
    if (newLevel === 1) {
        tryData.shipBlueprints.createUserShipBlueprint(tryData.stats, cmsId, newLevel, newExp, 1, 0, null, null);
    }
    else {
        tryData.shipBlueprints.setUserShipBlueprintLevel(cmsId, newLevel, newExp, undefined, null);
    }
    if (!changes.syncAdd.shipBlueprints) {
        changes.syncAdd.shipBlueprints = {};
    }
    if (!changes.syncAdd.shipBlueprints[cmsId]) {
        changes.syncAdd.shipBlueprints[cmsId] = {
            cmsId,
            level: newLevel,
            exp: newExp,
            slots: {},
        };
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opUpgradeShipBlueprint = opUpgradeShipBlueprint;
// -------------------------------------------------------------------------------------------------
function opSetDirectMailState(user, tryData, changes, mailId, state) {
    if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
    }
    // validate
    const mail = tryData.mails.getDirectMail(mailId);
    if (!mail) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (mail.state === state) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // save
    tryData.mails.setDirectMailState(mailId, state);
    lodash_1.default.merge(changes.syncAdd, {
        userDirectMails: {
            [mailId]: {
                id: mailId,
                state,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetDirectMailState = opSetDirectMailState;
// -------------------------------------------------------------------------------------------------
function opSetDirectmailExpireTimeUtc(user, tryData, changes, mailId, expireTimeUtc) {
    if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
    }
    // validate
    const mail = tryData.mails.getDirectMail(mailId);
    if (!mail) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (mail.expireTimeUtc === expireTimeUtc) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    mail.expireTimeUtc = expireTimeUtc;
    lodash_1.default.merge(changes.syncAdd, {
        userDirectMails: {
            [mailId]: {
                id: mailId,
                expireTimeUtc,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetDirectmailExpireTimeUtc = opSetDirectmailExpireTimeUtc;
// -------------------------------------------------------------------------------------------------
function opAddDirectmail(user, tryData, changes, cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, title = null, titleFormatValue = null, body = null, bodyFormatValue = null, attachment = null) {
    if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
    }
    const newMail = new mailBuilder_1.BuilderMailCreateParams(tryData.mails.generateNewDirectMailId(), cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, title, titleFormatValue, body, bodyFormatValue, attachment).getParam();
    tryData.mails.addDirectMail(newMail, null);
    if (!changes.newMail) {
        changes.newMail = [];
    }
    changes.newMail.push(newMail);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddDirectmail = opAddDirectmail;
// -------------------------------------------------------------------------------------------------
function opSetCompleteTask(user, tryData, changes, taskCmsId, category, index) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    const task = tryData.achievements.getTasks(category).tasks[taskCmsId];
    if (task.isRewarded) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_REWARD;
    }
    const taskCms = cms_1.default.Task[taskCmsId];
    tryData.achievements.setTaskCount(category, taskCmsId, taskCms.achievementCount);
    if (!changes.syncAdd.tasks) {
        changes.syncAdd.tasks = {};
    }
    if (!changes.syncAdd.tasks[category]) {
        changes.syncAdd.tasks[category] = {
            tasks: {},
        };
    }
    if (!changes.syncAdd.tasks[category].tasks[taskCmsId]) {
        changes.syncAdd.tasks[category].tasks[taskCmsId] = {
            cmsId: taskCmsId,
            index,
        };
    }
    changes.syncAdd.tasks[category].tasks[taskCmsId].count = taskCms.achievementCount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetCompleteTask = opSetCompleteTask;
// -------------------------------------------------------------------------------------------------
function opSetTaskRewarded(user, tryData, changes, taskCmsId, category, index) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    const task = tryData.achievements.getTasks(category).tasks[taskCmsId];
    if (task.isRewarded) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_REWARD;
    }
    tryData.achievements.setTaskRewarded(category, taskCmsId);
    if (!changes.syncAdd.tasks) {
        changes.syncAdd.tasks = {};
    }
    if (!changes.syncAdd.tasks[category]) {
        changes.syncAdd.tasks[category] = {
            tasks: {},
        };
    }
    if (!changes.syncAdd.tasks[category].tasks[taskCmsId]) {
        changes.syncAdd.tasks[category].tasks[taskCmsId] = {
            cmsId: taskCmsId,
            index,
        };
    }
    changes.syncAdd.tasks[category].tasks[taskCmsId].isRewarded = 1;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetTaskRewarded = opSetTaskRewarded;
// -------------------------------------------------------------------------------------------------
function opSetTaskCategoryRewarded(user, tryData, changes, category) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    const categoryTasks = tryData.achievements.getTasks(category);
    if (categoryTasks.isCategoryRewarded) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_TASK_CATEGORY_REWARD;
    }
    tryData.achievements.setTaskCategoryRewarded(category);
    if (!changes.syncAdd.tasks) {
        changes.syncAdd.tasks = {};
    }
    if (!changes.syncAdd.tasks[category]) {
        changes.syncAdd.tasks[category] = {
            tasks: {},
        };
    }
    changes.syncAdd.tasks[category].isCategoryRewarded = 1;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetTaskCategoryRewarded = opSetTaskCategoryRewarded;
// -------------------------------------------------------------------------------------------------
function opSetAchievementRewarded(user, tryData, changes, achievementCmsId) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    const a = tryData.achievements.getAchievement(achievementCmsId);
    if (a.isRewarded) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_ACHIEVEMENT_REWARD;
    }
    tryData.achievements.setAchievementRewarded(achievementCmsId);
    if (!changes.syncAdd.achievements) {
        changes.syncAdd.achievements = {};
    }
    if (!changes.syncAdd.achievements[achievementCmsId]) {
        changes.syncAdd.achievements[achievementCmsId] = {
            cmsId: achievementCmsId,
        };
    }
    changes.syncAdd.achievements[achievementCmsId].isRewarded = 1;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetAchievementRewarded = opSetAchievementRewarded;
// -------------------------------------------------------------------------------------------------
function opSetAchievementPointRewarded(user, tryData, changes, lastRewardedAchievementPointCmsId) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    if (tryData.achievements.lastRewardedAchievementPointCmsId === lastRewardedAchievementPointCmsId) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_ACHIEVEMENT_POINT_REWARD;
    }
    tryData.achievements.lastRewardedAchievementPointCmsId = lastRewardedAchievementPointCmsId;
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    changes.syncAdd.user.lastRewardedAchievementPointCmsId = lastRewardedAchievementPointCmsId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetAchievementPointRewarded = opSetAchievementPointRewarded;
// -------------------------------------------------------------------------------------------------
function opSetEventMissionRewarded(user, tryData, changes, eventPageCmsId, eventMissionCmsId) {
    if (!tryData.achievements) {
        tryData.achievements = user.userAchievement.clone();
    }
    const eventMission = tryData.achievements.getEventMission(eventPageCmsId, eventMissionCmsId);
    if (eventMission) {
        if (eventMission.isRewarded) {
            return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_EVENT_MISSION_REWARD;
        }
        eventMission.isRewarded = 1;
    }
    else {
        // 카운팅 개념을 사용하지 않고, isRewarded 만 사용하는 형태의 CMS.EventMission 도 있음.
        // DB, 메모리에 없을 수 있는 것 참고.
        tryData.achievements.setEventMission(eventPageCmsId, eventMissionCmsId, 0, 1);
    }
    if (!changes.syncAdd.eventPages) {
        changes.syncAdd.eventPages = {};
    }
    if (!changes.syncAdd.eventPages[eventPageCmsId]) {
        changes.syncAdd.eventPages[eventPageCmsId] = {
            eventMissions: {},
        };
    }
    changes.syncAdd.eventPages[eventPageCmsId].eventMissions[eventMissionCmsId] = {
        isRewarded: 1,
    };
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetEventMissionRewarded = opSetEventMissionRewarded;
// -------------------------------------------------------------------------------------------------
// 막 우겨넣었는데 정리가 필요함.
// 이 오퍼레이터 안에서도 ALREADY_RECEIVED_EVENT_MISSION_REWARD 같은 검증을 하는 게 좋을 듯한데, 고민.
function opSetPassEventMissions(user, tryData, changes, eventPageCmsId, eventMissionCmsIdsToReset, // 검증이 된 것들이어야함.
repeatedRewardReceiveCount, eventMissionCmsIdsToReward // 검증이 된 것들이어야함.
) {
    const passEventPageCms = cms_1.default.EventPage[eventPageCmsId];
    if (!passEventPageCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (passEventPageCms.type !== eventPageDesc_1.EventPageType.PASS_EVENT) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.passEvent) {
        tryData.passEvent = user.userPassEvent.clone();
    }
    const inEventMissionChanges = {};
    // 리셋
    for (const eventMissionCmsId of eventMissionCmsIdsToReset) {
        inEventMissionChanges[eventMissionCmsId] = {
            eventPageCmsId: passEventPageCms.id,
            eventMissionCmsId,
            count: 0,
            repeatedRewardReceiveCount: 0,
            isRewarded: 0,
        };
    }
    // 보상 수령 여부.
    for (const eventMissionCmsId of eventMissionCmsIdsToReward) {
        if (!inEventMissionChanges[eventMissionCmsId]) {
            inEventMissionChanges[eventMissionCmsId] = {
                eventPageCmsId: passEventPageCms.id,
                eventMissionCmsId,
                isRewarded: 1,
            };
        }
        else {
            inEventMissionChanges[eventMissionCmsId].isRewarded = 1;
        }
    }
    // 반복보상 횟수 반영
    lodash_1.default.forOwn(repeatedRewardReceiveCount, (receiveCount, eventMissionCmsId) => {
        if (!inEventMissionChanges[eventMissionCmsId]) {
            inEventMissionChanges[eventMissionCmsId] = {
                eventPageCmsId: passEventPageCms.id,
                eventMissionCmsId: parseInt(eventMissionCmsId, 10),
                repeatedRewardReceiveCount: receiveCount,
            };
        }
        else {
            inEventMissionChanges[eventMissionCmsId].repeatedRewardReceiveCount = receiveCount;
        }
    });
    const tryDataPassEventMissions = tryData.passEvent.getPassEventMissions(passEventPageCms.id);
    const actualChangedPassEventMissions = [];
    const actualChangedEventMissionChanges = [];
    lodash_1.default.forOwn(inEventMissionChanges, (change) => {
        const tryDataPassEventMission = tryDataPassEventMissions[change.eventMissionCmsId];
        const oldCount = tryDataPassEventMission ? tryDataPassEventMission.count : 0;
        const oldRepeatedRewardReceiveCount = tryDataPassEventMission
            ? tryDataPassEventMission.repeatedRewardReceiveCount
            : 0;
        const oldIsRewarded = tryDataPassEventMission ? tryDataPassEventMission.isRewarded : 0;
        if ((change.count === undefined || change.count === oldCount) &&
            (change.repeatedRewardReceiveCount === undefined ||
                change.repeatedRewardReceiveCount === oldRepeatedRewardReceiveCount) &&
            (change.isRewarded === undefined || change.isRewarded === oldIsRewarded)) {
            return;
        }
        actualChangedPassEventMissions.push({
            eventMissionCmsId: change.eventMissionCmsId,
            count: change.count !== undefined ? change.count : oldCount,
            repeatedRewardReceiveCount: change.repeatedRewardReceiveCount !== undefined
                ? change.repeatedRewardReceiveCount
                : oldRepeatedRewardReceiveCount,
            isRewarded: change.isRewarded !== undefined ? change.isRewarded : oldIsRewarded,
        });
        actualChangedEventMissionChanges.push(change);
    });
    tryData.passEvent.applyPassEventMissions(passEventPageCms, actualChangedPassEventMissions);
    // 관련 오퍼레이터를 섞어서 사용하는 것도 이상한데, 괜히 복잡해지는 데 차라리 아예 막는게 어떨지?
    if (!changes.passEventMissions) {
        changes.passEventMissions = {};
    }
    if (!changes.passEventMissions[passEventPageCms.id]) {
        changes.passEventMissions[passEventPageCms.id] = {};
    }
    const accEventMissionChanges = changes.passEventMissions[passEventPageCms.id];
    // 애매하긴 한데, 리셋된 다음 보상을 받아 최종적으로 DB 변경이 없는 경우를 방지하기 위함.
    // 메모리 apply 할 때도, 업데이트 함수에서 처리되지 않을텐데 문제없을지 모르겠음.
    actualChangedEventMissionChanges.forEach((elem) => {
        if (!accEventMissionChanges[elem.eventMissionCmsId]) {
            accEventMissionChanges[elem.eventMissionCmsId] = elem;
            return;
        }
        if (elem.count !== undefined) {
            accEventMissionChanges[elem.eventMissionCmsId].count = elem.count;
        }
        if (elem.repeatedRewardReceiveCount !== undefined) {
            accEventMissionChanges[elem.eventMissionCmsId].repeatedRewardReceiveCount =
                elem.repeatedRewardReceiveCount;
        }
        if (elem.isRewarded !== undefined) {
            accEventMissionChanges[elem.eventMissionCmsId].isRewarded = elem.isRewarded;
        }
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetPassEventMissions = opSetPassEventMissions;
// -------------------------------------------------------------------------------------------------
function opSetBattleRewards(_user, _tryData, changes, battleRewardChanges) {
    changes.syncAdd.battleRewards = battleRewardChanges;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetBattleRewards = opSetBattleRewards;
// -------------------------------------------------------------------------------------------------
function opSetInsuranceCmsId(user, tryData, changes, cmsId) {
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    if (tryData.points.getInsuranceCmsId() === cmsId) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.points.setInsuranceCmsId(cmsId);
    if (!changes.syncAdd.insurance) {
        changes.syncAdd.insurance = {};
    }
    changes.syncAdd.insurance.insuranceCmsId = cmsId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetInsuranceCmsId = opSetInsuranceCmsId;
// -------------------------------------------------------------------------------------------------
function opSetNation(user, tryData, changes, nationCmsId, lastUpdateNationTimeUtc) {
    // try data
    if (!tryData.nationCmsId) {
        tryData.nationCmsId = user.nationCmsId;
    }
    if (tryData.nationCmsId === nationCmsId) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.nationCmsId = nationCmsId;
    tryData.lastUpdateNationTimeUtc = lastUpdateNationTimeUtc;
    // changes
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    changes.syncAdd.user.nationCmsId = nationCmsId;
    changes.syncAdd.user.lastUpdateNationTimeUtc = lastUpdateNationTimeUtc;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetNation = opSetNation;
// -------------------------------------------------------------------------------------------------
function opResetPalaceRoyalOrder(user, tryData, changes) {
    changes.bResetPalaceRoyalOrder = true;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opResetPalaceRoyalOrder = opResetPalaceRoyalOrder;
// -------------------------------------------------------------------------------------------------
function opSetReputation(user, tryData, changes, nationCmsId, reputation, updateTimeUtc) {
    // try data
    if (!tryData.reputation) {
        tryData.reputation = user.userReputation.clone();
    }
    tryData.reputation.set(nationCmsId, {
        reputation: reputation,
        updateTimeUtc: updateTimeUtc,
    }, null);
    // changes
    if (!changes.syncAdd.reputations) {
        changes.syncAdd.reputations = {};
    }
    changes.syncAdd.reputations[nationCmsId] = {
        reputation: reputation,
        updateTimeUtc: updateTimeUtc,
    };
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetReputation = opSetReputation;
// -------------------------------------------------------------------------------------------------
function opModifyReputation(user, tryData, changes, cmsId, coefficient, updateTimeUtc) {
    // try data
    if (!tryData.reputation) {
        tryData.reputation = user.userReputation.clone();
    }
    const oldReputation = tryData.reputation.get(cmsId, updateTimeUtc);
    const newReputation = tryData.reputation.getNewValue(cmsId, updateTimeUtc, coefficient);
    if (oldReputation === newReputation) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.reputation.set(cmsId, {
        reputation: newReputation,
        updateTimeUtc: updateTimeUtc,
    }, null);
    // changes
    if (!changes.syncAdd.reputations) {
        changes.syncAdd.reputations = {};
    }
    changes.syncAdd.reputations[cmsId] = {
        reputation: newReputation,
        updateTimeUtc: updateTimeUtc,
    };
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opModifyReputation = opModifyReputation;
// -------------------------------------------------------------------------------------------------
function opRecordNationEventOccur(_user, _tryData, changes, cmsId1, cmsId2, nationDiplomacyCmsId) {
    if (!changes.nationEvents) {
        changes.nationEvents = [];
    }
    changes.nationEvents.push([cmsId1, cmsId2, nationDiplomacyCmsId]);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opRecordNationEventOccur = opRecordNationEventOccur;
// -------------------------------------------------------------------------------------------------
function opDecreaseMateLoyalty(user, fleetIndex, tryData, changes, cmsId, amount // 양수가 넘어온다
) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    const oldLoyalty = tryData.mates.getMate(cmsId).getLoyalty();
    if (!oldLoyalty) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // 충성도 감소가 발생할 수 있는 상황에서는 이 함수를 사용하여 충성도 감소 방지 효과 체크를 한다
    const added = statHelper_1.StatHelper.calcChangeLoyalty(tryData.fleets.getFleet(fleetIndex), user.companyStat, -amount, oldLoyalty);
    if (0 === added) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!changes.mateLoyalty) {
        changes.mateLoyalty = [];
    }
    const newLoyalty = Math.max(0, oldLoyalty + added);
    tryData.mates.getMate(cmsId).setLoyalty(newLoyalty, null, null, null);
    const change = changes.mateLoyalty.find((elem) => {
        return elem.mateCmsId === cmsId;
    });
    if (change) {
        change.value = newLoyalty;
    }
    else {
        changes.mateLoyalty.push({
            mateCmsId: cmsId,
            value: newLoyalty,
        });
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDecreaseMateLoyalty = opDecreaseMateLoyalty;
// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
function opDeleteCashShopRestrictedProduct(user, tryData, changes, cmsId) {
    if (!changes.syncRemove.cashShopRestrictedProducts) {
        changes.syncRemove.cashShopRestrictedProducts = [];
    }
    changes.syncRemove.cashShopRestrictedProducts.push(cmsId.toString());
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteCashShopRestrictedProduct = opDeleteCashShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
function opSetCashShopRestrictedProduct(user, tryData, changes, restrictedProduct) {
    if (!changes.syncAdd.cashShopRestrictedProducts) {
        changes.syncAdd.cashShopRestrictedProducts = {};
    }
    changes.syncAdd.cashShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetCashShopRestrictedProduct = opSetCashShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
function opDeleteCashShopFixedTermProduct(user, tryData, changes, cmsId) {
    if (!changes.syncRemove.cashShopFixedTermProducts) {
        changes.syncRemove.cashShopFixedTermProducts = [];
    }
    changes.syncRemove.cashShopFixedTermProducts.push(cmsId.toString());
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteCashShopFixedTermProduct = opDeleteCashShopFixedTermProduct;
// -------------------------------------------------------------------------------------------------
function opSetCashShopFixedTermProduct(user, tryData, changes, product) {
    if (!changes.syncAdd.cashShopFixedTermProducts) {
        changes.syncAdd.cashShopFixedTermProducts = {};
    }
    changes.syncAdd.cashShopFixedTermProducts[product.cmsId] = product;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetCashShopFixedTermProduct = opSetCashShopFixedTermProduct;
// -------------------------------------------------------------------------------------------------
function opSetSailing(user, tryData, changes, sailingChange, sailedDays) {
    // sailing 업데이트는 누적될 일이 없기 때문에 try data 사용하지 않음.
    changes.daysForLoyaltyDecrease = sailingChange.daysForLoyaltyDecrease;
    changes.daysForTownReset = sailingChange.daysForTownReset;
    changes.sailedDays = sailedDays;
    // reset
    changes.syncRemove.sailing = true;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetSailing = opSetSailing;
// -------------------------------------------------------------------------------------------------
function opAddTotalSailedDays(user, tryData, changes, sailedDays) {
    if (!tryData.sailing) {
        tryData.sailing = user.userSailing.clone();
    }
    tryData.sailing.totalSailedDays += sailedDays;
    changes.syncAdd.user.totalSailedDays = tryData.sailing.totalSailedDays;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddTotalSailedDays = opAddTotalSailedDays;
// -------------------------------------------------------------------------------------------------
function opResetTownState(user, tryData, changes) {
    changes.bResetTownState = true;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opResetTownState = opResetTownState;
// -------------------------------------------------------------------------------------------------
function opResetGameOverLosses(user, tryData, changes) {
    changes.syncRemove.gameOverLosses = true;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opResetGameOverLosses = opResetGameOverLosses;
// -------------------------------------------------------------------------------------------------
function opResetGameOverLossesPvpLoss(user, tryData, changes) {
    changes.syncRemove.multiPvpLoss = true;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opResetGameOverLossesPvpLoss = opResetGameOverLossesPvpLoss;
// -------------------------------------------------------------------------------------------------
function opSetbGameOver(user, tryData, changes, bGameOver) {
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    changes.syncAdd.user.bGameOver = bGameOver;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetbGameOver = opSetbGameOver;
// -------------------------------------------------------------------------------------------------
function opSetGameState(user, tryData, changes, gameState, gameEnterState) {
    if (!tryData.state) {
        tryData.state = user.userState.clone();
    }
    const gameStateChange = tryData.state.buildGameStateChange(gameState, gameEnterState);
    lodash_1.default.merge(changes.syncAdd, tryData.state.applyGameStateChange(gameStateChange, null).add);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetGameState = opSetGameState;
// -------------------------------------------------------------------------------------------------
function opSetLastGameState(user, tryData, changes, gameState) {
    if (!tryData.state) {
        tryData.state = user.userState.clone();
    }
    tryData.state.forceSetLastGameState(gameState);
    lodash_1.default.merge(changes.syncAdd, {
        user: {
            lastGameState: gameState,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetLastGameState = opSetLastGameState;
// -------------------------------------------------------------------------------------------------
function opApplyGameStateChange(user, tryData, changes, gameStateChange) {
    if (!tryData.state) {
        tryData.state = user.userState.clone();
    }
    lodash_1.default.merge(changes.syncAdd, tryData.state.applyGameStateChange(gameStateChange, null).add);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyGameStateChange = opApplyGameStateChange;
// -------------------------------------------------------------------------------------------------
function opSetLastTownCmsId(user, tryData, changes, lastTownCmsId) {
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    changes.syncAdd.user.lastTownCmsId = lastTownCmsId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetLastTownCmsId = opSetLastTownCmsId;
// -------------------------------------------------------------------------------------------------
function opSetArrivalTownCmsId(user, tryData, changes, arrivalTownCmsId) {
    changes.arrivalTownCmsId = arrivalTownCmsId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetArrivalTownCmsId = opSetArrivalTownCmsId;
// -------------------------------------------------------------------------------------------------
// 부락 arriveVillage 패킷 때 사용하던 기능 주석처리 현재 사용 안함
/*
export function opSetArrivalVillageCmsId(
  changes: Changes,
  arrivalVillageCmsId: number
): CHANGE_TASK_RESULT {
  changes.arrivalVillageCmsId = arrivalVillageCmsId;
  return CHANGE_TASK_RESULT.OK;
}
*/
// -------------------------------------------------------------------------------------------------
function opSetCashShopGachaBoxGuaranteeAccum(user, tryData, changes, cashShopCmsId, guaranteeAccum) {
    if (!changes.syncAdd.cashShopGachaBoxGuarantees) {
        changes.syncAdd.cashShopGachaBoxGuarantees = {};
    }
    changes.syncAdd.cashShopGachaBoxGuarantees[cashShopCmsId] = guaranteeAccum;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetCashShopGachaBoxGuaranteeAccum = opSetCashShopGachaBoxGuaranteeAccum;
// -------------------------------------------------------------------------------------------------
function opAddSoundPack(user, tryData, changes, soundPackCmsId) {
    if (!tryData.cashShop) {
        tryData.cashShop = user.userCashShop.clone();
    }
    if (tryData.cashShop.isSoundPackBought(soundPackCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_BOUGHT;
    }
    tryData.cashShop.addSoundPack(soundPackCmsId);
    if (!changes.syncAdd.soundPacks) {
        changes.syncAdd.soundPacks = {};
    }
    const offset = Math.floor(soundPackCmsId / 32);
    changes.syncAdd.soundPacks[offset] = tryData.cashShop.getSoundPackIdxField(soundPackCmsId);
    if (!changes.addedSoundPacks) {
        changes.addedSoundPacks = [];
    }
    changes.addedSoundPacks.push(soundPackCmsId);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddSoundPack = opAddSoundPack;
// -------------------------------------------------------------------------------------------------
function opAddMateIntimacyOrLoyalty(user, tryData, changes, mateCmsId, amount) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const mateCms = cms_1.default.Mate[mateCmsId];
    if (!mateCms) {
        mlog_1.default.error('[RewardTask] opAddIntimacyOrLoyalty. Invalid mate cms id.', {
            userId: user.userId,
            mateCmsId,
        });
        throw new merror_1.MError('invalid-mate-cms-id', merror_1.MErrorCode.INVALID_MATE_CMS_ID);
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    const mate = tryData.mates.getMate(mateCmsId);
    if (mate) {
        // 충성도 지급
        const oldLoyalty = mate.getLoyalty();
        if (oldLoyalty === null) {
            // 제독 가능 항해사인 경우.
            return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
        }
        const newLoyalty = Math.min(oldLoyalty + amount, cms_1.default.Const.MaxLoyalty.value);
        if (oldLoyalty === newLoyalty) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
        }
        lodash_1.default.merge(changes.syncAdd, {
            mates: {
                [mateCmsId]: {
                    cmsId: mateCmsId,
                    loyalty: newLoyalty,
                },
            },
        });
        mate.setLoyalty(newLoyalty, null, null, null);
        if (newLoyalty >= cms_1.default.Const.MateManagementLoyaltyTerms.value &&
            mate.hasState(cmsEx.MATE_STATE_FLAG.SLOWDOWN)) {
            lodash_1.default.merge(changes.syncAdd, {
                mates: {
                    [mateCmsId]: {
                        cmsId: mateCmsId,
                        stateFlags: mate.removeState(cmsEx.MATE_STATE_FLAG.SLOWDOWN, user.companyStat),
                    },
                },
            });
        }
    }
    else {
        // 친밀도 지급
        const oldIntimacy = tryData.mates.getUnemployedMateIntimacy(mateCmsId);
        const newIntimacy = Math.min(oldIntimacy + amount, cms_1.default.Const.MaxLoyalty.value);
        if (oldIntimacy === newIntimacy) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
        }
        lodash_1.default.merge(changes.syncAdd, {
            unemployedMates: {
                [mateCmsId]: {
                    mateCmsId,
                    isMet: 1,
                    intimacy: newIntimacy,
                },
            },
        });
        tryData.mates.setUnemployedMateIntimacy(mateCmsId, newIntimacy, null);
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddMateIntimacyOrLoyalty = opAddMateIntimacyOrLoyalty;
// -------------------------------------------------------------------------------------------------
function opAddMateInjuryState(user, tryData, changes, mateCmsId, curTimeUtc, injuryTimeSec) {
    // 전투(항해)에서는 항해 틱이 안돌아서 괜찮지만
    // *항해에서는 부상틱과 겹칠 여지가 있을 지, 다른 스테이트에서는 문제 없을 지 확인 필요합니다.
    // 알림용으로 가드를 넣어놓습니다.
    if (!user.userState.isInOceanBattle() && !user.userState.isInOceanLand()) {
        mlog_1.default.error('opAddMateInjuryState. invalid state.', {
            userId: user.userId,
            gameState: user.userState.getGameState(),
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (injuryTimeSec === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (injuryTimeSec < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const mateCms = cms_1.default.Mate[mateCmsId];
    if (!mateCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    const mate = tryData.mates.getMate(mateCmsId);
    if (!mate) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (mate.hasState(cmsEx.MATE_STATE_FLAG.INJURY)) {
        mlog_1.default.error('opAddMateInjuryState. already injured.', {
            userId: user.userId,
            mateCmsId,
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!changes.injuryDuration) {
        changes.injuryDuration = {};
    }
    changes.injuryDuration[mateCmsId] = injuryTimeSec;
    const injuryExpireTimeUtc = curTimeUtc + injuryTimeSec;
    lodash_1.default.merge(changes.syncAdd, {
        mates: {
            [mateCmsId]: {
                cmsId: mateCmsId,
                stateFlags: mate.addState(cmsEx.MATE_STATE_FLAG.INJURY, null),
                injuryExpireTimeUtc,
            },
        },
    });
    mate.setInjuryExpireTimeUtc(injuryExpireTimeUtc);
    mate.setInjuryImmuneTimeUtc(null);
    // apply 될 때는 부상 버프 적용되는 것 참고
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddMateInjuryState = opAddMateInjuryState;
// -------------------------------------------------------------------------------------------------
function opSetAttendance(user, tryData, changes, attendance) {
    if (!tryData.attendance) {
        tryData.attendance = user.userAttendance.clone();
    }
    tryData.attendance.setAttendance(attendance);
    if (!changes.syncAdd.attendances) {
        changes.syncAdd.attendances = {};
    }
    changes.syncAdd.attendances[attendance.eventPageCmsId] = attendance;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetAttendance = opSetAttendance;
// -------------------------------------------------------------------------------------------------
function opAddEnergy(user, tryData, changes, amount) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.energy) {
        tryData.energy = user.userEnergy.clone();
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    const curTimeUtc = mutil.curTimeUtc();
    const curEnergy = tryData.energy.getCurrentEnergy(curTimeUtc, tryData.userLevel);
    if (amount < 0 && curEnergy < -amount) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_ENERGY;
    }
    let energyChange;
    if (amount < 0) {
        energyChange = tryData.energy.buildEnergyChangeWithConsume(curTimeUtc, tryData.userLevel, tryData.userLevel, -amount, false);
        if (changes.energyConsumeAmount) {
            changes.energyConsumeAmount += amount;
        }
        else {
            changes.energyConsumeAmount = amount;
        }
    }
    else {
        energyChange = tryData.energy.buildEnergyChange(curTimeUtc, tryData.userLevel, tryData.userLevel);
        if (!energyChange) {
            energyChange = {
                energy: tryData.energy.rawEnergy,
                lastUpdateTimeUtc: tryData.energy.lastUpdateEnergyTimeUtc,
            };
        }
        energyChange.energy += amount;
        if (energyChange.energy === curEnergy) {
            energyChange = undefined;
        }
        if (!changes.pointsGainAmount) {
            changes.pointsGainAmount = {};
        }
        if (!changes.pointsGainAmount[cmsEx.EnergyPointCmsId]) {
            changes.pointsGainAmount[cmsEx.EnergyPointCmsId] = amount;
        }
        else {
            changes.pointsGainAmount[cmsEx.EnergyPointCmsId] += amount;
        }
    }
    if (energyChange) {
        lodash_1.default.merge(changes.syncAdd, tryData.energy.applyEnergyChange(energyChange, null).add);
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddEnergy = opAddEnergy;
// -------------------------------------------------------------------------------------------------
function opAddShipSlotItem(user, tryData, changes, shipSlotCmsId, amount, bAllowOverInven, bAllowAddToLimitIfExceeded, extraStr, bIsBound = true, bIfExceededAddMail = false) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    // try data
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const extra = extraStr ? JSON.parse(extraStr) : undefined;
    if (!bAllowOverInven) {
        const added = tryData.inven.calcShipSlotItemAddable(amount);
        if (added !== amount) {
            if (bAllowAddToLimitIfExceeded) {
                amount = added;
                if (amount === 0) {
                    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
                }
            }
            else {
                if (bIfExceededAddMail) {
                    let exceededCount;
                    if (added <= 0) {
                        exceededCount = amount;
                        amount = 0;
                    }
                    else {
                        exceededCount = amount - added;
                        amount = added;
                    }
                    const attachment = [
                        {
                            Id: shipSlotCmsId,
                            Type: rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM,
                            Quantity: exceededCount,
                            Extra: extraStr,
                        },
                    ];
                    AddMailExceededReward(user, tryData, changes, mutil.curTimeUtc(), attachment);
                }
                else {
                    return userChangeTask_1.CHANGE_TASK_RESULT.INVEN_FULL;
                }
            }
        }
    }
    const shipSlotCms = cms_1.default.ShipSlot[shipSlotCmsId];
    const curTimeUtc = mutil.curTimeUtc();
    const expireTimeUtc = extra === null || extra === void 0 ? void 0 : extra.expireTimeUtc;
    // 귀속 여부 결정.
    if (shipSlotCms.isCashMarket &&
        ((extra && extra.isBound === 0) || !bIsBound) &&
        !shipSlotCms.expireType) {
        bIsBound = false;
    }
    for (let i = 0; i < amount; i++) {
        const shipSlotItem = tryData.inven.buildShipSlotItem(shipSlotCmsId, bIsBound ? 1 : 0, 0, curTimeUtc, extra === null || extra === void 0 ? void 0 : extra.expireTimeUtc, extra === null || extra === void 0 ? void 0 : extra.enchantLv);
        tryData.inven.addShipSlotItem(shipSlotItem, null);
        lodash_1.default.merge(changes.syncAdd, {
            shipSlotItems: {
                [shipSlotItem.id]: {
                    id: shipSlotItem.id,
                    shipSlotCmsId,
                    isBound: bIsBound ? 1 : 0,
                    isLocked: 0,
                    expireTimeUtc,
                    enchantLv: shipSlotItem.enchantLv,
                },
            },
        });
        changes.lastShipSlotItemId = shipSlotItem.id;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShipSlotItem = opAddShipSlotItem;
// -------------------------------------------------------------------------------------------------
function opApplyMateExpChange(user, tryData, changes, jobType, mateExpChange) {
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    const mate = tryData.mates.getMate(mateExpChange.mateCmsId);
    const oldLevel = mate.getLevel(jobType);
    if (mate.getExp(jobType) === mateExpChange.exp && oldLevel === mateExpChange.level) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    mate.setExpLevel(jobType, mateExpChange, null, null);
    if (!changes.mateExp) {
        changes.mateExp = {};
    }
    if (!changes.mateExp[jobType]) {
        changes.mateExp[jobType] = [];
    }
    const change = changes.mateExp[jobType].find((changesElem) => {
        return changesElem.mateCmsId === mateExpChange.mateCmsId;
    });
    if (change) {
        change.exp = mateExpChange.exp;
        change.level = mateExpChange.level;
    }
    else {
        changes.mateExp[jobType].push({
            mateCmsId: mateExpChange.mateCmsId,
            exp: mateExpChange.exp,
            level: mateExpChange.level,
            oldLevel,
        });
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyMateExpChange = opApplyMateExpChange;
// -------------------------------------------------------------------------------------------------
function opApplyEnergyChange(user, tryData, changes, energyChange) {
    if (!energyChange) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.energy) {
        tryData.energy = user.userEnergy.clone();
    }
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    const curTimeUtc = mutil.curTimeUtc();
    const curEnergy = tryData.energy.getCurrentEnergy(curTimeUtc, tryData.userLevel);
    const energyDiff = energyChange.energy - curEnergy;
    if (energyDiff < 0) {
        if (changes.energyConsumeAmount) {
            changes.energyConsumeAmount += energyDiff;
        }
        else {
            changes.energyConsumeAmount = energyDiff;
        }
    }
    else {
        if (!changes.pointsGainAmount) {
            changes.pointsGainAmount = {};
        }
        if (!changes.pointsGainAmount[cmsEx.EnergyPointCmsId]) {
            changes.pointsGainAmount[cmsEx.EnergyPointCmsId] = energyDiff;
        }
        else {
            changes.pointsGainAmount[cmsEx.EnergyPointCmsId] += energyDiff;
        }
    }
    lodash_1.default.merge(changes.syncAdd, tryData.energy.applyEnergyChange(energyChange, null).add);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyEnergyChange = opApplyEnergyChange;
// -------------------------------------------------------------------------------------------------
function opApplyPointChange(user, tryData, changes, pointChange) {
    if (pointChange.cmsId === cmsEx.EnergyPointCmsId || (0, pointDesc_1.isCash)(pointChange.cmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    const curPoints = tryData.points.getPoint(pointChange.cmsId);
    if (curPoints === pointChange.value) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    const pointDiff = pointChange.value - curPoints;
    if (pointDiff > 0) {
        if (!changes.pointsGainAmount) {
            changes.pointsGainAmount = {};
        }
        if (!changes.pointsGainAmount[pointChange.cmsId]) {
            changes.pointsGainAmount[pointChange.cmsId] = pointDiff;
        }
        else {
            changes.pointsGainAmount[pointChange.cmsId] += pointDiff;
        }
    }
    else {
        if (!changes.pointsConsumeAmountForGlog) {
            changes.pointsConsumeAmountForGlog = {};
        }
        if (!changes.pointsConsumeAmountForGlog[pointChange.cmsId]) {
            changes.pointsConsumeAmountForGlog[pointChange.cmsId] = pointDiff;
        }
        else {
            changes.pointsConsumeAmountForGlog[pointChange.cmsId] += pointDiff;
        }
    }
    tryData.points.applyPointChanges([pointChange], null);
    lodash_1.default.merge(changes.syncAdd, {
        points: {
            [pointChange.cmsId]: pointChange,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyPointChange = opApplyPointChange;
// -------------------------------------------------------------------------------------------------
function opSetUserExpLevel(user, tryData, changes, exp, level) {
    if (tryData.userExp === undefined) {
        tryData.userExp = user.exp;
        tryData.userLevel = user.level;
    }
    if (mutil.isNotANumber(exp) || mutil.isNotANumber(level)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (tryData.userExp === exp && tryData.userLevel === level) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.userExp = exp;
    tryData.userLevel = level;
    lodash_1.default.merge(changes.syncAdd, {
        user: {
            exp,
            level,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetUserExpLevel = opSetUserExpLevel;
// -------------------------------------------------------------------------------------------------
function opApplyFreeTakebackChange(user, tryData, changes, freeTakebackChange) {
    if (!tryData.battle) {
        tryData.battle = user.userBattle.clone();
    }
    if (tryData.battle.usedFreeTurnTakebackCount === freeTakebackChange.usedFreeTurnTakebackCount &&
        tryData.battle.usedFreePhaseTakebackCount === freeTakebackChange.usedFreePhaseTakebackCount &&
        tryData.battle.lastFreeTakebackUpdateTimeUtc === freeTakebackChange.updateTimeUtc) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.battle.applyFreeTakebackChange(freeTakebackChange);
    lodash_1.default.merge(changes.syncAdd, {
        user: {
            usedFreeTurnTakebackCount: freeTakebackChange.usedFreeTurnTakebackCount,
            usedFreePhaseTakebackCount: freeTakebackChange.usedFreePhaseTakebackCount,
            lastFreeTakebackUpdateTimeUtc: freeTakebackChange.updateTimeUtc,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyFreeTakebackChange = opApplyFreeTakebackChange;
// -------------------------------------------------------------------------------------------------
function opApplyQuickModeChange(user, tryData, changes, quickModeChange) {
    if (!tryData.battle) {
        tryData.battle = user.userBattle.clone();
    }
    if (tryData.battle.quickModeCount === quickModeChange.quickModeCount &&
        tryData.battle.lastQuickModeCountUpdateTimeUtc === quickModeChange.updateTimeUtc) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.battle.applyQuickModeChange(quickModeChange);
    lodash_1.default.merge(changes.syncAdd, {
        user: {
            quickModeCount: quickModeChange.quickModeCount,
            lastQuickModeCountUpdateTimeUtc: quickModeChange.updateTimeUtc,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyQuickModeChange = opApplyQuickModeChange;
// -------------------------------------------------------------------------------------------------
/**
 * 명성을 획득한다면, {@link cmsEx.ACHIEVEMENT_TERMS.GAIN_FAME | 명성 획득 업적}을 따로 누적해줘야 합니다.
 */
function opSetFame(user, tryData, changes, jobType, fame) {
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    const leader = userMates_1.default.getLeaderMate(tryData.fleets, tryData.mates);
    if (!leader) {
        mlog_1.default.error('[RewardTask] Cannot add fame without an admiral.', {
            userId: user.userId,
        });
        throw new merror_1.MError('no-adirmal', merror_1.MErrorCode.NEED_ADMIRAL);
    }
    // Check job type.
    if (jobType < cmsEx.JOB_TYPE.ADVENTURE || jobType > cmsEx.JOB_TYPE.BATTLE) {
        mlog_1.default.error('[RewardTask] Invalid fame job type.', {
            userId: user.userId,
            jobType: jobType,
        });
        throw new merror_1.MError('invalid-job-type', merror_1.MErrorCode.INVALID_JOB_TYPE);
    }
    if (leader.getFame(jobType) === fame) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // Update the try data.
    leader.setFame(jobType, fame, null);
    // Save the change.
    if (!changes.syncAdd.mates) {
        changes.syncAdd.mates = {};
    }
    if (!changes.syncAdd.mates[leader.getNub().cmsId]) {
        changes.syncAdd.mates[leader.getNub().cmsId] = {
            cmsId: leader.getNub().cmsId,
        };
    }
    switch (jobType) {
        case cmsEx.JOB_TYPE.ADVENTURE:
            changes.syncAdd.mates[leader.getNub().cmsId].adventureFame = fame;
            break;
        case cmsEx.JOB_TYPE.TRADE:
            changes.syncAdd.mates[leader.getNub().cmsId].tradeFame = fame;
            break;
        case cmsEx.JOB_TYPE.BATTLE:
            changes.syncAdd.mates[leader.getNub().cmsId].battleFame = fame;
            break;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetFame = opSetFame;
// -------------------------------------------------------------------------------------------------
function opSetGameOverLosses(user, tryData, changes, gameOverLosses) {
    if (!tryData.sailing) {
        tryData.sailing = user.userSailing.clone();
    }
    if (lodash_1.default.isEqual(tryData.sailing.getGameOverLosses(), gameOverLosses)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.sailing.setGameOverLosses(gameOverLosses);
    changes.gameOverLosses = gameOverLosses;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetGameOverLosses = opSetGameOverLosses;
// -------------------------------------------------------------------------------------------------
function opSetMultiPvpLoss(user, tryData, changes, multiPvpLoss) {
    if (!tryData.sailing) {
        tryData.sailing = user.userSailing.clone();
    }
    if (lodash_1.default.isEqual(tryData.sailing.getMultiPvpLoss(), {})) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.sailing.setMultiPvpLoss(multiPvpLoss);
    changes.multiPvpLoss = multiPvpLoss;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetMultiPvpLoss = opSetMultiPvpLoss;
// -------------------------------------------------------------------------------------------------
function opSetShipDurabilityAndSailor(user, tryData, changes, shipId, durability, sailor) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    const ship = tryData.fleets.getShip(shipId);
    if (ship.getNub().durability === durability && ship.getNub().sailor === sailor) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (ship.getNub().durability !== durability) {
        ship.setDurability(durability, null, null);
        lodash_1.default.merge(changes.syncAdd, {
            ships: {
                [shipId]: {
                    id: shipId,
                    durability,
                },
            },
        });
    }
    if (ship.getNub().sailor !== sailor) {
        ship.setSailor(sailor, null, false, null);
        lodash_1.default.merge(changes.syncAdd, {
            ships: {
                [shipId]: {
                    id: shipId,
                    sailor,
                },
            },
        });
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetShipDurabilityAndSailor = opSetShipDurabilityAndSailor;
// -------------------------------------------------------------------------------------------------
function opSetShipLife(user, tryData, changes, shipId, life) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const ship = tryData.fleets.getShip(shipId);
    if (ship.getNub().life === life) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // 수명으로 인한 최대 내구도 감소된 상태에서 현재 내구도가 크다면 최대 내구도로 변경된다
    if (ship.getNub().life !== 0 && life === 0) {
        const expectMaxDurability = Math.floor(ship.getMaxDurability(tryData.stats) * 0.5);
        if (ship.getNub().durability > expectMaxDurability) {
            ship.setDurability(expectMaxDurability, null, null);
            lodash_1.default.merge(changes.syncAdd, {
                ships: {
                    [shipId]: {
                        id: shipId,
                        durability: expectMaxDurability,
                    },
                },
            });
        }
    }
    ship.setLife(life, null, tryData.stats);
    lodash_1.default.merge(changes.syncAdd, {
        ships: {
            [shipId]: {
                id: shipId,
                life,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetShipLife = opSetShipLife;
// -------------------------------------------------------------------------------------------------
function opSetVillageLastDepartureTimeUtc(user, tryData, changes, villageCmsId, lastDepartureTimeUtc) {
    if (!tryData.village) {
        tryData.village = user.userVillage.clone();
    }
    const village = tryData.village.getVillage(villageCmsId);
    village.lastDepartureTimeUtc = lastDepartureTimeUtc;
    lodash_1.default.merge(changes.syncAdd, {
        villages: {
            [villageCmsId]: village,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetVillageLastDepartureTimeUtc = opSetVillageLastDepartureTimeUtc;
// -------------------------------------------------------------------------------------------------
function opSetVillageFriendshipRewarded(user, tryData, changes, villageCmsId, grade, bIsWeeklyReward) {
    if (!tryData.village) {
        tryData.village = user.userVillage.clone();
    }
    const village = tryData.village.getVillage(villageCmsId);
    if (bIsWeeklyReward) {
        village.lastReceiveFriendshipWeeklyRewardGrade = grade;
        village.lastReceiveFriendshipWeeklyRewardTimeUtc = mutil.curTimeUtc();
    }
    else {
        village.friendshipFirstRewardReceiveBitflag += 1 << grade;
    }
    lodash_1.default.merge(changes.syncAdd, {
        villages: {
            [villageCmsId]: village,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetVillageFriendshipRewarded = opSetVillageFriendshipRewarded;
// -------------------------------------------------------------------------------------------------
function opApplyShipCargoChange(user, tryData, changes, cargoChange) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    const ship = tryData.fleets.getShip(cargoChange.shipId);
    if (ship.getCargoQuantity(cargoChange.cmsId) === cargoChange.quantity) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    ship.applyCargoChange(cargoChange, null, null);
    lodash_1.default.merge(changes.syncAdd, {
        ships: {
            [cargoChange.shipId]: {
                id: cargoChange.shipId,
                cargos: {
                    [cargoChange.cmsId]: cargoChange,
                },
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyShipCargoChange = opApplyShipCargoChange;
// -------------------------------------------------------------------------------------------------
function opApplyInsuranceUnpaidChange(user, tryData, changes, insuranceUnpaidChange) {
    if (!tryData.points) {
        tryData.points = user.userPoints.clone();
    }
    const oldInsurance = tryData.points.getInsurance();
    if (oldInsurance.unpaidDucat === insuranceUnpaidChange.unpaidDucat &&
        oldInsurance.unpaidSailor === insuranceUnpaidChange.unpaidSailor &&
        oldInsurance.unpaidShip === insuranceUnpaidChange.unpaidShip &&
        oldInsurance.unpaidTradeGoods === insuranceUnpaidChange.unpaidTradeGoods) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.points.applyInsuranceUnpaidChange(insuranceUnpaidChange);
    lodash_1.default.merge(changes.syncAdd, {
        insurance: insuranceUnpaidChange,
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyInsuranceUnpaidChange = opApplyInsuranceUnpaidChange;
// -------------------------------------------------------------------------------------------------
function opSetQuestFlags(user, tryData, changes, questCmsId, uflags, lflags) {
    if (!tryData.questManager) {
        tryData.questManager = user.questManager.clone();
    }
    const oldQuest = tryData.questManager.getContext(questCmsId);
    if (oldQuest.uflags === uflags && oldQuest.lflags === lflags) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    user.questManager.applyCtxChange({
        cmsId: questCmsId,
        uflags,
        lflags,
    });
    lodash_1.default.merge(changes.syncAdd, {
        questData: {
            contexts: {
                [questCmsId]: {
                    cmsId: questCmsId,
                    uflags,
                    lflags,
                },
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetQuestFlags = opSetQuestFlags;
// -------------------------------------------------------------------------------------------------
function opApplyItemChange(user, tryData, changes, itemChange) {
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const accums = [];
    lodash_1.default.merge(changes.syncAdd, tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add);
    if (accums && accums.length > 0) {
        if (!changes.itemGainAccumParams) {
            changes.itemGainAccumParams = [];
        }
        changes.itemGainAccumParams.push(...accums.map((acc) => {
            return { targets: acc.targets, addedValue: acc.addedValue };
        }));
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyItemChange = opApplyItemChange;
// -------------------------------------------------------------------------------------------------
function opBattleLogEnd(user, tryData, changes, battleId) {
    changes.battleIdForLogEnd = battleId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opBattleLogEnd = opBattleLogEnd;
// -------------------------------------------------------------------------------------------------
function opSetChallenge(user, tryData, changes, challenge) {
    if (!tryData.challenge) {
        tryData.challenge = user.userChallenges.clone();
    }
    if (lodash_1.default.isEqual(challenge, tryData.challenge.getChallenge(challenge.cmsId))) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.challenge.updateChallenge(challenge);
    lodash_1.default.merge(changes.syncAdd, {
        challenges: {
            [challenge.cmsId]: challenge,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetChallenge = opSetChallenge;
// -------------------------------------------------------------------------------------------------
function opDropQuest(user, tryData, changes, questCmsId) {
    if (!tryData.questManager) {
        tryData.questManager = user.questManager.clone();
    }
    if (!tryData.questManager.getContext(questCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.questManager.dropQuest(questCmsId);
    lodash_1.default.merge(changes.syncRemove, {
        questData: {
            contexts: {
                [questCmsId]: true,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDropQuest = opDropQuest;
// -------------------------------------------------------------------------------------------------
// 수정시 UserQuestManager의 questDrop도 확인 필요
function opQuestDropByReturn(user, tryData, changes, curTimeUtc) {
    if (!tryData.questManager) {
        tryData.questManager = user.questManager.clone();
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const dropQuestCmsIds = [];
    const royalOrderQuestCtxs = [];
    const notRoyalOrderQuestCtxs = [];
    lodash_1.default.forOwn(tryData.questManager.contexts, (elem) => {
        const questCms = cms_1.default.Quest[elem.cmsId];
        if (!(questCms === null || questCms === void 0 ? void 0 : questCms.isDropByReturn)) {
            return;
        }
        // 명성 페널티
        // 어드민 일시 정지된 상태에 한해서 패널티를 적용하지 않는다.
        if (!elem.isAdminPaused) {
            if (questCms.category === cmsEx.QUEST_CATEGORY.ROYAL_ORDER) {
                royalOrderQuestCtxs.push(elem);
            }
            else {
                notRoyalOrderQuestCtxs.push(elem);
            }
        }
        if (questCms.category === cmsEx.QUEST_CATEGORY.ROYAL_ORDER && !questCms.isRoyalTitle) {
            // 칙명 취소하면 다음날까지 못함
            tryData.questManager.setLastRoyalOrderCompletedTimeUtc(curTimeUtc);
            lodash_1.default.merge(changes.syncAdd, {
                user: {
                    lastRoyalOrderCompletedTimeUtc: curTimeUtc,
                },
            });
        }
        // 종업원 퀘스트 포기 쿨타임
        if (questCms.category === cmsEx.QUEST_CATEGORY.PUB_STAFF) {
            if (!changes.pubStaffChanges) {
                changes.pubStaffChanges = [];
            }
            const townCmsId = cmsEx.getTownCmsIdForPubStaffQuestCmsId(questCms.id);
            const pubStaffChange = lodash_1.default.cloneDeep(user.userTown.getPubStaff(townCmsId));
            if (!pubStaffChange) {
                return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
            }
            pubStaffChange.questBlockTimeUtc = curTimeUtc + cms_1.default.Const.PubstaffQuestCooltimeSec.value;
            changes.pubStaffChanges.push(pubStaffChange);
        }
        // 퀘스트 포기로 인한 아이템 제거
        if (questCms.clearItemIds && questCms.clearItemIds.length > 0) {
            const itemChanges = tryData.inven.itemInven.buildItemChangesForRemovingAll(new Set(questCms.clearItemIds), false);
            for (const itemChange of itemChanges) {
                lodash_1.default.merge(changes.syncAdd, tryData.inven.itemInven.applyItemChange(itemChange, null, null).add);
            }
        }
        if (!changes.questDropForGlogs) {
            changes.questDropForGlogs = [];
        }
        changes.questDropForGlogs.push({ questCmsId: questCms.id, step: elem.nodeIdx });
        dropQuestCmsIds.push(questCms.id);
    });
    // 칙명 먼저 계산 후 나머지 계산
    const leaderMate = tryData.mates.getLeaderMate(tryData.fleets);
    for (const ctx of royalOrderQuestCtxs.concat(notRoyalOrderQuestCtxs)) {
        const questCms = cms_1.default.Quest[ctx.cmsId];
        const mateChange = questUtil_1.QuestUtil.calcDropQuestMateFame(leaderMate, questCms);
        leaderMate.applyFameByMateChange(mateChange, { add: changes.syncAdd }, null);
    }
    for (const questCmsId of dropQuestCmsIds) {
        tryData.questManager.dropQuest(questCmsId);
        lodash_1.default.merge(changes.syncRemove, {
            questData: {
                contexts: {
                    [questCmsId]: true,
                },
            },
        });
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opQuestDropByReturn = opQuestDropByReturn;
// -------------------------------------------------------------------------------------------------
function opSetBattleEndResult(user, tryData, changes, battleEndResult) {
    changes.syncAdd.battleEndResult = battleEndResult;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetBattleEndResult = opSetBattleEndResult;
// -------------------------------------------------------------------------------------------------
function opDismantleShip(user, tryData, changes, shipId, firedSailors) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    const userShip = tryData.fleets.getShip(shipId);
    if (!userShip) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    // 기함은 분해 못 함.
    if (userShip.getNub().formationIndex === cmsEx.FlagShipFormationIndex) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    // 잠긴 선박은 분해 못함.
    if (userShip.getNub().isLocked === 1) {
        return userChangeTask_1.CHANGE_TASK_RESULT.LOCKED_SHIP;
    }
    if (!changes.shipIdsToDismantle) {
        changes.shipIdsToDismantle = [];
    }
    changes.shipIdsToDismantle.push(shipId);
    if (firedSailors > 0) {
        if (!changes.firedSailors) {
            changes.firedSailors = 0;
        }
        changes.firedSailors += firedSailors;
    }
    // 부품 해제
    if (!changes.unequippedShipSlotItemIds) {
        changes.unequippedShipSlotItemIds = [];
    }
    const bpCms = cms_1.default.Ship[userShip.getNub().cmsId].shipBlueprint;
    for (const slot of bpCms.shipSlot) {
        const defaultSlotCms = cms_1.default.ShipSlot[slot.Id];
        if (defaultSlotCms.slotType === shipSlotDesc_1.SHIP_SLOT_TYPE.MATE ||
            defaultSlotCms.slotType === shipSlotDesc_1.SHIP_SLOT_TYPE.NON_MATE) {
            continue;
        }
        const shipSlot = userShip.getSlot(slot.Index);
        if (!shipSlot || !shipSlot.shipSlotItemId) {
            continue;
        }
        changes.unequippedShipSlotItemIds.push(shipSlot.shipSlotItemId);
    }
    // 인벤 공간이 부족하면 부품을 해제 할 수 없다
    if (!user.userInven.canAddShipSlotItem(changes.unequippedShipSlotItemIds.length)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.SHIP_SLOT_INVEN_FULL;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDismantleShip = opDismantleShip;
// -------------------------------------------------------------------------------------------------
function opAddUserShipBuildingExp(user, tryData, changes, cultureType, addedExp) {
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    const userShipBuildingChange = tryData.shipBlueprints.buildUserShipBuildingChange(cultureType, addedExp);
    // 만렙인 경우 null.
    if (userShipBuildingChange === null) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.shipBlueprints.setUserShipBuildingExpLevel(userShipBuildingChange);
    lodash_1.default.merge(changes.syncAdd, {
        user: {
            westShipBuildLevel: userShipBuildingChange.westShipBuildLevel,
            westShipBuildExp: userShipBuildingChange.westShipBuildExp,
            orientShipBuildLevel: userShipBuildingChange.orientShipBuildLevel,
            orientShipBuildExp: userShipBuildingChange.orientShipBuildExp,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddUserShipBuildingExp = opAddUserShipBuildingExp;
// -------------------------------------------------------------------------------------------------
function opSetShipSailor(user, tryData, changes, shipId, sailor) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    const ship = tryData.fleets.getShip(shipId);
    if (ship.getNub().sailor === sailor) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    ship.setSailor(sailor, null, false, null);
    lodash_1.default.merge(changes.syncAdd, {
        ships: {
            [shipId]: {
                id: shipId,
                sailor,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetShipSailor = opSetShipSailor;
// -------------------------------------------------------------------------------------------------
function opSetShipFormationIndex(user, tryData, changes, shipId, formationIndex) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    const ship = tryData.fleets.getShip(shipId);
    if (ship.getNub().formationIndex === formationIndex) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    ship.setFormationIndex(formationIndex, null);
    lodash_1.default.merge(changes.syncAdd, {
        ships: {
            [shipId]: {
                id: shipId,
                formationIndex,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetShipFormationIndex = opSetShipFormationIndex;
// -------------------------------------------------------------------------------------------------
// ! operator 에 비지니스 로직이 들어가면 안되지만,
// ! 리워드 시스템에서 적용이 어려움이 있어 예외적으로 로직이 들어가 있습니다.
// ! 우편 지급은 문제가 있음.
// - 예) 수령 전 우편 아이콘 '상급 편지', 수령 시 퀘스트 뽑기 실패 등으로 '빈 편지(대체 아이템)'
// @param bAllowAddToLimitIfExceeded 대체 아이템에 적용되는 옵션인 것 참고
function opAddQuestItem(user, tryData, changes, itemCmsId, amount, bAllowOverInven, bAllowAddToLimitIfExceeded) {
    const itemCms = cms_1.default.Item[itemCmsId];
    if (!itemCms || itemCms.type !== itemDesc_1.ITEM_TYPE.RANDOM_QUEST) {
        return userChangeTask_1.CHANGE_TASK_RESULT.CMS_ERROR;
    }
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    else if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    // try data
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    if (!tryData.questManager) {
        tryData.questManager = user.questManager.clone();
    }
    const addReplacementItem = (repItemCmsId, repCount) => {
        (0, assert_1.default)(repCount > 0);
        const replacementItemCms = cms_1.default.Item[repItemCmsId];
        if (!replacementItemCms) {
            mlog_1.default.error('[RewardTask] opAddQuestItem. Invalid itemCmsId(replacement item)', {
                repItemCmsId,
            });
            return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
        }
        if (replacementItemCms.type === itemDesc_1.ITEM_TYPE.RANDOM_QUEST) {
            // 대체 아이템은 '퀘스트 시작 아이템'이 아니어야 함.
            mlog_1.default.error('[RewardTask] opAddQuestItem. Replacement item can not be quest starting item.', {
                repItemCmsId,
            });
            return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
        }
        const accums = [];
        const bIsBound = true;
        const result = tryData.inven.itemInven.calcReceivable(repItemCmsId, repCount, bIsBound, bAllowOverInven);
        if (result.receivable !== repCount) {
            if (bAllowAddToLimitIfExceeded) {
                repCount = result.receivable;
                if (repCount === 0) {
                    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
                }
            }
            else {
                mlog_1.default.warn('[RewardTask] opAddQuestItem. Exceeds limit(replacement item).', {
                    userId: user.userId,
                    repItemCmsId,
                    toAdd: repCount,
                    receivableResult: result,
                });
                return result.excessMaxHavable
                    ? userChangeTask_1.CHANGE_TASK_RESULT.ITEM_MAX_HAVABLE
                    : result.excessSpace
                        ? userChangeTask_1.CHANGE_TASK_RESULT.INVEN_FULL
                        : userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
            }
        }
        const itemChange = tryData.inven.itemInven.buildItemChange(repItemCmsId, repCount, true, bIsBound);
        lodash_1.default.merge(changes.syncAdd, tryData.inven.itemInven.applyItemChange(itemChange, accums, null).add);
        if (accums && accums.length > 0) {
            if (!changes.itemGainAccumParams) {
                changes.itemGainAccumParams = [];
            }
            changes.itemGainAccumParams.push(...accums.map((acc) => {
                return { targets: acc.targets, addedValue: acc.addedValue };
            }));
        }
        mlog_1.default.verbose('[RewardTask] opAddQuestItem. quest-starting-item replaced', {
            repItemCmsId,
            repCount,
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.OK;
    };
    //
    const result = tryData.inven.itemInven.calcQuestItemReceivable(itemCmsId, amount, bAllowOverInven);
    if (result.receivable !== amount) {
        mlog_1.default.verbose('[RewardTask] opAddQuestItem. Exceeds limit.', {
            userId: user.userId,
            itemCmsId,
            curCount: tryData.inven.itemInven.getQuestItemCount(itemCmsId),
            toAdd: amount,
            receivableResult: result,
        });
        // 소지 정책으로 인해 받을 수 없는 초과분은 대체 아이템으로 지급한다.
        const excessAmount = amount - result.receivable;
        if (excessAmount > 0) {
            const res = addReplacementItem(cms_1.default.Const.QuestStartItemReplaceItemId.value, excessAmount);
            if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
                return res;
            }
        }
        amount = result.receivable;
        if (amount === 0) {
            return userChangeTask_1.CHANGE_TASK_RESULT.OK;
        }
    }
    // quest 를 선택하기 위해서는 user가 필요한데, 여기서는 예외적으로 changeTask 영향을 받기 전의 user를 사용한다.
    // (치명적이지 않기 때문에)
    // 다만, 퀘스트 완료 같은 operator 가 추가된다면 uniqueGroup 관련해서 문제 없을지 확인이 필요할 듯 함.
    const arrPickedQuest = [];
    questUtil_1.QuestUtil.pickQuestItemQuestUsingUser(itemCms.id, user, new Set(lodash_1.default.map(tryData.questManager.contexts, (v) => v.cmsId)), true, amount, arrPickedQuest, false /* bEnsure */);
    for (let i = 0; i < amount; i++) {
        const pickedQuest = arrPickedQuest[i];
        if (!pickedQuest) {
            break;
        }
        const id = tryData.inven.itemInven.getNewQuestItemId();
        const questItem = {
            id,
            cmsId: itemCmsId,
            questCmsId: pickedQuest.questCmsId,
            questRnds: pickedQuest.rnds,
        };
        tryData.inven.itemInven.addQuestItem(questItem);
        lodash_1.default.merge(changes.syncAdd, {
            questItems: {
                [id]: questItem,
            },
        });
        changes.lastQuestItemId = id;
    }
    const failedCntForPickingQuest = amount - arrPickedQuest.length;
    if (failedCntForPickingQuest > 0) {
        mlog_1.default.verbose('[RewardTask] opAddQuestItem. failed to pick quest.', {
            userId: user.userId,
            itemCmsId,
            failedCntForPickingQuest,
        });
        // 퀘스트 뽑기에 실패한 경우, 실패한 만큼 대체 아이템 지급
        const res = addReplacementItem(cms_1.default.Const.QuestStartItemReplaceItemId.value, failedCntForPickingQuest);
        if (res > userChangeTask_1.CHANGE_TASK_RESULT.OK_MAX) {
            return res;
        }
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddQuestItem = opAddQuestItem;
// -------------------------------------------------------------------------------------------------
function opSetLineMailState(user, tryData, changes, mailId, state) {
    if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
    }
    // validate
    const mail = tryData.mails.getUserLineMail(mailId);
    if (!mail) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (mail.state === state) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    // save
    tryData.mails.setLineMailState(mailId, state);
    lodash_1.default.merge(changes.syncAdd, {
        lineMails: {
            [mailId]: {
                id: mailId,
                state,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetLineMailState = opSetLineMailState;
// -------------------------------------------------------------------------------------------------
function opConsumeShield(user, tryData, changes, shieldCmsId, curTimeUtc, userLevel) {
    if (!tryData.shield) {
        tryData.shield = user.userShield.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.shield.hasShield(shieldCmsId, curTimeUtc, userLevel, user.companyStat)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_SHIELD;
    }
    const change = tryData.shield.buildShieldChangeWithConsume(shieldCmsId, curTimeUtc, userLevel, false, tryData.stats);
    tryData.shield.applyShieldChange(change, null);
    lodash_1.default.merge(changes.syncAdd, {
        shields: {
            [change.cmsId]: change,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opConsumeShield = opConsumeShield;
// -------------------------------------------------------------------------------------------------
function opAddShieldNonPurchaseCount(user, tryData, changes, shieldCmsId, amount) {
    if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    else if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.shield) {
        tryData.shield = user.userShield.clone();
    }
    if (!tryData.userLevel) {
        tryData.userLevel = user.level;
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const curTimeUtc = mutil.curTimeUtc();
    const change = tryData.shield.buildShieldChange(shieldCmsId, curTimeUtc, tryData.userLevel, tryData.stats);
    change.nonPurchaseCount += amount;
    tryData.shield.applyShieldChange(change, null);
    lodash_1.default.merge(changes.syncAdd, {
        shields: {
            [change.cmsId]: change,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShieldNonPurchaseCount = opAddShieldNonPurchaseCount;
// -------------------------------------------------------------------------------------------------
function opAddShieldPurchaseCount(user, tryData, changes, shieldCmsId, amount) {
    if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    else if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.shield) {
        tryData.shield = user.userShield.clone();
    }
    if (!tryData.userLevel) {
        tryData.userLevel = user.level;
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const curTimeUtc = mutil.curTimeUtc();
    const change = tryData.shield.buildShieldChange(shieldCmsId, curTimeUtc, tryData.userLevel, tryData.stats);
    change.purchaseCount += amount;
    tryData.shield.applyShieldChange(change, null);
    lodash_1.default.merge(changes.syncAdd, {
        shields: {
            [change.cmsId]: change,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShieldPurchaseCount = opAddShieldPurchaseCount;
// -------------------------------------------------------------------------------------------------
function opAddPalaceTaxFreePermit(user, tryData, changes, taxFreePermitCmsId, amount) {
    if (amount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    else if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    const taxFreePermitCms = cms_1.default.TaxFreePermit[taxFreePermitCmsId];
    if (!taxFreePermitCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (cmsEx.getTaxFreePermitCashShopCmsId(taxFreePermitCmsId)) {
        mlog_1.default.error('[RewardTask] opAddTaxFreePermit. can not reward taxFreePermit of cashShop', {
            userId: user.userId,
            taxFreePermitCmsId,
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.trade) {
        tryData.trade = user.userTrade.clone();
    }
    const timeSecToAdd = taxFreePermitCms.expiredTimeValue * amount;
    const curTimeUtc = mutil.curTimeUtc(); // 다른 operator 들도 따로 인자로 넣어주진 않긴 한데, 리팩토링시 참고
    const curExpirationTimeUtc = tryData.trade.getTaxFreePermitExpiration(taxFreePermitCmsId);
    const newExpirationTimeUtc = curExpirationTimeUtc && curExpirationTimeUtc > curTimeUtc // 면세증을 갖고 있고, 만료되지 않았는지
        ? curExpirationTimeUtc + timeSecToAdd
        : curTimeUtc + timeSecToAdd;
    if (newExpirationTimeUtc > cmsEx.TheEndTimeUtc) {
        // cmsEx.EndTimeUtc 가 DB 의 최대치를 의미하진 않는 듯한데..
        mlog_1.default.error('[RewardTask] opAddTaxFreePermit. Invalid expirationTimeUtc.', {
            userId: user.userId,
            newExpirationTimeUtc,
            amount,
        });
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    //* 보상으로 받을 때는 구매할 때와 다르게 최대 일수 제한을 적용하지 않음.
    // if (false) {
    //   if (
    //     newExpirationTimeUtc - curTimeUtc >=
    //     cms.Const.CashShopDurationDaysLimit.value * SECONDS_PER_DAY
    //   ) {
    //     return CHANGE_TASK_RESULT; // NOTHING? FULL?
    //   }
    // }
    tryData.trade.setTaxFreePermitExpiration(taxFreePermitCmsId, newExpirationTimeUtc);
    lodash_1.default.merge(changes.syncAdd, {
        taxFreePermits: {
            [taxFreePermitCmsId]: {
                cmsId: taxFreePermitCmsId,
                expirationTimeUtc: newExpirationTimeUtc,
            },
        },
    });
    if (!changes.addedPalaceTaxFreePermitCounts) {
        changes.addedPalaceTaxFreePermitCounts = {};
    }
    if (!changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId]) {
        changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId] = amount;
    }
    else {
        changes.addedPalaceTaxFreePermitCounts[taxFreePermitCmsId] += amount;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddPalaceTaxFreePermit = opAddPalaceTaxFreePermit;
// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
function opDeleteContributionShopRestrictedProduct(user, tryData, changes, cmsId) {
    if (changes.syncAdd.contributionShopRestrictedProducts) {
        assert_1.default.fail('do-not-use-after-change-contributionShopRestrictedProducts');
        // contributionShopRestrictedProducts 를 변경하기 전에 사용해야 합니다.
        // 간단하게만 사용할 듯 해서 편의상, 각 상황에 대한 처리를 구현하지 않고 순서를 정했습니다.
    }
    if (!changes.syncRemove.contributionShopRestrictedProducts) {
        changes.syncRemove.contributionShopRestrictedProducts = [];
    }
    changes.syncRemove.contributionShopRestrictedProducts.push(cmsId.toString());
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteContributionShopRestrictedProduct = opDeleteContributionShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
function opSetContributionShopRestrictedProduct(user, tryData, changes, restrictedProduct) {
    if (!changes.syncAdd.contributionShopRestrictedProducts) {
        changes.syncAdd.contributionShopRestrictedProducts = {};
    }
    changes.syncAdd.contributionShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetContributionShopRestrictedProduct = opSetContributionShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
/**
 *! DB 에서 DELETE 문으로 제거하고 있는데, TPMUWO-441 같은 데드락 사례가 있던 것 참고해주세요.
 */
function opDeleteGuildShopRestrictedProduct(user, tryData, changes, cmsId) {
    if (changes.syncAdd.guildShopRestrictedProducts) {
        assert_1.default.fail('do-not-use-after-change-guildShopRestrictedProducts');
        // guildShopRestrictedProducts 를 변경하기 전에 사용해야 합니다.
        // 간단하게만 사용할 듯 해서 편의상, 각 상황에 대한 처리를 구현하지 않고 순서를 정했습니다.
    }
    if (!changes.syncRemove.guildShopRestrictedProducts) {
        changes.syncRemove.guildShopRestrictedProducts = [];
    }
    changes.syncRemove.guildShopRestrictedProducts.push(cmsId.toString());
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteGuildShopRestrictedProduct = opDeleteGuildShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
function opSetGuildShopRestrictedProduct(user, tryData, changes, restrictedProduct) {
    if (!changes.syncAdd.guildShopRestrictedProducts) {
        changes.syncAdd.guildShopRestrictedProducts = {};
    }
    changes.syncAdd.guildShopRestrictedProducts[restrictedProduct.cmsId] = restrictedProduct;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetGuildShopRestrictedProduct = opSetGuildShopRestrictedProduct;
// -------------------------------------------------------------------------------------------------
function opLandExploreAchievement(user, tryData, changes, landExploreCmsId) {
    if (!landExploreCmsId) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    changes.landExploreCmsId = landExploreCmsId;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opLandExploreAchievement = opLandExploreAchievement;
// -------------------------------------------------------------------------------------------------
function opAddDiscovery(user, tryData, changes, discoveryCmsId) {
    if (!discoveryCmsId) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.userDiscovery) {
        tryData.userDiscovery = user.userDiscovery.clone();
    }
    // discovery update
    tryData.userDiscovery.discover(discoveryCmsId, user, true);
    if (!changes.discoveryCmsIds) {
        changes.discoveryCmsIds = [];
    }
    changes.discoveryCmsIds.push(discoveryCmsId);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddDiscovery = opAddDiscovery;
// -------------------------------------------------------------------------------------------------
function opAddArenaTicket(user, tryData, changes, amount) {
    if (amount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (amount < 0) {
        // 지원하지 않는다.
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (changes.addedArenaTicket === undefined) {
        changes.addedArenaTicket = amount;
    }
    else {
        changes.addedArenaTicket += amount;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddArenaTicket = opAddArenaTicket;
// -------------------------------------------------------------------------------------------------
function opSetPassEventLastDailyResetTimeUtc(user, tryData, changes, eventPageCmsId, lastDailyResetTimeUtc) {
    const passEventPageCms = cms_1.default.EventPage[eventPageCmsId];
    if (!passEventPageCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (passEventPageCms.type !== eventPageDesc_1.EventPageType.PASS_EVENT) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.passEvent) {
        tryData.passEvent = user.userPassEvent.clone();
    }
    tryData.passEvent.setPassEventLastDailyResetTimeUtc(passEventPageCms.id, lastDailyResetTimeUtc);
    if (!changes.passEvents) {
        changes.passEvents = {};
    }
    if (!changes.passEvents[passEventPageCms.id]) {
        changes.passEvents[passEventPageCms.id] = {
            eventPageCmsId: passEventPageCms.id,
        };
    }
    changes.passEvents[passEventPageCms.id].lastDailyResetTimeUtc = lastDailyResetTimeUtc;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetPassEventLastDailyResetTimeUtc = opSetPassEventLastDailyResetTimeUtc;
// -------------------------------------------------------------------------------------------------
// 리워드 시스템으로 지급하려다 안하게 된거라 리워드 시스템을 위한 잔재(굳이 필요없을 검사 등)가 남아있는데,
// 추후에 아예 ReceivePassEventMissionRewardSpec 에서만 사용되는 것으로 한정하는 게 나을 듯함.
function opAddPassEventExp(user, tryData, changes, eventPageCmsId, expAmount, curTimeUtc) {
    if (!Number.isInteger(expAmount)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    else if (expAmount < 0) {
        // 지원하지 않는다.
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!Number.isInteger(curTimeUtc)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const passEventPageCms = cms_1.default.EventPage[eventPageCmsId];
    if (!passEventPageCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (passEventPageCms.type !== eventPageDesc_1.EventPageType.PASS_EVENT) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (expAmount === 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.passEvent) {
        tryData.passEvent = user.userPassEvent.clone();
    }
    if (userPassEvent_1.default.isOutOfPeriodRewardTimeIncluded(curTimeUtc, passEventPageCms)) {
        mlog_1.default.info('opAddPassEventExp ignored. event page expired', {
            userId: user.userId,
            eventPageCmsId: passEventPageCms.id,
            curTimeUtc,
        });
        // 에러가 나을지?
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (user.userPassEvent.isPassEventCompleted(passEventPageCms.id)) {
        // 굳이 막는다면 operator 실행 순서에 따라서 기대결과랑 다르게 동작할 수 있을 법한 부분 고민해봐야할 듯?
        // 관련해서 일단 tryData 가 아닌 user 에서 이미 완료됐었는지를 가져오고 로그만 남김.
        mlog_1.default.info('opAddPassEventExp operated on completed pass event', {
            userId: user.userId,
            eventPageCmsId: passEventPageCms.id,
        });
    }
    const maxPassEventExp = cmsEx.getMaxPassEventExp(passEventPageCms);
    const { exp: oldAccumulatedExp } = tryData.passEvent.getPassEventExpLevel(passEventPageCms);
    if (oldAccumulatedExp >= maxPassEventExp) {
        // 다른 EXP 들이 최대치만큼만 지급하고 있고, 동일한 정책이라고 함.
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    const newAccumulatedExp = Math.min(oldAccumulatedExp + expAmount, maxPassEventExp);
    if (!changes.passEvents) {
        changes.passEvents = {};
    }
    if (!changes.passEvents[passEventPageCms.id]) {
        changes.passEvents[passEventPageCms.id] = {
            eventPageCmsId: passEventPageCms.id,
        };
    }
    if (!changes.passEvents[passEventPageCms.id].expChange) {
        changes.passEvents[passEventPageCms.id].expChange = {
            exp: undefined,
            level: undefined,
            addedExp: 0,
        };
    }
    const change = changes.passEvents[passEventPageCms.id].expChange;
    change.exp = newAccumulatedExp;
    change.level = cmsEx.calcPassEventLevel(passEventPageCms, newAccumulatedExp);
    change.addedExp += newAccumulatedExp - oldAccumulatedExp;
    tryData.passEvent.setPassEventExpLevel(passEventPageCms, change.exp, change.level, null);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddPassEventExp = opAddPassEventExp;
// -------------------------------------------------------------------------------------------------
function opApplyExploreTicketChange(user, tryData, changes, ticketChange) {
    if (!ticketChange) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.explore) {
        tryData.explore = user.userExplore.clone();
    }
    lodash_1.default.merge(changes.syncAdd, tryData.explore.applyTicketChange(ticketChange, null).add);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyExploreTicketChange = opApplyExploreTicketChange;
// -------------------------------------------------------------------------------------------------
function opApplyExploreQuickModeChange(user, tryData, changes, quickModeChange) {
    if (!quickModeChange) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.explore) {
        tryData.explore = user.userExplore.clone();
    }
    lodash_1.default.merge(changes.syncAdd, tryData.explore.applyQuickModeChange(quickModeChange, null).add);
}
exports.opApplyExploreQuickModeChange = opApplyExploreQuickModeChange;
// -------------------------------------------------------------------------------------------------
function opApplyTradeArea(user, tryData, changes, tradeAreaChange) {
    if (!tryData.tradeArea) {
        tryData.tradeArea = user.userTradeArea.clone();
    }
    tryData.tradeArea.applyChange(tradeAreaChange, undefined);
    if (!changes.syncAdd.tradeArea) {
        changes.syncAdd.tradeArea = {};
    }
    changes.syncAdd.tradeArea[tradeAreaChange.tradeAreaCmsId] = tradeAreaChange;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyTradeArea = opApplyTradeArea;
// -------------------------------------------------------------------------------------------------
function opSetEventGame(user, tryData, changes, eventGame) {
    if (!changes.syncAdd.eventGames) {
        changes.syncAdd.eventGames = {};
    }
    if (!changes.syncAdd.eventGames[eventGame.eventPageCmsId]) {
        changes.syncAdd.eventGames[eventGame.eventPageCmsId] = {};
    }
    changes.syncAdd.eventGames[eventGame.eventPageCmsId][eventGame.eventGameCmsId] = eventGame;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetEventGame = opSetEventGame;
// -------------------------------------------------------------------------------------------------
function opAddShipBlueprintSailMasteryExp(user, tryData, changes, addedExp) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!changes.syncAdd.shipBlueprints) {
        changes.syncAdd.shipBlueprints = {};
    }
    if (!changes.bpSailMasteryforGlog) {
        changes.bpSailMasteryforGlog = {};
    }
    const bpSailMasteryExpChanges = {};
    const firstFleetShips = tryData.fleets.getFirstFleet().getShips();
    for (const ship of lodash_1.default.values(firstFleetShips)) {
        const shipBlueprintCmsId = cms_1.default.Ship[ship.getNub().cmsId].shipBlueprintId;
        const userBP = tryData.shipBlueprints.getUserShipBlueprint(shipBlueprintCmsId);
        // 설계도 없는 선박은 제외
        if (!userBP) {
            continue;
        }
        // 함대에 같은 설계도 선박이 여러대 있어도 경험치는 한번만 지급
        if (bpSailMasteryExpChanges[userBP.cmsId]) {
            continue;
        }
        const bluePrintCms = cms_1.default.ShipBlueprint[userBP.cmsId];
        if (!bluePrintCms.masteryAble) {
            continue;
        }
        const expChanges = userBP.calcSailMasteryExpLevel(addedExp, user);
        if (!expChanges) {
            continue;
        }
        bpSailMasteryExpChanges[userBP.cmsId] = expChanges;
        changes.bpSailMasteryforGlog[userBP.cmsId] = {
            cmsId: expChanges.cmsId,
            oldLevel: userBP.sailMasteryLevel,
            oldExp: userBP.sailMasteryExp,
            level: expChanges.sailMasteryLevel,
            exp: expChanges.sailMasteryExp,
            addedExp: expChanges.sailMasteryExp - userBP.sailMasteryExp,
        };
    }
    for (const change of lodash_1.default.values(bpSailMasteryExpChanges)) {
        tryData.shipBlueprints.setUserShipBlueprintSailMastery(change.cmsId, change.sailMasteryLevel, change.sailMasteryExp, tryData.stats, null);
        lodash_1.default.merge(changes.syncAdd, {
            shipBlueprints: {
                [change.cmsId]: {
                    cmsId: change.cmsId,
                    sailMasteryLevel: change.sailMasteryLevel,
                    sailMasteryExp: change.sailMasteryExp,
                },
            },
        });
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShipBlueprintSailMasteryExp = opAddShipBlueprintSailMasteryExp;
function opSetFleetDispatchLifeRemain(user, tryData, changes, fleetIndex, lifeRemain) {
    if (!changes.fleetDispatchLifeRemains) {
        changes.fleetDispatchLifeRemains = {};
    }
    changes.fleetDispatchLifeRemains[fleetIndex] = lifeRemain;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetFleetDispatchLifeRemain = opSetFleetDispatchLifeRemain;
// -------------------------------------------------------------------------------------------------
function opSetFleetDispatchEndView(user, tryData, changes, fleetIndex, state, actions, endView) {
    if (!changes.syncAdd.fleetDispatches) {
        changes.syncAdd.fleetDispatches = {};
    }
    if (!changes.syncAdd.fleetDispatches[fleetIndex]) {
        changes.syncAdd.fleetDispatches[fleetIndex] = { fleetIndex };
    }
    changes.syncAdd.fleetDispatches[fleetIndex].state = state;
    changes.syncAdd.fleetDispatches[fleetIndex].actions = actions;
    changes.syncAdd.fleetDispatches[fleetIndex].endView = endView;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetFleetDispatchEndView = opSetFleetDispatchEndView;
// -------------------------------------------------------------------------------------------------
function opSetFleetDispatchReward(user, tryData, changes, fleetIndex, rewardResult, lostRewardResult, lostRewardIdCount) {
    if (!changes.syncAdd.fleetDispatches) {
        changes.syncAdd.fleetDispatches = {};
    }
    if (!changes.syncAdd.fleetDispatches[fleetIndex]) {
        changes.syncAdd.fleetDispatches[fleetIndex] = { fleetIndex };
    }
    let rewards = [];
    lodash_1.default.forOwn(rewardResult, (elem) => {
        lodash_1.default.forOwn(elem, (reward) => {
            rewards.push(reward);
        });
    });
    let lostRewards = [];
    lodash_1.default.forOwn(lostRewardResult, (elem) => {
        lodash_1.default.forOwn(elem, (reward) => {
            lostRewards.push(reward);
        });
    });
    changes.syncAdd.fleetDispatches[fleetIndex].rewards = rewards;
    changes.syncAdd.fleetDispatches[fleetIndex].lostRewards = lostRewards;
    changes.syncAdd.fleetDispatches[fleetIndex].lostRewardIdCount = lostRewardIdCount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetFleetDispatchReward = opSetFleetDispatchReward;
// -------------------------------------------------------------------------------------------------
function opRemoveFleetDispatch(user, tryData, changes, fleetIndex) {
    lodash_1.default.merge(changes.syncRemove, {
        fleetDispatches: {
            [fleetIndex]: true,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opRemoveFleetDispatch = opRemoveFleetDispatch;
// -------------------------------------------------------------------------------------------------
function opBuyDailySubscription(user, tryData, changes, dsCmsId, curTimeUtc) {
    if (!tryData.cashShop) {
        tryData.cashShop = user.userCashShop.clone();
    }
    if (!changes.syncAdd.dailySubscriptions) {
        changes.syncAdd.dailySubscriptions = {};
    }
    const dsCms = cms_1.default.DailySubscription[dsCmsId];
    tryData.cashShop.buyDailySubscription(dsCms, curTimeUtc);
    lodash_1.default.merge(changes.syncAdd.dailySubscriptions, {
        [dsCmsId]: tryData.cashShop.getDailySubscription(dsCmsId, curTimeUtc),
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opBuyDailySubscription = opBuyDailySubscription;
// -------------------------------------------------------------------------------------------------
function opSetDailySubscriptionLastReceiveTimeUtc(user, tryData, changes, dsCmsId, curTimeUtc) {
    if (!tryData.cashShop) {
        tryData.cashShop = user.userCashShop.clone();
    }
    if (!changes.syncAdd.dailySubscriptions) {
        changes.syncAdd.dailySubscriptions = {};
    }
    const userDS = tryData.cashShop.getDailySubscription(dsCmsId, curTimeUtc);
    if (!userDS) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const dsResetHour = cms_1.default.ContentsResetHour.DailySubscriptionRewardReset.hour;
    if (!(0, formula_1.HasContentsResetTimePassed)(curTimeUtc, userDS.lastReceiveTimeUtc, dsResetHour)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_DAILY_SUBSCRIPTION_DAILY_REWARD;
    }
    userDS.lastReceiveTimeUtc = curTimeUtc;
    tryData.cashShop.setDailySubscription(userDS);
    lodash_1.default.merge(changes.syncAdd.dailySubscriptions, {
        [dsCmsId]: userDS,
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetDailySubscriptionLastReceiveTimeUtc = opSetDailySubscriptionLastReceiveTimeUtc;
function opDeleteGuildSynthesisSlot(user, tryData, changes, slotNo) {
    if (!tryData.userGuild) {
        tryData.userGuild = user.userGuild.clone();
    }
    delete tryData.userGuild.guildSynthesisProgresses[slotNo];
    lodash_1.default.merge(changes.syncRemove, {
        userGuild: {
            synthesisProgresses: {
                [slotNo]: true,
            },
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteGuildSynthesisSlot = opDeleteGuildSynthesisSlot;
// -------------------------------------------------------------------------------------------------
function opSetHotSpotCoolTimeUtc(user, tryData, changes, cmsId, curTimeUtc) {
    if (!tryData.cashShop) {
        tryData.cashShop = user.userCashShop.clone();
    }
    const cashShopCms = cms_1.default.CashShop[cmsId];
    if (!cashShopCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const hotSpotProduct = tryData.cashShop.getHotSpotProduct(cmsId);
    // 핫스팟 상품 등장 시간이 지났는지 검사
    if (!hotSpotProduct ||
        !hotSpotProduct.expireTimeUtc ||
        !hotSpotProduct.coolTimeUtc ||
        hotSpotProduct.expireTimeUtc < curTimeUtc) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOT_OPEN_HOT_SPOT_PRODUCT;
    }
    // expireTimeUtc랑 coolTimeUtc를 갱신
    hotSpotProduct.expireTimeUtc = curTimeUtc;
    hotSpotProduct.coolTimeUtc = curTimeUtc + cashShopCms.coolTimeDays * formula_1.SECONDS_PER_DAY;
    if (!changes.syncAdd.hotSpotProducts) {
        changes.syncAdd.hotSpotProducts = {};
    }
    lodash_1.default.merge(changes.syncAdd.hotSpotProducts, { [cmsId]: hotSpotProduct });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetHotSpotCoolTimeUtc = opSetHotSpotCoolTimeUtc;
// -------------------------------------------------------------------------------------------------
function opSetEventRankingMissionReward(user, tryData, changes, eventPageCmsId, rewardIdx) {
    if (!tryData.userEventRanking) {
        tryData.userEventRanking = user.userEventRanking.clone();
    }
    if (tryData.userEventRanking.isUserRewarded(eventPageCmsId, rewardIdx)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_EVENT_RANKING_MISSION_REWARD;
    }
    tryData.userEventRanking.addRewardIdx(eventPageCmsId, rewardIdx);
    if (!changes.addedEventRankingRewardIdx) {
        changes.addedEventRankingRewardIdx = {};
    }
    if (!changes.addedEventRankingRewardIdx[eventPageCmsId]) {
        changes.addedEventRankingRewardIdx[eventPageCmsId] = [];
    }
    changes.addedEventRankingRewardIdx[eventPageCmsId].push(rewardIdx);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetEventRankingMissionReward = opSetEventRankingMissionReward;
// -------------------------------------------------------------------------------------------------
function opSetDiscoveryReward(user, tryData, changes, cmsId) {
    if (!tryData.userDiscoveryReward) {
        tryData.userDiscoveryReward = user.userDiscoveryReward.clone();
    }
    if (tryData.userDiscoveryReward.isRewarded(cmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_DISCOVERY_REWARD;
    }
    tryData.userDiscoveryReward.addReward(cmsId);
    if (!changes.addedDiscoveryReward) {
        changes.addedDiscoveryReward = [];
    }
    changes.addedDiscoveryReward.push(cmsId);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetDiscoveryReward = opSetDiscoveryReward;
function opApplyKarmaChange(user, tryData, changes, karmaChange) {
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    changes.syncAdd.user.karma = karmaChange.karma;
    changes.syncAdd.user.lastKarmaUpdateTimeUtc = karmaChange.lastUpdateTimeUtc;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyKarmaChange = opApplyKarmaChange;
function opAddShipCamouflage(user, tryData, changes, shipCamouflageCmsId) {
    const shipCamouflageCms = cms_1.default.ShipCamouflage[shipCamouflageCmsId];
    if (!shipCamouflageCms) {
        throw new merror_1.MError('invaluid-ship-camouflage-cms', merror_1.MErrorCode.INVALID_SHIP_CAMOUFLAGE_CMS_ID, {
            shipCamouflageCmsId,
        });
    }
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (tryData.fleets.hasShipCamouflage(shipCamouflageCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_BOUGHT;
    }
    tryData.fleets.addShipCamouflage(user, shipCamouflageCmsId, null);
    if (!changes.addedShipCamouflages) {
        changes.addedShipCamouflages = [];
    }
    changes.addedShipCamouflages.push(shipCamouflageCmsId);
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddShipCamouflage = opAddShipCamouflage;
// -------------------------------------------------------------------------------------------------
function opSetFishCatchReward(user, tryData, changes, fishCmsId) {
    if (!tryData.userFishing) {
        tryData.userFishing = user.userFishing.clone();
    }
    if (tryData.userFishing.isRewarded(fishCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_RECEIVED_FISH_SIZE_REWARD;
    }
    if (!tryData.userFishing.canBigCatchReward(fishCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ENOUGH_FISH_BIG_CATCH_SIZE_REWARD;
    }
    const fishCatch = tryData.userFishing.getFishCatch(fishCmsId);
    fishCatch.isRewarded = 1;
    tryData.userFishing.setFishCatch(fishCatch);
    if (!changes.addedFishCatchRewards) {
        changes.addedFishCatchRewards = {};
    }
    changes.addedFishCatchRewards[fishCmsId] = fishCatch;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetFishCatchReward = opSetFishCatchReward;
// -------------------------------------------------------------------------------------------------
function AddMailExceededReward(user, tryData, changes, curTimeUtc, attachment) {
    if (!tryData.mails) {
        tryData.mails = user.userMails.clone();
    }
    const mailId = cms_1.default.Const.QuestExceededRewardMailId.value;
    const mailCms = cms_1.default.Mail[mailId];
    const expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
    const mail = new mailBuilder_1.BuilderMailCreateParams(tryData.mails.generateNewDirectMailId(), mailId, curTimeUtc, expireTimeUtc, 0, null, null, null, null, JSON.stringify(attachment)).getParam();
    tryData.mails.addDirectMail(mail, null);
    if (!changes.newMail) {
        changes.newMail = [];
    }
    changes.newMail.push(mail);
}
// -------------------------------------------------------------------------------------------------
function opAddUserTitle(user, tryData, changes, cmsId) {
    if (!tryData.userTitles) {
        tryData.userTitles = user.userTitles.clone();
    }
    const userTitleCms = cms_1.default.UserTitle[cmsId];
    if (!userTitleCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.CMS_ERROR;
    }
    const curTimeUtc = mutil.curTimeUtc();
    const userTitle = tryData.userTitles.buildUserTitle(cmsId, curTimeUtc);
    if (!userTitle) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    tryData.userTitles.setUserTitle(userTitle);
    if (!changes.addedUserTitles) {
        changes.addedUserTitles = {};
    }
    changes.addedUserTitles[cmsId] = userTitle;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddUserTitle = opAddUserTitle;
// -------------------------------------------------------------------------------------------------
function opAddFreeSweepTicket(user, tryData, changes, amount) {
    if (!amount || amount <= 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.CMS_ERROR;
    }
    if (!tryData.userSweepTicket) {
        tryData.userSweepTicket = user.userSweepTicket.clone();
    }
    const curCount = tryData.userSweepTicket.count;
    const newCount = tryData.userSweepTicket.buildAddFreeTicket(amount);
    if (curCount === newCount) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!changes.sweepTicket) {
        changes.sweepTicket = {
            count: tryData.userSweepTicket.count,
            buyCount: tryData.userSweepTicket.buyCount,
        };
    }
    tryData.userSweepTicket.count = newCount;
    changes.sweepTicket.count = newCount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddFreeSweepTicket = opAddFreeSweepTicket;
// -------------------------------------------------------------------------------------------------
function opAddBuySweepTicket(user, tryData, changes, amount) {
    if (!amount || amount <= 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.CMS_ERROR;
    }
    if (!tryData.userSweepTicket) {
        tryData.userSweepTicket = user.userSweepTicket.clone();
    }
    if (!changes.sweepTicket) {
        changes.sweepTicket = {
            count: tryData.userSweepTicket.count,
            buyCount: tryData.userSweepTicket.buyCount,
        };
    }
    tryData.userSweepTicket.buyCount += amount;
    changes.sweepTicket.buyCount += amount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddBuySweepTicket = opAddBuySweepTicket;
// -------------------------------------------------------------------------------------------------
function opApplySweepTicketChange(user, tryData, changes, sweepTicketChange) {
    if (sweepTicketChange.count < 0 || sweepTicketChange.buyCount < 0) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.userSweepTicket) {
        tryData.userSweepTicket = user.userSweepTicket.clone();
    }
    if (!changes.sweepTicket) {
        changes.sweepTicket = {
            count: 0,
            buyCount: 0,
        };
    }
    tryData.userSweepTicket.applyTicketCount(sweepTicketChange.count, sweepTicketChange.buyCount, null, null);
    changes.sweepTicket.count = sweepTicketChange.count;
    changes.sweepTicket.buyCount = sweepTicketChange.buyCount;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplySweepTicketChange = opApplySweepTicketChange;
// -------------------------------------------------------------------------------------------------
function opApplyShieldChange(user, tryData, changes, shieldChange) {
    if (!shieldChange) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (!tryData.shield) {
        tryData.shield = user.userShield.clone();
    }
    tryData.shield.applyShieldChange(shieldChange, null);
    lodash_1.default.merge(changes.syncAdd, {
        shields: {
            [shieldChange.cmsId]: shieldChange,
        },
    });
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opApplyShieldChange = opApplyShieldChange;
// -------------------------------------------------------------------------------------------------
function opAddPet(user, tryData, changes, petCmsId) {
    if (!tryData.userPets) {
        tryData.userPets = user.userPets.clone();
    }
    if (tryData.userPets.hasPet(petCmsId)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_BOUGHT;
    }
    tryData.userPets.addNewPet(petCmsId);
    if (!changes.syncAdd.pets) {
        changes.syncAdd.pets = {};
    }
    changes.syncAdd.pets[petCmsId] = {
        cmsId: petCmsId,
    };
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opAddPet = opAddPet;
// -------------------------------------------------------------------------------------------------
function opDeleteShip(user, tryData, changes, shipId) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.mates) {
        tryData.mates = user.userMates.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.inven) {
        tryData.inven = user.userInven.clone();
    }
    const ship = tryData.fleets.getShip(shipId);
    if (!ship) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    const shipNub = ship.getNub();
    if (shipNub.assignment !== lobby_1.SHIP_ASSIGNMENT.CAPTURED &&
        shipNub.assignment !== lobby_1.SHIP_ASSIGNMENT.DOCK) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (shipNub.formationIndex === cmsEx.FlagShipFormationIndex) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (shipNub.isLocked === 1) {
        return userChangeTask_1.CHANGE_TASK_RESULT.LOCKED_SHIP;
    }
    if (!changes.syncRemove.ships) {
        changes.syncRemove.ships = [];
    }
    changes.syncRemove.ships.push(shipId.toString());
    tryData.fleets.deleteShip(shipId, null, tryData.mates, tryData.stats, tryData.fleets, tryData.shipBlueprints, tryData.inven, null, null, null, null, null, null, undefined);
    // 부품 해제
    if (!changes.unequippedShipSlotItemIds) {
        changes.unequippedShipSlotItemIds = [];
    }
    const bpCms = cms_1.default.Ship[shipNub.cmsId].shipBlueprint;
    for (const slot of bpCms.shipSlot) {
        const defaultSlotCms = cms_1.default.ShipSlot[slot.Id];
        if (defaultSlotCms.slotType === shipSlotDesc_1.SHIP_SLOT_TYPE.MATE ||
            defaultSlotCms.slotType === shipSlotDesc_1.SHIP_SLOT_TYPE.NON_MATE) {
            continue;
        }
        const shipSlot = ship.getSlot(slot.Index);
        if (!shipSlot || !shipSlot.shipSlotItemId) {
            continue;
        }
        changes.unequippedShipSlotItemIds.push(shipSlot.shipSlotItemId);
    }
    if (!user.userInven.canAddShipSlotItem(changes.unequippedShipSlotItemIds.length)) {
        return userChangeTask_1.CHANGE_TASK_RESULT.SHIP_SLOT_INVEN_FULL;
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opDeleteShip = opDeleteShip;
// -------------------------------------------------------------------------------------------------
// opAddShip(_tryElem)을 먼저 수행 후 실행해야됨
function opComposeShip(user, tryData, changes, shipIds, shipComposeCmsId, pickedRandomRewards) {
    if (!tryData.fleets) {
        tryData.fleets = user.userFleets.clone();
    }
    if (!tryData.shipBlueprints) {
        tryData.shipBlueprints = user.userShipBlueprints.clone();
    }
    if (!tryData.stats) {
        tryData.stats = user.companyStat.clone();
    }
    const shipComposeCms = cms_1.default.ShipCompose[shipComposeCmsId];
    if (!shipComposeCms) {
        return userChangeTask_1.CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    if (shipIds.length !== shipComposeCms.shipComposeMaterialShipVal) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOT_ENOUGH_SHIP_COUNT;
    }
    for (const shipId of shipIds) {
        const ship = user.userFleets.getShip(shipId);
        if (!ship) {
            return userChangeTask_1.CHANGE_TASK_RESULT.INVALID_SHIP_ID;
        }
        if (shipComposeCms.shipComposeMaterialShipId !== ship.nub.cmsId) {
            return userChangeTask_1.CHANGE_TASK_RESULT.NOT_MATCHED_MATERIAL_SHIP_CMS_ID;
        }
        if (ship.nub.assignment !== lobby_1.SHIP_ASSIGNMENT.DOCK &&
            ship.nub.assignment !== lobby_1.SHIP_ASSIGNMENT.CAPTURED) {
            return userChangeTask_1.CHANGE_TASK_RESULT.COMPOSE_SHIP_ONLY_IN_DOCK;
        }
        if (ship.nub.isLocked) {
            return userChangeTask_1.CHANGE_TASK_RESULT.CAN_NOT_COMPOSE_LOCKED_SHIP;
        }
    }
    const shipCompose = tryData.fleets.getShipCompose(shipComposeCms.groupId);
    if (shipCompose.rewardedCount >= shipComposeCms.rewardMaxCnt) {
        return userChangeTask_1.CHANGE_TASK_RESULT.ALREADY_COMPOSE_MAX_COUNT;
    }
    if (shipCompose.turn >= cms_1.default.Const.shipComposeMax.value) {
        return userChangeTask_1.CHANGE_TASK_RESULT.CMS_ERROR;
    }
    const bIsGetComposeShip = pickedRandomRewards.some((pickedReward) => pickedReward.rewards.some((reward) => {
        if (reward.rewardType !== rewardDesc_1.REWARD_TYPE.SHIP) {
            return false;
        }
        const ship = tryData.fleets.getShip(reward.id);
        return (ship === null || ship === void 0 ? void 0 : ship.nub.cmsId) === shipComposeCms.shipComposeShipId;
    }));
    if (bIsGetComposeShip) {
        shipCompose.rewardedCount++;
        shipCompose.turn = 0;
    }
    else {
        shipCompose.turn++;
    }
    // 최초 획득인 경우 설계도 레벨 1
    if (bIsGetComposeShip) {
        const shipCms = cms_1.default.Ship[shipComposeCms.shipComposeShipId];
        const userBP = tryData.shipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
        if (!userBP) {
            if (!changes.syncAdd.shipBlueprints) {
                changes.syncAdd.shipBlueprints = {};
            }
            if (!changes.syncAdd.shipBlueprints[shipCms.shipBlueprintId]) {
                changes.syncAdd.shipBlueprints[shipCms.shipBlueprintId] = {
                    cmsId: shipCms.shipBlueprintId,
                    level: 1,
                    exp: 0,
                    slots: {},
                    sailMasteryExp: 0,
                    sailMasteryLevel: 1,
                    isPurchased: 0,
                };
                tryData.shipBlueprints.createUserShipBlueprint(tryData.stats, shipCms.shipBlueprintId, 1, 0, 1, 0, null, null);
            }
        }
    }
    if (!changes.syncAdd.shipCompose) {
        changes.syncAdd.shipCompose = {};
    }
    changes.syncAdd.shipCompose[shipComposeCms.groupId] = shipCompose;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opComposeShip = opComposeShip;
// -------------------------------------------------------------------------------------------------
function opResetLastPaidSmuggleEnterTownCmsId(user, tryData, changes) {
    if (!tryData.userSmuggle) {
        tryData.userSmuggle = user.userSmuggle.clone();
    }
    if (!changes.syncAdd.user) {
        changes.syncAdd.user = {};
    }
    tryData.userSmuggle.lastPaidSmuggleEnterTownCmsId = 0;
    changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId = 0;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opResetLastPaidSmuggleEnterTownCmsId = opResetLastPaidSmuggleEnterTownCmsId;
// -------------------------------------------------------------------------------------------------
function opSetInfiniteLighthouseClearInfo(user, tryData, changes, bIsWin, sessionId, stageNum, usedTurn) {
    if (!bIsWin) {
        return userChangeTask_1.CHANGE_TASK_RESULT.NOTHING;
    }
    if (!tryData.userInfiniteLighthouse) {
        tryData.userInfiniteLighthouse = user.userInfiniteLighthouse.clone();
    }
    const clearedInfo = tryData.userInfiniteLighthouse.getClearedStage(sessionId, stageNum);
    if (!clearedInfo || clearedInfo.usedTurn > usedTurn) {
        if (!changes.syncAdd.clearedInfiniteLighthouseStages) {
            changes.syncAdd.clearedInfiniteLighthouseStages = {};
        }
        if (!changes.syncAdd.clearedInfiniteLighthouseStages[sessionId]) {
            changes.syncAdd.clearedInfiniteLighthouseStages[sessionId] = {};
        }
        changes.syncAdd.clearedInfiniteLighthouseStages[sessionId][stageNum] = {
            stage: stageNum,
            usedTurn,
        };
    }
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetInfiniteLighthouseClearInfo = opSetInfiniteLighthouseClearInfo;
// -------------------------------------------------------------------------------------------------
function opSetClash(user, tryData, changes, session) {
    if (!tryData.userClash) {
        tryData.userClash = user.userClash.clone();
    }
    tryData.userClash.applySession(session, null);
    changes.clash = session;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetClash = opSetClash;
// -------------------------------------------------------------------------------------------------
function opSetReentry(user, tryData, changes, reentry) {
    if (!tryData.userReentry) {
        tryData.userReentry = user.userReentry.clone();
    }
    tryData.userReentry.applyReentry(reentry, null);
    if (!changes.reentrys) {
        changes.reentrys = {};
    }
    changes.reentrys[reentry.type] = reentry;
    return userChangeTask_1.CHANGE_TASK_RESULT.OK;
}
exports.opSetReentry = opSetReentry;
//# sourceMappingURL=userChangeOperator.js.map