cmd_Release/obj.target/ffi_bindings/src/threaded_callback_invokation.o := g++ -o Release/obj.target/ffi_bindings/src/threaded_callback_invokation.o ../src/threaded_callback_invokation.cc '-DNODE_GYP_MODULE_NAME=ffi_bindings' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/root/.cache/node-gyp/16.20.2/include/node -I/root/.cache/node-gyp/16.20.2/src -I/root/.cache/node-gyp/16.20.2/deps/openssl/config -I/root/.cache/node-gyp/16.20.2/deps/openssl/openssl/include -I/root/.cache/node-gyp/16.20.2/deps/uv/include -I/root/.cache/node-gyp/16.20.2/deps/zlib -I/root/.cache/node-gyp/16.20.2/deps/v8/include -I/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api -I/mnt/c/work/uwo/game/server/node/node_modules/get-uv-event-loop-napi-h/include -I/mnt/c/work/uwo/game/server/node/node_modules/get-symbol-from-current-process-h/include -I/mnt/c/work/uwo/game/server/node/node_modules/ref-napi/include -I../deps/libffi/include -I../deps/libffi/config/linux/x64  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -m64 -O3 -fno-omit-frame-pointer -fno-rtti -std=gnu++14 -MMD -MF ./Release/.deps/Release/obj.target/ffi_bindings/src/threaded_callback_invokation.o.d.raw   -c
Release/obj.target/ffi_bindings/src/threaded_callback_invokation.o: \
 ../src/threaded_callback_invokation.cc ../src/ffi.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv/errno.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv/version.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv/unix.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv/threadpool.h \
 /root/.cache/node-gyp/16.20.2/include/node/uv/linux.h \
 /mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi.h \
 /root/.cache/node-gyp/16.20.2/include/node/node_api.h \
 /root/.cache/node-gyp/16.20.2/include/node/js_native_api.h \
 /root/.cache/node-gyp/16.20.2/include/node/js_native_api_types.h \
 /root/.cache/node-gyp/16.20.2/include/node/node_api_types.h \
 /mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi-inl.h \
 /mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi-inl.deprecated.h \
 /mnt/c/work/uwo/game/server/node/node_modules/ref-napi/include/ref-napi.h \
 ../deps/libffi/config/linux/x64/ffi.h \
 ../deps/libffi/config/linux/x64/ffitarget.h
../src/threaded_callback_invokation.cc:
../src/ffi.h:
/root/.cache/node-gyp/16.20.2/include/node/uv.h:
/root/.cache/node-gyp/16.20.2/include/node/uv/errno.h:
/root/.cache/node-gyp/16.20.2/include/node/uv/version.h:
/root/.cache/node-gyp/16.20.2/include/node/uv/unix.h:
/root/.cache/node-gyp/16.20.2/include/node/uv/threadpool.h:
/root/.cache/node-gyp/16.20.2/include/node/uv/linux.h:
/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi.h:
/root/.cache/node-gyp/16.20.2/include/node/node_api.h:
/root/.cache/node-gyp/16.20.2/include/node/js_native_api.h:
/root/.cache/node-gyp/16.20.2/include/node/js_native_api_types.h:
/root/.cache/node-gyp/16.20.2/include/node/node_api_types.h:
/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi-inl.h:
/mnt/c/work/uwo/game/server/node/node_modules/node-addon-api/napi-inl.deprecated.h:
/mnt/c/work/uwo/game/server/node/node_modules/ref-napi/include/ref-napi.h:
../deps/libffi/config/linux/x64/ffi.h:
../deps/libffi/config/linux/x64/ffitarget.h:
