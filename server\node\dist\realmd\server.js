"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.RealmService = void 0;
const amqplib_1 = __importDefault(require("amqplib"));
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
require("express-async-errors");
const http_1 = __importDefault(require("http"));
const lodash_1 = __importDefault(require("lodash"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const typedi_1 = require("typedi");
const cms_1 = __importStar(require("../cms"));
const dirAsApi = __importStar(require("../motiflib/directoryAsApi"));
const expressError = __importStar(require("../motiflib/expressError"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const pool_1 = require("../mysqllib/pool");
const connPool_1 = require("../redislib/connPool");
const pubsub_1 = __importDefault(require("../redislib/pubsub"));
const lobbydHealthChecker_1 = __importDefault(require("./lobbydHealthChecker"));
const realmPubsub = __importStar(require("./realmPubsub"));
const schedule_1 = require("./schedule");
const ex_1 = require("../cms/ex");
const stoppable_1 = __importDefault(require("stoppable"));
const maintenaceChecker_1 = __importDefault(require("./maintenaceChecker"));
const Sentry = __importStar(require("@sentry/node"));
const fs_1 = __importDefault(require("fs"));
const chatApiClient_1 = require("../motiflib/mhttp/chatApiClient");
const cmsKeyGroup_1 = require("../motiflib/model/cmsKeyGroup");
// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------
function realmReqLog(tokens, req, res) {
    if (req.url === '/health') {
        return;
    }
    mlog_1.default.info('realmd-req', {
        url: tokens['url'](req, res),
        status: tokens['status'](req, res),
        'response-time': tokens['response-time'](req, res),
        mcode: tokens['mcode'](req, res),
    });
    return null;
}
// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
// Main realmd app.
const realmApp = (0, express_1.default)();
realmApp.disable('x-powered-by');
realmApp.disable('etag');
realmApp.disable('content-type');
realmApp.use((0, morgan_1.default)(realmReqLog));
realmApp.use(body_parser_1.default.json());
realmApp.use(expressError.middleware);
const realmServer = (0, stoppable_1.default)(http_1.default.createServer(realmApp));
let stopping = false;
let healthCheckTimeout = null;
// ----------------------------------------------------------------------------
// Service
// ----------------------------------------------------------------------------
let RealmService = class RealmService {
    async init() {
        this.townRedis = typedi_1.Container.of('town-redis').get(connPool_1.MRedisConnPool);
        await this.townRedis.init('town-redis', mconf_1.default.townRedis);
        this.nationRedis = typedi_1.Container.of('nation-redis').get(connPool_1.MRedisConnPool);
        await this.nationRedis.init('nation-redis', mconf_1.default.nationRedis);
        this.collectorRedis = typedi_1.Container.of('collector-redis').get(connPool_1.MRedisConnPool);
        await this.collectorRedis.init('collector-redis', mconf_1.default.collectorRedis);
        this.monitorRedis = typedi_1.Container.of('monitor-redis').get(connPool_1.MRedisConnPool);
        await this.monitorRedis.init('monitor-redis', mconf_1.default.monitorRedis);
        this.arenaRedis = typedi_1.Container.of('arena-redis').get(connPool_1.MRedisConnPool);
        await this.arenaRedis.init('arena-redis', mconf_1.default.arenaRedis);
        this.userCacheRedis = typedi_1.Container.of('user-cache-redis').get(connPool_1.MRedisConnPool);
        await this.userCacheRedis.init('user-cache-redis', mconf_1.default.userCacheRedis);
        this.authRedis = typedi_1.Container.of('auth-redis').get(connPool_1.MRedisConnPool);
        await this.authRedis.init('auth-redis', mconf_1.default.authRedis);
        this.userRedis = typedi_1.Container.of('user-redis').get(connPool_1.MRedisConnPool);
        await this.userRedis.init('user-redis', mconf_1.default.userRedis);
        this.orderRedis = typedi_1.Container.of('order-redis').get(connPool_1.MRedisConnPool);
        await this.orderRedis.init('order-redis', mconf_1.default.orderRedis);
        this.rankingRedis = typedi_1.Container.of('ranking-redis').get(connPool_1.MRedisConnPool);
        await this.rankingRedis.init('ranking-redis', mconf_1.default.rankingRedis);
        this.blindBidRedis = typedi_1.Container.of('blind-bid-redis').get(connPool_1.MRedisConnPool);
        await this.blindBidRedis.init('blind-bid-redis', mconf_1.default.blindBidRedis);
        const worldPubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
        worldPubsub.init(mconf_1.default.getWorldConfig().worldPubsubRedis);
        realmPubsub.init(worldPubsub, this.townRedis);
        // cms관련 데이터 로드
        this.nationCabinetRewardMailLookupTable = new cmsKeyGroup_1.NationCabinetRewardMailLookupTable(cms_1.default);
        await this.connectToRabbitMq();
        // Init mysql connection pool.
        this.userDbConnPoolMgr = typedi_1.Container.of('user').get(pool_1.DbConnPoolManager);
        await this.userDbConnPoolMgr.init(mconf_1.default.mysqlUserDb);
        this.worldDbConnPool = typedi_1.Container.of('world').get(pool_1.DBConnPool);
        await this.worldDbConnPool.init(mconf_1.default.mysqlWorldDb);
        // Init LobbydHealthChecker
        this.lobbydHealthChecker = new lobbydHealthChecker_1.default();
        // init MaintenanceChecker
        this.maintenanceChecker = new maintenaceChecker_1.default();
        // Init api server.
        const bindAddress = mconf_1.default.apiService.bindAddress;
        const port = mconf_1.default.apiService.port;
        mutil.registerHealthCheck(realmApp);
        mutil.registerGarbageCollector(realmApp);
        await dirAsApi.register(realmApp, path_1.default.join(__dirname, 'api'));
        realmServer.listen(port, bindAddress, () => {
            mlog_1.default.info('start listening ...', { bindAddress, port });
        });
        const schedule = typedi_1.Container.get(schedule_1.ScheduledJobs);
        schedule.start();
        healthCheckTimeout = setTimeout(() => {
            if (stopping) {
                return;
            }
            // zonelbd시작후 일정시간동안 타임아웃 처리를 유예하여
            // 이미 동작중인 존서버들의 ping처리가 가능하도록 한다.
            this.lobbydHealthChecker.startTick();
        }, mconf_1.default.lobbydHealthCheck.timeout);
        this.maintenanceChecker.startCheck();
    }
    async connectToRabbitMq(reconnect = false) {
        try {
            this.amqpConn = await amqplib_1.default.connect(mconf_1.default.amqp);
        }
        catch (err) {
            mlog_1.default.error('Failed to connect to RabbitMQ server.', {
                message: err.message,
                stack: err.stack,
            });
            if (reconnect) {
                await mutil.sleep(1000);
                this.connectToRabbitMq(reconnect);
                return;
            }
            else {
                throw err;
            }
        }
        mlog_1.default.info('connected to RabbitMQ', {
            config: mconf_1.default.amqp,
        });
        this.amqpConn.on('error', (err) => {
            mlog_1.default.error('The rabbitmq error occurs.', {
                message: err.message,
                stack: err.stack,
            });
        });
        this.amqpConn.on('close', async (err) => {
            if (err) {
                mlog_1.default.error('The RabbitMQ has been disconnected by server', {
                    message: err.message,
                    stack: err.stack,
                });
            }
            else {
                mlog_1.default.info('The RabbitMQ has been disconnected by server');
            }
            await mutil.sleep(1000);
            this.connectToRabbitMq(true);
        });
        this.amqpCh = await this.amqpConn.createChannel();
    }
    async destroy() {
        this.maintenanceChecker.stopCheck();
        this.lobbydHealthChecker.stopTick();
        await this.townRedis.destroy();
        await this.nationRedis.destroy();
        await this.collectorRedis.destroy();
        await this.monitorRedis.destroy();
        await this.arenaRedis.destroy();
        await this.userCacheRedis.destroy();
        await this.authRedis.destroy();
        await this.userRedis.destroy();
        await this.orderRedis.destroy();
        await this.rankingRedis.destroy();
        await this.blindBidRedis.destroy();
        const pubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
        await pubsub.quit();
        await this.amqpConn.close();
        await this.userDbConnPoolMgr.destroy();
        await this.worldDbConnPool.destroy();
    }
};
RealmService = __decorate([
    (0, typedi_1.Service)()
], RealmService);
exports.RealmService = RealmService;
// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------
morgan_1.default.token('mcode', (_req, res) => res.mcode || 0);
async function closeServer() {
    return new Promise((resolve, reject) => {
        realmServer.stop((err) => {
            if (err)
                return reject(err);
            resolve();
        });
    });
}
async function stopServer() {
    try {
        mlog_1.default.info('stopping server ...');
        await closeServer();
        if (healthCheckTimeout) {
            clearTimeout(healthCheckTimeout);
            healthCheckTimeout = null;
        }
        const app = typedi_1.Container.get(RealmService);
        await app.destroy();
        mlog_1.default.info('server stopped');
        // explicitly calls the exit function, since node-schedule job is not cancelled
        process.exit(0);
    }
    catch (error) {
        mlog_1.default.exception('graceful shutdown failed', error);
        process.exit(1);
    }
}
/**
 * LINE GAMES CHATTING
 * 기본 채널 생성.
 * 타운, 지역 ID와 기본 시스템 채널을 (없으면) 생성한다.
 */
async function initVolanteChannels() {
    try {
        // 채널명과 맵핑되는 별칭명 excel파일로 추출.(라인게임즈 전달용)
        // makeChannelNAliasFile();
        const exists = await mhttp_1.default.platformChatApi.getAllChannels();
        const listOfNameNAlias = [];
        let required = lodash_1.default.without(lodash_1.default.keys(cms_1.default.Town), ...exists);
        const towns = required.map((townCmsIdStr) => {
            return {
                name: townCmsIdStr,
                alias: (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, townCmsIdStr),
            };
        });
        listOfNameNAlias.push(...towns);
        required = lodash_1.default.without(lodash_1.default.keys(cms_1.default.Region), ...exists);
        const regions = required.map((regionCmsIdStr) => {
            return {
                name: regionCmsIdStr,
                alias: (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, regionCmsIdStr),
            };
        });
        listOfNameNAlias.push(...regions);
        required = lodash_1.default.without((0, ex_1.getSelectableNations)().map((elem) => elem.id.toString()), ...exists);
        required = (0, ex_1.getSelectableNations)().map((elem) => elem.id.toString());
        const nations = required.map((nationCmsIdStr) => {
            return {
                name: nationCmsIdStr,
                alias: (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.NATION, nationCmsIdStr),
            };
        });
        listOfNameNAlias.push(...nations);
        listOfNameNAlias.push({
            name: 'SYSTEM',
            alias: (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.SYSTEM),
        });
        listOfNameNAlias.push({
            name: 'WORLD',
            alias: (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.WORLD),
        });
        for (let i = 0; i < listOfNameNAlias.length; i++) {
            const channelName = listOfNameNAlias[i].name;
            const channelAlias = listOfNameNAlias[i].alias;
            const result = await mhttp_1.default.platformChatApi.createPublicChannel(channelName, channelAlias);
            // mlog.info('create volante channel success', result);
        }
        mlog_1.default.info('all volante channel prepared');
    }
    catch (error) {
        // 예외 발생 시 1분 후 재요청
        mlog_1.default.exception('create public channel failed', error);
        setTimeout(() => initVolanteChannels(), 1000 * 60);
    }
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
async function start() {
    try {
        await mhttp_1.default.configd.registerInstance(process.env.WORLD_ID ? process.env.WORLD_ID : mconf_1.default.instance.worldId, mconf_1.default.appInstanceId, mconf_1.default.hostname);
        mutil.initSentry();
        // Init http clients.
        mhttp_1.default.init();
        // realmd glog 성격 상 다량의 로그가 한 번에 발생하기 때문에 미리 토큰을 받아놓는다.
        await mhttp_1.default.lglogd.refreshTokenIfExpired();
        (0, cms_1.load)();
        const app = typedi_1.Container.get(RealmService);
        await app.init();
        // config final sync
        const beforeVer = mconf_1.default.layoutVersion;
        await mhttp_1.default.configd.sync(beforeVer, isStopping, stop).then(() => {
            if (beforeVer < mconf_1.default.layoutVersion) {
                // do something
            }
        });
        await initVolanteChannels();
    }
    catch (error) {
        mlog_1.default.error('failed to start', { error: error.message, extra: error.extra });
        mlog_1.default.error(error.stack);
        const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        await slackNotifier.notify({ username: process.name, text: error.message });
        process.exit(1);
    }
}
exports.start = start;
function isStopping() {
    return stopping;
}
async function stop() {
    if (stopping) {
        return;
    }
    stopping = true;
    await stopServer();
}
exports.stop = stop;
function makeChannelNAliasFile() {
    const f = (channelName, aliasName) => {
        return {
            '기존 채널명(channel_name)': channelName,
            '입력할 채널 별칭(alias)': aliasName,
        };
    };
    let results = [];
    results.push(f('SYSTEM', (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.SYSTEM)));
    results.push(f('WORLD', (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.WORLD)));
    const towns = lodash_1.default.keys(cms_1.default.Town).map((cmsIdStr) => f(cmsIdStr, (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, cmsIdStr)));
    results.push(...towns);
    const regions = lodash_1.default.keys(cms_1.default.Region).map((cmsIdStr) => f(cmsIdStr, (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, cmsIdStr)));
    results.push(...regions);
    const nations = (0, ex_1.getSelectableNations)().map((elem) => f(elem.id.toString(), (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.NATION, elem.id.toString())));
    results.push(...nations);
    // 추출된 json파일은 http://convertcsv.com/json-to-csv.htm에서 엑셀파일로 컨버팅가능.
    fs_1.default.writeFile('alias.json', JSON.stringify(results), (err) => {
        if (err) {
            throw err;
        }
    });
}
//# sourceMappingURL=server.js.map