// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';

export const spName = 'mp_u_user_mute_cn';

const spFunction = query.generateSPFunction(spName);

export default async function (
  connection: query.Connection,
  userId: number,
  mutedUserId: number,
): Promise<number> {
  const sq = await spFunction(connection, userId, mutedUserId);
  const rows = sq.rows;
  return Number.parseInt(rows[0][0].result);
}
