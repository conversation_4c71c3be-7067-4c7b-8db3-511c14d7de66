﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\TypeScript.Tasks.dll" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="RCCX" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";Managed Resources" ItemType="0" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
  <Item ItemId=";TypeScript.Tasks.Strings.resources" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" Path=" \ ;Managed Resources \ 0 \ 0" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";CompilerLogNotSpecified" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No compiler log specified, 'Clean' won't work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未指定編譯器記錄檔，因此 [清除]5D; 將無法運作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfigFileFoundOnDisk" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This project is using one or more tsconfig.json files for the build that may not be properly used by IntelliSense. Please set the item type of each tsconfig.json file to TypeScriptCompile or Content to ensure they are used properly by the editor.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此專案正在對 IntelliSense 可能無法正確使用的組建，使用一或多個 tsconfig.json 檔案。請將每個 tsconfig.json 檔案的項目類型設定為 TypeScriptCompile 或 Content，以確保編輯器正確使用它們。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorListBuildPrefix" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建置:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorWritingLog" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to write compiler log to '{0}. Exception: '{1}']]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法將編譯器記錄檔寫入 '{0}。例外狀況: '{1}']]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FallbackVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using compiler version ({0}), if this is incorrect change the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將使用編譯器版本 ({0})，若此版本不正確，請變更專案檔中的 <TypeScriptToolsVersion> 元素。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LocatedReferenceFilesAt_0" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Located references file at: '{0}'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 '{0}' 找到參考檔案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoCompilerError" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project file uses a different version of the TypeScript compiler and tools than is currently installed on this machine.  No compiler was found at {0}.  You may be able to fix this problem by changing the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的專案檔使用的 TypeScript 編譯器和工具版本與這部電腦目前安裝的版本不同。在 {0} 找不到任何編譯器。變更專案檔中的 <TypeScriptToolsVersion> 元素，或許可以修正此問題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeNotFound" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The build task could not find node.exe which is required to run the TypeScript compiler. Please install Node and ensure that the system path contains its location.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建置工作找不到執行 TypeScript 編譯器所需的 node.exe。請安裝 Node，並確定系統路徑中包含其位置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolsVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Couldn't locate the compiler version ({0}) specified in the project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到專案檔中指定的編譯器版本 ({0})。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptCompileBlockedSet" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeScript compile is skipped because the TypeScriptCompileBlocked property is set to 'true'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[因為 TypeScriptCompileBlocked 屬性設為 'true'，所以跳過 TypeScript 編譯。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptNoVersionWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project does not specify a TypeScriptToolsVersion. The latest available TypeScript compiler will be used ({0}). To remove this warning, set TypeScriptToolsVersion to a specific version or "Latest" to always select the latest compiler.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的專案未指定 TypeScriptToolsVersion。系統會使用最新的可用 TypeScript 編譯器 ({0})。若要移除這個警告，請將 TypeScriptToolsVersion 設為特定版本，或 [最新]5D; 以一律選取最新編譯器。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptVersionMismatchWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project specifies TypeScriptToolsVersion {0}, but a matching compiler was not found. The latest available TypeScript compiler will be used ({1}). To remove this warning, install the TypeScript {0} SDK or update the value of TypeScriptToolsVersion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的專案指定了 TypeScriptToolsVersion {0}，但找不到相符的編譯器。系統會使用最新可用的 TypeScript 編譯器 ({1})。若要移除此警告，請安裝 TypeScript {0} SDK 或更新 TypeScriptToolsVersion 的值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Ver" Disp="true" LocTbl="false" Path=" \ ;Version \ 8 \ 0" />
    <Item ItemId=";CompanyName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[Microsoft Corporation]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";FileDescription" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 建置工作]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";InternalName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";LegalCopyright" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[© Microsoft Corporation. All rights reserved.]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[© Microsoft Corporation. 著作權所有，並保留一切權利。]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";OriginalFilename" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";ProductName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 建置工作]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="8" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
</LCX>