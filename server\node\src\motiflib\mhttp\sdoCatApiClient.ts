// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import os from 'os';
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import * as crypto from 'crypto';
import type { AxiosResponse } from 'axios';
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import assert from 'assert';

import mconf from '../mconf';
import mlog from '../mlog';
import { ChatErrorCode } from './chatApiErrors';
import { BaseApiClient } from './baseApiClient';
import { MError, MErrorCode } from '../merror';
import { CreateSlackNotifier, type ISlackNotifier } from '../slackNotifier';
import type { User } from '../../lobbyd/user';
import cms from '../../cms';
import { IPlatformChatApiClient } from './iPlatformChatApiClient';
import {
  ChannelInfo,
  CreateChannelResult,
  VolanteUser,
  VolanteUserSession
} from './linegamesChatApiClient';
import { Container } from 'typedi';
import { LobbyService } from '../../lobbyd/server';
import puUserQueryMutedList from '../../mysqllib/sp/puUserQueryMutedList';
import puUserGetMutedCount from '../../mysqllib/sp/puUserGetMutedCount';
import puUserMute from '../../mysqllib/sp/puUserMute';
import puUserUnmute from '../../mysqllib/sp/puUserUnmute';
import Pubsub from '../../redislib/pubsub';

const AxiosTimeout = 5_000;

export enum CHANNEL_TYPE {
  ALL = 0,
  SYSTEM = 1,
  WORLD = 2,
  NATION = 3,
  GUILD = 4,
  REGION = 5,
  MAX = 5,
}

export function getAliasName(channelType: CHANNEL_TYPE, channelName?: string): string | null {
  if (channelType === CHANNEL_TYPE.SYSTEM) {
    return '안내';
  }

  if (channelType === CHANNEL_TYPE.WORLD) {
    return '세계';
  }

  if (channelType === CHANNEL_TYPE.NATION) {
    return `국가_${cms.Nation[channelName].name}`;
  }

  if (channelType === CHANNEL_TYPE.REGION) {
    if (cms.Region[channelName]) {
      return `지역_${cms.Region[channelName].name}`;
    }
    if (cms.Town[channelName]) {
      return `지역_${cms.Town[channelName].name}`;
    }
  }

  if (channelType === CHANNEL_TYPE.GUILD) {
    return `상회_${channelName.replace('GUILD_', '')}`;
  }

  return null;
}

export enum PublicChannle { }

export class SdoChatApiClient extends BaseApiClient implements IPlatformChatApiClient {
  private salt: string;
  private slackNotifier: ISlackNotifier;

  init(baseUrl: string, timeout?: number) {
    super.init(baseUrl, timeout);

    mlog.info(`chatd endpoint: ${baseUrl}`);
  }

  setSalt(salt: string) {
    this.salt = salt || '0';
  }

  async sendToSlack(message: string) {
    if (this.slackNotifier === undefined) {
      this.slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    }

    await this.slackNotifier.notify({
      username: `host: ${os.hostname()}`,
      text: message,
      channel: '#sdk-error',
    });
  }

  getIdToken(id: string) {
    const hash: crypto.Hash = crypto.createHash('SHA256');
    hash.update(id + this.salt);
    return hash.digest('hex');
  }

  async getAllChannels(): Promise<string[]> {
    const args = await this._get('/channels');
    return args.channels;
  }

  async getChannel(channelName: string): Promise<ChannelInfo> {
    const args = await this._get(`/channels/${channelName}`);
    return args as ChannelInfo;
  }

  async existChannel(channelName: string): Promise<boolean> {
    // const axios = this.mrest.axios();
    // const resp = await axios.get(`/channels/${channelName}`, {
    //   headers: { Authorization: `custom ${this.salt}` },
    //   timeout: AxiosTimeout,
    //   validateStatus: () => true,
    // });
    // const result = resp.data;
    // return result.success && result.args && result.args.channel_name;

    // 채널이 없으면 생성하는 로직이 있으므로, 무조건 채널이 있는것처럼 만들어서 기존 로직을 수정하지 않아도 되도록함.
    return true;
  }

  async getAllowUserList(channelName: string): Promise<string[]> {
    const axios = this.mrest.axios();
    const resp = await axios.get(`/channels/${channelName}/allows`, {
      headers: { Authorization: `custom ${this.salt}` },
      timeout: AxiosTimeout,
      validateStatus: () => true,
    });
    const result = resp.data;
    if (result.success && result.args) {
      mlog.info('[TEMP] [DEBUG] chat allow user list.', {
        channelName,
        list: result.args.allow_users,
      });
      return result.args.allow_users;
    }

    return [];
  }

  /**
   * 공용 채널 생성 (월드 / 지역 / 국가).
   * 
   * public & persistent & !group channel
   */
  async createPublicChannel(name: string, alias: string): Promise<CreateChannelResult> {
    const body = {
      type: 'persistent',
      name,
      alias,
      group: false,
      public: true,
      user_limit: 100000,
    };
    const args = await this._post('/channels', body, [ChatErrorCode.ALREADY_EXIST_CHANNEL]);
    return args as CreateChannelResult;
  }

  /**
   * 길드 채널 생성 (월드 / 지역 / 국가)
   * 
   * private & persistent & !group channel
   */
  async createGuildChannel(
    channelName: string,
    userId: string,
    bShouldAllowChannel: boolean
  ): Promise<CreateChannelResult> {
    const body = {
      type: 'persistent',
      name: channelName,
      alias: getAliasName(CHANNEL_TYPE.GUILD, channelName),
      group: false,
      public: false,
      user_limit: 1000,
    };
    const args = await this._post('/channels', body);
    if (bShouldAllowChannel) {
      await this.allowGuildChannel(channelName, userId);
    }

    return args as CreateChannelResult;
  }

  /**
   * 길드 채널 입장 허용
   */
  async allowGuildChannel(channelName: string, userId: string): Promise<any> {
    // const body = {
    //   user_id: userId,
    // };
    // return this._post(`/channels/${channelName}/allows`, body);
  }

  /**
   * 길드 채널 허용 사용자 삭제
   */
  async disallowGuildChannel(channelName: string, userId: string): Promise<any> {
    // return await this._delete(`/channels/${channelName}/allows/${userId}`);
  }

  /**
   * 채널 삭제
   */
  async deleteChannel(channelName: string): Promise<any> {
    // return await this._delete(`/channels/${channelName}`);
  }

  /**
   * 인증 시점에 채팅 서버에 등록되지 않은 유저면 추가한다.
   * (월드 선택 시점에는 채팅 서버에 접속해야 되므로 그 전 단계에서 필수로 진행해야 함)
   */
  async createUserIfNotExists(userId: string, nickName: string): Promise<void> {
    let url = `/users/${userId}`;
    try {
      // 조회
      const axios = this.mrest.axios();
      const resp = await axios.get(url, {
        headers: { Authorization: `custom ${this.salt}` },
        timeout: AxiosTimeout,
        validateStatus: () => true,
      });

      const result = resp.data;
      if (result.success) {
        return; // result.args;
      }

      if (result.error) {
        // 사용자 없는 경우에 생성
        const errcode = result.error.error_code;
        if (errcode === ChatErrorCode.NOT_EXIST_USER) {
          url = '/users';
          await this._post('/users', { id: userId, nickname: nickName }, [
            ChatErrorCode.ALREADY_EXIST_USER,
          ]);
        }
      } else {
        throw new MError('chat request error', MErrorCode.LGSDK_ERROR, {
          request: url,
          response: result,
        });
      }
    } catch (error) {
      this.handleError(url, null, error);
    }
  }

  /**
   * 사용자 정보 받아오기
   */
  async getUser(userId: string): Promise<VolanteUser> {
    const result = await this.get(`/users/${userId}`);
    return result as VolanteUser;
  }

  /**
   * 사용자 세션 정보 조회
   */
  async getSessions(userId: string): Promise<VolanteUserSession> {
    // const result = await this._get(`/users/${userId}/session`, [ChatErrorCode.SESSION_NOT_FOUND]);
    // if (result.hasIgnoredError) {
    //   throw new MError('session not found', MErrorCode.LG_VOLANTE_INVALID_SESSION);
    // }
    // return result as VolanteUserSession;

    return undefined;
  }

  /**
   * 유저 정보 수정
   */
  async updateUser(userId: string, nickName: string, extraData: JsonLike): Promise<void> {
    // await this._patch(`/users/${userId}`, { nickname: nickName, extra_data: extraData });
  }

  /**
   * 유저 채널 입장
   * 지역 이동 / 국가 변경시에 채널 Leave와 함께 Join 되어야 합니다. 채널 이름은 CmsId 사용
   */
  async channelJoin(channelName: string, userId: string): Promise<void> {
    // try {
    //   await this._post(
    //     `/users/${userId}/join`,
    //     {
    //       channel_name: channelName,
    //       options: 'DO_NOT_ANNOUNCE',
    //     },
    //     [ChatErrorCode.ALREADY_JOINED_CHANNEL]
    //   );
    // } catch (error) {
    //   if (error.extra?.error_code) {
    //     const errcode = error.extra.error_code;
    //     if (errcode === ChatErrorCode.INVALID_SESSION) {
    //       throw new MError('Join failed', MErrorCode.LG_VOLANTE_INVALID_SESSION);
    //     }
    //   }

    //   throw error;
    // }
  }

  /**
   * 유저 채널 퇴장
   * 지역 이동 / 국가 변경시에 채널에서 나가야 합니다. 채널 이름은 CmsId 사용
   */
  async channelLeave(channelName: string, userId: string): Promise<void> {
    // return await this._post('/users/${userId}/leave', { channel_name: channelName });
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=43424902
   *
   * 사용자가 채팅을 보이지 않도록 등록한 사용자의 수를 조회합니다.
   */
  async getUserMuteUserCount(userId: string): Promise<number> {
    // TODO database로 처리해야함.

    // const args = await this.get(`/users/${userId}/mute/count`);
    // const muteUserCount = args?.mute_user_count;
    // if (!Number.isInteger(muteUserCount)) {
    //   throw new MError(
    //     '[GET /users/{userId}/mute/count] unexpected-resp-received',
    //     MErrorCode.LG_VOLANTE_ERROR,
    //     { args }
    //   );
    // }

    // return muteUserCount;

    const userIdAsNumber = Number.parseInt(userId);

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
    const result = await puUserGetMutedCount(userDbPool, userIdAsNumber);
    return result;
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=43421870
   *
   * 차단한 사용자 목록을 가져옵니다.
   * @param userId 차단 주체 volante user id
   */
  async getMuteUserIds(userId: string): Promise<number[]> {
    const userIdAsNumber = Number.parseInt(userId);

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
    const result = await puUserQueryMutedList(userDbPool, userIdAsNumber);
    return result.mutedUserIds;
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=43421866
   *
   * 사용자가 다른 사용자의 채팅을 보이지 않도록 차단 등록합니다.
   * @param userId 차단 주체 volante user id
   * @param targetUserId 차단 당할 volante user id
   */
  async muteUser(userId: string, targetUserId: string): Promise<void> {
    const userIdAsNumber = Number.parseInt(userId);
    const targetUserIdAsNumber = Number.parseInt(targetUserId);

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
    const result = await puUserMute(userDbPool, userIdAsNumber, targetUserIdAsNumber);
    if (result === 1) {
      // already muted.
      throw new MError(
        '[Chat] already-muted-user',
        MErrorCode.CHAT_ALREADY_MUTED_USER,
      );
    }

    await this.notifyMutedListChanged('mute', userIdAsNumber, targetUserIdAsNumber);

    // const IGNORE_ERROR_CODES = [
    //   ChatErrorCode.NOT_EXIST_USER,
    //   ChatErrorCode.ALREADY_MUTED_USER,
    //   ChatErrorCode.EXCEED_MAX_MUTE_USER_COUNT,
    // ] as const;

    // // TODO database로 처리해야함.

    // const result = await this._post(
    //   `/users/${userId}/mute`,
    //   { mute_user_id: targetUserId },
    //   IGNORE_ERROR_CODES
    // );
    // if (result.hasIgnoredError) {
    //   const params = {
    //     userId,
    //     targetUserId,
    //   } as const;
    //   switch (result.ignoredErrorCode as (typeof IGNORE_ERROR_CODES)[number]) {
    //     case ChatErrorCode.NOT_EXIST_USER:
    //       throw new MError(
    //         '[Volante] not-exist-user',
    //         MErrorCode.LG_VOLANTE_NOT_EXIST_USER,
    //         params
    //       );
    //     case ChatErrorCode.ALREADY_MUTED_USER:
    //       throw new MError(
    //         '[Volante] already-muted-user',
    //         MErrorCode.CHAT_ALREADY_MUTED_USER,
    //         params
    //       );
    //     case ChatErrorCode.EXCEED_MAX_MUTE_USER_COUNT:
    //       throw new MError(
    //         '[Volante] exceed-max-mute-count',
    //         MErrorCode.CHAT_EXCEED_MAX_MUTE_USER_COUNT,
    //         params
    //       );
    //     default:
    //       assert.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
    //   }
    // }
    // return result;
  }

  /**
   * @see https://developer.line.games/pages/viewpage.action?pageId=43421868
   *
   * 사용자가 다른 사용자의 채팅을 보이도록 등록된 차단을 해제합니다.
   * @param userId 차단 주체 volante user id
   * @param targetUserId 차단 당할 volante user id
   */
  async unmuteUser(userId: string, targetUserId: string): Promise<JsonLike> {
    // const IGNORE_ERROR_CODES = [
    //   ChatErrorCode.NOT_EXIST_USER,
    //   ChatErrorCode.NOT_MUTED_USER,
    // ] as const;

    // // TODO database로 처리해야함.

    // const result = await this._delete(`/users/${userId}/mute/${targetUserId}`, IGNORE_ERROR_CODES);
    // if (result.hasIgnoredError) {
    //   const params = {
    //     userId,
    //     targetUserId,
    //   } as const;
    //   switch (result.ignoredErrorCode as (typeof IGNORE_ERROR_CODES)[number]) {
    //     case ChatErrorCode.NOT_EXIST_USER:
    //       throw new MError(
    //         '[Volante] not-exist-user',
    //         MErrorCode.LG_VOLANTE_NOT_EXIST_USER,
    //         params
    //       );
    //     case ChatErrorCode.NOT_MUTED_USER:
    //       throw new MError('[Volante] not-muted-user', MErrorCode.CHAT_NOT_MUTED_USER, params);
    //     default:
    //       assert.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
    //   }
    // }
    // return result;

    const userIdAsNumber = Number.parseInt(userId);
    const targetUserIdAsNumber = Number.parseInt(targetUserId);

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userIdAsNumber);
    const result = await puUserUnmute(userDbPool, userIdAsNumber, targetUserIdAsNumber);
    if (result === 1) {
      throw new MError('[Chat] not-muted-user', MErrorCode.CHAT_NOT_MUTED_USER);
    }

    await this.notifyMutedListChanged('unmute', userIdAsNumber, targetUserIdAsNumber);

    return {
    };
  }

  private async notifyMutedListChanged(op: string, userId: number, targetUserId: number): Promise<void> {
    const pubsub = Container.of('pubsub-world').get(Pubsub);
    if (pubsub) {
      const payload = {
        op,
        userId,
        targetUserId,
      }
      await pubsub.publish('sdo-user-mute-list-changed', JSON.stringify(payload));
    }
  }

  async updateVolanteUser(user: User): Promise<void> {
    // const volanteId = user.userId.toString();
    // const extraData = {
    //   nationCmsId: user.nationCmsId,
    //   guildId: user.userGuild.guildId,
    //   guildName: user.userGuild.getGuildName(),
    // };
    // try {
    //   return await this.updateUser(volanteId, user.userName, extraData);
    // } catch (err) {
    //   mlog.error('[VOLANTE] update user is failed', { userId: user.userId, err });
    // }
  }

  protected async _get(url: string, ignoreErrors: ChatErrorCode[] = []): Promise<JsonLike> {
    try {
      const axios = this.mrest.axios();
      const resp = await axios.get(url, {
        headers: { Authorization: `custom ${this.salt}` },
        timeout: AxiosTimeout,
        validateStatus: () => true,
      });
      return await this.handleResponse(url, {}, resp, ignoreErrors);
    } catch (error) {
      this.handleError(url, null, error);
    }
  }

  protected async _post(
    url: string,
    body: any,
    ignoreErrors: readonly ChatErrorCode[] = []
  ): Promise<JsonLike> {
    try {
      const axios = this.mrest.axios();
      const resp = await axios.post(url, body, {
        headers: { Authorization: `custom ${this.salt}` },
        timeout: AxiosTimeout,
        validateStatus: () => true,
      });

      return await this.handleResponse(url, body, resp, ignoreErrors);
    } catch (error) {
      this.handleError(url, body, error);
    }
  }

  protected async _patch(
    url: string,
    body: any,
    ignoreErrors: ChatErrorCode[] = []
  ): Promise<JsonLike> {
    try {
      const axios = this.mrest.axios();
      const resp = await axios.patch(url, body, {
        headers: { Authorization: `custom ${this.salt}` },
        timeout: AxiosTimeout,
        validateStatus: () => true,
      });
      return await this.handleResponse(url, body, resp, ignoreErrors);
    } catch (error) {
      this.handleError(url, body, error);
    }
  }

  protected async _delete(
    url: string,
    ignoreErrors: readonly ChatErrorCode[] = []
  ): Promise<JsonLike> {
    try {
      const axios = this.mrest.axios();
      const resp = await axios.delete(url, {
        headers: { Authorization: `custom ${this.salt}` },
        timeout: AxiosTimeout,
        validateStatus: () => true,
      });
      return await this.handleResponse(url, {}, resp, ignoreErrors);
    } catch (error) {
      this.handleError(url, null, error);
    }
  }

  private handleError(url: string, body: any, error: MError | NodeJS.ErrnoException) {
    if (error instanceof MError) {
      this.sendToSlack(
        `[Volante] handle reponse error, url: ${url}, error: ${error.message}`
      ).catch();

      const volanteErrorCode = error.extra?.error?.error_code || undefined;
      throw new MError('chatApiClient, volante api err', error.mcode, {
        url,
        volanteErrorCode,
        merror: {
          mcode: error.mcode,
          message: error.message,
          extra: error.extra,
        },
      });
    }

    this.sendToSlack(
      `[Volante] exception, url: ${url}, error: ${error.message}, code: ${error.code ? error.code : 'undefined'
      }`
    ).catch();
    throw new MError(
      'chatApiClient, error occured',
      MErrorCode.INTERNAL_ERROR,
      `'${url}', message: ${error.message}`
    );
  }

  private async handleResponse(
    url: string,
    body: any,
    resp: AxiosResponse,
    ignoreErrors: readonly ChatErrorCode[]
  ): Promise<JsonLike> {
    const result = resp.data;
    if (result.success) {
      return result.args;
    }

    // if skippable error, return empty body result
    if (result.error?.error_code) {
      const errcode = result.error.error_code;
      if (ignoreErrors.indexOf(errcode) !== -1) {
        return {
          hasIgnoredError: true,
          ignoredErrorCode: errcode,
        };
      }
    } else {
      this.sendToSlack(
        `[Volante] response error, url: ${url}, body: ${JSON.stringify(
          body
        )}, result: ${JSON.stringify(result)}`
      ).catch();
    }

    throw new MError('Volante-api-failed', MErrorCode.LG_VOLANTE_ERROR, result);
  }
}
