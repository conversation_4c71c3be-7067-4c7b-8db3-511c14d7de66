// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import * as cmsEx from './ex';
import { ContentsTermsParamDesc } from './contentsTermsDesc';
import { CmsTable, CmsTableQuery, processTable } from './cmsTable';
import { EventPageDesc } from './eventPageDesc';
import { WORLD_BUFF_TIME_TYPE, WorldBuffElem } from './worldBuffDesc';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { SECONDS_PER_DAY, SECONDS_PER_HOUR } from '../formula';
import { Cms } from '.';
import { LGBillingCode } from '../motiflib/mhttp/iPlatformBillingApiClient';

// ----------------------------------------------------------------------------
// https://wiki.line.games/display/MOTIF/CashShop
// ----------------------------------------------------------------------------

export enum CASH_SHOP_PRODUCT_CATEGORY {
  POINT = 1, // 재화
  RECOMMEND = 2, // 추천
  SINGLE = 3, // 상품
  DAILY = 4, // 일일 한정
  BUFF = 5, // 기능
  GACHA = 6, // 상자
  QUEST_PASS = 7, // 회고록
  EXCHANGE = 8, // 교환소
  BGM = 9, // BGM
}

export enum CASH_SHOP_SALE_TYPE {
  UNLIMITED = 1,
  DAY = 2,
  WEEK = 3,
  MONTH = 4,
  LIMIT = 5,
}

export enum CASH_SHOP_SALE_POINT_TYPE {
  POINT = 1,
  CASH = 2,
}

export enum CASH_SHOP_PRODUCT_TYPE {
  REWARD_FIXED = 1,
  BUFF = 2,
  TAX_FREE_PERMIT = 3,
  GACHA_BOX = 4,
  QUEST_PASS = 5,
  SOUND = 6,
  // ENCOUNT_SHIELD = 7,
  EVENT_PAGE = 8,
  DAILY_SUBSCRIPTION = 9,
  HOT_SPOT = 10,
  ILLUST_SKIN = 11,
  MATE = 12,
  SERVER_TRANSFER = 13,
  USER_TITLE = 14,
}

export enum HOT_SPOT_RESET_TYPE {
  NONE = 0,
  RESET = 1,
}

export enum SHOP_CASE {
  WEB_SHOP = 0,
  CASH_SHOP = 1,
  BOTH = 2,
}

export interface RawCashShopDesc {
  id: number;
  productName: string;
  isToBuy: boolean;
  productNameFormatText: string;
  productCategory: CASH_SHOP_PRODUCT_CATEGORY;
  productType: CASH_SHOP_PRODUCT_TYPE;
  saleType: CASH_SHOP_SALE_TYPE;
  saleTypeVal: number; // 구매 횟수
  productLocalBitflag: number;
  salePointType: CASH_SHOP_SALE_POINT_TYPE;
  salePointId: number;
  salePointVal: number;
  mileageBonusPer?: number;
  previousId: number;
  contentsTerms: ContentsTermsParamDesc[];
  saleStartDate: string;
  saleEndDate: string;
  productRewardFixedId: number;
  durationDays: number;
  durationHours: number;
  buffGroup: number;
  buffGroupLevel: number;
  productWorldBuffId: number[];
  taxFreePermitId: number;
  boxGroup: number;
  bonusboxGroup: number;
  illustSkinId: number;
  ceilingCount: number;
  ceilingCashShopBoxRatioGroup: number;
  /**
   * - {@link CASH_SHOP_PRODUCT_TYPE.QUEST_PASS}.
   * - 하나의 id 를 여러 CMS.CashShop 에서 참조하지 않는 구조로 협의된 듯.
   */
  questPassId: number;
  /**
   * - {@link CASH_SHOP_PRODUCT_TYPE.SOUND}.
   */
  soundPackId: number;
  /**
   * - {@link CASH_SHOP_PRODUCT_TYPE.EVENT_PAGE}.
   * - CMS.EventPage.type 에 따라 컨텐츠가 다른 것 참고
   *   - 패스 이벤트: 패스 이벤트 티켓(추가보상권)
   *   - 패키지 이벤트: 이벤트 오픈(해금)
   *   - 나머지 타입: 사용X, 사용시 구현 필요.
   * - 하나의 id 를 여러 CMS.CashShop 에서 참조하지 않는 구조라고는 함.
   */
  eventPageId: number;

  mateId: number;

  // Billing
  /** ex) 'uwo_google_play_redgem300' */
  productCodeGoogle: string;
  /** ex) 'uwo_apple_app_redgem300' */
  productCodeApple: string;
  /** ex) 'uwo_floor_store_redgem300' */
  productCodeFloor: string;
  /** ex) 'uwo_steam_redgem300' */
  productCodeSteam: string;
  cashShopBoxTicketId: number[];
  cashShopBoxTicketVal: number;
  singleBoxCashShopId: number; // 11연차 일 경우 존재함.
  dailySubscriptionId: number;

  endContentsTerms: ContentsTermsParamDesc[];
  endPopupCount: number;
  coolTimeDays: number;
  isSaleReturnUser: boolean; // 복귀 유저 상품
  returnUserSaleDurationDays: number; // 복귀 유저 상품 판매 기간

  userTitleId: number;

  // 연속 구매 할인은 현금 구매만 적용
  consecutiveProductCodeGoogle: string;
  consecutiveProductCodeApple: string;
  consecutiveProductCodeFloor: string;
  consecutiveProductCodeSteam: string;
  normalSaleHours: number;
  discountSaleHours: number;
  discountValue: number;

  petId: number;

  HotSpotResetType: HOT_SPOT_RESET_TYPE; // 0: 등장 횟수 초기화 x, 1: 등장 횟수 초기화 o
  HotSpotResetValue: number;

  shopCase: SHOP_CASE;
  passGroup: number;
}

export interface CashShopDesc extends RawCashShopDesc {
  worldBuffElems: WorldBuffElem[];
  durationSec?: number;
  expiredAt?: number;
  needToExtend: boolean;
}

export function getCashShopMileageBonus(
  cashShopCms: Pick<CashShopDesc, 'salePointId' | 'mileageBonusPer'>,
  costPointValue: number
): number | undefined {
  if (cashShopCms.mileageBonusPer === undefined) {
    return undefined;
  }
  if (cashShopCms.salePointId === cmsEx.RedGemPointCmsId) {
    // RedGem으로 구매할 경우 마일리지 적립.
    // 구매 비용 * (mileageBonusPer/1000), 소수점버림
    return Math.floor(costPointValue * (cashShopCms.mileageBonusPer / 1000));
  }
  return undefined;
}

export function isConsecutiveProductCode(cashShopCms: CashShopDesc, productCode: string) {
  if (
    !productCode ||
    !cashShopCms.consecutiveProductCodeGoogle ||
    !cashShopCms.consecutiveProductCodeApple ||
    !cashShopCms.consecutiveProductCodeFloor ||
    !cashShopCms.consecutiveProductCodeSteam
  ) {
    return false;
  }

  switch (productCode) {
    case cashShopCms.consecutiveProductCodeGoogle:
    case cashShopCms.consecutiveProductCodeApple:
    case cashShopCms.consecutiveProductCodeFloor:
    case cashShopCms.consecutiveProductCodeSteam:
      return true;
    default:
      return false;
  }
}

export function getConsecutiveProductCodeByStoreCode(cashShopCms: CashShopDesc, storeCode: string) {
  switch (storeCode) {
    case LGBillingCode.APP_STORE_CD.GOOGLE_PLAY:
      return cashShopCms.consecutiveProductCodeGoogle;
    case LGBillingCode.APP_STORE_CD.APPLE_APP_STORE:
      return cashShopCms.consecutiveProductCodeApple;
    case LGBillingCode.APP_STORE_CD.FLOOR_STORE:
      return cashShopCms.consecutiveProductCodeFloor;
    case LGBillingCode.APP_STORE_CD.STEAM:
      return cashShopCms.consecutiveProductCodeSteam;
    default:
      return undefined;
  }
}

export function getProductCodeByStoreCode(cashShopCms: CashShopDesc, storeCode: string) {
  switch (storeCode) {
    case LGBillingCode.APP_STORE_CD.GOOGLE_PLAY:
      return cashShopCms.productCodeGoogle;
    case LGBillingCode.APP_STORE_CD.APPLE_APP_STORE:
      return cashShopCms.productCodeApple;
    case LGBillingCode.APP_STORE_CD.FLOOR_STORE:
      return cashShopCms.productCodeFloor;
    case LGBillingCode.APP_STORE_CD.STEAM:
      return cashShopCms.productCodeSteam;
    default:
      return undefined;
  }
}

export function getWorldBuffAddTime(cashShopCms: CashShopDesc): number {
  if (cashShopCms.expiredAt !== undefined) {
    return Math.max(0, cashShopCms.expiredAt - mutil.curTimeUtc());
  }
  if (cashShopCms.durationSec !== undefined) {
    return cashShopCms.durationSec;
  }
  
  return 0;
}

export function isExistWorldBuffTimeField(cashShopCms: CashShopDesc): boolean {
  return cashShopCms.expiredAt !== undefined || cashShopCms.durationSec !== undefined;
}

export const processCashShop = (cms: Cms) =>
  (rawCashShopDesc: RawCashShopDesc): CashShopDesc => {
    let worldBuffElems: WorldBuffElem[] = [];
    let durationSec = undefined;
    let expiredAt = undefined;
    let worldBuffTimeType: WORLD_BUFF_TIME_TYPE = undefined;

    const rawCashShops = cms.CashShop
    const eventPages = cms.EventPage

    // 버프인 경우.
    if (rawCashShopDesc.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
      // 1. 시즌 패스 버프. (passGroup이 있고 productType이 BUFF이면)
      if (rawCashShopDesc.passGroup && rawCashShopDesc.productType === CASH_SHOP_PRODUCT_TYPE.BUFF) {
        // passGroup 으로 연결된 상위 상품 찾고
        // 그 상품이 가진 eventPageId 로 EventPage를 찾고
        // 1. EventPage의 endDate를 expiredAt으로 사용

        // EventPage의 endDate 이 없을 경우
        // 2. durationDays을 사용. 없다면
        // 3. durationHours을 사용. 없다면
        // 4. saleEndDate를 사용. 없다면
        // 5. cmsEx.TheEndTimeUtc 사용
        // 미쳤따..

        const parentCashShops = Object.values(rawCashShops).filter((rawCashShop) =>
          rawCashShop.passGroup === rawCashShopDesc.passGroup && rawCashShop.eventPageId
        );

        if (parentCashShops.length != 1) {
          mlog.error('multiple-or-missing-parent-cash-shops-for-pass-group', {
            cashShopId: rawCashShopDesc.id,
            passGroup: rawCashShopDesc.passGroup,
            parentCashShopsCount: parentCashShops.length,
          });
          throw new Error(`multiple-or-missing-parent-cash-shops-for-pass-group`);
        }

        const parentCashShop = parentCashShops[0];
        if (!parentCashShop.eventPageId) {
          mlog.error('parent-cash-shop-doesnt-have-event-page-id', {
            cashShopId: rawCashShopDesc.id,
            passGroup: rawCashShopDesc.passGroup,
            parentCashShopId: parentCashShop.id,
          });
          throw new Error(`parent-cash-shop-doesnt-have-event-page-id`);
        }

        const eventPage = eventPages[parentCashShop.eventPageId]
        if (!eventPage) {
          mlog.error('cant-find-event-page-for-pass-group', {
            eventPageId: parentCashShop.eventPageId,
            passGroup: parentCashShop.passGroup,
          });
          throw new Error(`cant-find-event-page-for-pass-group`);
        }

        if (eventPage.endDate) { // 1. EventPage의 endDate
          worldBuffTimeType = WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
          expiredAt = mutil.dateToUtc(mutil.newDateByCmsDateStr(eventPage.endDate));
        }
        else if (rawCashShopDesc.durationDays) {  // 2. durationDays
          worldBuffTimeType = WORLD_BUFF_TIME_TYPE.DURATION;
          durationSec = rawCashShopDesc.durationDays * SECONDS_PER_DAY;
        }
        else if (rawCashShopDesc.durationHours) { // 3. durationHours
          worldBuffTimeType = WORLD_BUFF_TIME_TYPE.DURATION;
          durationSec = rawCashShopDesc.durationHours * SECONDS_PER_HOUR;
        }
        else if (rawCashShopDesc.saleEndDate) { // 4. cashShop의 saleEndDate
          worldBuffTimeType = WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
          expiredAt = mutil.dateToUtc(mutil.newDateByCmsDateStr(rawCashShopDesc.saleEndDate));
        }
        else {  // 5. cmsEx.TheEndTimeUtc
          worldBuffTimeType = WORLD_BUFF_TIME_TYPE.EXPIRED_AT;
          expiredAt = cmsEx.TheEndTimeUtc;
        }

      } else if (rawCashShopDesc.durationDays) { // 2. durationDays
        worldBuffTimeType = WORLD_BUFF_TIME_TYPE.DURATION;
        durationSec = rawCashShopDesc.durationDays * SECONDS_PER_DAY;
      } else if (rawCashShopDesc.durationHours) {  // 3. durationHours
        worldBuffTimeType = WORLD_BUFF_TIME_TYPE.DURATION;
        durationSec = rawCashShopDesc.durationHours * SECONDS_PER_HOUR;
      }

      if (worldBuffTimeType === undefined) {
        mlog.error('cash-shop-has-no-world-buff-time-type', {
          cashShopId: rawCashShopDesc.id,
        });
        throw new Error(`cash-shop-has-no-world-buff-time-type`);
      }

      if (worldBuffTimeType === WORLD_BUFF_TIME_TYPE.DURATION && durationSec === undefined) {
        mlog.error('cash-shop-has-no-duration-sec', {
          cashShopId: rawCashShopDesc.id,
        });
        throw new Error(`cash-shop-has-no-duration-sec`);
      }

      if (worldBuffTimeType === WORLD_BUFF_TIME_TYPE.EXPIRED_AT && expiredAt === undefined) {
        mlog.error('cash-shop-has-no-expired-at', {
          cashShopId: rawCashShopDesc.id,
        });
        throw new Error(`cash-shop-has-no-expired-at`);
      }

      if (worldBuffTimeType === WORLD_BUFF_TIME_TYPE.DURATION && durationSec === undefined) {
        mlog.error('cash-shop-has-zero-duration-sec', {
          cashShopId: rawCashShopDesc.id,
        });
        throw new Error(`cash-shop-has-zero-duration-sec`);
      }
    }
    
    // worldBuffElems
    worldBuffElems = rawCashShopDesc.productWorldBuffId?.map((worldBuffId) => {
      return {
        worldBuffId,
        worldBuffTimeType: worldBuffTimeType,
        expiredAt: expiredAt,
        durationSec: durationSec,
        needToExtend: false,
      }
    }) || [];

    return {
      ...rawCashShopDesc,
      worldBuffElems: worldBuffElems,
      durationSec: durationSec,
      expiredAt: expiredAt,
      needToExtend: expiredAt === undefined,
    }
  }

export const processCashShops = (cms: Cms) => {
  const process = processCashShop(cms);
  return (rawCashShops: CmsTable<RawCashShopDesc>, name: string) =>
    processTable(process, rawCashShops, name);
};

