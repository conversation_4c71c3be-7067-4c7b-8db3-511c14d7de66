"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_SelectNation = void 0;
const typedi_1 = require("typedi");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const merror_1 = require("../../../motiflib/merror");
const server_1 = require("../../server");
const userConnection_1 = require("../../userConnection");
const userChangeTask_1 = require("../../UserChangeTask/userChangeTask");
const userChangeOperator_1 = require("../../UserChangeTask/userChangeOperator");
const cms_1 = __importDefault(require("../../../cms"));
const cmsEx = __importStar(require("../../../cms/ex"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const rewardDesc_1 = require("../../../cms/rewardDesc");
const mutil_1 = require("../../../motiflib/mutil");
// ----------------------------------------------------------------------------
// !! 이민 => changeNation 패킷으로 대체 되었습니다.
// ----------------------------------------------------------------------------
const rsn = 'select_nation';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_SelectNation {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        // validate connection state
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const body = packet.bodyObj;
        const { nationCmsId } = body;
        const { nationManager } = typedi_1.Container.get(server_1.LobbyService);
        // validate request body
        if (nationCmsId === 0 || !nationManager.has(nationCmsId)) {
            throw new merror_1.MError('no-key-in-nation-cms', merror_1.MErrorCode.NO_KEY_IN_CMS);
        }
        const nation = nationManager.get(nationCmsId);
        // 1. 유저의 조건이 국가 선택 조건과 맞아야 합니다.
        // 기획에서 레벨 조건 필요없다고 함
        // if (user.level < cms.Const.NationChooseFleetLv.value) {
        //   throw new MError('nation-select-not-allowed', MErrorCode.NATION_SELECT_NOT_ALLOWED_ERROR, {
        //     level: user.level,
        //   });
        // }
        // 2. 왕궁이 있는 도시인지 체크
        if (user.userTown.getTownBldg() !== cmsEx.BUILDING_TYPE.PALACE) {
            throw new merror_1.MError('not-palace-town', merror_1.MErrorCode.NOT_PALACE_TOWN, {
                body,
            });
        }
        // 3. 왕궁의 언어레벨 조건을 만족 해야 한다
        user.userContentsTerms.ensureBuildingContentsUnlock(cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.CHANGE_NATION, user);
        // 4. 첫 망명 인지 체크
        if (user.lastUpdateNationTimeUtc) {
            throw new merror_1.MError('already-change-nation', merror_1.MErrorCode.INVALID_CHANGE_NATION, {
                body,
                lastUpdateNationTimeUtc: user.lastUpdateNationTimeUtc,
            });
        }
        // 해당 국가가 선택 제한 상태가 아니어야 합니다.
        if (nation.isRestricted(nationManager)) {
            return user.sendJsonPacket(packet.seqNum, packet.type, { bRestricted: true });
        }
        const reward_data = [];
        const changeTask = new userChangeTask_1.UserChangeTask(user, userChangeTask_1.CHANGE_TASK_REASON.SELECT_NATION, new SelectNationSpec(nationCmsId, (0, mutil_1.curTimeUtc)(), nationManager.getPowerRank(nationCmsId), reward_data));
        const res = changeTask.trySpec();
        if (res !== userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            throw new merror_1.MError('try-spec-failed', merror_1.MErrorCode.TRY_SELECT_NATION_SPEC_FAILED, {
                res,
            });
        }
        const resp = {};
        return changeTask.apply().then((sync) => {
            resp.sync = sync;
            const townInfo = user.userTown.getTownInfo();
            if (townInfo) {
                const townApi = mhttp_1.default.townpx.channel(townInfo.url);
                townApi.updateTownUserSyncData(user.userId, { user: { nationCmsId } }).catch((err) => {
                    mlog_1.default.error('Town api updateTownUserSyncData is failed.', {
                        userId: user.userId,
                        err: err.message,
                    });
                });
            }
            mhttp_1.default.authd.changeUserNation(user.userId, nationCmsId).catch((err) => {
                mlog_1.default.error('Auth api changeUserNation is failed.', {
                    userId: user.userId,
                    nationCmsId,
                    err: err.message,
                });
            });
            mhttp_1.default.platformChatApi.updateVolanteUser(user);
            // glog
            user.glog('nation_change', {
                rsn,
                add_rsn,
                old_nation: null,
                cur_nation: cms_1.default.Nation[nationCmsId].name,
                pr_data: null,
                reward_data,
            });
            return user.sendJsonPacket(packet.seqNum, packet.type, resp);
        });
    }
}
exports.Cph_Common_SelectNation = Cph_Common_SelectNation;
class SelectNationSpec {
    constructor(nationCmsId, lastUpdateNationTimeUtc, nationRank, reward_data) {
        this.nationCmsId = nationCmsId;
        this.lastUpdateNationTimeUtc = lastUpdateNationTimeUtc;
        this.nationRank = nationRank;
        this.reward_data = reward_data;
    }
    accumulate(user, tryData, changes) {
        let res = (0, userChangeOperator_1.opSetNation)(user, tryData, changes, this.nationCmsId, this.lastUpdateNationTimeUtc);
        if (res === userChangeTask_1.CHANGE_TASK_RESULT.OK) {
            if (this.nationRank) {
                const nationRankingEffectCms = cms_1.default.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.SELECT_NATION];
                const nationRankBonus = nationRankingEffectCms.rankingEffectVal[this.nationRank - 1];
                if (nationRankBonus) {
                    res = (0, userChangeOperator_1.opAddPoint)(user, tryData, changes, cms_1.default.Const.NationPickBonusPointId.value, nationRankBonus, false, false, undefined, false);
                    this.reward_data.push({
                        type: rewardDesc_1.REWARD_TYPE[rewardDesc_1.REWARD_TYPE.POINT],
                        id: cms_1.default.Const.NationPickBonusPointId.value,
                        uid: null,
                        amt: nationRankBonus,
                    });
                }
            }
        }
        return res;
    }
}
//# sourceMappingURL=selectNation.js.map