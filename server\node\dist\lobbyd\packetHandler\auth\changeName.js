"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Auth_ChangeName = void 0;
const typedi_1 = require("typedi");
const lodash_1 = __importDefault(require("lodash"));
const userConnection_1 = require("../../userConnection");
const tuChangeUserName_1 = __importDefault(require("../../../mysqllib/txn/tuChangeUserName"));
const server_1 = require("../../server");
const gameState_1 = require("../../../motiflib/model/lobby/gameState");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const cms_1 = __importDefault(require("../../../cms"));
const merror_1 = require("../../../motiflib/merror");
const userPoints_1 = __importDefault(require("../../userPoints"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const townManager_1 = require("../../townManager");
const mutilLanguage = __importStar(require("../../../motiflib/mutilLanguage"));
// ----------------------------------------------------------------------------
// 선단명 변경.
// ----------------------------------------------------------------------------
const rsn = 'change_name';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Auth_ChangeName {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        const body = packet.bodyObj;
        const { name, bPermitExchange, preoccupancyCode } = body;
        // Check body.
        const oldName = user.userName;
        mutilLanguage.ensureValidName(oldName, name, cms_1.default.Const.CompanyNameMinimum.value, cms_1.default.Const.CompanyNameMaximum.value);
        const { userDbConnPoolMgr, userRedis, userCacheRedis } = typedi_1.Container.get(server_1.LobbyService);
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        // 처음 계정을 생성하는 경우 game state 를 NAME_SET 로 변경해준다.
        let bIsNewUser = false;
        let gameStateChange;
        let pointChanges;
        let cashPayments;
        if (!oldName) {
            bIsNewUser = true;
            gameStateChange = user.userState.buildGameStateChange(gameState_1.GAME_STATE.NAME_SET);
        }
        else {
            const pcChanges = user.userPoints.buildPointAndCashChangesByPayment([
                {
                    cmsId: cms_1.default.Const.ChangeCompanyNamePointId.value,
                    cost: cms_1.default.Const.ChangeCompanyNamePointValue.value,
                },
            ], bPermitExchange, { itemId: rsn }, true);
            pointChanges = pcChanges.pointChanges;
            cashPayments = pcChanges.cashPayments;
            // 시장은 이름을 변경할 수 없음.
            const townManager = typedi_1.Container.get(townManager_1.TownManager);
            const curTimeUtc = mutil.curTimeUtc();
            for (const townCms of lodash_1.default.values(cms_1.default.Town)) {
                if (townManager.getTownMayorUserId(townCms.id, curTimeUtc) === user.userId) {
                    throw new merror_1.MError('can-not-change-user-name-because-your-are-mayor', merror_1.MErrorCode.CANNOT_CHANGE_MAYOR_USER_NAME, {
                        townCmsId: townCms.id,
                    });
                }
            }
        }
        const resp = {
            bDuplicated: false,
            sync: {},
        };
        let bCommittedToAuthDb = false; // rollback 을 위한
        let bCommittedToUserDb = false; // rollback 을 위한
        const exchangeHash = userPoints_1.default.generateExchangeHash(user.userId);
        return Promise.resolve()
            .then(() => {
            if (!user.isTestBot()) {
                return mhttp_1.default.platformApi.hasBadWord(name);
            }
            return false;
        })
            .then((bHas) => {
            if (bHas) {
                throw new merror_1.MError('has-bad-word', merror_1.MErrorCode.HAS_BAD_WORD, {
                    name,
                    oldName: user.userName,
                });
            }
            return mhttp_1.default.authd.changeUserName(user.userId, name, mconf_1.default.worldId, preoccupancyCode);
        })
            .then((bIsDuplicated) => {
            resp.bDuplicated = bIsDuplicated;
            if (!resp.bDuplicated) {
                bCommittedToAuthDb = true;
                return user.userPoints.tryConsumeCashs(cashPayments, resp.sync, user, {
                    user,
                    rsn,
                    add_rsn,
                    exchangeHash,
                });
            }
            return null;
        })
            .then(() => {
            if (!resp.bDuplicated) {
                return (0, tuChangeUserName_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, name, gameStateChange, pointChanges);
            }
            return null;
        })
            .then(() => {
            if (!resp.bDuplicated) {
                bCommittedToUserDb = true;
                user.glog('common_nickname', {
                    old_nick: user.userName,
                    cur_nick: name,
                    exchange_hash: exchangeHash,
                });
                user.userName = name;
                userRedis['setUserName'](user.userId, name).catch((err) => {
                    mlog_1.default.error('userRedis setUserName is failed.', {
                        userId: user.userId,
                        name,
                        err: err.message,
                    });
                });
                userCacheRedis['setUserName'](user.userId, name).catch((err) => {
                    mlog_1.default.error('userCacheRedis setUserName is failed.', {
                        userId: user.userId,
                        name,
                        err: err.message,
                    });
                });
                lodash_1.default.merge(resp.sync, {
                    add: {
                        user: {
                            name,
                        },
                    },
                });
                if (gameStateChange) {
                    lodash_1.default.merge(resp.sync, user.userState.applyGameStateChange(gameStateChange, { user }));
                }
                lodash_1.default.merge(resp.sync, user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn }));
            }
            if (!bIsNewUser) {
                // 근처 유저에게 노티.
                if (user.userState.isInTown()) {
                    const townInfo = user.userTown.getTownInfo();
                    if (townInfo) {
                        const townApi = mhttp_1.default.townpx.channel(townInfo.url);
                        townApi.updateTownUserSyncData(user.userId, { user: { name } }).catch((err) => {
                            mlog_1.default.error('Town api updateTownUserSyncData is failed.', {
                                userId: user.userId,
                                err: err.message,
                                name,
                            });
                        });
                    }
                }
                else if (user.userState.isInOcean()) {
                    // TODO
                }
                mhttp_1.default.platformChatApi.updateVolanteUser(user);
            }
            // 선단명 선점 glog
            if (preoccupancyCode) {
                user.glog('nick_preoccupancy_code', {
                    rsn,
                    add_rsn,
                    preoccupancy_code: preoccupancyCode,
                });
            }
            return user.sendJsonPacket(packet.seqNum, packet.type, resp);
        })
            .catch((err) => {
            return Promise.resolve()
                .then(() => {
                if (bCommittedToAuthDb && !bCommittedToUserDb) {
                    mlog_1.default.warn('auth/changeName rollback auth name change.', {
                        userId: user.userId,
                        oldName,
                        name,
                        err: err.message,
                    });
                    return mhttp_1.default.authd.changeUserName(user.userId, oldName, mconf_1.default.worldId);
                }
                return null;
            })
                .then((bIsDuplicated) => {
                if (bCommittedToAuthDb && !bCommittedToUserDb && bIsDuplicated) {
                    // TODO 완전한 해결을 위해 reserved system 필요함.
                    // (한 번 사용된 닉네임은 다른 닉네임으로 변경하더라도 일정 기간 사용 못 하게 해야 됨)
                    mlog_1.default.warn('auth/changeName rollback is failed.', {
                        userId: user.userId,
                        oldName,
                        name,
                        bIsDuplicated,
                        err: err.message,
                    });
                }
                throw err;
            });
        });
    }
}
exports.Cph_Auth_ChangeName = Cph_Auth_ChangeName;
//# sourceMappingURL=changeName.js.map