"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authApiClient_1 = require("./authApiClient");
const configApiClient_1 = require("./configApiClient");
const sailApiClient_1 = require("./sailApiClient");
const oceanApiClient_1 = require("./oceanApiClient");
const lobbyApiClient_1 = require("./lobbyApiClient");
const townApiClient_1 = require("./townApiClient");
const zonelbApiClient_1 = require("./zonelbApiClient");
const realmApiClient_1 = require("./realmApiClient");
const navApiClient_1 = require("./navApiClient");
const chatApiClient_1 = require("./chatApiClient");
const linegamesApiClient_1 = require("./linegamesApiClient");
const mconf_1 = __importDefault(require("../mconf"));
const linegamesLogApiClient_1 = require("./linegamesLogApiClient");
const linegamesBillingApiClient_1 = require("./linegamesBillingApiClient");
const linegamesPayApiClient_1 = require("./linegamesPayApiClient");
const sdoApiClient_1 = require("./sdoApiClient");
const sdoBillingApiClient_1 = require("./sdoBillingApiClient");
const enum_1 = require("../model/auth/enum");
const mlog_1 = __importDefault(require("../mlog"));
const sdoAntiAddictionApiClient_1 = require("./sdoAntiAddictionApiClient");
const linegamesChatApiClient_1 = require("./linegamesChatApiClient");
const sdoChatApiClient_1 = require("./sdoChatApiClient");
// 모든 서버에 configd 설정은 있어야 한다.
class HttpClients {
    constructor() {
        // 각 월드에 소속된 http목록. admind에서 사용.
        this.worldHttp = {};
        this.configd = new configApiClient_1.ConfigApiClient(mconf_1.default.http.configd.url, mconf_1.default.http.configd.timeout);
        this.authd = new authApiClient_1.AuthApiClient();
        this.oceanpx = new oceanApiClient_1.ProxyOceanApiClient();
        this.townpx = new townApiClient_1.ProxyTownApiClient();
        this.lobbypx = new lobbyApiClient_1.ProxyLobbyApiClient();
        this.saild = new sailApiClient_1.SailApiClient();
        this.zonelbd = new zonelbApiClient_1.ZonelbApiClient();
        this.realmd = new realmApiClient_1.RealmApiClient();
        this.lglogd = new linegamesLogApiClient_1.LineGamesLogApiClient();
        // this.lgbillingd = new LineGamesBillingApiClient();
        //this.chatd = new ChatApiClient();
        this.navid = new navApiClient_1.NavApiClient();
        this.lgpayd = new linegamesPayApiClient_1.LineGamesPayApiClient();
        this.sdoaa = new sdoAntiAddictionApiClient_1.SdoAntiAddictionApiClient();
    }
    // this.lgbillingd.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
    // this.lgbillingd.setAuthPassword(mconf.http.lgbillingd.authPwd);
    init() {
        // Initialize platform API based on platform configuration
        if (mconf_1.default.platform === enum_1.PLATFORM.LINE) {
            const lineGamesApiClient = new linegamesApiClient_1.LineGamesApiClient();
            lineGamesApiClient.init(mconf_1.default.http.lgd.url, mconf_1.default.http.lgd.timeout);
            this.platformApi = lineGamesApiClient;
            const linegamesBillingApiClient = new linegamesBillingApiClient_1.LineGamesBillingApiClient();
            linegamesBillingApiClient.init(mconf_1.default.http.lgbillingd.url, mconf_1.default.http.lgbillingd.timeout);
            linegamesBillingApiClient.setAuthPassword(mconf_1.default.http.lgbillingd.authPwd);
            this.platformBillingApi = linegamesBillingApiClient;
            const linegamesChatApiClient = new linegamesChatApiClient_1.LineGamesChatApiClient();
            linegamesChatApiClient.init(mconf_1.default.http.chatd.url, mconf_1.default.http.chatd.timeout);
        }
        else if (mconf_1.default.platform === enum_1.PLATFORM.SDO) {
            const sdoApiClient = new sdoApiClient_1.SdoApiClient();
            sdoApiClient.init(mconf_1.default.http.sdo.url, mconf_1.default.http.sdo.timeout);
            this.platformApi = sdoApiClient;
            // Initialize platform billing API
            const sdoBillingApiClient = new sdoBillingApiClient_1.SdoBillingApiClient();
            if (mconf_1.default.http.sdo) {
                //sdoBillingClient.init(mconf.http.sdo.url, mconf.http.sdo.timeout);
            }
            this.platformBillingApi = sdoBillingApiClient;
            const sdoChatApiClient = new sdoChatApiClient_1.SdoChatApiClient();
            this.platformChatApi = sdoChatApiClient;
            this.sdoaa.init(mconf_1.default.http.sdoaa.url, mconf_1.default.http.sdoaa.timeout, mconf_1.default.http.sdoaa.remainingTimeWarnThresholdInSec);
        }
        else {
            mlog_1.default.error(`platform id ${mconf_1.default.platform} is not supported`);
        }
        if (mconf_1.default.http.authd) {
            this.authd.init(mconf_1.default.http.authd.url, mconf_1.default.http.authd.timeout);
        }
        if (mconf_1.default.http.saild) {
            this.saild.init(mconf_1.default.http.saild.url, mconf_1.default.http.saild.timeout);
        }
        if (mconf_1.default.http.zonelbd) {
            this.zonelbd.init(mconf_1.default.http.zonelbd.url, mconf_1.default.http.zonelbd.timeout);
        }
        if (mconf_1.default.http.realmd) {
            this.realmd.init(mconf_1.default.http.realmd.url, mconf_1.default.http.realmd.timeout);
        }
        // if (mconf.http.lgd) {
        //   this.lgd.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
        // }
        if (mconf_1.default.http.lglogd) {
            this.lglogd.init(mconf_1.default.http.lglogd.url, mconf_1.default.http.lglogd.timeout);
        }
        // if (mconf.http.lgbillingd) {
        //   this.lgbillingd.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
        //   this.lgbillingd.setAuthPassword(mconf.http.lgbillingd.authPwd);
        // }
        // if (mconf.http.chatd) {
        //   this.chatd.init(mconf.http.chatd.url, mconf.http.chatd.timeout);
        //   this.chatd.setSalt(mconf.http.chatd.salt);
        // }
        if (mconf_1.default.http.navid) {
            this.navid.init(mconf_1.default.http.navid.url, mconf_1.default.http.navid.timeout);
        }
        if (mconf_1.default.http.lgpayd) {
            this.lgpayd.init(mconf_1.default.http.lgpayd.url, mconf_1.default.http.lgpayd.timeout);
            this.lgpayd.setAuthPassword(mconf_1.default.http.lgpayd.authPwd);
        }
        mconf_1.default.worlds.forEach((world) => {
            let platformApi = null;
            let platformBillingApi = null;
            let platformChatApi = null;
            let chatd;
            if (world.http.chatd) {
                chatd = new chatApiClient_1.ChatApiClient();
                chatd.init(world.http.chatd.url, world.http.chatd.timeout);
                chatd.setSalt(world.http.chatd.salt);
            }
            let lgbillingd;
            if (world.http.lgbillingd) {
                lgbillingd = new linegamesBillingApiClient_1.LineGamesBillingApiClient();
                lgbillingd.init(world.http.lgbillingd.url, world.http.lgbillingd.timeout);
                lgbillingd.setAuthPassword(world.http.lgbillingd.authPwd);
            }
            let lgpayd;
            if (world.http.lgpayd) {
                lgpayd = new linegamesPayApiClient_1.LineGamesPayApiClient();
                lgpayd.init(world.http.lgpayd.url, world.http.lgpayd.timeout);
                lgpayd.setAuthPassword(world.http.lgpayd.authPwd);
            }
            let saild;
            if (world.http.saild) {
                saild = new sailApiClient_1.SailApiClient();
                saild.init(world.http.saild.url, world.http.saild.timeout);
            }
            let zonelbd;
            if (world.http.zonelbd) {
                zonelbd = new zonelbApiClient_1.ZonelbApiClient();
                zonelbd.init(world.http.zonelbd.url, world.http.zonelbd.timeout);
            }
            let realmd;
            if (world.http.realmd) {
                realmd = new realmApiClient_1.RealmApiClient();
                realmd.init(world.http.realmd.url, world.http.realmd.timeout);
            }
            //TODO
            // this.worldHttp[world.id] = {
            //   chatd,
            //   lgbillingd,
            //   saild,
            //   zonelbd,
            //   realmd,
            //   lgpayd,
            // };
        });
    }
}
const httpClients = new HttpClients();
exports.default = httpClients;
//# sourceMappingURL=index.js.map