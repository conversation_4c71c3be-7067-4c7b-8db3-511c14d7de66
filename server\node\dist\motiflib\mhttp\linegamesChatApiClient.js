"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LineGamesChatApiClient = void 0;
const os_1 = __importDefault(require("os"));
const crypto = __importStar(require("crypto"));
const assert_1 = __importDefault(require("assert"));
const mconf_1 = __importDefault(require("../mconf"));
const mlog_1 = __importDefault(require("../mlog"));
const baseApiClient_1 = require("./baseApiClient");
const merror_1 = require("../merror");
const slackNotifier_1 = require("../slackNotifier");
const iPlatformChatApiClient_1 = require("./iPlatformChatApiClient");
const AxiosTimeout = 5000;
/**
 * Volante SDK 대응
 */
class LineGamesChatApiClient extends baseApiClient_1.BaseApiClient {
    constructor() {
        super();
    }
    init(baseUrl, timeout) {
        super.init(baseUrl, timeout);
        mlog_1.default.info(`chatd endpoint: ${baseUrl}`);
    }
    setSalt(salt) {
        this.salt = salt || '0';
    }
    async sendToSlack(message) {
        if (this.slackNotifier === undefined) {
            this.slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        }
        await this.slackNotifier.notify({
            username: 'host: ' + os_1.default.hostname(),
            text: message,
            channel: '#sdk-error',
        });
        return Promise.resolve();
    }
    getIdToken(id) {
        const hash = crypto.createHash('SHA256');
        hash.update(id + this.salt);
        return hash.digest('hex');
    }
    async getAllChannels() {
        const args = await this._get('/channels');
        return args.channels;
    }
    async getChannel(channelName) {
        const args = await this._get(`/channels/${channelName}`);
        return args;
    }
    async existChannel(channelName) {
        const axios = this.mrest.axios();
        const resp = await axios.get(`/channels/${channelName}`, {
            headers: { Authorization: `custom ${this.salt}` },
            timeout: AxiosTimeout,
            validateStatus: () => true,
        });
        const result = resp.data;
        return result.success && result.args && result.args.channel_name;
    }
    async getAllowUserList(channelName) {
        const axios = this.mrest.axios();
        const resp = await axios.get(`/channels/${channelName}/allows`, {
            headers: { Authorization: `custom ${this.salt}` },
            timeout: AxiosTimeout,
            validateStatus: () => true,
        });
        const result = resp.data;
        if (result.success && result.args) {
            mlog_1.default.info('[TEMP] [DEBUG] chat allow user list.', {
                channelName,
                list: result.args.allow_users,
            });
            return result.args.allow_users;
        }
        else {
            return [];
        }
    }
    /**
     * 공용 채널 생성 (월드 / 지역 / 국가).
     * public & persistent & !group channel
     */
    async createPublicChannel(name, alias) {
        const body = {
            type: 'persistent',
            name,
            alias,
            group: false,
            public: true,
            user_limit: 100000,
        };
        const args = await this._post(`/channels`, body, [33 /* ALREADY_EXIST_CHANNEL */]);
        return args;
    }
    /**
     * 길드 채널 생성 (월드 / 지역 / 국가)
     * private & persistent & !group channel
     */
    async createGuildChannel(channelName, userId, bShouldAllowChannel) {
        const body = {
            type: 'persistent',
            name: channelName,
            alias: (0, iPlatformChatApiClient_1.getAliasName)(iPlatformChatApiClient_1.CHANNEL_TYPE.GUILD, channelName),
            group: false,
            public: false,
            user_limit: 1000,
        };
        const args = await this._post(`/channels`, body);
        if (bShouldAllowChannel) {
            await this.allowGuildChannel(channelName, userId);
        }
        return args;
    }
    /**
     * 길드 채널 입장 허용
     */
    async allowGuildChannel(channelName, userId) {
        mlog_1.default.info('[TEMP] allowGuildChannel', { channelName, userId });
        const body = {
            user_id: userId,
        };
        return this._post(`/channels/${channelName}/allows`, body);
    }
    /**
     * 길드 채널 허용 사용자 삭제
     */
    async disallowGuildChannel(channelName, userId) {
        await this._delete(`/channels/${channelName}/allows/${userId}`);
    }
    /**
     * 채널 삭제
     */
    async deleteChannel(channelName) {
        await this._delete(`/channels/${channelName}`);
        return Promise.resolve();
    }
    /**
     * 인증 시점에 채팅 서버에 등록되지 않은 유저면 추가한다.
     * (월드 선택 시점에는 채팅 서버에 접속해야 되므로 그 전 단계에서 필수로 진행해야 함)
     */
    async createUserIfNotExists(userId, nickName) {
        let url = `/users/${userId}`;
        try {
            // 조회
            const axios = this.mrest.axios();
            const resp = await axios.get(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            const result = resp.data;
            if (result.success) {
                return; // result.args;
            }
            else {
                if (result.error) {
                    // 사용자 없는 경우에 생성
                    const errcode = result.error.error_code;
                    if (errcode == 45 /* NOT_EXIST_USER */) {
                        url = `/users`;
                        await this._post(`/users`, { id: userId, nickname: nickName }, [
                            46 /* ALREADY_EXIST_USER */,
                        ]);
                    }
                }
                else {
                    throw new merror_1.MError('volante request error', merror_1.MErrorCode.LGSDK_ERROR, {
                        request: url,
                        response: result,
                    });
                }
            }
        }
        catch (error) {
            // 기타 에러 핸들링
            this.handleError(url, null, error);
        }
    }
    /**
     * 사용자 정보 받아오기
     */
    async getUser(userId) {
        const result = await this._get(`/users/${userId}`);
        return result;
    }
    /**
     * 사용자 세션 정보 조회
     */
    async getSessions(userId) {
        const result = await this._get(`/users/${userId}/session`, [1 /* SESSION_NOT_FOUND */]);
        if (result.hasIgnoredError) {
            throw new merror_1.MError('session not found', merror_1.MErrorCode.LG_VOLANTE_INVALID_SESSION);
        }
        return result;
    }
    /**
     * 유저 정보 수정
     */
    async updateUser(userId, nickName, extraData) {
        await this._patch(`/users/${userId}`, { nickname: nickName, extra_data: extraData });
    }
    /**
     * 유저 채널 입장
     * 지역 이동 / 국가 변경시에 채널 Leave와 함께 Join 되어야 합니다. 채널 이름은 CmsId 사용
     */
    async channelJoin(channelName, userId) {
        try {
            await this._post(`/users/${userId}/join`, {
                channel_name: channelName,
                options: 'DO_NOT_ANNOUNCE',
            }, [14 /* ALREADY_JOINED_CHANNEL */]);
        }
        catch (error) {
            // Volante error check
            if (error.extra && error.extra.error_code) {
                const errcode = error.extra.error_code;
                if (errcode == 2 /* INVALID_SESSION */) {
                    throw new merror_1.MError('Join failed', merror_1.MErrorCode.LG_VOLANTE_INVALID_SESSION);
                }
            }
            throw error;
        }
    }
    /**
     * 유저 채널 퇴장
     * 지역 이동 / 국가 변경시에 채널에서 나가야 합니다. 채널 이름은 CmsId 사용
     */
    async channelLeave(channelName, userId) {
        await this._post(`/users/${userId}/leave`, { channel_name: channelName });
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43424902
     *
     * 사용자가 채팅을 보이지 않도록 등록한 사용자의 수를 조회합니다.
     */
    async getUserMuteUserCount(userId) {
        const args = await this._get(`/users/${userId}/mute/count`);
        const muteUserCount = args === null || args === void 0 ? void 0 : args.mute_user_count;
        if (!Number.isInteger(muteUserCount)) {
            throw new merror_1.MError(`[Volante] [GET /users/{userId}/mute/count] unexpected-resp-received`, merror_1.MErrorCode.LG_VOLANTE_ERROR, { args });
        }
        return muteUserCount;
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421870
     *
     * 차단한 사용자 목록을 가져옵니다.
     * @param userId 차단 주체 volante user id
     */
    async getMuteUserIds(userId) {
        const result = await this._get(`/users/${userId}/mute?user_info=0`);
        if (result && result.mute_user_ids) {
            return result.mute_user_ids.map((userId) => {
                return parseInt(userId, 10);
            });
        }
        else {
            return [];
        }
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421866
     *
     * 사용자가 다른 사용자의 채팅을 보이지 않도록 차단 등록합니다.
     * @param userId 차단 주체 volante user id
     * @param targetUserId 차단 당할 volante user id
     */
    async muteUser(userId, targetUserId) {
        const IGNORE_ERROR_CODES = [
            45 /* NOT_EXIST_USER */,
            65 /* ALREADY_MUTED_USER */,
            72 /* EXCEED_MAX_MUTE_USER_COUNT */,
        ];
        const result = await this._post(`/users/${userId}/mute`, { mute_user_id: targetUserId }, IGNORE_ERROR_CODES);
        if (result.hasIgnoredError) {
            const params = {
                userId,
                targetUserId,
            };
            switch (result.ignoredErrorCode) {
                case 45 /* NOT_EXIST_USER */:
                    throw new merror_1.MError('[Volante] not-exist-user', merror_1.MErrorCode.LG_VOLANTE_NOT_EXIST_USER, params);
                case 65 /* ALREADY_MUTED_USER */:
                    throw new merror_1.MError('[Volante] already-muted-user', merror_1.MErrorCode.CHAT_ALREADY_MUTED_USER, params);
                case 72 /* EXCEED_MAX_MUTE_USER_COUNT */:
                    throw new merror_1.MError('[Volante] exceed-max-mute-count', merror_1.MErrorCode.CHAT_EXCEED_MAX_MUTE_USER_COUNT, params);
                default:
                    assert_1.default.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
            }
        }
        return result;
    }
    /**
     * @see https://developer.line.games/pages/viewpage.action?pageId=43421868
     *
     * 사용자가 다른 사용자의 채팅을 보이도록 등록된 차단을 해제합니다.
     * @param userId 차단 주체 volante user id
     * @param targetUserId 차단 당할 volante user id
     */
    async unmuteUser(userId, targetUserId) {
        const IGNORE_ERROR_CODES = [
            45 /* NOT_EXIST_USER */,
            66 /* NOT_MUTED_USER */,
        ];
        const result = await this._delete(`/users/${userId}/mute/${targetUserId}`, IGNORE_ERROR_CODES);
        if (result.hasIgnoredError) {
            const params = {
                userId,
                targetUserId,
            };
            switch (result.ignoredErrorCode) {
                case 45 /* NOT_EXIST_USER */:
                    throw new merror_1.MError('[Volante] not-exist-user', merror_1.MErrorCode.LG_VOLANTE_NOT_EXIST_USER, params);
                case 66 /* NOT_MUTED_USER */:
                    throw new merror_1.MError('[Volante] not-muted-user', merror_1.MErrorCode.CHAT_NOT_MUTED_USER, params);
                default:
                    assert_1.default.fail(`unexpected-ignoredErrorCode: ${result.ignoredErrorCode}`);
            }
        }
        return result;
    }
    updateVolanteUser(user) {
        const volanteId = user.userId.toString();
        const extraData = {
            nationCmsId: user.nationCmsId,
            guildId: user.userGuild.guildId,
            guildName: user.userGuild.getGuildName(),
        };
        return this.updateUser(volanteId, user.userName, extraData).catch((err) => {
            mlog_1.default.error('[VOLANTE] update user is failed', { userId: user.userId, err });
        });
    }
    async _get(url, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.get(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, {}, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, null, error);
        }
    }
    async _post(url, body, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.post(url, body, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, body, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, body, error);
        }
    }
    async _patch(url, body, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.patch(url, body, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, body, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, body, error);
        }
    }
    async _delete(url, ignoreErrors = []) {
        try {
            const axios = this.mrest.axios();
            const resp = await axios.delete(url, {
                headers: { Authorization: `custom ${this.salt}` },
                timeout: AxiosTimeout,
                validateStatus: () => true,
            });
            return await this.handleResponse(url, {}, resp, ignoreErrors);
        }
        catch (error) {
            this.handleError(url, null, error);
        }
    }
    handleError(url, body, error) {
        var _a, _b;
        if (error instanceof merror_1.MError) {
            this.sendToSlack(`[Volante] handle reponse error, url: ${url}, error: ${error.message}`).catch();
            const volanteErrorCode = ((_b = (_a = error.extra) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.error_code) || undefined;
            throw new merror_1.MError(`chatApiClient, volante api err`, error.mcode, {
                url,
                volanteErrorCode,
                merror: {
                    mcode: error.mcode,
                    message: error.message,
                    extra: error.extra,
                },
            });
        }
        else {
            this.sendToSlack(`[Volante] exception, url: ${url}, error: ${error.message}, code: ${error.code ? error.code : 'undefined'}`).catch();
            throw new merror_1.MError('chatApiClient, error occured', merror_1.MErrorCode.INTERNAL_ERROR, `'${url}', message: ${error.message}`);
        }
    }
    async handleResponse(url, body, resp, ignoreErrors) {
        const result = resp.data;
        if (result.success) {
            return result.args;
        }
        else {
            // if skippable error, return empty body result
            if (result.error && result.error.error_code) {
                const errcode = result.error.error_code;
                if (ignoreErrors.indexOf(errcode) !== -1) {
                    return {
                        hasIgnoredError: true,
                        ignoredErrorCode: errcode,
                    };
                }
            }
            else {
                this.sendToSlack(`[Volante] response error, url: ${url}, body: ${JSON.stringify(body)}, result: ${JSON.stringify(result)}`).catch();
            }
            throw new merror_1.MError('Volante-api-failed', merror_1.MErrorCode.LG_VOLANTE_ERROR, result);
        }
    }
}
exports.LineGamesChatApiClient = LineGamesChatApiClient;
//# sourceMappingURL=linegamesChatApiClient.js.map