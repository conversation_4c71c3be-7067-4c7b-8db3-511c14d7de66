"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_ChatMuteUser = void 0;
const typedi_1 = __importDefault(require("typedi"));
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const merror_1 = require("../../../motiflib/merror");
const userCacheRedisHelper_1 = require("../../../motiflib/userCacheRedisHelper");
const cms_1 = __importDefault(require("../../../cms"));
const userConnection_1 = require("../../userConnection");
const server_1 = require("../../server");
const rsn = 'friend_enemy';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_ChatMuteUser {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    async exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const reqBody = packet.bodyObj;
        const { targetUserId } = reqBody;
        if (targetUserId === undefined || !Number.isInteger(targetUserId)) {
            throw new merror_1.MError('targetUserId-integer-expected', merror_1.MErrorCode.INVALID_REQ_BODY_CHAT_MUTE_USER, { reqBody });
        }
        if (targetUserId == user.userId) {
            throw new merror_1.MError('can-not-mute-self', merror_1.MErrorCode.INVALID_REQ_BODY_CHAT_MUTE_USER, {
                reqBody,
            });
        }
        if (user.userFriends.getFriend(targetUserId)) {
            throw new merror_1.MError('target-is-friend', merror_1.MErrorCode.CANNOT_BE_MUTE_FRIEND, {
                reqBody,
            });
        }
        const volanteUserId = user.userId.toString();
        const volanteTargetUserId = targetUserId.toString();
        let targetUser;
        return Promise.resolve()
            .then(() => {
            // 에러 절차를 명확하게 하기 위해.
            // chatInit 이 된 상태인지와 볼란테에 접속 중인지를 알기 위함.
            // 무거운 조회는 아닐 거라 보고..
            return mhttp_1.default.platformChatApi.getSessions(volanteUserId);
        })
            .then(() => {
            return mhttp_1.default.platformChatApi.getUserMuteUserCount(volanteUserId).then((muteUserCount) => {
                const bEnough = muteUserCount < cms_1.default.Define.ChatMaxMuteUserCount;
                if (!bEnough) {
                    throw new merror_1.MError('exceed-max-mute-count', merror_1.MErrorCode.CHAT_EXCEED_MAX_MUTE_USER_COUNT, {
                        muteUserCount,
                        max: cms_1.default.Define.ChatMaxMuteUserCount,
                    });
                }
                return null;
            });
        })
            .then(() => {
            // 대상 유저가 이 게임 서버에 존재하는 유저인지 확인용.
            // - 현재 시스템상 다른 서버 userId 도 볼란테에 등록될 가능성이 있을 듯 함. 예방 차원
            // getUserLightInfos 가 단순 조회치고는 무거울 수 있을 듯 함..
            const { userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
            const worldConfg = mconf_1.default.getWorldConfig();
            return (0, userCacheRedisHelper_1.getUserLightInfos)([targetUserId], userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((results) => {
                targetUser = results === null || results === void 0 ? void 0 : results[targetUserId];
                if (!targetUser) {
                    throw new merror_1.MError('target-user-not-exist', merror_1.MErrorCode.CHAT_MUTE_USER_NOT_EXIST_TARGET_USER, { reqBody });
                }
                return null;
            });
        })
            .then(() => {
            return mhttp_1.default.platformChatApi.muteUser(volanteUserId, volanteTargetUserId);
        })
            .then(() => {
            user.glog('friend_enemy', {
                rsn,
                add_rsn,
                friend_nid: targetUser.pubId,
                friend_gameUserId: targetUserId,
                type: 1, // 차단:1 해제:0
            });
            return user.sendJsonPacket(packet.seqNum, packet.type, {});
        });
    }
}
exports.Cph_Common_ChatMuteUser = Cph_Common_ChatMuteUser;
//# sourceMappingURL=chatMuteUser.js.map