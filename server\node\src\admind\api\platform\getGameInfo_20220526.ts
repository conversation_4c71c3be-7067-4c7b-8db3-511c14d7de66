// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { AdminService } from '../../server';
import { CommonResponseBody } from '../../adminCommon';
import puAdminUserLoad from '../../../mysqllib/sp/puAdminUserLoad';
import taAdminGetUserIdsByPubId from '../../../mysqllib/txn/taAdminGetUserIdsByPubId';
import mhttp from '../../../motiflib/mhttp';
import { Promise as promise } from 'bluebird';

interface RequestBody {
  nids: string;
}

interface ResponseBody extends CommonResponseBody {
  isSuccess: boolean;
  msg: string;
  errorCd?: string;
  data?: {
    nick: string;
    level: number;
    nid: string;
    gameUserId: number;
    serverId: string;
    coinCd: string;
    // coinBalance: number;
  }[];
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /user/getGameInfo_20220526', { body: req.body });

  req.url;
  const curTimeUtc = mutil.curTimeUtc();
  let { nids }: RequestBody = req.body;

  if (!nids) {
    return res.status(400).json({
      isSuccess: false,
      msg: 'Invalid your parameter',
      errorCd: 'INVALID_PARAMETER',
    });
  }

  const splitNids: string[] = nids.replace(/ /g, '').split(',');

  const { authDbConnPool, userDbConnPoolMgrs, serviceLayoutMgr } = Container.get(AdminService);

  const resp: ResponseBody = {
    isSuccess: true,
    msg: 'request success',
  };

  const userServerIds: { [userId: number]: string } = {};
  const userPubIds: { [userId: number]: string } = {};
  return Promise.resolve()
    .then(() => {
      const promises = splitNids.map((nid: string) => {
        return taAdminGetUserIdsByPubId(authDbConnPool.getPool(), nid);
      });

      return Promise.all(promises);
    })
    .then((results) => {
      if (!results) {
        throw new MError(`not exist nid(${nids}).`, MErrorCode.NOT_EXIST_NID, req.body);
      }

      let users: { userId: number; name: string; worldId: string; pubId: string }[] = [];
      results.forEach((elem) => {
        users = users.concat(elem);
      });

      splitNids.forEach((nid) => {
        const index = users.findIndex((user) => {
          return user.pubId === nid;
        });
        if (index === -1) {
          throw new MError(`not exist nid(${nid}).`, MErrorCode.NOT_EXIST_NID, req.body);
        }
      });

      const promises = users.map(
        (user: { userId: number; name: string; worldId: string; pubId: string }) => {
          const worldId: string = user.worldId;
          const pubId: string = user.pubId;
          const userDbConnPoolMgr = userDbConnPoolMgrs[worldId];
          if (!userDbConnPoolMgr) {
            throw new MError(
              'no gameServerId in service layout',
              MErrorCode.ADMIN_INVALID_PARAMETER,
              {
                worldId,
              }
            );
          }

          userServerIds[user.userId] = worldId;
          userPubIds[user.userId] = pubId;
          return puAdminUserLoad(
            userDbConnPoolMgr.getPoolByUserIdAndShardFuncName(
              user.userId,
              serviceLayoutMgr.getUserDbShardFunction(worldId),
              user.worldId
            ),
            user.userId,
            curTimeUtc
          );
        }
      );

      return Promise.all(promises);
    })
    .then((results) => {
      resp.data = [];
      return promise.reduce(
        results,
        (_, user) => {
          //라인게임즈에서 appStoreCd, countryCreated는  'FLOOR_STORE', 'KR'로 하도록 전달받음.
          // 히스토리 --> https://jira.line.games/browse/UWO-11578
          // return mhttp.platformBillingApi
          //   .queryCash(user.userId, 'FLOOR_STORE', 'KR')
          //   .then((data: { coinCd: string; balance: number; paymentType: string }[]) => {
          //     let coinBalance: number = 0;
          //     for (const elem of data) {
          //       if (elem.coinCd === 'red_gem') {
          //         coinBalance += elem.balance;
          //       }
          //     }
          return Promise.resolve().then(() => {
            resp.data.push({
              nick: user.name,
              level: user.level,
              nid: userPubIds[user.userId],
              serverId: userServerIds[user.userId],
              gameUserId: user.userId,
              coinCd: 'red_gem',
            });
          });
        },
        {}
      );
    })
    .then(() => {
      mlog.info('[TX] /user/getGameInfo_20220526', { body: resp });
      res.json(resp);
    });
};
