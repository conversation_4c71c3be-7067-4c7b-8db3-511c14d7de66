"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.AdminService = void 0;
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
require("express-async-errors");
const http_1 = __importDefault(require("http"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const JSON5 = __importStar(require("json5"));
const typedi_1 = require("typedi");
const lodash_1 = __importDefault(require("lodash"));
const dirAsApi = __importStar(require("../motiflib/directoryAsApi"));
const adminError = __importStar(require("./adminError"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const resolveLocalDotJson5_1 = require("../motiflib/resolveLocalDotJson5");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const connPool_1 = require("../redislib/connPool");
const pubsub_1 = __importDefault(require("../redislib/pubsub"));
const pool_1 = require("../mysqllib/pool");
const cms_1 = __importStar(require("../cms"));
const adminAuthApiClient_1 = require("./apiClient/adminAuthApiClient");
const stoppable_1 = __importDefault(require("stoppable"));
const os_1 = __importDefault(require("os"));
const adminHttpUtils_1 = require("./apiClient/adminHttpUtils");
const serviceLayoutMgr_1 = require("./serviceLayoutMgr");
const serverHealthChecker_1 = __importDefault(require("./serverHealthChecker"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const configPubsubSyncer = __importStar(require("../motiflib/model/config/configPubsubSyncer"));
const adminUtil_1 = require("../motiflib/model/admin/adminUtil");
const Sentry = __importStar(require("@sentry/node"));
const adminLookup_1 = require("./adminLookup");
const enum_1 = require("../motiflib/model/auth/enum");
// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
// admind app.
const adminApp = (0, express_1.default)();
adminApp.disable('x-powered-by');
adminApp.disable('etag');
adminApp.disable('content-type');
const adminServer = (0, stoppable_1.default)(http_1.default.createServer(adminApp));
let stopping = false;
let AdminService = class AdminService {
    async init(layoutData) {
        this.serviceLayoutMgr = typedi_1.Container.get(serviceLayoutMgr_1.ServiceLayoutMgr);
        this.serviceLayoutMgr = new serviceLayoutMgr_1.ServiceLayoutMgr(layoutData);
        this.serviceLayoutMgr.loadInstances();
        const worlds = layoutData.world.worlds;
        const sharedConfig = layoutData.sharedConfig;
        const lobbydCommon = layoutData.lobbyd.common;
        const zonelbdCommon = layoutData.zonelbd.common;
        this.monitorRedis = typedi_1.Container.of('monitor-redis').get(connPool_1.MRedisConnPool);
        await this.monitorRedis.init('monitor-redis', sharedConfig.monitorRedis);
        this.userCacheRedis = typedi_1.Container.of('user-cache-redis').get(connPool_1.MRedisConnPool);
        await this.userCacheRedis.init('user-cache-redis', sharedConfig.userCacheRedis);
        this.battleLogRedis = typedi_1.Container.of('battle-log-redis').get(connPool_1.MRedisConnPool);
        await this.battleLogRedis.init('battle-log-redis', lobbydCommon.battleLogRedis);
        this.globalBattleLogRedis = typedi_1.Container.of('global-battle-log-redis').get(connPool_1.MRedisConnPool);
        await this.globalBattleLogRedis.init('global-battle-log-redis', mconf_1.default.globalBattleLogRedis);
        this.orderRedis = typedi_1.Container.of('order-redis').get(connPool_1.MRedisConnPool);
        await this.orderRedis.init('order-redis', mconf_1.default.orderRedis);
        this.authRedis = typedi_1.Container.of('auth-redis').get(connPool_1.MRedisConnPool);
        await this.authRedis.init('auth-redis', mconf_1.default.authRedis);
        this.authPubsub = typedi_1.Container.of('pubsub-auth').get(pubsub_1.default);
        this.authPubsub.init(sharedConfig.authPubsubRedis);
        this.configPubsub = typedi_1.Container.of('pubsub-config').get(pubsub_1.default);
        this.configPubsub.init(sharedConfig.configPubsubRedis);
        this.authDbConnPool = typedi_1.Container.of('auth').get(pool_1.DBConnPool);
        await this.authDbConnPool.init(sharedConfig.mysqlAuthDb);
        this.authApiClient = typedi_1.Container.get(adminAuthApiClient_1.AdminAuthApiClient);
        this.authApiClient = new adminAuthApiClient_1.AdminAuthApiClient();
        this.authApiClient.init(sharedConfig.http.authd.url, sharedConfig.http.authd.timeout);
        this.townRedises = {};
        this.nationRedises = {};
        this.collectorRedises = {};
        this.sailRedises = {};
        this.townLbRedises = {};
        this.oceanLbRedises = {};
        this.userRedises = {};
        this.guildRedises = {};
        this.auctionRedisConnPoolMgr = {};
        this.arenaRedises = {};
        this.rankingRedises = {};
        this.raidRedises = {};
        this.worldPubsubRedises = {};
        this.guildPubsubRedises = {};
        this.userDbConnPoolMgrs = {};
        this.worldDbConnPools = {};
        for (const world of worlds) {
            if (world.disabled) {
                mlog_1.default.warn('skipping disabled world', { worldId: world.id });
                continue;
            }
            this.townRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.townRedises[world.id].init('town-redis-' + world.id, world.townRedis);
            this.nationRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.nationRedises[world.id].init('nation-redis-' + world.id, world.nationRedis);
            this.collectorRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.collectorRedises[world.id].init('collector-redis-' + world.id, world.collectorRedis);
            this.sailRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.sailRedises[world.id].init('sail-redis-' + world.id, world.sailRedis);
            this.townLbRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.townLbRedises[world.id].init('town-lb-redis-' + world.id, world.townLbRedis);
            this.oceanLbRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.oceanLbRedises[world.id].init('ocean-lb-redis-' + world.id, world.oceanLbRedis);
            this.userRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.userRedises[world.id].init('user-redis-' + world.id, world.userRedis);
            this.guildRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.guildRedises[world.id].init('guild-redis-' + world.id, world.guildRedis);
            this.auctionRedisConnPoolMgr[world.id] = new connPool_1.AuctionRedisConnPoolMgr();
            await this.auctionRedisConnPoolMgr[world.id].init('auction-redis-' + world.id, world.auctionRedis);
            this.arenaRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.arenaRedises[world.id].init('arena-redis-' + world.id, world.arenaRedis);
            this.rankingRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.rankingRedises[world.id].init('ranking-redis-' + world.id, world.rankingRedis);
            this.raidRedises[world.id] = new connPool_1.MRedisConnPool();
            await this.raidRedises[world.id].init('ranking-redis-' + world.id, world.raidRedis);
            this.worldPubsubRedises[world.id] = new pubsub_1.default();
            this.worldPubsubRedises[world.id].init(world.worldPubsubRedis);
            this.guildPubsubRedises[world.id] = new pubsub_1.default();
            this.guildPubsubRedises[world.id].init(world.guildPubsubRedis);
            this.userDbConnPoolMgrs[world.id] = new pool_1.DbConnPoolManager();
            await this.userDbConnPoolMgrs[world.id].init(world.mysqlUserDb);
            this.worldDbConnPools[world.id] = new pool_1.DBConnPool();
            await this.worldDbConnPools[world.id].init(world.mysqlWorldDb);
        }
        this.serverHealthChecker = new serverHealthChecker_1.default();
        //this.serverHealthChecker.startTick();
        // 동적 서버 인스턴스 증가 대응(scale out)
        configPubsubSyncer.subscribeForRegisterInstance(this.configPubsub, (fullCfg) => {
            mlog_1.default.info('configPubsubSyncer updating new layout', { fullCfg });
            // 새로운 인스턴스 등록 이벤트 발생시 처리할 작업을 등록
            this.serviceLayoutMgr.setLayout(fullCfg);
            this.serviceLayoutMgr.loadInstances();
        }, isStopping, stop, true);
        // 캐시샵 정보 캐싱 및 DB cms테이블 업데이트
        this.adminCashShopFixedTermProductsLookup = new adminLookup_1.AdminCashShopFixedTermProductsLookup(cms_1.default);
        this.adminReligionBuffLookup = new adminLookup_1.AdminReligionBuffLookup(cms_1.default);
        mlog_1.default.info('adminCashShopFixedTermProductsLookup initialized');
    }
    async destroy() {
        //this.serverHealthChecker.stopTick();
        await this.authPubsub.quit();
        await this.configPubsub.quit();
        await this.monitorRedis.destroy();
        await this.userCacheRedis.destroy();
        await this.battleLogRedis.destroy();
        await this.orderRedis.destroy();
        await this.authRedis.destroy();
        await this.authDbConnPool.destroy();
        await this.globalBattleLogRedis.destroy();
        for (const redis of Object.values(this.townRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.nationRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.collectorRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.sailRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.townLbRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.oceanLbRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.userRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.guildRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.auctionRedisConnPoolMgr)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.arenaRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.rankingRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.raidRedises)) {
            await redis.destroy();
        }
        for (const redis of Object.values(this.worldPubsubRedises)) {
            await redis.quit();
        }
        for (const redis of Object.values(this.guildPubsubRedises)) {
            await redis.quit();
        }
        for (const mgr of Object.values(this.userDbConnPoolMgrs)) {
            await mgr.destroy();
        }
        for (const connPool of Object.values(this.worldDbConnPools)) {
            await connPool.destroy();
        }
    }
};
AdminService = __decorate([
    (0, typedi_1.Service)()
], AdminService);
exports.AdminService = AdminService;
// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------
morgan_1.default.token('mcode', (_req, res) => res.mcode || 0);
function admindReqLog(tokens, req, res) {
    mlog_1.default.info('admind-req', {
        'remote-addr': tokens['remote-addr'](req, res),
        url: tokens['url'](req, res),
        status: tokens['status'](req, res),
        'response-time': tokens['response-time'](req, res),
        mcode: tokens['mcode'](req, res),
    });
    return null;
}
// ----------------------------------------------------
// 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
// + admind/allowIp 추가 허용
// ----------------------------------------------------
async function adminIpFilter(req, res, next) {
    const clientIp = adminUtil_1.AdminUtil.getReqClientIp(req);
    let allowIpList = await adminHttpUtils_1.AdminHttpUtils.platformApi.reqWhiteServerIpList();
    if (mconf_1.default.apiService.allowIp && mconf_1.default.apiService.allowIp.length > 0) {
        allowIpList = allowIpList.concat(mconf_1.default.apiService.allowIp);
    }
    // 중국인 경우 allowIpList가 비어 있는 경우에는 모두 허용.
    // 중국이 아닌 경우에는 기존 로직 유지.
    if (mconf_1.default.platform === enum_1.PLATFORM.SDO) {
        if (allowIpList.length > 0 && !lodash_1.default.includes(allowIpList, clientIp)) {
            return res.status(405).json({
                isSuccess: false,
                msg: 'Not allow your request',
                errorCd: 'NOT_ALLOW_AUTH',
            });
        }
    }
    else {
        if (!lodash_1.default.includes(allowIpList, clientIp)) {
            return res.status(405).json({
                isSuccess: false,
                msg: 'Not allow your request',
                errorCd: 'NOT_ALLOW_AUTH',
            });
        }
    }
    mlog_1.default.info('[adminIpFilter] Access granted to IP address: ', { clientIp });
    next();
}
async function closeHttpServer() {
    return new Promise((resolve, reject) => {
        adminServer.stop((err) => {
            if (err) {
                return reject(err);
            }
        });
    });
}
async function stopServer() {
    try {
        mlog_1.default.info('stopping server ...');
        await closeHttpServer();
        const service = typedi_1.Container.get(AdminService);
        await service.destroy();
        mlog_1.default.info('admin-server closed');
    }
    catch (error) {
        mlog_1.default.error('graceful shutdown failed', { error: error.message });
        process.exit(1);
    }
    mlog_1.default.info('server stopped');
    process.exitCode = 0;
}
function loadLayout() {
    const ServiceLayoutFolder = 'service_layout';
    const layoutFileName = mconf_1.default.layout || (0, resolveLocalDotJson5_1.resolveLocalDotJson5)();
    const layoutFilePath = path_1.default.join(ServiceLayoutFolder, layoutFileName);
    const fileStr = fs_1.default.readFileSync(layoutFilePath, 'utf8');
    try {
        // Replace '__HOSTNAME__'.
        const replaceRe = new RegExp('__HOSTNAME__', 'g');
        const newData = fileStr.replace(replaceRe, os_1.default.hostname());
        const layoutData = JSON5.parse(newData);
        // merge layout data to mconf
        mconf_1.default.append(layoutData.sharedConfig);
        mconf_1.default.append(layoutData.world);
        mconf_1.default.append(layoutData.admind);
        return layoutData;
    }
    catch (err) {
        throw new Error(`failed to parse layout': ${err.message}`);
    }
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
const start = async () => {
    try {
        const layoutData = await mhttp_1.default.configd.fetchForAdmind();
        (0, cms_1.load)();
        // Init http clients.
        mhttp_1.default.init();
        mutil.initSentry();
        adminHttpUtils_1.AdminHttpUtils.init();
        const service = typedi_1.Container.get(AdminService);
        await service.init(layoutData);
        // listen admin app
        const bindAddress = mconf_1.default.apiService.bindAddress;
        const port = mconf_1.default.apiService.port;
        // 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
        adminApp.use(adminIpFilter);
        adminApp.use((0, morgan_1.default)(admindReqLog));
        adminApp.use(body_parser_1.default.json({ limit: '50mb' }));
        adminApp.use(body_parser_1.default.urlencoded({ extended: true }));
        mutil.registerHealthCheck(adminApp);
        mutil.registerGarbageCollector(adminApp);
        await dirAsApi.register(adminApp, path_1.default.join(__dirname, 'api'));
        adminApp.use(adminError.middleware);
        adminServer.listen(port, bindAddress, () => {
            mlog_1.default.info('start admin server listening ...', { bindAddress, port });
        });
    }
    catch (error) {
        mlog_1.default.error('failed to start', { error: error.message, extra: error.extra });
        mlog_1.default.error(error.stack);
        const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        await slackNotifier.notify({ username: process.name, text: error.message });
        process.exit(1);
    }
};
exports.start = start;
function isStopping() {
    return stopping;
}
async function stop() {
    if (stopping) {
        return;
    }
    stopping = true;
    await stopServer();
}
exports.stop = stop;
//# sourceMappingURL=server.js.map