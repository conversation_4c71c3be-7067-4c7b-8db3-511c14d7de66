{"version": 3, "file": "userGuild.js", "sourceRoot": "", "sources": ["../../src/lobbyd/userGuild.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;AAE/E,oDAAuB;AACvB,iDAAyB;AACzB,kCAA2C;AAG3C,mDAQiC;AAEjC,6CAA6D;AAE7D,gDAA6C;AAC7C,qCAAwC;AACxC,4DAAoC;AACpC,8DAAsC;AAEtC,sDAAmE;AACnE,2CAAsD;AACtD,oDAAoD;AACpD,yDAAsD;AAItD,IAAiB,iBAAiB,CAkkBjC;AAlkBD,WAAiB,iBAAiB;IAChC,SAAS,IAAI,CAAC,MAAqB,EAAE,QAAgB,EAAE,CAAS;QAC9D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,EAAE,CAAC;SACb;QACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SACtB;QACD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAgB,IAAI,CAClB,IAAU,EACV,SAAoB,EACpB,cAAmD,EACnD,QAA8B,EAC9B,KAAa;QAEb,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,qBAAS,CAAC,GAAG,CAAC,qBAAY,CAAC,CAAC;QAEnE,MAAM,OAAO,GAAW,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QAE/C,MAAM,GAAG,GAAkB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1F,MAAM,GAAG,GAAkB,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3F,MAAM,GAAG,GAAkB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAE1F,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC9C,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC/C,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAE9C,MAAM,WAAW,GAAmB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,WAAW,CAAC,uBAAuB,GAAG,IAAA,kBAAU,GAAE,CAAC;QAEnD,MAAM,IAAI,GAAS,EAAE,CAAC;QAEtB,YAAY;QACZ,IAAI,MAAM,GAAW,IAAI,CAAC;QAC1B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,GAAG,CAAC;QACR,MAAM,SAAS,GAAG,aAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;QAEnD,IAAI,QAAQ,KAAK,4BAAoB,CAAC,UAAU,EAAE;YAChD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC;YAClD,GAAG,GAAG,mBAAmB,CAAC;SAC3B;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,MAAM,EAAE;YACnD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACnD,GAAG,GAAG,eAAe,CAAC;SACvB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,KAAK,EAAE;YAClD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC7C,GAAG,GAAG,cAAc,CAAC;SACtB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,OAAO,EAAE;YACpD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,CAAC;YACpD,GAAG,GAAG,gBAAgB,CAAC;SACxB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,OAAO,EAAE;YACpD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC;YAChD,GAAG,GAAG,gBAAgB,CAAC;SACxB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,KAAK,EAAE;YAClD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC;YAClD,GAAG,GAAG,cAAc,CAAC;SACtB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,MAAM,EAAE;YACnD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC;YAC9C,GAAG,GAAG,eAAe,CAAC;SACvB;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,oBAAoB,EAAE;YACjE,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,KAAK,CAAC;YACzD,GAAG,GAAG,6BAA6B,CAAC;SACrC;aAAM,IAAI,QAAQ,KAAK,4BAAoB,CAAC,SAAS,EAAE;YACtD,OAAO,GAAG,aAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,KAAK,CAAC;YACzD,GAAG,GAAG,kBAAkB,CAAC;SAC1B;QACD,kBAAkB;QAClB,OAAO,CACL,UAAU,CAAC,kBAAkB,CAAC,CAC5B,IAAI,CAAC,MAAM,EACX,OAAO,EACP,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EACnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EACnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CACpB;aACE,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,UAAU,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5F,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,gBAAC,CAAC,KAAK,CAAC,IAAI,EAAE;gBACZ,GAAG,EAAE;oBACH,SAAS,EAAE;wBACT,KAAK,EAAE;4BACL,OAAO,EAAE;gCACP,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oCACb,gBAAgB,EAAE;wCAChB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC;qCAC1B;oCACD,iBAAiB,EAAE;wCACjB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC;qCAC1B;oCACD,gBAAgB,EAAE;wCAChB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC;qCAC1B;iCACF;6BACF;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,wBAAY,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACzF,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,GAAG,EAAE,oBAAoB;gBACzB,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,KAAK;gBACL,QAAQ;gBACR,WAAW,EAAE,KAAK;gBAClB,iBAAiB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,OAAO,EAAE;gBAChD,WAAW,EAAE,sBAAsB,CAAC,GAAG,CAAC;gBACxC,YAAY,EAAE,sBAAsB,CAAC,GAAG,CAAC;gBACzC,WAAW,EAAE,sBAAsB,CAAC,GAAG,CAAC;gBACxC,UAAU;aACX,CAAC,CAAC;QACL,CAAC,CAAC;YACF,QAAQ;aACP,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,qBAAS,CAAC,SAAS,CACxB,IAAI,EACJ,OAAO,EACP,SAAS,EACT,cAAc,EACd,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EACjB,GAAG,EACH,IAAI,CACL,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,UAAgB,EAAE,EAAE;YACzB,gBAAC,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAE1B,iBAAiB;YACjB,IAAI,KAAK,GAAW,CAAC,CAAC;YACtB,gBAAC,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC3C,gBAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,cAAc,CAAC,aAAa,CAC1B,4BAAc,CAAC,kBAAkB,EACjC,OAAO,EACP,KAAK,EACL,IAAI,CAAC,MAAM,CACZ,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACL,CAAC;IACJ,CAAC;IA5Ie,sBAAI,OA4InB,CAAA;IAED,0BAA0B;IAC1B,SAAgB,yBAAyB,CAAC,IAAU,EAAE,WAAoB;QACxE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,IAAI,SAAoB,CAAC;QACzB,IAAI,cAAmD,CAAC;QAExD,MAAM,IAAI,GAAS,EAAE,CAAC;QACtB,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;aAC3E,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACf,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YAC7B,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAEvC,IAAI,WAAW,KAAK,IAAI,EAAE;gBACxB,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,UAAU,CAAC,EAAE;wBAChD,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,UAAU,CAAC,CAAC;qBAC9D;iBACF;gBAED,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,4BAA4B,CAAC,KAAK,EAAE;oBAChE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC;oBACrD,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,4BAA4B,CAAC,KAAK,EAAE;wBAC7E,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,4BAA4B,CAAC,KAAK,GAAG,aAAa,CAAC;qBAC3E;iBACF;gBAED,IAAI,CAAC,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE;oBAC/C,OAAO,IAAI,CACT,IAAI,EACJ,SAAS,EACT,cAAc,EACd,4BAAoB,CAAC,UAAU,EAC/B,UAAU,CACX,CAAC;iBACH;aACF;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,gBAAC,CAAC,KAAK,CAAC,IAAI,EAAE,qBAAS,CAAC,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC;YAChF,gBAAC,CAAC,KAAK,CAAC,IAAI,EAAE;gBACZ,GAAG,EAAE;oBACH,SAAS,EAAE;wBACT,KAAK,EAAE;4BACL,gBAAgB,EAAE,IAAA,kBAAU,GAAE;yBAC/B;qBACF;iBACF;aACF,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC;IAvDe,2CAAyB,4BAuDxC,CAAA;IAED,sBAAsB;IACtB,SAAgB,gBAAgB,CAAC,IAAU,EAAE,QAAgB;QAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QACD,MAAM,MAAM,GAAiB,IAAA,mBAAc,GAAE,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,EAAE;YACX,cAAI,CAAC,KAAK,CAAC,8CAA8C,EAAE;gBACzD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ;aACT,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,MAAM,CAAC,EAAE;oBAC5C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,MAAM,CAAC,CAAC;iBAC1D;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IACE,MAAM,CAAC,YAAY,KAAK,6BAAc,CAAC,KAAK;gBAC5C,MAAM,CAAC,YAAY,KAAK,6BAAc,CAAC,KAAK,EAC5C;gBACA,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC;aACnD;iBAAM,IAAI,MAAM,CAAC,YAAY,KAAK,6BAAc,CAAC,KAAK,EAAE;gBACvD,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC;aACnD;YAED,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,EAAE;gBACzE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,GAAG,aAAa,CAAC;aACvE;YACD,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YACD,OAAO,IAAI,CACT,IAAI,EACJ,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,cAAc,EACrB,4BAAoB,CAAC,MAAM,EAC3B,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAlDe,kCAAgB,mBAkD/B,CAAA;IAED,sBAAsB;IACtB,SAAgB,iBAAiB,CAAC,IAAU;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,OAAO,CAAC,EAAE;oBAC7C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,OAAO,CAAC,CAAC;iBAC3D;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,EAAE;gBACzD,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBAC9C,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,EAAE;oBACtE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,GAAG,aAAa,CAAC;iBACpE;aACF;YACD,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CACT,IAAI,EACJ,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,cAAc,EACrB,4BAAoB,CAAC,OAAO,EAC5B,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IArCe,mCAAiB,oBAqChC,CAAA;IAED,eAAe;IACf,SAAgB,cAAc,CAAC,IAAU;QACvC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,OAAO,CAAC,EAAE;oBAC7C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,OAAO,CAAC,CAAC;iBAC3D;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,EAAE;gBAC7D,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC;gBAClD,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,EAAE;oBAC1E,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,GAAG,aAAa,CAAC;iBACxE;aACF;YACD,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CACT,IAAI,EACJ,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,cAAc,EACrB,4BAAoB,CAAC,OAAO,EAC5B,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IArCe,gCAAc,iBAqC7B,CAAA;IAED,eAAe;IACf,SAAgB,YAAY,CAAC,IAAU,EAAE,MAAc;QACrD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,IAAI,MAAM,IAAI,CAAC,EAAE;YACf,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,KAAK,CAAC,EAAE;oBAC3C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,KAAK,CAAC,CAAC;iBACzD;aACF;YACD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,EAAE;gBACtD,MAAM,CAAC,GAAG,MAAM,GAAG,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAEvD,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC;gBACpD,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBAChD,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,EAAE;oBACnE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,GAAG,aAAa,CAAC;iBACjE;aACF;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,EAAE,4BAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC9F,CAAC,CACF,CAAC;IACJ,CAAC;IAtCe,8BAAY,eAsC3B,CAAA;IAED,eAAe;IACf,SAAgB,YAAY,CAC1B,IAAU,EACV,GAAW,EACX,KAAqF;QAErF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAChF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACd,MAAM,SAAS,GAAc,KAAK,CAAC,SAAS,CAAC;YAE7C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,KAAK,CAAC,EAAE;oBAC3C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,KAAK,CAAC,CAAC;iBACzD;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,EAAE;gBAC3D,UAAU,GAAG,GAAG,CAAC;gBACjB,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,EAAE;oBACxE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,GAAG,aAAa,CAAC;iBACtE;aACF;YAED,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,cAAc,EAAE,4BAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;IACP,CAAC;IAzCe,8BAAY,eAyC3B,CAAA;IAED,eAAe;IACf,SAAgB,gBAAgB,CAC9B,IAAU,EACV,GAAW,EACX,KAAqF;QAErF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAChF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACd,MAAM,SAAS,GAAc,KAAK,CAAC,SAAS,CAAC;YAE7C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,SAAS,CAAC,EAAE;oBAC/C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,SAAS,CAAC,CAAC;iBAC7D;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK,EAAE;gBAC/D,UAAU,GAAG,GAAG,CAAC;gBACjB,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK,EAAE;oBAC5E,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK,GAAG,aAAa,CAAC;iBAC1E;aACF;YAED,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CACT,IAAI,EACJ,SAAS,EACT,KAAK,CAAC,cAAc,EACpB,4BAAoB,CAAC,SAAS,EAC9B,UAAU,CACX,CAAC;QACJ,CAAC,CAAC,CAAC;IACP,CAAC;IA/Ce,kCAAgB,mBA+C/B,CAAA;IAED,eAAe;IACf,SAAgB,kBAAkB,CAAC,IAAU,EAAE,YAAoB;QACjE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,IAAI,YAAY,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,MAAM,CAAC,EAAE;oBAC5C,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,MAAM,CAAC,CAAC;iBAC1D;aACF;YACD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE;gBACvD,MAAM,CAAC,GAAG,YAAY,GAAG,aAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC;gBAE9D,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACjE,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE;oBACpE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,GAAG,aAAa,CAAC;iBAClE;aACF;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CACT,IAAI,EACJ,SAAS,EACT,MAAM,CAAC,cAAc,EACrB,4BAAoB,CAAC,MAAM,EAC3B,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IA3Ce,oCAAkB,qBA2CjC,CAAA;IAED,oBAAoB;IACpB,SAAgB,yBAAyB,CAAC,IAAU;QAClD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAE9C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,WAAW,GAAkB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3E,IAAI,WAAW,CAAC,4BAAoB,CAAC,oBAAoB,CAAC,EAAE;oBAC1D,aAAa,GAAG,WAAW,CAAC,4BAAoB,CAAC,oBAAoB,CAAC,CAAC;iBACxE;aACF;YAED,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,aAAa,GAAG,aAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,KAAK,EAAE;gBAClE,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,CAAC;gBACvD,IAAI,aAAa,GAAG,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,KAAK,EAAE;oBAC/E,UAAU,GAAG,aAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,KAAK,GAAG,aAAa,CAAC;iBAC7E;aACF;YACD,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CACT,IAAI,EACJ,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,cAAc,EACrB,4BAAoB,CAAC,oBAAoB,EACzC,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IArCe,2CAAyB,4BAqCxC,CAAA;IAED,SAAgB,UAAU,CAAC,IAAU,EAAE,QAAgB,EAAE,KAAa;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,OAAO,qBAAS,CAAC,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CACjF,CAAC,MAAM,EAAE,EAAE;YACT,UAAU;YACV,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC,CACF,CAAC;IACJ,CAAC;IAXe,4BAAU,aAWzB,CAAA;AACH,CAAC,EAlkBgB,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QAkkBjC;AAED,MAAa,SAAS;IA0BpB;QAzBA,YAAO,GAAW,CAAC,CAAC;QACpB,yBAAoB,GAAW,CAAC,CAAC;QAEjC,gBAAgB;QAChB,wBAAmB,GAIf,EAAE,CAAC;QAEP,2CAA2C;QAC3C,mBAAc,GAAoB;YAChC,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,CAAC;YACR,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;SACrB,CAAC;QAEF,iBAAiB;QACjB,yBAAoB,GAA2C,EAAE,CAAC;QAElE,eAAe;QACf,6BAAwB,GAA+C,EAAE,CAAC;IAE3D,CAAC;IAEhB,KAAK;QACH,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;QAC1B,CAAC,CAAC,QAAQ,CACR,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,oBAAoB,EACzB,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,EACrC,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,EAChC,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,EACtC,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C,CAAC;QACF,OAAO,CAAC,CAAC;IACX,CAAC;IACD,QAAQ,CACN,OAAe,EACf,oBAA4B,EAC5B,mBAIC,EACD,cAA+B,EAC/B,oBAA4D,EAC5D,wBAAoE;QAEpE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC3D,CAAC;IAED,iBAAiB,CAAC,SAAoB;QACpC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,SAAS,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAEzE,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;gBACxC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC;aAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/C,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;gBACtC,KAAK,EAAE,KAAK,CAAC,eAAe;gBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC9C,iBAAiB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC;aACrD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACvD,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;gBAC9C,KAAK,EAAE,SAAS,CAAC,mBAAmB;gBACpC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,CAAC;gBAClD,iBAAiB,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC;aACzD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW;QACT,MAAM,eAAe,GAOjB,EAAE,CAAC;QAEP,gBAAC,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,IAAI,EAAE,EAAE;YAC3C,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;aAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAOrB,EAAE,CAAC;QAEP,gBAAC,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,IAAI,EAAE,EAAE;YAC/C,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;aAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,GAAa;YACrB,SAAS,EAAE;gBACT,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;gBAC7C,gBAAgB,EAAE,IAAI,CAAC,oBAAoB;gBAC3C,eAAe;gBACf,mBAAmB;aACpB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IACD,YAAY;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;IACvC,CAAC;IAED,kBAAkB,CAAC,EAAmB;QACpC,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,SAAS,CAAC,IAAU,EAAE,EAAU,EAAE,SAAoB;QACpD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG;YACpB,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS;YACpC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;YAC3C,gBAAgB,EAAE,SAAS,CAAC,KAAK,CAAC,gBAAgB;YAClD,gBAAgB,EAAE,SAAS,CAAC,KAAK,CAAC,gBAAgB;YAClD,iBAAiB,EAAE,SAAS,CAAC,KAAK,CAAC,iBAAiB;SACrD,CAAC;QAEF,WAAW;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,SAAS,EAAE,EAAE,CAAC;QAClC,eAAK,CAAC,eAAe;aAClB,YAAY,CAAC,WAAW,CAAC;aACzB,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACZ,IAAI,CAAC,GAAG,EAAE;gBACR,OAAO,eAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;aAChF;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,eAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzE,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,eAAK,CAAC,eAAe,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACnE,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,cAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC7C,GAAG,EAAE,GAAG,CAAC,OAAO;gBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,EAAE;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEL,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,OAAO;YACL,MAAM,EAAE;gBACN,SAAS,EAAE;oBACT,mBAAmB,EAAE,IAAI;iBAC1B;aACF;SACF,CAAC;IACJ,CAAC;IACD,qBAAqB,CACnB,IAAU,EACV,SAAkB,EAClB,KAAc,EACd,gBAAyB,EACzB,gBAAyB,EACzB,iBAA0B;QAE1B,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,SAAS,EAAE;YAC5D,SAAS,GAAG,IAAI,CAAC;YACjB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAA,oBAAY,EAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,KAAK,EAAE;YAC/D,SAAS,GAAG,IAAI,CAAC;YACjB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;SACzC;QAED,IACE,CAAC,IAAA,oBAAY,EAAC,gBAAgB,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,gBAAgB,KAAK,gBAAgB,EACzD;YACA,SAAS,GAAG,IAAI,CAAC;YACjB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;SACpD;QACD,IACE,CAAC,IAAA,oBAAY,EAAC,gBAAgB,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,gBAAgB,KAAK,gBAAgB,EACzD;YACA,SAAS,GAAG,IAAI,CAAC;YACjB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;SACpD;QACD,IACE,CAAC,IAAA,oBAAY,EAAC,iBAAiB,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,iBAAiB,KAAK,iBAAiB,EAC3D;YACA,SAAS,GAAG,IAAI,CAAC;YACjB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC;SACrD;QAED,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;IACH,CAAC;IAED,UAAU,CACR,IAAU,EACV,4BAAqC,EACrC,8BAAuC;QAEvC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,oBAAoB,GAAG,IAAA,kBAAU,GAAE,CAAC;QAEzC,IAAI,CAAC,cAAc,GAAG;YACpB,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,CAAC;YACR,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;SACrB,CAAC;QAEF,MAAM,qBAAqB,GAAa,mCAAgB,CAAC,eAAe,CACtE,IAAI,CAAC,MAAM,EACX,OAAO,EACP,IAAA,kBAAU,GAAE,CACb,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,SAAS,OAAO,EAAE,CAAC;QACvC,eAAK,CAAC,eAAe;aAClB,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC;aACpC,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,8BAA8B,EAAE;gBAClC,OAAO,eAAK,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aAC3E;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,4BAA4B,EAAE;gBAChC,OAAO,eAAK,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aACzD;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,cAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBAC9C,GAAG,EAAE,GAAG,CAAC,OAAO;gBAChB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO;aACR,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEL,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,MAAM,IAAI,GAAS;YACjB,MAAM,EAAE;gBACN,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;iBACZ;aACF;YACD,GAAG,EAAE;gBACH,SAAS,EAAE;oBACT,gBAAgB,EAAE,IAAI,CAAC,oBAAoB;iBAC5C;aACF;SACF,CAAC;QAEF,KAAK,MAAM,cAAc,IAAI,qBAAqB,EAAE;YAClD,gBAAC,CAAC,KAAK,CAAa,IAAI,EAAE;gBACxB,GAAG,EAAE;oBACH,YAAY,EAAE;wBACZ,CAAC,cAAc,CAAC,EAAE;4BAChB,cAAc,EAAE,CAAC;yBAClB;qBACF;iBACF;aACF,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,OAAe,EAAE,UAAkB;QACrD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG;YAClC,UAAU;SACX,CAAC;QACF,OAAO;YACL,GAAG,EAAE;gBACH,SAAS,EAAE;oBACT,mBAAmB,EAAE;wBACnB,CAAC,OAAO,CAAC,EAAE;4BACT,UAAU;yBACX;qBACF;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACzC,OAAO;YACL,MAAM,EAAE;gBACN,SAAS,EAAE;oBACT,mBAAmB,EAAE;wBACnB,CAAC,OAAO,CAAC,EAAE,IAAI;qBAChB;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,2BAA2B,CAAC,GAA2B;QACrD,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;IAChD,CAAC;IAED,4BAA4B,CAAC,MAAc;QACzC,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;CACF;AAnWD,8BAmWC;AAED,MAAM,sBAAsB,GAAG,CAAC,EAAiB,EAAE,EAAE;IACnD,MAAM,MAAM,GAER,EAAE,CAAC;IAEP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,4BAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;QACjD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KAC/B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC"}