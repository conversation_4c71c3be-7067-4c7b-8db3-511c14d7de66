"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingQuerySalesList = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const userConnection_1 = require("../../userConnection");
const cashShopManager_1 = require("../../cashShopManager");
const Container_1 = require("typedi/Container");
const mutil = __importStar(require("../../../motiflib/mutil"));
// ----------------------------------------------------------------------------
class Cph_Common_BillingQuerySalesList {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    async exec(user, packet) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const curTimeUtc = mutil.curTimeUtc();
        return Promise.resolve()
            .then(() => {
            return mhttp_1.default.platformBillingApi.querySalesList(user.storeCode);
        })
            .then((billingApiResp) => {
            const cashShopManager = Container_1.Container.get(cashShopManager_1.CashShopManager);
            cashShopManager.updateSalesList(billingApiResp, user.storeCode, curTimeUtc);
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                billingApiResp,
            });
        });
    }
}
exports.Cph_Common_BillingQuerySalesList = Cph_Common_BillingQuerySalesList;
//# sourceMappingURL=billingQuerySalesList.js.map