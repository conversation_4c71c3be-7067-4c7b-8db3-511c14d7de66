// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import tuChangeUserName from '../../../mysqllib/txn/tuChangeUserName';
import { Resp, Sync } from '../../type/sync';
import { LobbyService } from '../../server';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import mhttp from '../../../motiflib/mhttp';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import UserPoints, { CashPayment, PointChange } from '../../userPoints';
import mlog from '../../../motiflib/mlog';
import mconf from '../../../motiflib/mconf';
import * as mutil from '../../../motiflib/mutil';
import { GameStateChange } from '../../userState';
import { TownManager } from '../../townManager';
import { ClientPacketHandler } from '../index';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';

// ----------------------------------------------------------------------------
// 선단명 변경.
// ----------------------------------------------------------------------------

const rsn = 'change_name';
const add_rsn = null;
interface RequestBody {
  name: string;
  bPermitExchange?: boolean;
  preoccupancyCode?: string;
}

interface Response extends Resp {
  bDuplicated: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Auth_ChangeName implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { name, bPermitExchange, preoccupancyCode } = body;

    // Check body.
    const oldName = user.userName;

    mutilLanguage.ensureValidName(
      oldName,
      name,
      cms.Const.CompanyNameMinimum.value,
      cms.Const.CompanyNameMaximum.value
    );

    const { userDbConnPoolMgr, userRedis, userCacheRedis } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // 처음 계정을 생성하는 경우 game state 를 NAME_SET 로 변경해준다.
    let bIsNewUser = false;
    let gameStateChange: GameStateChange;
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];

    if (!oldName) {
      bIsNewUser = true;
      gameStateChange = user.userState.buildGameStateChange(GAME_STATE.NAME_SET);
    } else {
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [
          {
            cmsId: cms.Const.ChangeCompanyNamePointId.value,
            cost: cms.Const.ChangeCompanyNamePointValue.value,
          },
        ],
        bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;

      // 시장은 이름을 변경할 수 없음.
      const townManager = Container.get(TownManager);
      const curTimeUtc = mutil.curTimeUtc();
      for (const townCms of _.values(cms.Town)) {
        if (townManager.getTownMayorUserId(townCms.id, curTimeUtc) === user.userId) {
          throw new MError(
            'can-not-change-user-name-because-your-are-mayor',
            MErrorCode.CANNOT_CHANGE_MAYOR_USER_NAME,
            {
              townCmsId: townCms.id,
            }
          );
        }
      }
    }

    const resp: Response = {
      bDuplicated: false,
      sync: {},
    };

    let bCommittedToAuthDb = false; // rollback 을 위한
    let bCommittedToUserDb = false; // rollback 을 위한

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return Promise.resolve()
      .then(() => {
        if (!user.isTestBot()) {
          return mhttp.platformApi.hasBadWord(name);
        }
        return false;
      })
      .then((bHas) => {
        if (bHas) {
          throw new MError('has-bad-word', MErrorCode.HAS_BAD_WORD, {
            name,
            oldName: user.userName,
          });
        }

        return mhttp.authd.changeUserName(user.userId, name, mconf.worldId, preoccupancyCode);
      })
      .then((bIsDuplicated) => {
        resp.bDuplicated = bIsDuplicated;
        if (!resp.bDuplicated) {
          bCommittedToAuthDb = true;

          return user.userPoints.tryConsumeCashs(cashPayments, resp.sync, user, {
            user,
            rsn,
            add_rsn,
            exchangeHash,
          });
        }
        return null;
      })
      .then(() => {
        if (!resp.bDuplicated) {
          return tuChangeUserName(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            name,
            gameStateChange,
            pointChanges
          );
        }
        return null;
      })
      .then(() => {
        if (!resp.bDuplicated) {
          bCommittedToUserDb = true;

          user.glog('common_nickname', {
            old_nick: user.userName,
            cur_nick: name,
            exchange_hash: exchangeHash,
          });

          user.userName = name;

          userRedis['setUserName'](user.userId, name).catch((err) => {
            mlog.error('userRedis setUserName is failed.', {
              userId: user.userId,
              name,
              err: err.message,
            });
          });
          userCacheRedis['setUserName'](user.userId, name).catch((err) => {
            mlog.error('userCacheRedis setUserName is failed.', {
              userId: user.userId,
              name,
              err: err.message,
            });
          });

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              user: {
                name,
              },
            },
          });

          if (gameStateChange) {
            _.merge<Sync, Sync>(
              resp.sync,
              user.userState.applyGameStateChange(gameStateChange, { user })
            );
          }

          _.merge<Sync, Sync>(
            resp.sync,
            user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
          );
        }

        if (!bIsNewUser) {
          // 근처 유저에게 노티.
          if (user.userState.isInTown()) {
            const townInfo = user.userTown.getTownInfo();
            if (townInfo) {
              const townApi = mhttp.townpx.channel(townInfo.url);
              townApi.updateTownUserSyncData(user.userId, { user: { name } }).catch((err) => {
                mlog.error('Town api updateTownUserSyncData is failed.', {
                  userId: user.userId,
                  err: err.message,
                  name,
                });
              });
            }
          } else if (user.userState.isInOcean()) {
            // TODO
          }

          mhttp.platformChatApi.updateVolanteUser(user);
        }

        // 선단명 선점 glog
        if (preoccupancyCode) {
          user.glog('nick_preoccupancy_code', {
            rsn,
            add_rsn,
            preoccupancy_code: preoccupancyCode,
          });
        }

        return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
      })
      .catch((err) => {
        return Promise.resolve()
          .then(() => {
            if (bCommittedToAuthDb && !bCommittedToUserDb) {
              mlog.warn('auth/changeName rollback auth name change.', {
                userId: user.userId,
                oldName,
                name,
                err: err.message,
              });

              return mhttp.authd.changeUserName(user.userId, oldName, mconf.worldId);
            }
            return null;
          })
          .then((bIsDuplicated) => {
            if (bCommittedToAuthDb && !bCommittedToUserDb && bIsDuplicated) {
              // TODO 완전한 해결을 위해 reserved system 필요함.
              // (한 번 사용된 닉네임은 다른 닉네임으로 변경하더라도 일정 기간 사용 못 하게 해야 됨)

              mlog.warn('auth/changeName rollback is failed.', {
                userId: user.userId,
                oldName,
                name,
                bIsDuplicated,
                err: err.message,
              });
            }
            throw err;
          });
      });
  }
}
