"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicChannle = exports.getAliasName = exports.CHANNEL_TYPE = void 0;
const cms_1 = __importDefault(require("../../cms"));
var CHANNEL_TYPE;
(function (CHANNEL_TYPE) {
    CHANNEL_TYPE[CHANNEL_TYPE["ALL"] = 0] = "ALL";
    CHANNEL_TYPE[CHANNEL_TYPE["SYSTEM"] = 1] = "SYSTEM";
    CHANNEL_TYPE[CHANNEL_TYPE["WORLD"] = 2] = "WORLD";
    CHANNEL_TYPE[CHANNEL_TYPE["NATION"] = 3] = "NATION";
    CHANNEL_TYPE[CHANNEL_TYPE["GUILD"] = 4] = "GUILD";
    CHANNEL_TYPE[CHANNEL_TYPE["REGION"] = 5] = "REGION";
    CHANNEL_TYPE[CHANNEL_TYPE["MAX"] = 5] = "MAX";
})(CHANNEL_TYPE = exports.CHANNEL_TYPE || (exports.CHANNEL_TYPE = {}));
function getAliasName(channelType, channelName) {
    if (channelType === CHANNEL_TYPE.SYSTEM) {
        return '안내';
    }
    else if (channelType === CHANNEL_TYPE.WORLD) {
        return '세계';
    }
    else if (channelType === CHANNEL_TYPE.NATION) {
        return '국가_' + cms_1.default.Nation[channelName].name;
    }
    else if (channelType === CHANNEL_TYPE.REGION) {
        if (cms_1.default.Region[channelName]) {
            return '지역_' + cms_1.default.Region[channelName].name;
        }
        else if (cms_1.default.Town[channelName]) {
            return '지역_' + cms_1.default.Town[channelName].name;
        }
    }
    else if (channelType === CHANNEL_TYPE.GUILD) {
        return '상회_' + channelName.replace('GUILD_', '');
    }
    return null;
}
exports.getAliasName = getAliasName;
var PublicChannle;
(function (PublicChannle) {
})(PublicChannle = exports.PublicChannle || (exports.PublicChannle = {}));
//# sourceMappingURL=iPlatformChatApiClient.js.map