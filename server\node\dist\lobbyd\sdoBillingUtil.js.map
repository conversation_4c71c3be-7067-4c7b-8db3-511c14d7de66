{"version": 3, "file": "sdoBillingUtil.js", "sourceRoot": "", "sources": ["../../src/lobbyd/sdoBillingUtil.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;AAE/E,iDAAyB;AACzB,+CAA4C;AAC5C,uDAAoD;AAEpD,iDAA6C;AAE7C,sGAAsF;AACtF,6CAA+C;AAC/C,8DAAsC;AAGtC,4DAAoC;AACpC,kDAAgD;AAUhD,MAAa,cAAc;IACzB,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,IAAU,EAAE,aAAqB;QAC5D,MAAM,WAAW,GAAG,aAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,eAAM,CAAC,uBAAuB,EAAE,uBAAU,CAAC,8BAA8B,EAAE;gBACnF,aAAa;aACd,CAAC,CAAC;SACJ;QAED,MAAM,MAAM,GAA0B,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QACnD,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,oBAAoB,GACxB,cAAc,CAAC,sCAAsC,CAAC,WAAW,CAAC,CAAC;QAErE,MAAM,IAAA,8CAAe,EAAC,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAA,kBAAU,GAAE,CAAC,CAAC;QACjF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;SAC1B;QAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,kBAAkB;aACxC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvE,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC;YACxC;gBACE,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,QAAQ,CAAC,iBAAiB;aACpC;YACD;gBACE,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,QAAQ,CAAC,iBAAiB;aACpC;SACF,EAAE,IAAI,CAAC,CAAC;QAET,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,sCAAsC,CAAC,WAAyB;QAC7E,MAAM,oBAAoB,GAAwB,EAAE,CAAC;QAErD,IAAI,WAAW,CAAC,oBAAoB,EAAE;YACpC,oBAAoB,CAAC,IAAI,CACvB,cAAc,CAAC,oCAAoC,CAAC,WAAW,CAAC,oBAAoB,CAAC,CACtF,CAAC;SACH;QAED,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,MAAM,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YACvG,IAAI,gBAAgB,EAAE;gBACpB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7C;SACF;QAED,IAAI,WAAW,CAAC,mBAAmB,EAAE;YACnC,MAAM,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAC3D,mBAAmB,EACnB,WAAW,CAAC,mBAAmB,EAC/B,CAAC,CACF,CAAC;YACF,IAAI,gBAAgB,EAAE;gBACpB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7C;SACF;QAED,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,MAAM,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAC3D,YAAY,EACZ,WAAW,CAAC,YAAY,EACxB,CAAC,CACF,CAAC;YACF,IAAI,gBAAgB,EAAE;gBACpB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7C;SACF;QAED,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,MAAM,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YACvG,IAAI,gBAAgB,EAAE;gBACpB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7C;SACF;QAED,IAAI,WAAW,CAAC,kBAAkB,IAAI,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/E,MAAM,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC9F,IAAI,gBAAgB,EAAE;gBACpB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7C;SACF;QAED,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAClC,eAAuB,EACvB,MAAc,EACd,MAAc;;QAEd,MAAM,eAAe,GAAsB,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAa;YACzB,eAAe;YACf,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;YACzB,gBAAgB,EAAE,SAAS;YAC3B,mBAAmB,EAAE,GAAG;YACxB,MAAM;SACP,CAAC;QACF,MAAM,GAAG,GAAG,0BAAW,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,EAAE;YACpB,cAAI,CAAC,IAAI,CACP,+BAA+B,0BAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cACnE,MAAA,GAAG,CAAC,GAAG,0CAAE,MACX,GAAG,CACJ,CAAC;YACF,OAAO,SAAS,CAAC;SAClB;QAED,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,MAAM,CAAC,oCAAoC,CAAC,aAAqB;QACvE,MAAM,cAAc,GAAG,aAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAC/C,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,WAAW,EAAE;YAC7C,MAAM,eAAe,GAAG,cAAc,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9E,MAAM,2BAA2B,GAAG,cAAc,CAAC,qBAAqB,CACtE,eAAe,EACf,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,QAAQ,CACd,CAAC;YACF,IAAI,2BAA2B,EAAE;gBAC/B,gBAAgB,CAAC,IAAI,CAAC,GAAG,2BAA2B,CAAC,CAAC;aACvD;SACF;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,4CAA4C;IAC5C,qDAAqD;IAC7C,MAAM,CAAC,2BAA2B,CAAC,UAAuB;QAChE,QAAQ,UAAU,EAAE;YAClB,KAAK,wBAAW,CAAC,IAAI;gBACnB,OAAO,MAAM,CAAC;YAChB,KAAK,wBAAW,CAAC,IAAI;gBACnB,OAAO,MAAM,CAAC;YAChB,KAAK,wBAAW,CAAC,UAAU;gBACzB,OAAO,QAAQ,CAAC;YAClB,KAAK,wBAAW,CAAC,cAAc;gBAC7B,OAAO,UAAU,CAAC;YACpB,KAAK,wBAAW,CAAC,IAAI;gBACnB,OAAO,MAAM,CAAC;YAChB,KAAK,wBAAW,CAAC,UAAU;gBACzB,OAAO,WAAW,CAAC;YACrB,KAAK,wBAAW,CAAC,KAAK;gBACpB,OAAO,OAAO,CAAC;YACjB,KAAK,wBAAW,CAAC,GAAG;gBAClB,OAAO,KAAK,CAAC;YACf;gBACE,OAAO,cAAc,CAAC;SACzB;IACH,CAAC;CACF;AAtKD,wCAsKC"}