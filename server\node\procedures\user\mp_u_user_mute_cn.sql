CREATE PROCEDURE `mp_u_user_mute`(
  IN inUserId INT,
  IN inMutedUserId INT
)
label_body:BEGIN
    -- TODO: userId/mutedUserId 가 정상적인 유저 레코드인지 체크.
    IF EXISTS (SELECT 1 FROM u_mute WHERE userId = inUserId AND mutedUserId = inMutedUserId) THEN
      SELECT 1 as result;
    ELSE
      INSERT INTO u_mute (userId, mutedUserId) VALUES (inUserId, inMutedUserId)
      ON DUPLICATE KEY UPDATE
          userId = inUserId,
          mutedUserId = inMutedUserId;
      SELECT 0 as result;
    END IF;
END
