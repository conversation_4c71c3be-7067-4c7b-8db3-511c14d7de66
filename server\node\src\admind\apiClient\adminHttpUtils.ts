import { LineGamesApiClient } from '../../motiflib/mhttp/linegamesApiClient';
import { ProxyAdminHealthApiClient } from './adminHealthApiClient';
import mconf from '../../motiflib/mconf';
import { AdminProxyLobbyApiClient } from './adminLobbyApiClient';
import { AdminProxySailApiClient } from './adminSailApiClient';
import { AdminProxyRealmApiClient } from './adminRealmApiClient';
import { PLATFORM } from '../../motiflib/model/auth/enum';
import { IPlatformApiClient } from '../../motiflib/mhttp/iPlatformApiClient';
import { SdoApiClient } from '../../motiflib/mhttp/sdoApiClient';

export namespace AdminHttpUtils {
  export let platformApi: IPlatformApiClient = null;

  export const proxyHealth = new ProxyAdminHealthApiClient();
  export const proxyLobby = new AdminProxyLobbyApiClient();
  export const proxySail = new AdminProxySailApiClient();
  export const proxyRealm = new AdminProxyRealmApiClient();

  export function init() {
    if (mconf.platform === PLATFORM.LINE) {
      const linegamesApiClient = new LineGamesApiClient();
      linegamesApiClient.init(mconf.http.lgd.url);
      platformApi = linegamesApiClient;
    } else if (mconf.platform === PLATFORM.SDO) {
      const sdoClient = new SdoApiClient();
      sdoClient.init(mconf.http.sdo.url);
      platformApi = sdoClient;
    }
  }
}
