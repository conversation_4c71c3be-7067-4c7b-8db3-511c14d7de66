libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/prep_cif.lo -MD -MP -MF src/.deps/prep_cif.Tpo -c src/prep_cif.c  -fPIC -DPIC -o src/prep_cif.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/types.lo -MD -MP -MF src/.deps/types.Tpo -c src/types.c  -fPIC -DPIC -o src/types.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/raw_api.lo -MD -MP -MF src/.deps/raw_api.Tpo -c src/raw_api.c  -fPIC -DPIC -o src/raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/java_raw_api.lo -MD -MP -MF src/.deps/java_raw_api.Tpo -c src/java_raw_api.c  -fPIC -DPIC -o src/java_raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/closures.lo -MD -MP -MF src/.deps/closures.Tpo -c src/closures.c  -fPIC -DPIC -o src/closures.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/x86/ffi64.lo -MD -MP -MF src/x86/.deps/ffi64.Tpo -c src/x86/ffi64.c  -fPIC -DPIC -o src/x86/ffi64.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -MT src/x86/unix64.lo -MD -MP -MF src/x86/.deps/unix64.Tpo -c src/x86/unix64.S  -fPIC -DPIC -o src/x86/unix64.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -O3 -fomit-frame-pointer -malign-double -fstrict-aliasing -ffast-math -march=pentium2 -Wall -fexceptions -MT src/x86/ffi.lo -MD -MP -MF src/x86/.deps/ffi.Tpo -c src/x86/ffi.c  -fPIC -DPIC -o src/x86/ffi.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -MT src/x86/sysv.lo -MD -MP -MF src/x86/.deps/sysv.Tpo -c src/x86/sysv.S  -fPIC -DPIC -o src/x86/sysv.o