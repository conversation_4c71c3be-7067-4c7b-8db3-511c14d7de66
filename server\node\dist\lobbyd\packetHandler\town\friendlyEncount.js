"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Town_FriendlyEncount = void 0;
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const gameState_1 = require("../../../motiflib/model/lobby/gameState");
const merror_1 = require("../../../motiflib/merror");
const userFriendlyEncount_1 = require("../../userFriendlyEncount");
const townUserState_1 = require("../../../townd/townUserState");
const lodash_1 = __importDefault(require("lodash"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const nanoid_1 = require("nanoid");
const fleetHelper_1 = require("../../fleetHelper");
const lobby_1 = require("../../../motiflib/model/lobby");
const typedi_1 = __importDefault(require("typedi"));
const server_1 = require("../../server");
const userFriendlyEncount_2 = require("../../userFriendlyEncount");
// ----------------------------------------------------------------------------
class Cph_Town_FriendlyEncount {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        if (user.userState.getGameState() === gameState_1.GAME_STATE.IN_TOWN &&
            user.userState.getTownUserState() === townUserState_1.TOWN_USER_STATE.IN_TOWN) {
            return true;
        }
        return false;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        const { userId: defenserUserId } = packet.bodyObj;
        const { userCacheRedis } = typedi_1.default.get(server_1.LobbyService);
        const curTimeUtc = mutil.curTimeUtc();
        if (!defenserUserId || !lodash_1.default.isInteger(defenserUserId)) {
            throw new merror_1.MError('invalid-user-id', merror_1.MErrorCode.INVALID_REQ_BODY_REQUEST_FRIENDLY_BATTLE, {
                defenserUserId,
            });
        }
        const encountResult = userFriendlyEncount_1.FriendlyEncountUtil.checkFriendlyEncount(user, true);
        if (encountResult !== userFriendlyEncount_2.FriendlyEncountResult.WAITING_FOR_CHOICE) {
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                encountResult,
            });
        }
        const pvpId = (0, nanoid_1.nanoid)(8);
        return mhttp_1.default.platformChatApi
            .getMuteUserIds(defenserUserId)
            .then((muteUserIds) => {
            for (const muteUserId of muteUserIds) {
                if (muteUserId === user.userId) {
                    throw new merror_1.MError('attacker-is-blocked-user', merror_1.MErrorCode.FRIENDLY_ENCOUNT_REQUEST_BLOCKED_USER, {
                        attackerUserId: user.userId,
                        defenserUserId,
                        defenserMuteUserIds: muteUserIds,
                    });
                }
            }
        })
            .then(() => {
            return userCacheRedis['getIsFriendlyBattleRequestable'](defenserUserId);
        })
            .then((defenserRequestable) => {
            // 상대가 친선전 허용한 상태인지 검사.
            if (defenserRequestable !== 1) {
                throw new merror_1.MError('no-friendly-encount-requestable', merror_1.MErrorCode.INVALID_REQ_BODY_REQUEST_FRIENDLY_BATTLE, {
                    defenserUserId,
                    defenserRequestable,
                });
            }
        })
            .then(() => {
            const attackerFleetData = fleetHelper_1.FleetHelper.buildOceanFleetData(user, lobby_1.BattleType.Friendly);
            const townInfo = user.userTown.getTownInfo();
            const townApi = mhttp_1.default.townpx.channel(townInfo.url);
            return townApi.friendlyEncount(user.userId, user.pubId, attackerFleetData, user.userGuild.getGuildName(), defenserUserId, pvpId);
        })
            .then((recv) => {
            if (recv.encountResult === userFriendlyEncount_2.FriendlyEncountResult.WAITING_FOR_CHOICE) {
                const encountState = {
                    bAttack: true,
                    encountResult: userFriendlyEncount_2.FriendlyEncountResult.WAITING_FOR_CHOICE,
                    timeUtc: curTimeUtc,
                    bClosing: false,
                    fleetData: recv.defenserFleetData,
                    userId: defenserUserId,
                    pubId: recv.defenserPubId,
                    pvpId,
                    representedMateCmsId: recv.defenserRepresentedMateCmsId > 0 ? recv.defenserRepresentedMateCmsId : undefined,
                    representedMateIllustCmsId: recv.defenserRepresentedMateIllustCmsId > 0
                        ? recv.defenserRepresentedMateIllustCmsId
                        : undefined,
                    oceanNpcStageCmsId: recv.oceanStageCmsId,
                };
                user.userFriendlyEncount.beginEncount(encountState);
                user.userState.onEncountStateSet();
            }
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                encountResult: recv.encountResult,
            });
        });
    }
}
exports.Cph_Town_FriendlyEncount = Cph_Town_FriendlyEncount;
//# sourceMappingURL=friendlyEncount.js.map