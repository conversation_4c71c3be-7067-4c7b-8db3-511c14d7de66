libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/prep_cif.lo -MD -MP -MF src/.deps/prep_cif.Tpo -c src/prep_cif.c  -fno-common -DPIC -o src/prep_cif.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/types.lo -MD -MP -MF src/.deps/types.Tpo -c src/types.c  -fno-common -DPIC -o src/types.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/raw_api.lo -MD -MP -MF src/.deps/raw_api.Tpo -c src/raw_api.c  -fno-common -DPIC -o src/raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/java_raw_api.lo -MD -MP -MF src/.deps/java_raw_api.Tpo -c src/java_raw_api.c  -fno-common -DPIC -o src/java_raw_api.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/closures.lo -MD -MP -MF src/.deps/closures.Tpo -c src/closures.c  -fno-common -DPIC -o src/closures.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/x86/ffi.lo -MD -MP -MF src/x86/.deps/ffi.Tpo -c src/x86/ffi.c  -fno-common -DPIC -o src/x86/ffi.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -MT src/x86/darwin.lo -MD -MP -MF src/x86/.deps/darwin.Tpo -c src/x86/darwin.S  -fno-common -DPIC -o src/x86/darwin.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -Wall -fexceptions -MT src/x86/ffi64.lo -MD -MP -MF src/x86/.deps/ffi64.Tpo -c src/x86/ffi64.c  -fno-common -DPIC -o src/x86/ffi64.o
libtool: compile:  gcc -DHAVE_CONFIG_H -I. -I. -I./include -Iinclude -I./src -DFFI_BUILDING -I. -I./include -Iinclude -I./src -DFFI_BUILDING -g -arch i386 -MT src/x86/darwin64.lo -MD -MP -MF src/x86/.deps/darwin64.Tpo -c src/x86/darwin64.S  -fno-common -DPIC -o src/x86/darwin64.o