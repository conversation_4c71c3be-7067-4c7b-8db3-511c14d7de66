import { AuthApiClient } from './authApiClient';
import { ConfigApiClient } from './configApiClient';
import { SailApiClient } from './sailApiClient';
import { ProxyOceanApiClient } from './oceanApiClient';
import { ProxyLobbyApiClient } from './lobbyApiClient';
import { ProxyTownApiClient } from './townApiClient';
import { ZonelbApiClient } from './zonelbApiClient';
import { RealmApiClient } from './realmApiClient';
import { NavApiClient } from './navApiClient';
import { LineGamesApiClient } from './linegamesApiClient';
import mconf, { World } from '../mconf';
import { LineGamesLogApiClient } from './linegamesLogApiClient';
import { LineGamesBillingApiClient } from './linegamesBillingApiClient';
import { LineGamesPayApiClient } from './linegamesPayApiClient';
import { IPlatformApiClient } from './iPlatformApiClient';
import { IPlatformBillingApiClient } from './iPlatformBillingApiClient';
import { SdoApiClient } from './sdoApiClient';
import { SdoBillingApiClient } from './sdoBillingApiClient';
import { PLATFORM } from '../model/auth/enum';
import mlog from '../mlog';
import { SdoAntiAddictionApiClient } from './sdoAntiAddictionApiClient';
import { IPlatformChatApiClient } from './iPlatformChatApiClient';
import { LineGamesChatApiClient } from './linegamesChatApiClient';
import { SdoChatApiClient } from './sdoChatApiClient';

export { LobbyGroup } from './lobbyApiClient';

// 모든 서버에 configd 설정은 있어야 한다.
class HttpClients {
  public configd: ConfigApiClient;
  public authd: AuthApiClient;
  public oceanpx: ProxyOceanApiClient;
  public townpx: ProxyTownApiClient;
  public lobbypx: ProxyLobbyApiClient;
  public saild: SailApiClient;
  public zonelbd: ZonelbApiClient;
  public realmd: RealmApiClient;
  public lglogd: LineGamesLogApiClient;
  // public lgbillingd: LineGamesBillingApiClient;
  //public chatd: ChatApiClient;
  public navid: NavApiClient;
  // public lgpayd: LineGamesPayApiClient;

  public platformApi: IPlatformApiClient;
  public platformBillingApi: IPlatformBillingApiClient;
  public platformPayApi: IPlatformPayApiClient;
  public platformChatApi: IPlatformChatApiClient;
  public sdoaa: SdoAntiAddictionApiClient;

  // 각 월드에 소속된 http목록. admind에서 사용.
  public worldHttp: {
    [worldId: string]: {
      saild: SailApiClient;
      zonelbd: ZonelbApiClient;
      realmd: RealmApiClient;
      //chatd: ChatApiClient;
      //lgpayd: LineGamesPayApiClient;
      //lgbillingd: LineGamesBillingApiClient;

      platformChatApi: IPlatformChatApiClient;
      platformBillingApi: IPlatformBillingApiClient;
      platformApiApi: IPlatformApiClient;
    };
  } = {};

  constructor() {
    this.configd = new ConfigApiClient(mconf.http.configd.url, mconf.http.configd.timeout);
    this.authd = new AuthApiClient();
    this.oceanpx = new ProxyOceanApiClient();
    this.townpx = new ProxyTownApiClient();
    this.lobbypx = new ProxyLobbyApiClient();
    this.saild = new SailApiClient();
    this.zonelbd = new ZonelbApiClient();
    this.realmd = new RealmApiClient();
    this.lglogd = new LineGamesLogApiClient();
    // this.lgbillingd = new LineGamesBillingApiClient();
    //this.chatd = new ChatApiClient();
    this.navid = new NavApiClient();
    // this.lgpayd = new LineGamesPayApiClient();
    this.sdoaa = new SdoAntiAddictionApiClient();
  }

  init() {
    // Initialize platform API based on platform configuration
    if (mconf.platform === PLATFORM.LINE) {
      if (mconf.lgd) {
        const lineGamesApiClient = new LineGamesApiClient();
        lineGamesApiClient.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
        this.platformApi = lineGamesApiClient;
      }

      if (mconf.http.lgbillingd) {
        const linegamesBillingApiClient = new LineGamesBillingApiClient();
        linegamesBillingApiClient.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
        linegamesBillingApiClient.setAuthPassword(mconf.http.lgbillingd.authPwd);
        this.platformBillingApi = linegamesBillingApiClient;
      }

      if (mconf.http.chatd) {
        const linegamesChatApiClient = new LineGamesChatApiClient();
        linegamesChatApiClient.init(mconf.http.chatd.url, mconf.http.chatd.timeout);
      }
    } else if (mconf.platform === PLATFORM.SDO) {
      if (mconf.http.sdo) {
        const sdoApiClient = new SdoApiClient();
        sdoApiClient.init(mconf.http.sdo.url, mconf.http.sdo.timeout);
        this.platformApi = sdoApiClient;
      }

      if (mconf.http.sdoBilling) {
        const sdoBillingApiClient = new SdoBillingApiClient();
        //sdoBillingClient.init(mconf.http.sdo.url, mconf.http.sdo.timeout);
        this.platformBillingApi = sdoBillingApiClient;
      }

      if (mconf.http.sdoChat) {
        const sdoChatApiClient = new SdoChatApiClient();
        this.platformChatApi = sdoChatApiClient;
      }

      this.sdoaa.init(
        mconf.http.sdoaa.url, 
        mconf.http.sdoaa.timeout, 
        mconf.http.sdoaa.remainingTimeWarnThresholdInSec);
    } else {
      mlog.error(`platform id ${mconf.platform} is not supported`);
    }

    if (mconf.http.authd) {
      this.authd.init(mconf.http.authd.url, mconf.http.authd.timeout);
    }
    if (mconf.http.saild) {
      this.saild.init(mconf.http.saild.url, mconf.http.saild.timeout);
    }
    if (mconf.http.zonelbd) {
      this.zonelbd.init(mconf.http.zonelbd.url, mconf.http.zonelbd.timeout);
    }
    if (mconf.http.realmd) {
      this.realmd.init(mconf.http.realmd.url, mconf.http.realmd.timeout);
    }

    // if (mconf.http.lgd) {
    //   this.lgd.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
    // }
    if (mconf.http.lglogd) {
      this.lglogd.init(mconf.http.lglogd.url, mconf.http.lglogd.timeout);
    }
    // if (mconf.http.lgbillingd) {
    //   this.lgbillingd.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
    //   this.lgbillingd.setAuthPassword(mconf.http.lgbillingd.authPwd);
    // }
    // if (mconf.http.chatd) {
    //   this.chatd.init(mconf.http.chatd.url, mconf.http.chatd.timeout);
    //   this.chatd.setSalt(mconf.http.chatd.salt);
    // }
    if (mconf.http.navid) {
      this.navid.init(mconf.http.navid.url, mconf.http.navid.timeout);
    }
    if (mconf.http.lgpayd) {
      this.lgpayd.init(mconf.http.lgpayd.url, mconf.http.lgpayd.timeout);
      this.lgpayd.setAuthPassword(mconf.http.lgpayd.authPwd);
    }

    mconf.worlds.forEach((world: World) => {
      let platformBillingApi: IPlatformBillingApiClient | null = null;
      let platformPayApi: IPlatformPayApiClient | null = null;
      let platformChatApi: IPlatformChatApiClient | null = null;

      if (mconf.platform === PLATFORM.SDO) {
        const sdoChatApiClient = new SdoChatApiClient();
        sdoChatApiClient.init(world.http.chatd.url, world.http.chatd.timeout);
        platformChatApi = sdoChatApiClient;

        const sdoBillingApiClient = new SdoBillingApiClient();
        platformBillingApi = sdoBillingApiClient;

        // TODO
      } else {
        if (world.http.chatd) {
          const linegamesChatApiClient = new LineGamesChatApiClient();
          linegamesChatApiClient.init(world.http.chatd.url, world.http.chatd.timeout);
          platformChatApi = linegamesChatApiClient;
        }

        if (world.http.lgbillingd) {
          const linegamesBillingApiClient = new LineGamesBillingApiClient();
          linegamesBillingApiClient.init(world.http.lgbillingd.url, world.http.lgbillingd.timeout);
          linegamesBillingApiClient.setAuthPassword(world.http.lgbillingd.authPwd);
          platformBillingApi = linegamesBillingApiClient;
        }

        if (world.http.lgpayd) {
          const linegamesPayApiClient = new LineGamesPayApiClient();
          linegamesPayApiClient.init(world.http.lgpayd.url, world.http.lgpayd.timeout);
          linegamesPayApiClient.setAuthPassword(world.http.lgpayd.authPwd);
          platformPayApi = linegamesPayApiClient;
        }
      }

      let saild: SailApiClient;
      if (world.http.saild) {
        saild = new SailApiClient();
        saild.init(world.http.saild.url, world.http.saild.timeout);
      }

      let zonelbd: ZonelbApiClient;
      if (world.http.zonelbd) {
        zonelbd = new ZonelbApiClient();
        zonelbd.init(world.http.zonelbd.url, world.http.zonelbd.timeout);
      }

      let realmd: RealmApiClient;
      if (world.http.realmd) {
        realmd = new RealmApiClient();
        realmd.init(world.http.realmd.url, world.http.realmd.timeout);
      }

      //TODO
      // this.worldHttp[world.id] = {
      //   chatd,
      //   lgbillingd,
      //   saild,
      //   zonelbd,
      //   realmd,
      //   lgpayd,
      // };
    });
  }
}

const httpClients = new HttpClients();

export default httpClients;
