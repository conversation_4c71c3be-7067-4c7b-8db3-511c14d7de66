import { AuthApiClient } from './authApiClient';
import { ConfigApiClient } from './configApiClient';
import { SailApiClient } from './sailApiClient';
import { ProxyOceanApiClient } from './oceanApiClient';
import { ProxyLobbyApiClient } from './lobbyApiClient';
import { ProxyTownApiClient } from './townApiClient';
import { ZonelbApiClient } from './zonelbApiClient';
import { RealmApiClient } from './realmApiClient';
import { NavApiClient } from './navApiClient';
import { ChatApiClient } from './chatApiClient';
import { LineGamesApiClient } from './linegamesApiClient';
import mconf, { World } from '../mconf';
import { LineGamesLogApiClient } from './linegamesLogApiClient';
import { LineGamesBillingApiClient } from './linegamesBillingApiClient';
import { LineGamesPayApiClient } from './linegamesPayApiClient';
import { IPlatformApiClient } from './iPlatformApiClient';
import { SdoApiClient } from './sdoApiClient';
import { PLATFORM } from '../model/auth/enum';
import mlog from '../mlog';
import { SdoAntiAddictionApiClient } from './sdoAntiAddictionApiClient';

export { LobbyGroup } from './lobbyApiClient';

// 모든 서버에 configd 설정은 있어야 한다.
class HttpClients {
  public configd: ConfigApiClient;
  public authd: AuthApiClient;
  public oceanpx: ProxyOceanApiClient;
  public townpx: ProxyTownApiClient;
  public lobbypx: ProxyLobbyApiClient;
  public saild: SailApiClient;
  public zonelbd: ZonelbApiClient;
  public realmd: RealmApiClient;
  public lglogd: LineGamesLogApiClient;
  public lgbillingd: LineGamesBillingApiClient;
  public chatd: ChatApiClient;
  public navid: NavApiClient;
  public lgpayd: LineGamesPayApiClient;
  public platformApi: IPlatformApiClient;
  public platformBillingApi: IPlatformBillingApiClient;
  public sdoaa: SdoAntiAddictionApiClient;

  // 각 월드에 소속된 http목록. admind에서 사용.
  public worldHttp: {
    [worldId: string]: {
      lgbillingd: LineGamesBillingApiClient;
      saild: SailApiClient;
      zonelbd: ZonelbApiClient;
      realmd: RealmApiClient;
      chatd: ChatApiClient;
      lgpayd: LineGamesPayApiClient;
    };
  } = {};

  constructor() {
    this.configd = new ConfigApiClient(mconf.http.configd.url, mconf.http.configd.timeout);
    this.authd = new AuthApiClient();
    this.oceanpx = new ProxyOceanApiClient();
    this.townpx = new ProxyTownApiClient();
    this.lobbypx = new ProxyLobbyApiClient();
    this.saild = new SailApiClient();
    this.zonelbd = new ZonelbApiClient();
    this.realmd = new RealmApiClient();
    this.lglogd = new LineGamesLogApiClient();
    this.lgbillingd = new LineGamesBillingApiClient();
    this.chatd = new ChatApiClient();
    this.navid = new NavApiClient();
    this.lgpayd = new LineGamesPayApiClient();
    this.sdoaa = new SdoAntiAddictionApiClient();
  }

  init() {
    // Initialize platform API based on platform configuration
    if (mconf.platform === PLATFORM.LINE) {
      const lineGamesApiClient = new LineGamesApiClient();
      lineGamesApiClient.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
      this.platformApi = lineGamesApiClient;
    } else if (mconf.platform === PLATFORM.SDO) {
      const sdoClient = new SdoApiClient();
      sdoClient.init(mconf.http.sdo.url, mconf.http.sdo.timeout);
      this.platformApi = sdoClient;

      this.sdoaa.init(mconf.http.sdoaa.url, mconf.http.sdoaa.timeout, mconf.http.sdoaa.remainingTimeWarnThresholdInSec);
    } else {
      mlog.error(`platform id ${mconf.platform} is not supported`);
    }

    if (mconf.http.authd) {
      this.authd.init(mconf.http.authd.url, mconf.http.authd.timeout);
    }
    if (mconf.http.saild) {
      this.saild.init(mconf.http.saild.url, mconf.http.saild.timeout);
    }
    if (mconf.http.zonelbd) {
      this.zonelbd.init(mconf.http.zonelbd.url, mconf.http.zonelbd.timeout);
    }
    if (mconf.http.realmd) {
      this.realmd.init(mconf.http.realmd.url, mconf.http.realmd.timeout);
    }

    // if (mconf.http.lgd) {
    //   this.lgd.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
    // }
    if (mconf.http.lglogd) {
      this.lglogd.init(mconf.http.lglogd.url, mconf.http.lglogd.timeout);
    }
    if (mconf.http.lgbillingd) {
      this.lgbillingd.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
      this.lgbillingd.setAuthPassword(mconf.http.lgbillingd.authPwd);
    }
    if (mconf.http.chatd) {
      this.chatd.init(mconf.http.chatd.url, mconf.http.chatd.timeout);
      this.chatd.setSalt(mconf.http.chatd.salt);
    }
    if (mconf.http.navid) {
      this.navid.init(mconf.http.navid.url, mconf.http.navid.timeout);
    }
    if (mconf.http.lgpayd) {
      this.lgpayd.init(mconf.http.lgpayd.url, mconf.http.lgpayd.timeout);
      this.lgpayd.setAuthPassword(mconf.http.lgpayd.authPwd);
    }

    mconf.worlds.forEach((world: World) => {
      let chatd: ChatApiClient;
      if (world.http.chatd) {
        chatd = new ChatApiClient();
        chatd.init(world.http.chatd.url, world.http.chatd.timeout);
        chatd.setSalt(world.http.chatd.salt);
      }
      let lgbillingd: LineGamesBillingApiClient;
      if (world.http.lgbillingd) {
        lgbillingd = new LineGamesBillingApiClient();
        lgbillingd.init(world.http.lgbillingd.url, world.http.lgbillingd.timeout);
        lgbillingd.setAuthPassword(world.http.lgbillingd.authPwd);
      }

      let saild: SailApiClient;
      if (world.http.saild) {
        saild = new SailApiClient();
        saild.init(world.http.saild.url, world.http.saild.timeout);
      }

      let zonelbd: ZonelbApiClient;
      if (world.http.zonelbd) {
        zonelbd = new ZonelbApiClient();
        zonelbd.init(world.http.zonelbd.url, world.http.zonelbd.timeout);
      }

      let realmd: RealmApiClient;
      if (world.http.realmd) {
        realmd = new RealmApiClient();
        realmd.init(world.http.realmd.url, world.http.realmd.timeout);
      }

      let lgpayd: LineGamesPayApiClient;
      if (world.http.lgpayd) {
        lgpayd = new LineGamesPayApiClient();
        lgpayd.init(world.http.lgpayd.url, world.http.lgpayd.timeout);
        lgpayd.setAuthPassword(world.http.lgpayd.authPwd);
      }
      this.worldHttp[world.id] = {
        chatd,
        lgbillingd,
        saild,
        zonelbd,
        realmd,
        lgpayd,
      };
    });
  }
}

const httpClients = new HttpClients();

export default httpClients;
