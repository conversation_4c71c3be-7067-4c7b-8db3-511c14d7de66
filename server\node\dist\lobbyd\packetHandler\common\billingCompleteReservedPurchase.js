"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_BillingCompleteReservedPurchase = void 0;
const assert_1 = __importDefault(require("assert"));
const lodash_1 = __importDefault(require("lodash"));
const typedi_1 = __importDefault(require("typedi"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const merror_1 = require("../../../motiflib/merror");
const userConnection_1 = require("../../userConnection");
const linegamesBillingApiClient_1 = require("../../../motiflib/mhttp/linegamesBillingApiClient");
const puCashShopRestrictedProductUpdate_1 = __importDefault(require("../../../mysqllib/sp/puCashShopRestrictedProductUpdate"));
const server_1 = require("../../server");
const userCashShop_1 = require("../../userCashShop");
const cmsEx = __importStar(require("../../../cms/ex"));
const cashShopDesc_1 = require("../../../cms/cashShopDesc");
const cms_1 = __importDefault(require("../../../cms"));
const formula_1 = require("../../../formula");
const displayNameUtil = __importStar(require("../../../motiflib/displayNameUtil"));
const tuUpdateBillingProducts_1 = __importDefault(require("../../../mysqllib/txn/tuUpdateBillingProducts"));
// ----------------------------------------------------------------------------
// 빌링 서버에 예약된 구매건을 완료 요청
// ----------------------------------------------------------------------------
const rsn = 'billing_complete_reserved_purchase';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Common_BillingCompleteReservedPurchase {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        var _a, _b;
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const requestBody = packet.bodyObj;
        const respBody = { billingApiResp: undefined, sync: {} };
        const { orderId, cmsId, floorOrderId, productId, floorStoreOrderId } = requestBody;
        const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
        if (!linegamesBillingApiClient_1.LineGamesBillingApiClient.isCompleteReservedPurchaseImplemented(user.storeCode)) {
            // 이 부분이 필요할지 모르겠음.
            throw new merror_1.MError('invalid-store-code', merror_1.MErrorCode.INTERNAL_ERROR, {
                storeCode: user.storeCode,
            });
        }
        const signature = user.storeCode === 'GOOGLE_PLAY'
            ? (_a = requestBody.googleSignature) !== null && _a !== void 0 ? _a : null
            : (_b = requestBody.signature) !== null && _b !== void 0 ? _b : null;
        /**
         * 구매 가능한지 검사
         * https://jira.line.games/browse/QAUWO-10905
         */
        const cashShopCms = cms_1.default.CashShop[cmsId];
        if (!cashShopCms) {
            throw new merror_1.MError('failed-to-get-cash-shop-cms', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_COMPLETE_RESERVE_PURCHASE, { requestBody });
        }
        const curTimeUtc = mutil.curTimeUtc();
        if (floorOrderId) {
            const reason = user.userCashShop.isBuyableProduct(user, cmsId, null, user.userCashShop.getExpiredRestrictedProducts(curTimeUtc), 1);
            if (reason === userCashShop_1.UNBUYABLE_REASON.SOLD_OUT ||
                reason === userCashShop_1.UNBUYABLE_REASON.ALREADY_BOUGHT_SOUND ||
                reason === userCashShop_1.UNBUYABLE_REASON.ALREADY_BOUGHT_EVENT_PAGE_PRODUCT ||
                reason === userCashShop_1.UNBUYABLE_REASON.ALREADY_BOUGHT_MATE ||
                reason === userCashShop_1.UNBUYABLE_REASON.ALREADY_BOUGHT_PET) {
                return mhttp_1.default.lgpayd
                    .cancelFloorReserve(user.storeCode, floorOrderId, floorStoreOrderId, orderId, productId, user.userId)
                    .catch((err) => {
                    throw new merror_1.MError('billing-cancel-is-failed-2', merror_1.MErrorCode.BILLING_CANCEL_IS_FAILED, {
                        reason,
                        restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
                        err: err.message,
                    });
                })
                    .then((ret) => {
                    if (ret.errorCd) {
                        throw new merror_1.MError('billing-cancel-is-failed-1', merror_1.MErrorCode.BILLING_CANCEL_IS_FAILED, {
                            reason,
                            restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
                            ret,
                        });
                    }
                    else {
                        throw new merror_1.MError('can-not-buy-cash-shop-biiling-product', merror_1.MErrorCode.BILLING_UNBUYABLE_PRODUCT, {
                            reason,
                            restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
                        });
                    }
                });
            }
        }
        // 핫스팟은 쿨타임 및 상품 등장 관련 여러 문제로 보관함에서 받았을때가 아닌 구매했을때 바로 쿨타임이 시작하도록 함
        let restrictedProductChangeForGLog;
        let hotSpotProduct;
        if (cashShopCms.productType === cashShopDesc_1.CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
            hotSpotProduct = lodash_1.default.cloneDeep(user.userCashShop.getHotSpotProduct(cashShopCms.id));
            if (hotSpotProduct) {
                hotSpotProduct.expireTimeUtc = curTimeUtc;
                hotSpotProduct.coolTimeUtc = curTimeUtc + cashShopCms.coolTimeDays * formula_1.SECONDS_PER_DAY;
            }
        }
        let consecutiveProduct;
        if ((0, cashShopDesc_1.getConsecutiveProductCodeByStoreCode)(cashShopCms, user.storeCode)) {
            const product = user.userCashShop.getConsecutiveProduct(cashShopCms.id);
            const normalSaleSeconds = cashShopCms.normalSaleHours * formula_1.SECONDS_PER_HOUR;
            const discountSaleSeconds = cashShopCms.discountSaleHours * formula_1.SECONDS_PER_HOUR;
            if (!product || curTimeUtc > product.discountSaleTimeUtc) {
                consecutiveProduct = {
                    cmsId: cashShopCms.id,
                    normalSaleTimeUtc: curTimeUtc + normalSaleSeconds,
                    discountSaleTimeUtc: curTimeUtc + normalSaleSeconds + discountSaleSeconds,
                };
            }
            else {
                consecutiveProduct = lodash_1.default.cloneDeep(product);
                const normalSaleTimeUtc = curTimeUtc + (product.discountSaleTimeUtc - curTimeUtc) + normalSaleSeconds;
                consecutiveProduct.normalSaleTimeUtc = normalSaleTimeUtc;
                consecutiveProduct.discountSaleTimeUtc = normalSaleTimeUtc + discountSaleSeconds;
            }
        }
        return Promise.resolve()
            .then(() => {
            return _completeReservedPurchaseByStoreCode(user, requestBody);
        })
            .then(async (billingApiResp) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;
            respBody.billingApiResp = billingApiResp;
            if (billingApiResp.success === true) {
                // 빌링 API 에서 인자들의 타입(RequestBody) 및 유효성이 검증됐다고 가정.
                const productId = (_b = (_a = billingApiResp.data) === null || _a === void 0 ? void 0 : _a.productId) !== null && _b !== void 0 ? _b : null;
                user.glog('common_iap', {
                    pn: productId,
                    spn: (_c = requestBody.productName) !== null && _c !== void 0 ? _c : null,
                    pr: Number(requestBody.price),
                    currency: requestBody.currency,
                    os: (_e = (_d = user.deviceType) === null || _d === void 0 ? void 0 : _d.toUpperCase()) !== null && _e !== void 0 ? _e : null,
                    osv: (_f = user.osv) !== null && _f !== void 0 ? _f : null,
                    lang: (_g = user.deviceLang) !== null && _g !== void 0 ? _g : null,
                    lang_game: (_h = user.lineLangCultre) !== null && _h !== void 0 ? _h : null,
                    sk: user.storeCode,
                    country_ip: (_j = user.countryIp) !== null && _j !== void 0 ? _j : null,
                    receipt: requestBody.receipt,
                    sig: signature,
                    order_id: requestBody.orderId,
                    billingOrderId: requestBody.orderId,
                    adjust_id: (_k = user.adjustId) !== null && _k !== void 0 ? _k : null,
                    gps_adid: (_l = user.gpsAdid) !== null && _l !== void 0 ? _l : null,
                    idfa: (_m = user.idfa) !== null && _m !== void 0 ? _m : null,
                    idfv: (_o = user.idfv) !== null && _o !== void 0 ? _o : null,
                });
                user.glog('common_purchase_box', {
                    rsn,
                    add_rsn,
                    flag: 1,
                    type: cashShopDesc_1.CASH_SHOP_SALE_POINT_TYPE.CASH,
                    product_id: productId,
                    product_name: (_p = requestBody.productName) !== null && _p !== void 0 ? _p : null,
                    order_id: requestBody.orderId,
                    inven_id: (_r = (_q = billingApiResp.data) === null || _q === void 0 ? void 0 : _q.invenId) !== null && _r !== void 0 ? _r : null,
                });
                // 빌링 보관함에 자동으로 들어간다!
                // 횟수 제한 상품 처리.
                // 폴리싱(?) 필요. 실패하는 경우에 대한 방안을 고려 해봐야 함.
                try {
                    const result = await _applyCashShopRestrictedProductIfNeeded(user, productId, curTimeUtc);
                    if (!result) {
                        return;
                    }
                    respBody.sync = result.sync;
                    restrictedProductChangeForGLog = result.restrictedProductChange;
                }
                catch (err) {
                    let e;
                    if (err instanceof merror_1.MError) {
                        e = { mcode: err.mcode, message: err.message, extra: err.extra, stack: err.stack };
                    }
                    else if (err instanceof Error) {
                        e = { message: err.message, stack: err.stack };
                    }
                    else {
                        e = err;
                    }
                    mlog_1.default.error(`[${rsn}] restictedProductChange-apply-failed`, {
                        userId: user.userId,
                        requestBody,
                        billingApiResp,
                        error: e,
                    });
                }
            }
            else {
                mlog_1.default.warn(`[${rsn}] billing-api-failed`, {
                    userId: user.userId,
                    requestBody,
                    billingApiResp,
                });
            }
        })
            .then(() => {
            return (0, tuUpdateBillingProducts_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, hotSpotProduct, consecutiveProduct).then(() => {
                if (hotSpotProduct) {
                    user.userCashShop.setHotSpotProduct(hotSpotProduct);
                    lodash_1.default.merge(respBody.sync, {
                        add: { hotSpotProducts: { [hotSpotProduct.cmsId]: hotSpotProduct } },
                    });
                    let remain_cnt = null;
                    if (restrictedProductChangeForGLog) {
                        remain_cnt = cashShopCms.saleTypeVal - restrictedProductChangeForGLog.amount;
                    }
                    user.glog('hot_spot_product_start', {
                        rsn,
                        add_rsn,
                        id: cmsId,
                        name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
                        limit_type: cashShopCms.saleType,
                        remain_cnt: cashShopCms.saleType === cashShopDesc_1.CASH_SHOP_SALE_TYPE.UNLIMITED
                            ? 999
                            : remain_cnt !== null && remain_cnt !== void 0 ? remain_cnt : cashShopCms.saleTypeVal,
                        amt: 1,
                        pr_data: [],
                        reward_data: cmsEx.convertRewardFixedToGLogRewardData(cashShopCms.productRewardFixedId),
                        is_exposure: false,
                    });
                }
                if (consecutiveProduct) {
                    user.userCashShop.setConsecutiveProduct(consecutiveProduct);
                    lodash_1.default.merge(respBody.sync, {
                        add: {
                            cashShopConsecutiveProducts: { [consecutiveProduct.cmsId]: consecutiveProduct },
                        },
                    });
                }
            });
        })
            .then(() => {
            const accums = [
                {
                    achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.CASH_SHOP_BUY_COUNT,
                    targets: [cashShopCms.id],
                    addedValue: 1,
                },
            ];
            return user.userAchievement.accumulate(accums, user, respBody.sync, { user, rsn, add_rsn });
        })
            .then(() => {
            mlog_1.default.info('billingCompleteReservedPurchase is done.', {
                userId: user.userId,
                customInt1: cmsId,
            });
            return user.sendJsonPacket(packet.seqNum, packet.type, respBody);
        });
    }
}
exports.Cph_Common_BillingCompleteReservedPurchase = Cph_Common_BillingCompleteReservedPurchase;
async function _completeReservedPurchaseByStoreCode(user, reqBody) {
    var _a;
    // linegamesBillingApiClient.ts, CompleteReservedPurchaseReqBody.cfViewerCountry 참고
    const cfViewerCountry = (_a = user.countryIp) !== null && _a !== void 0 ? _a : '';
    switch (user.storeCode) {
        case 'GOOGLE_PLAY': {
            return mhttp_1.default.platformBillingApi.completeReservedPurchaseGoogle(user.accountId, user.userId.toString(), user.storeCode, cfViewerCountry, user.idfa, user.idfv, user.adjustId, user.udid, user.gpsAdid, undefined, //* 필요시 구현 필요. ( 클라에서 보내주는 작업 등 )
            reqBody.orderId, reqBody.receipt, reqBody.ignoreReceiptYn, reqBody.price, reqBody.microPrice, reqBody.currency, reqBody.memo, reqBody.googleSignature);
        }
        case 'APPLE_APP_STORE': {
            return mhttp_1.default.platformBillingApi.completeReservedPurchaseApple(user.accountId, user.userId.toString(), user.storeCode, cfViewerCountry, user.idfa, user.idfv, user.adjustId, user.udid, user.gpsAdid, reqBody.orderId, reqBody.receipt, reqBody.transactionId, reqBody.ignoreReceiptYn, reqBody.price, reqBody.microPrice, reqBody.currency, reqBody.memo);
        }
        case 'FLOOR_STORE': {
            return mhttp_1.default.platformBillingApi.completeReservedPurchaseFloor(user.accountId, user.userId.toString(), user.storeCode, cfViewerCountry, user.idfa, user.idfv, user.adjustId, user.udid, user.gpsAdid, reqBody.orderId, reqBody.receipt, reqBody.ignoreReceiptYn, reqBody.price, reqBody.microPrice, reqBody.currency, reqBody.memo, reqBody.signature);
        }
        case 'STEAM': {
            return mhttp_1.default.platformBillingApi.completeReservedPurchaseSteam(user.accountId, user.userId.toString(), user.storeCode, cfViewerCountry, user.idfa, user.idfv, user.adjustId, user.udid, user.gpsAdid, reqBody.orderId, reqBody.receipt, reqBody.ignoreReceiptYn, reqBody.price, reqBody.microPrice, reqBody.currency, reqBody.memo);
        }
        default:
            assert_1.default.fail(`invalid-store-code: ${user.storeCode}`);
    }
}
async function _applyCashShopRestrictedProductIfNeeded(user, productId, timeUtc) {
    const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
    if (!cashShopCms) {
        throw new merror_1.MError('failed-to-get-cash-shop-cms', merror_1.MErrorCode.INVALID_REQ_BODY_BILLING_COMPLETE_RESERVE_PURCHASE, { productId });
    }
    const restrictedProductChange = userCashShop_1.BillingUtil.buildRestrictedProductChange(user, cashShopCms, timeUtc);
    if (!restrictedProductChange) {
        return undefined;
    }
    const { userDbConnPoolMgr } = typedi_1.default.get(server_1.LobbyService);
    return (0, puCashShopRestrictedProductUpdate_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, restrictedProductChange).then(() => {
        user.userCashShop.setRestrictedProduct(restrictedProductChange);
        const sync = {
            add: {
                cashShopRestrictedProducts: {
                    [restrictedProductChange.cmsId]: restrictedProductChange,
                },
            },
        };
        return { sync, restrictedProductChange };
    });
}
//# sourceMappingURL=billingCompleteReservedPurchase.js.map