{"version": 3, "file": "billingSteamPurchaseInitTxn.js", "sourceRoot": "", "sources": ["../../../../src/lobbyd/packetHandler/common/billingSteamPurchaseInitTxn.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;AAG/E,qDAA8D;AAE9D,oEAA4C;AAE5C,yDAAiE;AAEjE,iGAAkF;AAsBlF,+EAA+E;AAC/E,MAAa,sCAAsC;IACjD,6EAA6E;IAC7E,gBAAe,CAAC;IAEhB,6EAA6E;IAC7E,aAAa,CAAC,IAAU;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6EAA6E;IAC7E,IAAI,CAAC,IAAU,EAAE,MAAe;QAC9B,IAAI,CAAC,eAAe,CAAC,iCAAgB,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,SAAS,KAAK,yCAAa,CAAC,YAAY,CAAC,KAAK,EAAE;YACvD,sDAAsD;YACtD,MAAM,IAAI,eAAM,CAAC,cAAc,EAAE,mBAAU,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;SAC7F;QAED,MAAM,OAAO,GAAgB,MAAM,CAAC,OAAO,CAAC;QAC5C,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAE1E,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,eAAM,CACd,kBAAkB,EAClB,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;SACH;QACD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,eAAM,CACd,wBAAwB,EACxB,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;SACH;QACD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,eAAM,CACd,wBAAwB,EACxB,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YAClC,MAAM,IAAI,eAAM,CACd,+BAA+B,EAC/B,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;SACH;QACD,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;YACjC,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;gBACnC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAChC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EACnC;gBACA,MAAM,IAAI,eAAM,CACd,uBAAuB,EACvB,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;aACH;YACD,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE;gBAC7C,MAAM,IAAI,eAAM,CACd,iDAAiD,EACjD,mBAAU,CAAC,gDAAgD,EAC3D,EAAE,OAAO,EAAE,CACZ,CAAC;aACH;SACF;QAED,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,eAAK,CAAC,kBAAkB,CAAC,oBAAoB,CAClD,OAAO,EACP,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,aAAa,EACb,aAAa,EACb,cAAc,CACf,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,cAAc,CAAe,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACnE,cAAc;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAtFD,wFAsFC"}