"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.spName = void 0;
const query = __importStar(require("../query"));
exports.spName = 'mp_u_user_load_cash_cn';
const spFunction = query.generateSPFunction(exports.spName);
async function default_1(connection, userId) {
    const qr = await spFunction(connection, userId);
    const rows = qr.rows;
    const paidRedGemBalance = Number.parseInt(rows[0][0].paidRedGemBalance);
    const freeRedGemBalance = Number.parseInt(rows[0][0].freeRedGemBalance);
    const result = {
        userId,
        paidRedGemBalance,
        freeRedGemBalance,
    };
    return result;
}
exports.default = default_1;
//# sourceMappingURL=puUserLoadCash_cn.js.map