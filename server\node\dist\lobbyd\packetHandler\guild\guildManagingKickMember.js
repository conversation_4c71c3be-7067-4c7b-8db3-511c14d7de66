"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Guild_ManagingKickMember = void 0;
const Container_1 = require("typedi/Container");
const cms_1 = __importDefault(require("../../../cms"));
const merror_1 = require("../../../motiflib/merror");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const lobby_1 = require("../../../motiflib/model/lobby");
const mutil_1 = require("../../../motiflib/mutil");
const mysqlUtil_1 = require("../../../mysqllib/mysqlUtil");
const tuGuildLeave_1 = __importDefault(require("../../../mysqllib/txn/tuGuildLeave"));
const guildPubsub_1 = require("../../guildPubsub");
const server_1 = require("../../server");
const userConnection_1 = require("../../userConnection");
const guildUtil_1 = require("../../guildUtil");
const rsn = 'guild_leave';
const add_rsn = 'kicked';
/**
 * 길드원 추방(길드장전용)
 */
// ----------------------------------------------------------------------------
class Cph_Guild_ManagingKickMember {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        const body = packet.bodyObj;
        const { kickUserId } = body;
        const { guildRedis, monitorRedis, userCacheRedis, userDbConnPoolMgr, townRedis } = Container_1.Container.get(server_1.LobbyService);
        const guildId = user.userGuild.guildId;
        if (!guildId) {
            throw new merror_1.MError('there-is-no-guild-joined.', merror_1.MErrorCode.GUILD_NOT_JOINED, {
                userId: user.userId,
            });
        }
        let guildData;
        let userLightInfos;
        let sync = {};
        let kickedMemberNid;
        let kickedMemberGrade;
        let kickedMemberNationCmsId;
        const now = (0, mutil_1.curTimeUtc)();
        //======================================================================================================
        return (guildUtil_1.GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
            .then((result) => {
            if (!result) {
                throw new merror_1.MError('cannot-find-guild.', merror_1.MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
                    userId: user.userId,
                    guildId,
                });
            }
            guildData = result.guildData;
            userLightInfos = result.userLightInfos;
            // 길드장 검사.
            // GuildUtil.ensureMaster(user, guildId, guildData.members);
            guildUtil_1.GuildUtil.ensureGuildMemberAccess(lobby_1.GUILD_MEMBER_GRADE_ACCESS_CATEGORY.MEMBER_MANAGE, user, guildId, guildData);
            if (user.userId === kickUserId) {
                throw new merror_1.MError('cannot-do-self-kick', merror_1.MErrorCode.GUILD_FAILED_SELF_KICK, {
                    userId: user.userId,
                    guildId,
                    kickUserId,
                });
            }
            // 길드원 확인.
            const member = guildData.members[kickUserId];
            if (!member) {
                throw new merror_1.MError('not-guild-member', merror_1.MErrorCode.GUILD_NOT_MEMBER, {
                    userId: user.userId,
                    guildId,
                    kickUserId,
                });
            }
            const manager = guildData.members[user.userId];
            if (!member) {
                throw new merror_1.MError('not-guild-member', merror_1.MErrorCode.GUILD_NOT_MEMBER, {
                    userId: user.userId,
                    guildId,
                    kickUserId,
                });
            }
            // --강퇴시행자보다 낮은 등급유저에게만 강퇴가능.
            if (manager.grade >= member.grade) {
                throw new merror_1.MError('unable-to-kick-higher-grade-member', merror_1.MErrorCode.UNABLE_TO_KICK_HIGHER_GRADE_MEMBER, {
                    userId: user.userId,
                    guildId,
                    kickUserId,
                    managerGrade: manager.grade,
                    kickMemberGrade: member.grade,
                });
            }
            kickedMemberNid = userLightInfos[kickUserId].pubId;
            kickedMemberGrade = guildData.members[kickUserId].grade;
            kickedMemberNationCmsId = userLightInfos[kickUserId].nationCmsId;
            delete guildData.members[kickUserId];
            const newScore = guildUtil_1.GuildUtil.makeSearchScore(guildData);
            // 멤버삭제는 RDB 실패와 상관없이 제거해주자.
            return guildRedis['deleteGuildMember'](kickUserId, guildId, guildData.guild.nationCmsId, newScore);
        })
            //======================================================================================================
            // RDB 길드 제거 업데이트
            //======================================================================================================
            .then(() => {
            return (0, tuGuildLeave_1.default)(userDbConnPoolMgr.getPoolByShardId((0, mysqlUtil_1.getUserDbShardId)(kickUserId)), kickUserId, 0, // guildId
            now);
        })
            //======================================================================================================
            // 길드 자동안내 업데이트
            //======================================================================================================
            .then(() => {
            if (userLightInfos[kickUserId]) {
                guildData.guild.autoNotificationType = lobby_1.GUILD_AUTO_NOTIFICATION_TYPE.KICKED;
                guildData.guild.autoNotificationParam1 = userLightInfos[kickUserId].name;
                guildData.guild.autoNotificationRegTimeUtc = now;
                return guildRedis['updateGuild'](guildId, JSON.stringify(guildData.guild));
            }
        })
            //======================================================================================================
            // 유저캐시 redis  업데이트
            //======================================================================================================
            .then(() => {
            userCacheRedis['setUserGuild'](kickUserId, 0).catch((err) => {
                mlog_1.default.error('userCacheRedis setUserGuild is failed at guildMemberKick.', {
                    err: err.message,
                    userId: kickUserId,
                });
            });
        })
            //======================================================================================================
            // 타운 길드 유저 점수 제거
            //======================================================================================================
            .then(() => {
            return townRedis['deleteTownGuildUserScore'](JSON.stringify(Object.keys(cms_1.default.Town)), guildId, kickUserId, cms_1.default.Const.InvestGuildPointPer.value, cms_1.default.Const.InvestGuildSharePointPer.value);
        })
            //======================================================================================================
            // 추방당한 유저가 접속 중일 경우 알림.
            //======================================================================================================
            .then((result) => {
            if (result) {
                mlog_1.default.info('[UPDATE-GUILD-SCORE-BY-LEAVING] kicked-by-master', {
                    guildId,
                    userId: user.userId,
                    kickUserId: kickUserId,
                    changes: JSON.parse(result),
                });
            }
            // 길드 채널에서 유저 삭제
            mhttp_1.default.platformChatApi
                .disallowGuildChannel(`GUILD_${guildId}`, kickUserId.toString())
                .catch((err) => {
                mlog_1.default.error('mhttp.chatd.disallowGuildChannel is failed.', {
                    err: err.message,
                    userId: user.userId,
                    guildId,
                });
            });
            if (userLightInfos[kickUserId] && userLightInfos[kickUserId].isOnline) {
                return mhttp_1.default.authd
                    .getLastLobbyOfOnlineUsers([kickUserId])
                    .then((ret) => {
                    if (ret && ret.userLobbies && ret.userLobbies.length > 0) {
                        const curLobby = ret.userLobbies[0].lastLobby;
                        return monitorRedis['getLobbydUrls'](JSON.stringify([curLobby]));
                    }
                    return null;
                })
                    .then((ret) => {
                    if (ret) {
                        const lobbydUrls = JSON.parse(ret);
                        if (lobbydUrls) {
                            const url = lobbydUrls[Object.keys(lobbydUrls)[0]];
                            const lobbyApi = mhttp_1.default.lobbypx.channel(url);
                            const notiPacket = {
                                guildId: guildId,
                            };
                            return lobbyApi.sendGuildMemberKickToKickedUser(kickUserId, notiPacket);
                        }
                    }
                })
                    .catch((err) => {
                    mlog_1.default.error('[GUILD] /guildMemberKickToKickedUser is failed.', {
                        kickUserId,
                        guildId,
                        err: err.message,
                    });
                });
            }
        })
            //======================================================================================================
            // 추방당한 유저에게 메일 전송.
            //======================================================================================================
            .then(() => {
            return guildUtil_1.GuildUtil.sendGuildMail([kickUserId], lobby_1.GUILD_MAIL.KICKED);
        })
            //======================================================================================================
            // 나머지 기존 길드원에게 추방정보를 알려준다.
            //======================================================================================================
            .then(() => {
            sync = {
                add: {
                    userGuild: {
                        guild: {
                            autoNotificationType: guildData.guild.autoNotificationType,
                            autoNotificationParam1: guildData.guild.autoNotificationParam1,
                            autoNotificationParam2: guildData.guild.autoNotificationParam2,
                        },
                    },
                },
                remove: {
                    userGuild: {
                        guild: {
                            members: {
                                [kickUserId]: true,
                            },
                        },
                    },
                },
            };
            (0, guildPubsub_1.onGuildPublish)(guildData, userLightInfos, [user.userId, kickUserId], sync);
        })
            //======================================================================================================
            // 결과 응답.
            //======================================================================================================
            .then(() => {
            gLog_guildLeave(user, kickedMemberNid, kickedMemberGrade, kickedMemberNationCmsId, guildId, guildData, userLightInfos);
            return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        }));
    }
}
exports.Cph_Guild_ManagingKickMember = Cph_Guild_ManagingKickMember;
function gLog_guildLeave(user, kickedMemberNid, kickedMemberGrade, kickedMemberNationCmsId, guildId, guildData, userLightInfos) {
    const guild_data = guildUtil_1.GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);
    let nation = null;
    if (kickedMemberNationCmsId) {
        const nationCms = cms_1.default.Nation[kickedMemberNationCmsId];
        nation = nationCms ? nationCms.name : null;
    }
    user.glog('guild_leave', {
        rsn,
        add_rsn,
        leave_nid: kickedMemberNid,
        nation,
        type: 1,
        grade: kickedMemberGrade,
        guild_data,
    });
}
//# sourceMappingURL=guildManagingKickMember.js.map