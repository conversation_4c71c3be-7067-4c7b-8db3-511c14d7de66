"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_ChatJoinChannel = void 0;
const lodash_1 = __importDefault(require("lodash"));
const userConnection_1 = require("../../userConnection");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const chatApiClient_1 = require("../../../motiflib/mhttp/chatApiClient");
const merrorCode_1 = require("../../../motiflib/merrorCode");
const merror_1 = require("../../../motiflib/merror");
const cms_1 = __importDefault(require("../../../cms"));
// ----------------------------------------------------------------------------
class Cph_Common_ChatJoinChannel {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    exec(user, packet) {
        const reqBody = packet.bodyObj;
        const { off } = reqBody;
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const userIdStr = user.userId.toString();
        // 들어가야 될 채널들
        const channelsToJoin = {};
        const channelAliases = {};
        // 시스템 채널
        if (shouldJoin(chatApiClient_1.CHANNEL_TYPE.SYSTEM, off)) {
            channelsToJoin[chatApiClient_1.CHANNEL_TYPE.SYSTEM] = 'SYSTEM';
            channelAliases[chatApiClient_1.CHANNEL_TYPE.SYSTEM] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.SYSTEM);
        }
        // 월드 채널
        if (shouldJoin(chatApiClient_1.CHANNEL_TYPE.WORLD, off)) {
            channelsToJoin[chatApiClient_1.CHANNEL_TYPE.WORLD] = 'WORLD';
            channelAliases['WORLD'] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.WORLD);
        }
        // 국가 채널
        if (shouldJoin(chatApiClient_1.CHANNEL_TYPE.NATION, off) && user.nationCmsId) {
            const channelName = user.nationCmsId.toString();
            channelsToJoin[chatApiClient_1.CHANNEL_TYPE.NATION] = channelName;
            channelAliases[user.nationCmsId.toString()] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.NATION, channelName);
        }
        // 길드
        if (shouldJoin(chatApiClient_1.CHANNEL_TYPE.GUILD, off) && user.userGuild.guildId) {
            const channelName = `GUILD_${user.userGuild.guildId}`;
            channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD] = channelName;
            channelAliases[channelName] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.GUILD, channelName);
        }
        // 지역 채널
        if (shouldJoin(chatApiClient_1.CHANNEL_TYPE.REGION, off)) {
            if (user.userState.isInTown()) {
                const townCmsId = user.userTown.getLastTownCmsId();
                if (!townCmsId) {
                    throw new merror_1.MError('no-town-cms-id-to-join', merrorCode_1.MErrorCode.LG_VOLANTE_ERROR, { townCmsId });
                }
                const channelName = townCmsId.toString();
                channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION] = channelName;
                channelAliases[townCmsId.toString()] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, channelName);
            }
            else if (user.userState.isInOcean()) {
                const channelName = user.userSailing.getSailState().region.id.toString();
                channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION] = channelName;
                channelAliases[channelName] = (0, chatApiClient_1.getAliasName)(chatApiClient_1.CHANNEL_TYPE.REGION, channelName);
            }
        }
        return mhttp_1.default.platformChatApi
            .getSessions(userIdStr)
            .then((ret) => {
            const promises = [];
            for (const channel of ret.session.channels) {
                if (cms_1.default.Town[channel] || cms_1.default.Region[channel]) {
                    if (channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION]) {
                        if (channel !== channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION]) {
                            // 이미 입장해 있는 지역 채널과 다르면 나가고 들어간다.
                            promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                            promises.push(mhttp_1.default.platformChatApi.channelJoin(channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION], userIdStr));
                        }
                        delete channelsToJoin[chatApiClient_1.CHANNEL_TYPE.REGION];
                    }
                    else {
                        promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                    }
                }
                else if (cms_1.default.Nation[channel]) {
                    if (channelsToJoin[chatApiClient_1.CHANNEL_TYPE.NATION]) {
                        if (channel !== channelsToJoin[chatApiClient_1.CHANNEL_TYPE.NATION]) {
                            // 이미 입장해 있는 국가 채널과 다르면 나가고 들어간다.
                            promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                            promises.push(mhttp_1.default.platformChatApi.channelJoin(channelsToJoin[chatApiClient_1.CHANNEL_TYPE.NATION], userIdStr));
                        }
                        delete channelsToJoin[chatApiClient_1.CHANNEL_TYPE.NATION];
                    }
                    else {
                        promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                    }
                }
                else if (channel === 'SYSTEM') {
                    if (channelsToJoin[chatApiClient_1.CHANNEL_TYPE.SYSTEM]) {
                        delete channelsToJoin[chatApiClient_1.CHANNEL_TYPE.SYSTEM];
                    }
                    else {
                        promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                    }
                }
                else if (channel === 'WORLD') {
                    if (channelsToJoin[chatApiClient_1.CHANNEL_TYPE.WORLD]) {
                        delete channelsToJoin[chatApiClient_1.CHANNEL_TYPE.WORLD];
                    }
                    else {
                        promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                    }
                }
                else if (channel.includes('GUILD_')) {
                    if (channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD]) {
                        if (channel !== channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD]) {
                            promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                            promises.push(joinGuildChannel(channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD], userIdStr));
                        }
                        delete channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD];
                    }
                    else {
                        promises.push(mhttp_1.default.platformChatApi.channelLeave(channel, userIdStr));
                    }
                }
            }
            lodash_1.default.forOwn(channelsToJoin, (channel) => {
                if (channel.includes('GUILD_')) {
                    promises.push(joinGuildChannel(channelsToJoin[chatApiClient_1.CHANNEL_TYPE.GUILD], userIdStr));
                }
                else {
                    promises.push(mhttp_1.default.platformChatApi.channelJoin(channel, userIdStr));
                }
            });
            return Promise.all(promises);
        })
            .then(() => {
            return user.sendJsonPacket(packet.seqNum, packet.type, { channelAliases, success: true });
        })
            .catch((err) => {
            if (err instanceof merror_1.MError) {
                if (err.mcode === merrorCode_1.MErrorCode.LG_VOLANTE_INVALID_SESSION) {
                    return user.sendJsonPacket(packet.seqNum, packet.type, { success: false });
                }
                // 기타 정의된 에러
                throw err;
            }
            throw new merror_1.MError(err.message, merrorCode_1.MErrorCode.INTERNAL_ERROR);
        });
    }
}
exports.Cph_Common_ChatJoinChannel = Cph_Common_ChatJoinChannel;
function shouldJoin(channel, offChannels) {
    return offChannels.findIndex((elem) => elem === channel) === -1;
}
function joinGuildChannel(channelName, userIdStr) {
    return mhttp_1.default.platformChatApi.channelJoin(channelName, userIdStr).catch((err) => {
        var _a;
        const errcode = ((_a = err.extra) === null || _a === void 0 ? void 0 : _a.volanteErrorCode) || undefined;
        if (!errcode) {
            throw err;
        }
        if (errcode === 15 /* CHANNEL_NOT_EXIST */) {
            mlog_1.default.error('[CHAT] Guild channel is not exist, so create new channel.', {
                userId: userIdStr,
                channelName,
            });
            return mhttp_1.default.platformChatApi.createGuildChannel(channelName, userIdStr, true).then(() => {
                return joinGuildChannel(channelName, userIdStr);
            });
        }
        else if (errcode === 32 /* NOT_ALLOW_USER */) {
            mlog_1.default.error('[CHAT] Not allowed from guild chat channel.', {
                userId: userIdStr,
                channelName,
            });
            return mhttp_1.default.platformChatApi.allowGuildChannel(channelName, userIdStr).then(() => {
                return joinGuildChannel(channelName, userIdStr);
            });
        }
        else {
            throw err;
        }
    });
}
//# sourceMappingURL=chatJoinChannel.js.map