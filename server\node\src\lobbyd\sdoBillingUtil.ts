// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import cms from "../cms";
import { MError } from "../motiflib/merror";
import { MErrorCode } from "../motiflib/merrorCode";
import { BuffSync } from "./userBuffs";
import { BillingUtil } from "./userCashShop";
import EnsuredGiveItem = BillingUtil.EnsuredGiveItem;
import { ReceiveProducts } from "./packetHandler/common/billingReceiveInvenPurchases";
import { curTimeUtc } from "../motiflib/mutil";
import mhttp from "../motiflib/mhttp";
import { User } from "./user";
import { CashShopDesc } from "../cms/cashShopDesc";
import mlog from "../motiflib/mlog";
import { REWARD_TYPE } from "../cms/rewardDesc";
import { GiveItem } from "../motiflib/mhttp/iPlatformBillingApiClient";

export interface ReceiveProductsResult extends BuffSync {
  failStep?: string; // 일단은 클라 로그 참고용으로 보냄.
  billingApiRespForFail?: unknown;
  /** 공간 부족 등 받을 수 없어 메일로 간 것들 */
  mailIds?: number[];
}

export class SdoBillingUtil {
  static async receiveProducts(user: User, cashShopCmsId: number): Promise<ReceiveProductsResult> {
    const cashShopCms = cms.CashShop[cashShopCmsId];
    if (!cashShopCms) {
      throw new MError('invalid-cash-shop-cms', MErrorCode.ADMIN_INVALID_CASH_SHOP_CMS_ID, {
        cashShopCmsId,
      });
    }

    const result: ReceiveProductsResult = { sync: {} };
    const mailIds: number[] = [];
    const ensuredGiveItemsList: EnsuredGiveItem[][] =
      SdoBillingUtil.buildEnsuredGiveItemsListByCashShopCms(cashShopCms);

    await ReceiveProducts(ensuredGiveItemsList, result, mailIds, user, curTimeUtc());
    if (mailIds.length > 0) {
      result.mailIds = mailIds;
    }

    const cashPair = await mhttp.platformBillingApi
          .queryCashPair(user.userId, user.storeCode, user.countryCreated);

    user.userPoints.onChargeByPurchaseProduct([
      {
        coinCd: 'red_gem',
        paymentType: 'PAID',
        balance: cashPair.paidRedGemBalance,
      },
      {
        coinCd: 'red_gem',
        paymentType: 'FREE',
        balance: cashPair.freeRedGemBalance,
      }
    ], null);
      
    return result;
  }

  private static buildEnsuredGiveItemsListByCashShopCms(cashShopCms: CashShopDesc): EnsuredGiveItem[][] {
    const ensuredGiveItemsList: EnsuredGiveItem[][] = [];

    if (cashShopCms.productRewardFixedId) {
      ensuredGiveItemsList.push(
        SdoBillingUtil.buildEnsuredGiveItemsByRewardFixedId(cashShopCms.productRewardFixedId)
      );
    }

    if (cashShopCms.eventPageId) {
      const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('EventPage', cashShopCms.eventPageId, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.dailySubscriptionId) {
      const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems(
        'DailySubscription',
        cashShopCms.dailySubscriptionId,
        1
      );
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.illustSkinId) {
      const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems(
        'IllustSkin',
        cashShopCms.illustSkinId,
        1
      );
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.userTitleId) {
      const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('UserTitle', cashShopCms.userTitleId, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.productWorldBuffId && cashShopCms.productWorldBuffId.length > 0) {
      const ensuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems('WorldBuff', cashShopCms.id, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    return ensuredGiveItemsList;
  }

  private static buildEnsuredGiveItems(
    productItemType: string,
    itemCd: number,
    amount: number
  ): EnsuredGiveItem[] {
    const ensureGiveItems: EnsuredGiveItem[] = [];
    const giveItem: GiveItem = {
      productItemType,
      itemCd: itemCd.toString(),
      coinChargeTypeCd: undefined,
      coinManageBalanceYn: 'N',
      amount,
    };
    const ret = BillingUtil.buildEnsuredGiveItem(giveItem);
    if (ret.bOk !== true) {
      mlog.warn(
        `failed to ensure give item (${BillingUtil.giveItemToString(giveItem)}). reason: ${
          ret.err?.reason
        }.`
      );
      return undefined;
    }

    ensureGiveItems.push(ret.value);
    return ensureGiveItems;
  }

  private static buildEnsuredGiveItemsByRewardFixedId(rewardFixedId: number): EnsuredGiveItem[] {
    const rewardFixedCms = cms.RewardFixed[rewardFixedId];
    if (!rewardFixedCms) {
      return undefined;
    }

    const ensuredGiveItems: EnsuredGiveItem[] = [];
    for (const elem of rewardFixedCms.rewardFixed) {
      const productItemType = SdoBillingUtil.rewardTypeToProductItemType(elem.Type);
      const fixedRewardEnsuredGiveItems = SdoBillingUtil.buildEnsuredGiveItems(
        productItemType,
        elem.Id,
        elem.Quantity
      );
      if (fixedRewardEnsuredGiveItems) {
        ensuredGiveItems.push(...fixedRewardEnsuredGiveItems);
      }
    }

    return ensuredGiveItems;
  }

  // REWARD_TYPE을 빌링 어드민 툴을 통해 등록되어 있는 타입으로 변환
  // userCashShop.ts의 buildEnsuredGiveItem의 함수 안에 타입 있음
  private static rewardTypeToProductItemType(rewardType: REWARD_TYPE): string {
    switch (rewardType) {
      case REWARD_TYPE.ITEM:
        return 'Item';
      case REWARD_TYPE.SHIP:
        return 'Ship';
      case REWARD_TYPE.MATE_EQUIP:
        return 'CEquip';
      case REWARD_TYPE.SHIP_SLOT_ITEM:
        return 'ShipSlot';
      case REWARD_TYPE.MATE:
        return 'Mate';
      case REWARD_TYPE.USER_TITLE:
        return 'UserTitle';
      case REWARD_TYPE.POINT:
        return 'Point';
      case REWARD_TYPE.PET:
        return 'Pet';
      default:
        return 'DEFAULT_TYPE';
    }
  }
}
